# Supabase Forum Anonymous Access Tests

Run each query in the SQL Editor as an anonymous user and mark which outcome occurred.

## 1. Community Forum Categories

```sql
SELECT * FROM public.community_forum_categories 
WHERE community_id = 'c43d1ae7-8ec5-492e-a3d7-d69db6047f89';
```
- [X] Returned rows (correct for public community)
- [ ] Returned no rows (incorrect - should return categories)
- [ ] Error occurred

```sql
SELECT * FROM public.community_forum_categories 
WHERE community_id = 'c43d1ae7-8ec5-492e-a3d7-d69db6047f91';
```
- [ ] Returned rows (incorrect - shouldn't see private community categories)
- [X] Returned no rows (correct for private community)
- [ ] Error occurred

## 2. Community Forum Posts

```sql
SELECT * FROM public.community_forum_posts 
WHERE community_id = 'c43d1ae7-8ec5-492e-a3d7-d69db6047f89' 
AND status = 'published'
LIMIT 5;
```
- [X] Returned rows (correct for public community)
- [ ] Returned no rows (incorrect - should return posts)
- [ ] Error occurred

```sql
SELECT * FROM public.community_forum_posts 
WHERE id = '08750cfd-9f23-4187-bd33-6a0283af4db5';
```
- [X] Returned the post (correct)
- [ ] Returned no rows (incorrect if post exists and is public)
- [ ] Error occurred

```sql
SELECT * FROM public.community_forum_posts 
WHERE community_id = 'c43d1ae7-8ec5-492e-a3d7-d69db6047f91' 
AND status = 'published'
LIMIT 5;
```
- [ ] Returned rows (incorrect - shouldn't see private community posts)
- [X] Returned no rows (correct for private community)
- [ ] Error occurred

## 3. Community Forum Comments

```sql
SELECT * FROM public.community_forum_comments 
WHERE post_id = '08750cfd-9f23-4187-bd33-6a0283af4db5';
```
- [X] Returned comments (correct for public community post)
- [ ] Returned no rows (incorrect if comments exist)
- [ ] Error occurred

```sql
SELECT * FROM public.community_forum_comments 
WHERE id = '633be75b-eff7-4e80-846a-bf2ebf813663';
```
- [X] Returned the comment (correct)
- [ ] Returned no rows (incorrect if comment exists on public post)
- [ ] Error occurred

```sql
-- Test comments on a private community post
SELECT * FROM public.community_forum_comments 
WHERE post_id = '9e23803f-6d14-4c06-8b9e-5345a0380811';
```
- [ ] Returned comments (incorrect - shouldn't see private community comments)
- [X] Returned no rows (correct for private community)
- [ ] Error occurred

```sql
-- Test specific comment in private community
SELECT * FROM public.community_forum_comments 
WHERE id = '060c12c1-16a7-4a4f-81d4-d039218af491';
```
- [ ] Returned the comment (incorrect - shouldn't see private community comment)
- [X] Returned no rows (correct for private community)
- [ ] Error occurred

## 4. Community Forum Reactions

```sql
SELECT * FROM public.community_forum_reactions 
WHERE post_id = '08750cfd-9f23-4187-bd33-6a0283af4db5';
```
- [X] Returned reactions (correct for public community post)
- [ ] Returned no rows (incorrect if reactions exist)
- [ ] Error occurred

```sql
SELECT * FROM public.community_forum_reactions 
WHERE id = 'a1e14e67-a655-474c-8e84-2d922f69b772';
```
- [X] Returned the reaction (correct)
- [ ] Returned no rows (incorrect if reaction exists on public post)
- [ ] Error occurred

```sql
-- Test reactions on a private community post
SELECT * FROM public.community_forum_reactions 
WHERE post_id = '9e23803f-6d14-4c06-8b9e-5345a0380811';
```
- [ ] Returned reactions (incorrect - shouldn't see private community reactions)
- [X] Returned no rows (correct for private community)
- [ ] Error occurred

## 5. Public Profiles

```sql
SELECT * FROM public.public_profiles 
LIMIT 5;
```
- [X] Returned profiles (correct)
- [ ] Returned no rows (incorrect)
- [ ] Error occurred

## 6. Communities Table

```sql
SELECT id, name, is_private FROM public.communities 
WHERE id IN ('c43d1ae7-8ec5-492e-a3d7-d69db6047f89', 'c43d1ae7-8ec5-492e-a3d7-d69db6047f91');
```
- [X] Returned both communities (correct)
- [ ] Returned only public community (incorrect)
- [ ] Returned no rows (incorrect)
- [ ] Error occurred

## 7. Direct Function Tests

```sql
SELECT * FROM public.get_paginated_community_forum_posts_base(
  'c43d1ae7-8ec5-492e-a3d7-d69db6047f89', 
  NULL, 
  3, 
  FALSE, 
  10, 
  0
);
```
- [X] Returned posts (correct for public community)
- [ ] Returned no rows (incorrect if posts exist)
- [ ] Error occurred

```sql
SELECT * FROM public.get_paginated_community_forum_posts(
  'c43d1ae7-8ec5-492e-a3d7-d69db6047f91', 
  NULL,
  3,
  NULL,
  10,
  0
);
```
- [ ] Returned posts (incorrect for private community)
- [X] Returned no rows (corrent for private community)
- [ ] Error occurred

```sql
SELECT * FROM public.get_paginated_community_forum_posts(
  'c43d1ae7-8ec5-492e-a3d7-d69db6047f89', 
  '07114115-dd5a-4f39-8247-432c76adf831'
);
```
- [X] Returned posts in the category (correct if posts exist in category)
- [ ] Returned no rows (incorrect if posts exist in category)
- [ ] Error occurred

```sql
-- Test function with private community
SELECT * FROM public.get_paginated_community_forum_posts(
  'c43d1ae7-8ec5-492e-a3d7-d69db6047f91', 
  NULL
);
```
- [ ] Returned posts (incorrect - shouldn't see private community posts)
- [X] Returned no rows (correct for private community)
- [ ] Error occurred

```sql
-- Test function with private community and category filter
SELECT * FROM public.get_paginated_community_forum_posts(
  'c43d1ae7-8ec5-492e-a3d7-d69db6047f91', 
  'cfe8e35e-fcc9-48ac-9562-153679abf96c'
);
```
- [ ] Returned posts (incorrect - shouldn't see private community posts)
- [X] Returned no rows (correct for private community)
- [ ] Error occurred 