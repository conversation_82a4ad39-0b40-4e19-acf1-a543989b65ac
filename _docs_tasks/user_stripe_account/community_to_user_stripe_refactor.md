# Refactor Stripe Connect: User Management, Community Linkage

This plan outlines the steps to refactor the Stripe Connect integration. The goal is to move the primary account management and onboarding UI to the User Settings, while retaining the ability to link that single user-owned account to multiple communities for processing payments specific to those communities.

## Phase 1: Database Schema & Function Modifications (Modify Existing Migrations)

- [x] **Modify `20250310212100_connected-accounts.sql`**
  - [x] Remove `connected_account.settings` permission value from `public.community_permissions` type.
  - [x] Remove the assignment of `connected_account.settings` permission from `public.community_role_permissions`.
  - [x] Update RLS policies for `public.connected_accounts`:
    - [x] Drop old community-based policies (e.g., `policy_connected_accounts_select_self`).
    - [x] Add `policy_connected_accounts_manage_user` (Allow user CRUD on their own account via `user_id = auth.uid()`).
    - [x] Add `policy_connected_accounts_read_for_community_context` (Allow read access based on community link and user's role/permission in that community, e.g., owner).
- [x] **Modify `20250310221035_connect_operations_functions.sql`**
  - [x] Update `public.create_stripe_connect_account` function:
    - [x] Change signature to accept `p_user_id` instead of `p_community_id`.
    - [x] Ensure `INSERT` uses `p_user_id` for the `user_id` column.
    - [x] Update the function to modify `public.users` table (setting `connected_account_id`) instead of `public.communities`.
  - [x] Verify `public.get_connected_account_details_by_community_id` function remains functional for community context reads.
  - [x] Add new `public.link_community_to_connected_account` function:
    - [x] Accepts `p_community_id` and `p_user_id`.
    - [x] Verifies user ownership/permission for the community.
    - [x] Fetches the user's `connected_account_id` from `public.users`.
    - [x] Updates `public.communities.connected_account_id` with the user's account ID.
    - [x] Set appropriate `SECURITY DEFINER` and grant `EXECUTE` permissions.
- [x] **Modify `20250310212102_connected-account-payouts.sql` (If exists & relevant)**
  - [x] Update RLS policy for `public.connected_account_payouts` to be user-centric (based on `connected_accounts.user_id` via join).

## Phase 2: Backend Service Layer Refactoring (`~/lib/stripe`)

- [ ] **Refactor Services (e.g., `ConnectAccountService`, `StripeProductsService`, etc.)**
  - [ ] Update method signatures and internal logic to handle both user-centric management and community-centric usage contexts.
  - [ ] Ensure functions creating community-specific items (products, prices) use the `connected_account_id` from the linked `public.communities` record.
  - [ ] Adapt webhook handlers if necessary.

## Phase 3: UI Implementation & Migration

- [x] **User Settings UI (`apps/web/app/home/<USER>/`)**

  - [x] Create directory structure: `_components/payments/_lib/server/actions/`.
  - [x] **Server Actions (`_lib/server/actions/`)**
    - [x] Move `create-account-links.ts`, `create-connected-account.ts`, `get-connected-account.ts` (renamed to `get-user-connected-account.ts`).
    - [x] Refactor actions to be user-centric.
  - [x] **UI Components (`_components/payments/`)**
    - [x] Move `payments-tab-content.tsx` and `stripe-connect-container.tsx`.
    - [x] Refactor `StripeConnectContainer`: Use user props, update URLs, fix imports (partially done, remaining errors noted).
    - [x] Refactor `PaymentsTabContent`: Fetch user details, pass user props, fix imports (partially done, remaining errors noted).
  - [x] **Integration**
    - [x] Integrate `PaymentsTabContent` into the User Settings page (`UserSettingsContainer`).

- [ ] **Community Settings UI (`apps/web/app/[community]/settings/`)**

  - [x] **Refactor Existing Components**
    - [x] Remove complex onboarding/management logic from any existing payment-related components (Achieved by deleting/confirming deletion of old directory).
  - [x] **Display Status Component**
    - [x] Create/update component to display read-only status of the linked connected account (fetch using `get_community_connect_status`).
  - [x] **"Link Account" Component**
    - [x] Create a new component shown only to the owner if `communities.connected_account_id` is NULL and `users.connected_account_id` is NOT NULL.
    - [x] Add a button "Link My Connected Account".
    - [x] Create a new Server Action (`linkCommunityAccountAction`) that calls the `link_community_to_connected_account` database function.

- [ ] **Subscription Management UI (Review & Adjust)**

  - [ ] Review components related to subscription creation/management within the community context.
  - [ ] Ensure they correctly use the `communities.connected_account_id` for Stripe operations.
  - [ ] Adjust UI/logic as needed based on the refactoring.

- [ ] **Cleanup**
  - [ ] Delete any fully redundant files/code from `apps/web/app/[community]/settings/_components/payments/` if the directory is now empty or unused.
  - [ ] Remove UI elements related to direct Stripe Connect _management_ from community settings.

## Phase 4: Finalization

- [ ] Update Storybook Stories affected by component moves/refactoring.
- [ ] Update i18n Locale Files (`public/locales/en/...`).
  - [ ] Add/modify keys for user settings UI.
  - [ ] Add/modify keys for community settings linking UI.
  - [ ] Remove keys related to old community management UI.
