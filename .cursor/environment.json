{"snapshot": "snapshot-20250520-432e97c3-ef72-426e-863b-154cbbeadc7b", "install": "pnpm install", "start": "sudo service docker start", "terminals": [{"name": "Supabase", "command": "pnpm run supabase:web:start", "description": "Start the Supabase server"}, {"name": "Stripe", "command": "pnpm stripe:listen", "description": "Start the Stripe server"}, {"name": "Next", "command": "pnpm dev", "dependencies": ["supabase", "stripe"], "description": "Start the Next.js server"}]}