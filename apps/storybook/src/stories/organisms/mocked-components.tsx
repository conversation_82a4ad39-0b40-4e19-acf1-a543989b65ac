import React, { useState } from 'react';

import {
  ChevronDown,
  ChevronUp,
  FileText,
  MoreHorizontal,
  Scroll,
  Video,
} from 'lucide-react';

import { Badge } from '@kit/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@kit/ui/dropdown-menu';
import {
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
} from '@kit/ui/shadcn-sidebar';

// Local implementation of cn function
function cn(...classes: (string | boolean | undefined)[]) {
  return classes.filter(Boolean).join(' ');
}

// Types that match our actual components
type Chapter = {
  id: string;
  title: string;
  sequence_order: number;
  course_id: string;
};

type Lesson = {
  id: string;
  title: string;
  chapter_id: string | null;
  sequence_order: number;
  status: string;
  content_type: string;
  _count: {
    quiz_questions: number;
  };
};

type Course = {
  id: string;
  slug: string;
  title: string;
};

// Chapter Item Component
export const SidebarChapterItemShadcn = ({
  chapter,
  lessons,
  expanded,
  onToggleExpanded,
  canMoveChapter,
  isEditable,
  currentLessonId,
  onLessonClick,
}: {
  chapter: Chapter;
  lessons: Lesson[];
  expanded: boolean;
  onToggleExpanded: () => void;
  canMoveChapter?: boolean;
  isEditable?: boolean;
  currentLessonId?: string;
  onLessonClick: (lessonId: string) => void;
}) => {
  return (
    <SidebarGroup className="mb-2 space-y-1 rounded-xl border-2 border-gray-200 p-1">
      <div className="group flex items-center justify-between px-2 py-1">
        <div className="grow">
          <SidebarGroupLabel onClick={onToggleExpanded} className="w-full">
            <div className="flex w-full items-center">
              <span className="shrink-0">
                {expanded ? (
                  <ChevronDown className="mr-2 h-4 w-4" />
                ) : (
                  <ChevronUp className="mr-2 h-4 w-4" />
                )}
              </span>
              <span className="text-foreground mr-2 text-base font-bold">
                {chapter.title}
              </span>
            </div>
          </SidebarGroupLabel>
        </div>
        {isEditable && (
          <div className="shrink-0">
            <DropdownMenu>
              <DropdownMenuTrigger className="hover:bg-muted p-1">
                <MoreHorizontal className="h-5 w-5" />
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>Edit Chapter</DropdownMenuItem>
                <DropdownMenuItem>Add Lesson</DropdownMenuItem>
                {canMoveChapter && (
                  <>
                    <DropdownMenuItem>Move Up</DropdownMenuItem>
                    <DropdownMenuItem>Move Down</DropdownMenuItem>
                  </>
                )}
                <DropdownMenuItem className="text-destructive">
                  Delete Chapter
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </div>

      {expanded && (
        <SidebarGroupContent className="block!" style={{ display: 'block' }}>
          <SidebarMenu className="pl-8">
            {lessons.map((lesson) => (
              <SidebarLessonItemShadcn
                key={lesson.id}
                lesson={lesson}
                isActive={currentLessonId === lesson.id}
                canEdit={isEditable}
                onClick={() => onLessonClick(lesson.id)}
              />
            ))}
          </SidebarMenu>
        </SidebarGroupContent>
      )}
    </SidebarGroup>
  );
};

// Lesson Item Component
export const SidebarLessonItemShadcn = ({
  lesson,
  isActive,
  canEdit,
  onClick,
}: {
  lesson: Lesson;
  isActive: boolean;
  canEdit?: boolean;
  onClick: () => void;
}) => {
  const getLessonIcon = () => {
    switch (lesson.content_type) {
      case 'video':
        return <Video className="h-4 w-4 opacity-70" />;
      case 'text':
        return <FileText className="h-4 w-4 opacity-70" />;
      case 'exercise':
        return <Scroll className="h-4 w-4 opacity-70" />;
      default:
        return <FileText className="h-4 w-4 opacity-70" />;
    }
  };

  return (
    <SidebarMenuItem
      className={cn(
        'my-1 rounded-xl border-2 border-gray-200 p-2',
        isActive
          ? 'bg-primary/10 text-primary font-medium'
          : 'hover:bg-primary/5 hover:text-primary',
      )}
    >
      <div className="flex w-full items-center justify-between">
        <div
          className="flex flex-1 cursor-pointer items-center px-2 py-1 text-left text-sm"
          onClick={onClick}
        >
          <span className="mr-2">{getLessonIcon()}</span>
          <span className="flex-1">{lesson.title}</span>
          {lesson.status === 'draft' && <Badge className="ml-2">Draft</Badge>}
          {lesson._count.quiz_questions > 0 && (
            <Badge variant="outline" className="ml-2">
              Quiz
            </Badge>
          )}
        </div>
        {canEdit && (
          <DropdownMenu>
            <DropdownMenuTrigger className="mr-1">
              <MoreHorizontal className="h-5 w-5" />
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>Edit Lesson</DropdownMenuItem>
              <DropdownMenuItem>Move Up</DropdownMenuItem>
              <DropdownMenuItem>Move Down</DropdownMenuItem>
              <DropdownMenuItem>Move to Chapter</DropdownMenuItem>
              <DropdownMenuItem className="text-destructive">
                Delete Lesson
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    </SidebarMenuItem>
  );
};

// Complete Sidebar Component
export const CourseSidebarShadcn = ({
  course,
  chapters,
  lessons,
  currentLessonId,
  onLessonClick,
  canEdit,
}: {
  course: Course;
  chapters: Chapter[];
  lessons: Lesson[];
  currentLessonId?: string;
  onLessonClick: (lessonId: string) => void;
  canEdit?: boolean;
}) => {
  const [expandedChapters, setExpandedChapters] = useState<Set<string>>(
    new Set(chapters.map((c) => c.id)),
  );

  const toggleChapter = (chapterId: string) => {
    setExpandedChapters((prev) => {
      const next = new Set(prev);
      if (next.has(chapterId)) {
        next.delete(chapterId);
      } else {
        next.add(chapterId);
      }
      return next;
    });
  };

  // Group lessons by chapter
  const lessonsByChapter = chapters.map((chapter) => ({
    chapter,
    lessons: lessons
      .filter((lesson) => lesson.chapter_id === chapter.id)
      .sort((a, b) => a.sequence_order - b.sequence_order),
  }));

  // Get standalone lessons
  const standaloneLessons = lessons
    .filter((lesson) => lesson.chapter_id === null)
    .sort((a, b) => a.sequence_order - b.sequence_order);

  return (
    <div className="bg-sidebar flex h-full w-[280px] flex-col border-r">
      <SidebarHeader className="flex items-center justify-between border-b p-3">
        <h2 className="truncate font-semibold">{course.title}</h2>
        {canEdit && (
          <DropdownMenu>
            <DropdownMenuTrigger className="hover:bg-muted rounded-md p-1">
              <MoreHorizontal className="h-5 w-5" />
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>Add Chapter</DropdownMenuItem>
              <DropdownMenuItem>Add Lesson</DropdownMenuItem>
              <DropdownMenuItem>Edit Course</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </SidebarHeader>
      <SidebarContent className="flex-1 overflow-y-auto p-3">
        {/* Render Chapters */}
        {lessonsByChapter.map(({ chapter, lessons }) => (
          <SidebarChapterItemShadcn
            key={chapter.id}
            chapter={chapter}
            lessons={lessons}
            expanded={expandedChapters.has(chapter.id)}
            onToggleExpanded={() => toggleChapter(chapter.id)}
            canMoveChapter={canEdit}
            isEditable={canEdit}
            currentLessonId={currentLessonId}
            onLessonClick={onLessonClick}
          />
        ))}

        {/* Render Standalone Lessons */}
        {standaloneLessons.length > 0 && (
          <div className="mb-2 mt-4">
            <h3 className="text-muted-foreground mb-2 px-2 text-sm font-medium">
              Standalone Lessons
            </h3>
            {standaloneLessons.map((lesson) => (
              <SidebarLessonItemShadcn
                key={lesson.id}
                lesson={lesson}
                isActive={currentLessonId === lesson.id}
                canEdit={canEdit}
                onClick={() => onLessonClick(lesson.id)}
              />
            ))}
          </div>
        )}
      </SidebarContent>
    </div>
  );
};
