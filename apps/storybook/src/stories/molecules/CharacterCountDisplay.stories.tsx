import { CharacterCountDisplay } from '@kit/ui/dojo/molecules/character-count-display';

export default {
  title: 'Molecules/CharacterCountDisplay',
  component: CharacterCountDisplay,
  parameters: {
    layout: 'centered',
  },
};

export const Default = {
  args: {
    characterCount: 150,
    wordCount: 25,
    characterLimit: 500,
  },
};

export const WithoutLimit = {
  args: {
    characterCount: 150,
    wordCount: 25,
  },
};

export const ApproachingLimit = {
  args: {
    characterCount: 400,
    wordCount: 75,
    characterLimit: 500,
  },
};

export const NearLimit = {
  args: {
    characterCount: 485,
    wordCount: 85,
    characterLimit: 500,
  },
};

export const CustomThresholds = {
  args: {
    characterCount: 350,
    wordCount: 60,
    characterLimit: 500,
    warningThreshold: 0.7,
    errorThreshold: 0.9,
  },
};

export const WordsOnly = {
  args: {
    characterCount: 150,
    wordCount: 25,
    showCharacters: false,
  },
};

export const CharactersOnly = {
  args: {
    characterCount: 150,
    wordCount: 25,
    characterLimit: 500,
    showWords: false,
  },
};
