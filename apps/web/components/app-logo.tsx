import Link from 'next/link';

import { cn } from '@kit/ui/utils';

function LogoImage({
  className,
  width = 75,
}: {
  className?: string;
  width?: number;
}) {
  const ASPECT_RATIO = 672 / 1263; // Based on viewBox="0 0 1263 672"
  const height = width * ASPECT_RATIO;

  return (
    <svg
      width={width}
      height={height}
      className={cn('w-[75px] sm:w-[100px] md:w-[120px]', className)}
      viewBox="0 0 1263 672"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1137.57 282.173C1152.85 329.288 1153.69 378.565 1139.73 416.795C1073.18 402.639 1040.72 361.748 1042.86 293.023C1007.24 297.251 973.181 301.182 939.186 305.576C936.885 305.873 933.972 309.302 933.133 311.86C916.961 361.096 925.482 408.364 969.03 443.407C1002.7 470.499 1041.11 486.607 1083.88 493.052C1103.64 496.031 1121.06 494.425 1137.32 480.426C1147.84 471.372 1161.32 465.414 1174.09 459.343C1219.73 437.633 1246.39 401.328 1257.84 353.034C1259.87 344.49 1261.21 335.783 1262.86 327.152V299.452C1260.7 289.333 1258.55 279.213 1256.26 268.518C1215.99 273.152 1176.88 277.65 1137.57 282.173Z"
        fill="#323232"
      />
      <path
        d="M1063.69 172.578C1060.9 172.512 1057.51 173.084 1055.35 174.656C1036 188.749 1016.87 203.132 997.676 217.431C998.223 218.281 998.77 219.131 999.317 219.982C1023.34 216.071 1047.37 212.163 1071.39 208.248C1116.9 200.831 1162.43 193.522 1207.88 185.793C1211.85 185.119 1215.34 181.664 1220.77 178.499C1217.12 176.985 1216.54 176.543 1215.95 176.527C1165.2 175.157 1114.44 173.784 1063.69 172.578Z"
        fill="#323232"
      />
      <path
        d="M819.464 579.532C837.798 553.83 852.501 526.055 855.897 494.237C862.278 434.438 867.444 374.511 873.209 313.656C869.776 313.656 866.868 313.365 864.035 313.7C827.357 318.043 790.738 322.957 753.99 326.569C743.968 327.553 741.605 331.212 741.419 340.345C740.199 400.572 738.558 460.791 736.969 521.011C736.83 526.314 736.085 531.601 735.521 538.027C719.92 530.6 706.079 524.01 691.802 517.212C680.26 537.233 668.998 556.533 658.08 576.023C656.801 578.305 656.903 581.703 657.233 584.481C659.175 600.813 661.449 617.105 663.473 633.427C666.359 656.697 666.265 656.91 688.809 661.588C706.138 665.185 723.591 668.176 740.989 671.437H752.895C754.804 668.427 756.544 665.293 758.649 662.426C778.916 634.79 799.565 607.427 819.464 579.532Z"
        fill="#323232"
      />
      <path
        d="M818.775 139.78C856.719 139.817 886.976 110.255 887.353 72.7748C887.726 35.7538 856.961 5.15942 819.302 5.09936C781.68 5.0393 751.186 35.4097 751.38 72.7448C751.572 109.821 781.655 139.742 818.775 139.78Z"
        fill="#EB1D24"
      />
      <path
        d="M764.207 192.299C761.63 192.488 757.367 194.707 757.027 196.556C753.133 217.706 749.848 238.965 746.212 261.351C792.291 253.901 836.163 246.886 879.978 239.523C882.546 239.092 886.171 236.199 886.709 233.857C890.452 217.583 893.509 201.152 897.058 183.407C888.117 183.945 880.935 184.337 873.76 184.815C837.238 187.243 800.71 189.614 764.207 192.299Z"
        fill="#323232"
      />
      <path
        d="M568.376 417.321C533.087 409.612 503.961 396.085 485.582 365.796C481.974 359.85 478.193 358.559 472.009 359.374C455.983 361.483 439.906 363.203 423.853 365.112C400.694 367.867 377.538 370.654 353.607 373.519C354.385 377.609 354.837 380.813 355.615 383.938C361.576 407.899 375.376 426.84 394.183 442.256C427.854 469.858 466.38 486.968 509.603 492.557C521.408 494.084 535.558 496.365 545.272 491.555C572.05 478.299 597.239 461.776 622.707 445.986C663.29 420.825 679.223 381.017 688.218 334.389C649.668 338.925 613.201 343.217 578.016 347.357C574.727 371.225 571.647 393.58 568.376 417.321Z"
        fill="#323232"
      />
      <path
        d="M483.767 243.964C487.532 236.193 491.509 234.305 499.759 236.963C529.092 246.413 553.377 261.956 567.721 290.669C606.121 284.388 644.142 278.169 683.434 271.743C673.927 241.855 656.832 220.223 627.245 209.074C633.452 198.198 639.246 188.042 645.509 177.066C642.796 176.741 641.26 176.416 639.719 176.395C600.11 175.841 560.147 178.344 521.007 173.798C491.521 170.375 470.649 179.877 449.55 197.417C416.554 224.845 385.814 253.895 367.056 293.248C362.385 303.047 359.25 313.574 354.714 325.613C394.614 319.048 431.835 312.924 469.332 306.757C469.385 284.087 474.265 263.581 483.767 243.964Z"
        fill="#323232"
      />
      <path
        d="M189.383 392.551C190.567 428.397 191.704 462.844 192.885 498.568C235.581 490.8 277.855 483.109 319.64 475.506C316.139 442.431 312.792 410.823 309.366 378.471C268.635 383.251 228.474 387.963 189.383 392.551Z"
        fill="#323232"
      />
      <path
        d="M9.32597 465.047C9.75514 467.746 13.3102 471.005 16.1563 472.095C32.1322 478.22 48.4457 483.47 64.399 489.649C70.2518 491.915 73.1455 490.13 76.7947 485.754C92.6301 466.765 108.834 448.08 124.973 429.343C133.95 418.921 143.061 408.614 153.589 396.566C102.029 402.571 52.8202 408.303 2.6525 414.147C4.89121 431.947 6.70452 448.561 9.32597 465.047Z"
        fill="#323232"
      />
      <path
        d="M201.091 24.1323C195.423 25.1783 193.424 26.8526 193.361 32.7612C192.972 69.3305 192.137 105.895 191.535 142.462C191.374 152.175 191.511 161.894 191.511 171.979C169.581 167.692 149.899 163.938 130.264 159.956C123.062 158.495 116.666 159.065 110.892 164.207C91.2146 181.732 70.6395 198.331 51.795 216.699C4.75066 262.555 -5.14786 319.98 2.43289 383.26C39.3679 377.217 76.3656 371.165 112.566 365.242V240.156C133.001 240.626 180.729 254.801 192.106 263.938C173.941 295.724 155.716 327.617 136.964 360.431C140.041 360.431 141.696 360.655 143.271 360.397C195.695 351.85 248.1 343.186 300.542 334.755C306.975 333.722 308.948 330.967 309.033 324.411C309.709 272.363 309.85 220.279 312.04 168.286C314.394 112.372 318.793 56.5422 322.299 0.675341H318.331C316.17 1.39484 314.058 2.36462 311.842 2.79757C274.943 10.0063 238.062 17.3102 201.091 24.1323Z"
        fill="#323232"
      />
    </svg>
  );
}

export function AppLogo({
  href,
  label,
  className,
}: {
  href?: string | null;
  className?: string;
  label?: string;
}) {
  if (href === null) {
    return <LogoImage className={className} />;
  }

  return (
    <Link aria-label={label ?? 'Home Page'} href={href ?? '/'}>
      <LogoImage className={className} />
    </Link>
  );
}
