# Dojo Web Scripts

This directory contains utility scripts for the Dojo web application.

## Seed Scripts

The `seed` directory contains scripts for seeding the database with sample data.

### Image Seeding

The image seeding scripts download sample images and upload them to Supabase storage, then update the database records with the image URLs.

#### Usage

```bash
# Run all seeding operations
npm run seed

# Run only specific operations
npm run seed -- --communities --users

# Only download images, don't upload to Supabase
npm run seed -- --download-only

# Only upload images to Supabase, don't download
npm run seed -- --seed-only

# Example: Only seed community images
npm run seed -- --communities --seed-only
```

#### Available Operations

- `--communities`: Download/upload community images (logo and cover)
- `--courses`: Download/upload course cover images
- `--users`: Download/upload user profile images
- `--pages`: Download/upload community page images

#### Directory Structure

The scripts expect the following directory structure for seed images:

```
seed_images/
├── communites/
│   ├── [community_slug]/
│   │   ├── public_images/
│   │   │   ├── logo.png
│   │   │   └── cover.png
│   │   ├── courses/
│   │   │   └── [course_slug].png
│   │   └── pages/
│   │       └── [page_slug]/
│   │           └── [001..NNN].png
└── user_profiles/
    └── [username].png
```

Images will be uploaded to Supabase storage with appropriate paths and linked to their respective database records.
