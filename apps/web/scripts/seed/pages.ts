import chalk from 'chalk';
import fs from 'node:fs';
import path from 'node:path';
import { fileURLToPath } from 'node:url';

import { Community } from '../../lib/communities/community/types';
import { CommunityPage } from '../../lib/communities/pages/types';
import {
  SupabaseClientType,
  downloadSampleImage,
  uploadImageFromFile,
} from './utils/storage-utils';

// Get __dirname equivalent in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to seeding images directory
const seedDir = path.join(__dirname, '..', '..', 'supabase', 'seed_images');

// Define types from database schema
type Page = CommunityPage & {
  communities: Pick<Community, 'slug'>;
};

/**
 * Downloads sample images for community pages
 * @param supabase Supabase client
 */
export async function downloadCommunityPageImages(
  supabase: SupabaseClientType,
): Promise<void> {
  console.log(chalk.cyan('Fetching community pages from the database...'));

  // Query pages from Supabase, including the slug and community info
  const { data: pagesData, error } = await supabase
    .from('community_pages')
    .select('id, community_id, title, slug, communities!inner(id, slug)');

  if (error) {
    console.error(chalk.red('Error fetching pages:'), error);
    return;
  }

  // Cast to proper type
  const pages = pagesData as unknown as Page[];
  console.log(
    chalk.dim(`Found ${chalk.cyan(pages.length)} pages to download images for`),
  );

  // Define image dimensions
  const imageWidth = 800;
  const imageHeight = 600;

  for (const page of pages) {
    try {
      if (!page.slug) {
        console.log(chalk.yellow(`Page ${page.id} has no slug, skipping...`));
        continue;
      }

      const communitySlug = page.communities.slug;
      if (!communitySlug) {
        console.log(
          chalk.yellow(`Page ${page.id} has no community slug, skipping...`),
        );
        continue;
      }

      // Create directory structure if it doesn't exist
      const pageImagesDir = path.join(
        seedDir,
        `communities/${communitySlug}/pages/${page.slug}`,
      );
      if (!fs.existsSync(pageImagesDir)) {
        fs.mkdirSync(pageImagesDir, { recursive: true });
      }

      // Generate 3-5 random images for this page
      const imageCount = Math.floor(Math.random() * 3) + 3; // 3-5 images
      for (let i = 1; i <= imageCount; i++) {
        // Generate unique seed for each image based on page id and image number
        const seed =
          (Number.parseInt(page.id.replace(/-/g, '').substring(0, 8), 16) + i) %
          1000;

        // Create URL for random image
        const imageUrl = `https://picsum.photos/${imageWidth}/${imageHeight}?random=${seed}.jpg`;

        // Format image number with leading zeros (001, 002, etc.)
        const imageNumber = String(i).padStart(3, '0');
        const imagePath = path.join(pageImagesDir, `${imageNumber}.jpg`);

        // Download the image
        await downloadSampleImage(imageUrl, imagePath);
      }

      console.log(
        chalk.dim(
          `Downloaded ${chalk.cyan(imageCount)} images for page ${chalk.green(page.slug)}`,
        ),
      );
    } catch (err) {
      console.error(chalk.red(`Error processing page ${page.id}:`), err);
    }
  }
}

/**
 * Uploads community page images and links them to pages
 * @param supabase Supabase client
 */
export async function seedCommunityPageImages(
  supabase: SupabaseClientType,
): Promise<void> {
  console.log(chalk.cyan('Uploading community page images...'));

  // Get pages from database, including the community slug
  const { data: pagesData, error } = await supabase
    .from('community_pages')
    .select('id, community_id, title, slug, communities!inner(id, slug)');

  if (error) {
    console.error(chalk.red('Error fetching pages:'), error);
    return;
  }

  // Cast to proper type
  const pages = pagesData as unknown as Page[];
  const bucketName = 'community_storage';
  let uploadedCount = 0;
  let linkedCount = 0;

  for (const page of pages) {
    if (!page.slug) {
      console.log(chalk.yellow(`Page ${page.id} has no slug, skipping...`));
      continue;
    }

    const communitySlug = page.communities.slug;
    if (!communitySlug) {
      console.log(
        chalk.yellow(`Page ${page.id} has no community slug, skipping...`),
      );
      continue;
    }

    // Check for page images directory
    const pageImagesDir = path.join(
      seedDir,
      `communities/${communitySlug}/pages/${page.slug}`,
    );

    if (!fs.existsSync(pageImagesDir)) {
      console.log(
        chalk.yellow(`No images directory found for page: ${page.slug}`),
      );
      continue;
    }

    // Get all image files in the page directory
    const imageFiles = fs
      .readdirSync(pageImagesDir)
      .filter(
        (file) =>
          file.endsWith('.png') ||
          file.endsWith('.jpg') ||
          file.endsWith('.jpeg'),
      );

    console.log(
      chalk.dim(
        `Found ${chalk.cyan(imageFiles.length)} images for page: ${chalk.green(page.slug)}`,
      ),
    );

    let displayOrder = 1;
    for (const imageFile of imageFiles) {
      const imageFilePath = path.join(pageImagesDir, imageFile);

      // Use pattern: community_storage/<community_id>/pages/<page_id>/<file-name>
      const storagePath = `${page.community_id}/pages/${page.id}/${imageFile}`;

      // Upload the image
      const imageUrl = await uploadImageFromFile(
        supabase,
        imageFilePath,
        bucketName,
        storagePath,
      );

      if (imageUrl) {
        // Add to community_page_media table and link to page
        const { data: mediaData, error: mediaError } = await supabase
          .from('community_page_media')
          .insert({
            page_id: page.id,
            url: imageUrl,
            media_type: 'image',
            display_order: displayOrder,
            created_at: new Date().toISOString(),
          })
          .select('id');

        if (mediaError) {
          console.error(
            chalk.red(`Error adding page media for ${page.id}:`),
            mediaError,
          );
        } else if (mediaData && mediaData.length > 0) {
          linkedCount++;
        }

        uploadedCount++;
      }

      displayOrder++;
    }
  }

  console.log(
    `\n${chalk.cyan('Completed')}: ${chalk.green(uploadedCount)} images uploaded, ${chalk.green(linkedCount)} linked to pages`,
  );
}
