import chalk from 'chalk';
import fs from 'node:fs';
import path from 'node:path';
import { fileURLToPath } from 'node:url';

import {
  SupabaseClientType,
  downloadSampleImage,
  uploadImageFromFile,
} from './utils/storage-utils';

// Get __dirname equivalent in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to seeding images directory
const seedDir = path.join(__dirname, '..', '..', 'supabase', 'seed_images');

/**
 * Downloads sample images for each user
 * @param supabase Supabase client
 */
export async function downloadUserImages(
  supabase: SupabaseClientType,
): Promise<void> {
  console.log(chalk.cyan('Fetching users from the database...'));

  // Query users from Supabase, including username
  const { data: users, error } = await supabase
    .from('users')
    .select('id, username');

  if (error) {
    console.error(chalk.red('Error fetching users:'), error);
    return;
  }

  console.log(
    chalk.dim(`Found ${chalk.cyan(users.length)} users to download images for`),
  );

  // Create the directory if it doesn't exist
  const userImagesDir = path.join(seedDir, 'user_profiles');
  if (!fs.existsSync(userImagesDir)) {
    fs.mkdirSync(userImagesDir, { recursive: true });
  }

  // Define image dimensions
  const profileWidth = 200;
  const profileHeight = 200;

  // Download images sequentially (not in parallel) to avoid rate limiting
  for (const user of users) {
    try {
      if (!user.username) {
        console.log(
          chalk.yellow(`User ${user.id} has no username, using ID instead.`),
        );
        continue;
      }

      // Strip numeric suffix from username (e.g., "bruce-lee-0001" -> "bruce-lee")
      const baseUsername = user.username.replace(/-\d{4}$/, '');

      // Generate random seed based on user id to get consistent but different images
      const seed =
        Number.parseInt(user.id.replace(/-/g, '').substring(0, 8), 16) % 1000;

      // Create URL for random image with consistent dimensions
      const profileUrl = `https://picsum.photos/${profileWidth}/${profileHeight}?random=${seed}.jpg`;

      // Full path for the user's profile image using the base username
      const profilePath = path.join(userImagesDir, `${baseUsername}.jpg`);

      // Download profile image
      await downloadSampleImage(profileUrl, profilePath);
    } catch (err) {
      console.error(chalk.red(`Error processing user ${user.id}:`), err);
    }
  }
}

/**
 * Uploads user profile images and updates user records
 * @param supabase Supabase client
 */
export async function seedUserImages(
  supabase: SupabaseClientType,
): Promise<void> {
  console.log(chalk.cyan('Uploading user profile images...'));

  // Get users from database, including username
  const { data: users, error } = await supabase
    .from('users')
    .select('id, username');

  if (error) {
    console.error(chalk.red('Error fetching users:'), error);
    return;
  }

  console.log(chalk.dim(`Processing ${chalk.cyan(users.length)} users`));

  const bucketName = 'user_storage';
  let updatedCount = 0;

  for (const user of users) {
    if (!user.username) {
      console.log(chalk.yellow(`User ${user.id} has no username, skipping...`));
      continue;
    }

    // Strip numeric suffix from username (e.g., "bruce-lee-0001" -> "bruce-lee")
    const baseUsername = user.username.replace(/-\d{4}$/, '');

    // Check for user profile image file with base username
    const userImagesDir = path.join(seedDir, 'user_profiles');
    const profileFile = path.join(userImagesDir, `${baseUsername}.jpg`);

    // Upload profile image if exists
    if (fs.existsSync(profileFile)) {
      // Use the correct path pattern: user_storage/<user_id>/<user_id>-profile.jpg
      const imagePath = `${user.id}/${user.id}-profile.jpg`;

      const profileUrl = await uploadImageFromFile(
        supabase,
        profileFile,
        bucketName,
        imagePath,
      );

      if (profileUrl) {
        // Update the user record with the image URL
        const { error: updateError } = await supabase
          .from('users')
          .update({ picture_url: profileUrl })
          .eq('id', user.id);

        if (updateError) {
          console.error(
            chalk.red(`Error updating user ${user.id}:`),
            updateError,
          );
        } else {
          process.stdout.write(chalk.green('✓ '));
          updatedCount++;
        }
      }
    } else {
      console.log(
        chalk.yellow(`No profile image found for user: ${user.username}`),
      );
    }
  }

  console.log(
    `\n${chalk.cyan('Completed')}: ${chalk.green(updatedCount)} user profiles updated`,
  );
}
