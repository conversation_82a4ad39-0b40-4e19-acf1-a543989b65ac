import { SupabaseClient } from '@supabase/supabase-js';

import chalk from 'chalk';
import fs from 'node:fs';
import path from 'node:path';

import type { Database } from '../../../lib/database.types';

export type SupabaseClientType = SupabaseClient<Database>;

/**
 * Uploads an image from a local file to Supabase storage
 * @param supabase Supabase client instance
 * @param filePath Local file path
 * @param bucketName Storage bucket name
 * @param storagePath Path within the bucket
 * @returns Public URL of the uploaded file or null if failed
 */
export async function uploadImageFromFile(
  supabase: SupabaseClientType,
  filePath: string,
  bucketName: string,
  storagePath: string,
): Promise<string | null> {
  try {
    if (!fs.existsSync(filePath)) {
      console.error(chalk.red(`File does not exist: ${filePath}`));
      return null;
    }

    const fileBuffer = fs.readFileSync(filePath);
    const fileExt = path.extname(filePath);
    const fileName = path.basename(filePath);

    // More concise log without the full path
    process.stdout.write(
      chalk.dim(
        `Uploading ${chalk.cyan(fileName)} to ${chalk.yellow(bucketName)} `,
      ),
    );

    const { error } = await supabase.storage
      .from(bucketName)
      .upload(storagePath, fileBuffer, {
        contentType: `image/${fileExt.substring(1)}`,
        upsert: true,
      });

    if (error) {
      process.stdout.write(chalk.red('✗\n'));
      console.error(chalk.red(`Error uploading ${fileName}:`), error.message);
      return null;
    }

    process.stdout.write(chalk.green('✓\n'));

    // Get the public URL
    const { data: urlData } = supabase.storage
      .from(bucketName)
      .getPublicUrl(storagePath);

    return urlData.publicUrl;
  } catch (err) {
    process.stdout.write(chalk.red('✗\n'));
    console.error(
      chalk.red(`Error processing ${path.basename(filePath)}:`),
      err,
    );
    return null;
  }
}

/**
 * Downloads an image from a URL to a local file
 * @param url Source URL
 * @param destination Local destination path
 * @returns True if download was successful, false otherwise
 */
export async function downloadSampleImage(
  url: string,
  destination: string,
): Promise<boolean> {
  // Create directory if it doesn't exist
  const dir = path.dirname(destination);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(chalk.dim(`Created directory: ${chalk.cyan(dir)}`));
  }

  // Skip if file already exists
  if (fs.existsSync(destination)) {
    console.log(
      chalk.dim(`File exists: ${chalk.cyan(path.basename(destination))}`),
    );
    return true;
  }

  try {
    // Only log the filename, not the full path
    const fileName = path.basename(destination);
    process.stdout.write(chalk.dim(`Downloading ${chalk.cyan(fileName)} `));

    const response = await fetch(url);
    if (!response.ok) {
      process.stdout.write(chalk.red('✗\n'));
      throw new Error(`Failed to download image: ${response.statusText}`);
    }

    const buffer = await response.arrayBuffer();
    fs.writeFileSync(destination, Buffer.from(buffer));

    process.stdout.write(chalk.green('✓\n'));
    return true;
  } catch (err) {
    process.stdout.write(chalk.red('✗\n'));
    console.error(chalk.red('Error downloading image:'), err);
    return false;
  }
}

/**
 * Creates required storage buckets if they don't exist
 * @param supabase Supabase client instance
 */
export async function ensureStorageBuckets(
  supabase: SupabaseClientType,
): Promise<void> {
  console.log(chalk.cyan('\n🪣 Checking storage buckets...'));

  const buckets = [
    { id: 'community_storage', name: 'community_storage', public: true },
    { id: 'user_storage', name: 'user_storage', public: true },
  ];

  const { data: existingBuckets, error } = await supabase.storage.listBuckets();
  if (error) {
    console.error(chalk.red('Error checking buckets:'), error);
    return;
  }

  for (const bucket of buckets) {
    if (!existingBuckets.find((b) => b.id === bucket.id)) {
      process.stdout.write(
        chalk.dim(`Creating bucket ${chalk.yellow(bucket.id)} `),
      );
      const { error: createError } = await supabase.storage.createBucket(
        bucket.id,
        {
          public: bucket.public,
        },
      );

      if (createError) {
        process.stdout.write(chalk.red('✗\n'));
        console.error(
          chalk.red(`Error creating bucket ${bucket.id}:`),
          createError,
        );
      } else {
        process.stdout.write(chalk.green('✓\n'));
      }
    } else {
      console.log(
        chalk.dim(`Bucket ${chalk.yellow(bucket.id)} already exists`),
      );
    }
  }
}
