import { createClient } from '@supabase/supabase-js';

import { config } from 'dotenv';
import path from 'node:path';
import { fileURLToPath } from 'node:url';

import type { Database } from '../../../lib/database.types';

// Get __dirname equivalent in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to the root directory (3 levels up from utils)
export const rootDir = path.join(__dirname, '../../..');

/**
 * Creates and returns a configured Supabase client
 * @returns Configured Supabase client
 */
export function createSupabaseClient() {
  // Load environment variables from .env file
  config({ path: path.join(rootDir, '.env.local') });

  // Check for required environment variables
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL) {
    throw new Error(
      'NEXT_PUBLIC_SUPABASE_URL environment variable is required',
    );
  }

  if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {
    throw new Error(
      'SUPABASE_SERVICE_ROLE_KEY environment variable is required',
    );
  }

  // Setup Supabase client with service role key to bypass RLS
  return createClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY,
    {
      auth: {
        persistSession: false,
        autoRefreshToken: false,
      },
    },
  );
}
