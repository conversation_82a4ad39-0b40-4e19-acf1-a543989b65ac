import chalk from 'chalk';
import fs from 'node:fs';
import path from 'node:path';
import { fileURLToPath } from 'node:url';

import { Community } from '../../lib/communities/community/types';
import { CommunityCourse } from '../../lib/communities/courses/types';
import {
  SupabaseClientType,
  downloadSampleImage,
  uploadImageFromFile,
} from './utils/storage-utils';

// Get __dirname equivalent in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to seeding images directory
const seedDir = path.join(__dirname, '..', '..', 'supabase', 'seed_images');

// Define types from database schema
type Course = CommunityCourse & {
  communities: Pick<Community, 'slug'>;
};

/**
 * Downloads sample images for each course
 * @param supabase Supabase client
 */
export async function downloadCourseImages(
  supabase: SupabaseClientType,
): Promise<void> {
  console.log(chalk.cyan('Fetching courses from the database...'));

  // Query courses from Supabase, including the slug and community info
  const { data: coursesData, error } = await supabase
    .from('community_courses')
    .select('id, community_id, title, slug, communities!inner(id, slug)');

  if (error) {
    console.error(chalk.red('Error fetching courses:'), error);
    return;
  }

  // Cast to proper type
  const courses = coursesData as unknown as Course[];
  console.log(
    chalk.dim(
      `Found ${chalk.cyan(courses.length)} courses to download images for`,
    ),
  );

  // Define image dimensions
  const coverWidth = 384;
  const coverHeight = 192;

  for (const course of courses) {
    try {
      if (!course.slug) {
        console.log(
          chalk.yellow(`Course ${course.id} has no slug, skipping...`),
        );
        continue;
      }

      const communitySlug = course.communities.slug;
      if (!communitySlug) {
        console.log(
          chalk.yellow(
            `Course ${course.id} has no community slug, skipping...`,
          ),
        );
        continue;
      }

      // Generate random seed based on course id to get consistent but different images
      const seed =
        Number.parseInt(course.id.replace(/-/g, '').substring(0, 8), 16) % 1000;

      // Create URL for random image with consistent dimensions
      const coverUrl = `https://picsum.photos/${coverWidth}/${coverHeight}?random=${seed}.jpg`;

      // Create directory structure if it doesn't exist
      const courseImagesDir = path.join(
        seedDir,
        `communities/${communitySlug}/courses`,
      );
      if (!fs.existsSync(courseImagesDir)) {
        fs.mkdirSync(courseImagesDir, { recursive: true });
      }

      // Full path for the course's cover image using the slug
      const coverPath = path.join(courseImagesDir, `${course.slug}.jpg`);

      // Download cover image
      await downloadSampleImage(coverUrl, coverPath);
    } catch (err) {
      console.error(chalk.red(`Error processing course ${course.id}:`), err);
    }
  }
}

/**
 * Uploads course images and updates course records
 * @param supabase Supabase client
 */
export async function seedCourseImages(
  supabase: SupabaseClientType,
): Promise<void> {
  console.log(chalk.cyan('Uploading course cover images...'));

  // Get courses from database, including the community slug
  const { data: coursesData, error } = await supabase
    .from('community_courses')
    .select('id, community_id, title, slug, communities!inner(id, slug)');

  if (error) {
    console.error(chalk.red('Error fetching courses:'), error);
    return;
  }

  // Cast to proper type
  const courses = coursesData as unknown as Course[];
  console.log(chalk.dim(`Processing ${chalk.cyan(courses.length)} courses`));

  const bucketName = 'community_storage';
  let updatedCount = 0;

  for (const course of courses) {
    if (!course.slug) {
      console.log(chalk.yellow(`Course ${course.id} has no slug, skipping...`));
      continue;
    }

    const communitySlug = course.communities.slug;
    if (!communitySlug) {
      console.log(
        chalk.yellow(`Course ${course.id} has no community slug, skipping...`),
      );
      continue;
    }

    // Check for cover image file using the slug
    const coverFile = path.join(
      seedDir,
      `communities/${communitySlug}/courses/${course.slug}.jpg`,
    );

    // Upload cover image if exists
    if (fs.existsSync(coverFile)) {
      // Use the pattern: community_storage/<community_id>/courses/<course_id>/<course_id>-cover.jpg
      const imagePath = `${course.community_id}/courses/${course.id}/${course.id}-cover.jpg`;

      const coverUrl = await uploadImageFromFile(
        supabase,
        coverFile,
        bucketName,
        imagePath,
      );

      if (coverUrl) {
        // Update the course record with the image URL
        const { error: updateError } = await supabase
          .from('community_courses')
          .update({ cover_url: coverUrl })
          .eq('id', course.id);

        if (updateError) {
          console.error(
            chalk.red(`Error updating course ${course.id}:`),
            updateError,
          );
        } else {
          process.stdout.write(chalk.green('✓ '));
          updatedCount++;
        }
      }
    } else {
      console.log(
        chalk.yellow(`No cover image found for course: ${course.slug}`),
      );
    }
  }

  console.log(
    `\n${chalk.cyan('Completed')}: ${chalk.green(updatedCount)} courses updated`,
  );
}
