import chalk from 'chalk';
import fs from 'node:fs';
import path from 'node:path';
import { fileURLToPath } from 'node:url';

import {
  SupabaseClientType,
  downloadSampleImage,
  uploadImageFromFile,
} from './utils/storage-utils';

// Get __dirname equivalent in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to seeding images directory
const seedDir = path.join(__dirname, '..', '..', 'supabase', 'seed_images');

/**
 * Downloads sample images for each community
 * @param supabase Supabase client
 */
export async function downloadCommunityImages(
  supabase: SupabaseClientType,
): Promise<void> {
  console.log(chalk.cyan('Fetching communities from the database...'));

  // Query communities from Supabase
  const { data: communities, error } = await supabase
    .from('communities')
    .select('id, slug');

  if (error) {
    console.error(chalk.red('Error fetching communities:'), error);
    return;
  }

  console.log(
    chalk.dim(
      `Found ${chalk.cyan(communities.length)} communities to download images for`,
    ),
  );

  // Define image dimensions
  const logoWidth = 200;
  const logoHeight = 200;
  const coverWidth = 384;
  const coverHeight = 192;

  for (const community of communities) {
    // Generate random seed based on community id to get consistent but different images
    const logoSeed =
      Number.parseInt(community.id.replace(/-/g, '').substring(0, 8), 16) %
      1000;
    const coverSeed = (logoSeed + 1) % 1000;

    // Create URLs for random images with consistent dimensions
    const logoUrl = `https://picsum.photos/${logoWidth}/${logoHeight}?random=${logoSeed}.jpg`;
    const coverUrl = `https://picsum.photos/${coverWidth}/${coverHeight}?random=${coverSeed}.jpg`;

    // Create directory structure if it doesn't exist
    const communityDir = path.join(seedDir, `communities/${community.slug}`);
    const publicImagesDir = path.join(communityDir, 'public_images');
    if (!fs.existsSync(publicImagesDir)) {
      fs.mkdirSync(publicImagesDir, { recursive: true });
    }

    // Download logo
    await downloadSampleImage(logoUrl, path.join(publicImagesDir, 'logo.jpg'));

    // Download cover
    await downloadSampleImage(
      coverUrl,
      path.join(publicImagesDir, 'cover.jpg'),
    );
  }
}

/**
 * Uploads community images and updates community records
 * @param supabase Supabase client
 */
export async function seedCommunityImages(
  supabase: SupabaseClientType,
): Promise<void> {
  // Get communities from database
  const { data: communities, error } = await supabase
    .from('communities')
    .select('id, name, slug');

  if (error) {
    console.error(chalk.red('Error fetching communities:'), error);
    return;
  }

  console.log(
    chalk.dim(`Processing ${chalk.cyan(communities.length)} communities`),
  );

  const bucketName = 'community_storage';
  let updatedCount = 0;

  for (const community of communities) {
    // Check for logo and cover files using the new directory structure
    const logoFile = path.join(
      seedDir,
      `communities/${community.slug}/public_images/logo.jpg`,
    );
    const coverFile = path.join(
      seedDir,
      `communities/${community.slug}/public_images/cover.jpg`,
    );

    let logoUrl = null;
    let coverUrl = null;

    // Upload logo if exists
    if (fs.existsSync(logoFile)) {
      // Use the correct path pattern: community_storage/<community_id>/public_images/<community_id>-logo.jpg
      const logoPath = `${community.id}/public_images/${community.id}-logo.jpg`;
      logoUrl = await uploadImageFromFile(
        supabase,
        logoFile,
        bucketName,
        logoPath,
      );
    } else {
      console.log(
        chalk.yellow(`No logo found for community: ${community.slug}`),
      );
    }

    // Upload cover if exists
    if (fs.existsSync(coverFile)) {
      // Use the correct path pattern: community_storage/<community_id>/public_images/<community_id>-cover.jpg
      const coverPath = `${community.id}/public_images/${community.id}-cover.jpg`;
      coverUrl = await uploadImageFromFile(
        supabase,
        coverFile,
        bucketName,
        coverPath,
      );
    } else {
      console.log(
        chalk.yellow(`No cover found for community: ${community.slug}`),
      );
    }

    // Update the community records with the image URLs
    if (logoUrl || coverUrl) {
      const updates: Record<string, string> = {};
      if (logoUrl) updates.logo_url = logoUrl;
      if (coverUrl) updates.cover_url = coverUrl;

      const { error: updateError } = await supabase
        .from('communities')
        .update(updates)
        .eq('id', community.id);

      if (updateError) {
        console.error(
          chalk.red(`Error updating community ${community.name}:`),
          updateError,
        );
      } else {
        process.stdout.write(chalk.green('✓ '));
        updatedCount++;
      }
    }
  }

  console.log(`\nCompleted: ${chalk.green(updatedCount)} communities updated`);
}
