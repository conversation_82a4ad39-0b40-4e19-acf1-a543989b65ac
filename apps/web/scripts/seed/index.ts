#!/usr/bin/env node
import chalk from 'chalk';
import { parseArgs } from 'node:util';

import { setupStorageBuckets } from './buckets';
import { downloadCommunityImages, seedCommunityImages } from './communities';
import { downloadCourseImages, seedCourseImages } from './courses';
import { downloadCommunityPageImages, seedCommunityPageImages } from './pages';
import { downloadUserImages, seedUserImages } from './users';
import { createSupabaseClient } from './utils/supabase-client';

type SeedOptions = {
  communities: boolean;
  courses: boolean;
  users: boolean;
  pages: boolean;
  downloadOnly: boolean;
  seedOnly: boolean;
  buckets: boolean;
};

/**
 * Main entry point for seeding images
 */
async function seedImages(options: SeedOptions) {
  console.log(chalk.bold.blue('🌱 Starting image seeding process...\n'));

  // Log which operations will be performed
  const operations = [];
  if (options.buckets) operations.push('Storage buckets');
  if (options.communities) operations.push('Community images');
  if (options.courses) operations.push('Course images');
  if (options.users) operations.push('User profile images');
  if (options.pages) operations.push('Community page images');

  if (operations.length === 0) {
    console.log(
      chalk.yellow('⚠️ No specific operations selected, running all...'),
    );
    options = {
      communities: true,
      courses: true,
      users: true,
      pages: true,
      buckets: true,
      downloadOnly: options.downloadOnly,
      seedOnly: options.seedOnly,
    };
  } else {
    console.log(chalk.cyan('🔍 Operations:'));
    operations.forEach((op) => console.log(chalk.dim(`  • ${chalk.cyan(op)}`)));
  }

  console.log(chalk.cyan('\n⚙️ Options:'));
  console.log(
    chalk.dim(
      `  • ${chalk.yellow('Download only')}: ${options.downloadOnly ? chalk.green('Yes') : chalk.red('No')}`,
    ),
  );
  console.log(
    chalk.dim(
      `  • ${chalk.yellow('Seed only')}: ${options.seedOnly ? chalk.green('Yes') : chalk.red('No')}`,
    ),
  );

  try {
    // Create Supabase client
    const supabase = createSupabaseClient();

    // Setup storage buckets if needed
    if (options.buckets && !options.downloadOnly) {
      console.log(chalk.magenta('\n🪣 Setting up storage buckets...'));
      await setupStorageBuckets(supabase);
    }

    // Download phase
    if (!options.seedOnly) {
      console.log(chalk.magenta('\n📥 Download phase...'));

      // Download community images
      if (options.communities) {
        console.log(
          chalk.dim(`\n${chalk.cyan('Communities')}: Downloading images...`),
        );
        await downloadCommunityImages(supabase);
      }

      // Download course images
      if (options.courses) {
        console.log(
          chalk.dim(`\n${chalk.cyan('Courses')}: Downloading images...`),
        );
        await downloadCourseImages(supabase);
      }

      // Download user profile images
      if (options.users) {
        console.log(
          chalk.dim(`\n${chalk.cyan('Users')}: Downloading images...`),
        );
        await downloadUserImages(supabase);
      }

      // Download community page images
      if (options.pages) {
        console.log(
          chalk.dim(`\n${chalk.cyan('Pages')}: Downloading images...`),
        );
        await downloadCommunityPageImages(supabase);
      }
    }

    // Seed phase
    if (!options.downloadOnly) {
      console.log(chalk.magenta('\n📤 Upload phase...'));

      // Seed community images
      if (options.communities) {
        console.log(
          chalk.dim(`\n${chalk.cyan('Communities')}: Uploading images...`),
        );
        await seedCommunityImages(supabase);
      }

      // Seed course images
      if (options.courses) {
        console.log(
          chalk.dim(`\n${chalk.cyan('Courses')}: Uploading images...`),
        );
        await seedCourseImages(supabase);
      }

      // Seed user profile images
      if (options.users) {
        console.log(chalk.dim(`\n${chalk.cyan('Users')}: Uploading images...`));
        await seedUserImages(supabase);
      }

      // Seed community page images
      if (options.pages) {
        console.log(chalk.dim(`\n${chalk.cyan('Pages')}: Uploading images...`));
        await seedCommunityPageImages(supabase);
      }
    }

    console.log(chalk.bold.blue('\n🎉 Image seeding complete!'));
  } catch (error) {
    console.error(chalk.red('\n❌ Error during image seeding:'), error);
    process.exit(1);
  }
}

// Check if running as main module
const isMainModule = import.meta.url === `file://${process.argv[1]}`;

if (isMainModule) {
  const { values } = parseArgs({
    options: {
      communities: { type: 'boolean', default: false },
      courses: { type: 'boolean', default: false },
      users: { type: 'boolean', default: false },
      pages: { type: 'boolean', default: false },
      'download-only': { type: 'boolean', default: false },
      'seed-only': { type: 'boolean', default: false },
      buckets: { type: 'boolean', default: false },
      help: { type: 'boolean', default: false },
    },
    allowPositionals: false,
  });

  if (values.help) {
    console.log(chalk.bold.blue('\n📋 Image Seeding Script Help'));
    console.log(chalk.cyan('\nUsage:'));
    console.log('  npm run seed:images -- [options]');

    console.log(chalk.cyan('\nOptions:'));
    console.log(chalk.dim(`  --communities     Seed community images`));
    console.log(chalk.dim(`  --courses         Seed course images`));
    console.log(chalk.dim(`  --users           Seed user profile images`));
    console.log(chalk.dim(`  --pages           Seed community page images`));
    console.log(chalk.dim(`  --buckets         Setup storage buckets`));
    console.log(
      chalk.dim(
        `  --download-only   Only download sample images without uploading to Supabase`,
      ),
    );
    console.log(
      chalk.dim(`  --seed-only       Only upload images without downloading`),
    );
    console.log(chalk.dim(`  --help            Show this help message`));

    console.log(chalk.cyan('\nExamples:'));
    console.log(chalk.dim(`  Run all operations:`));
    console.log(chalk.dim(`  npm run seed:images`));

    console.log(chalk.dim(`\n  Only seed community and user images:`));
    console.log(chalk.dim(`  npm run seed:images -- --communities --users`));

    console.log(chalk.dim(`\n  Only download images without uploading:`));
    console.log(chalk.dim(`  npm run seed:images -- --download-only`));
    process.exit(0);
  }

  // Run with provided options
  seedImages({
    communities: values.communities,
    courses: values.courses,
    users: values.users,
    pages: values.pages,
    downloadOnly: values['download-only'],
    seedOnly: values['seed-only'],
    buckets: values.buckets,
  });
}
