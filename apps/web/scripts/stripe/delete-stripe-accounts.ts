#!/usr/bin/env node
import { createClient } from '@supabase/supabase-js';

import chalk from 'chalk';
import { config } from 'dotenv';
import { createInterface } from 'node:readline/promises';
import <PERSON><PERSON> from 'stripe';

import type { Database } from '../../lib/database.types';
import { deleteDbStripeAccountHelper } from './helpers/delete-db-stripe-account-helper';
import { deleteStripeAccountHelper } from './helpers/delete-stripe-account-helper';
import { getDbStripeAccountByStripeIdHelper } from './helpers/get-db-stripe-account-by-stripe-id-helper';
import { listStripeAccountsHelper } from './helpers/list-stripe-accounts-helper';
import { logger } from './helpers/logger';
import { EnvSchema } from './schema/env.schema';

/**
 * This script is used to delete Stripe Connect accounts.
 * It will delete accounts from both Stripe and Supabase.
 * Only works in development mode.
 */

// Load environment variables from .env file
config({ path: '.env.local' });

// Ensure we're in development
if (process.env.NODE_ENV === 'production') {
  logger.error(
    { script: 'delete-stripe-accounts' },
    chalk.red('❌ This script cannot be run in production!'),
  );
}

/**
 * Parse command line arguments
 */
function parseCommandLineArgs(): {
  communityIds: string[];
  accountIds: string[];
  dryRun: boolean;
  autoConfirm: boolean;
} {
  const args = process.argv.slice(2);
  const communityIds: string[] = [];
  const accountIds: string[] = [];
  let dryRun = false;
  let autoConfirm = false;

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    if (arg === '--community-id' && i + 1 < args.length) {
      const nextArg = args[i + 1];
      if (nextArg) {
        communityIds.push(nextArg);
        i++;
      }
    } else if (arg === '--account-id' && i + 1 < args.length) {
      const nextArg = args[i + 1];
      if (nextArg) {
        accountIds.push(nextArg);
        i++;
      }
    } else if (arg === '--dry-run') {
      dryRun = true;
    } else if (arg === '-y' || arg === '--yes') {
      autoConfirm = true;
    }
  }

  return { communityIds, accountIds, dryRun, autoConfirm };
}

/**
 * Asks for confirmation before deleting a stripeAccount
 */
async function confirmDeletion(
  stripeAccount: Stripe.V2.Core.Account,
): Promise<boolean> {
  // Check for auto-confirm flag
  const { autoConfirm } = parseCommandLineArgs();
  if (autoConfirm) {
    return true;
  }

  const rl = createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  const answer = await rl.question(
    chalk.yellow(
      `Delete stripe account ${chalk.cyan(stripeAccount.id)} (${stripeAccount.contact_email || 'No email'})? [y/N] `,
    ),
  );
  rl.close();

  return answer.toLowerCase() === 'y';
}

/**
 * Prompt for confirmation to proceed
 */
async function confirmDeletionStart(
  communityIds: string[],
  accountIds: string[],
  dryRun: boolean,
): Promise<boolean> {
  if (dryRun) {
    logger.warn(
      { script: 'delete-stripe-accounts' },
      chalk.yellow('Running in dry-run mode. No accounts will be deleted.'),
    );
    return true;
  }

  // Check for auto-confirm flag
  const { autoConfirm } = parseCommandLineArgs();
  if (autoConfirm) {
    logger.info(
      {},
      chalk.yellow('Auto-confirmation enabled via command line argument.'),
    );
    return true;
  }

  const rl = createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  const filterMsg = [];
  if (communityIds.length > 0) {
    filterMsg.push(`${communityIds.length} specific communities`);
  }
  if (accountIds.length > 0) {
    filterMsg.push(`${accountIds.length} specific accounts`);
  }
  const filterText =
    filterMsg.length > 0 ? ` for ${filterMsg.join(' and ')}` : '';

  const answer = await rl.question(
    chalk.red(
      `⚠️  WARNING: This will DELETE Stripe Accounts${filterText}. Continue? [y/N] `,
    ),
  );
  rl.close();

  return answer.toLowerCase() === 'y';
}

/**
 * Main function that runs the script
 */
async function main() {
  try {
    logger.warn(
      { script: 'delete-stripe-accounts' },
      chalk.bold.blue('🗑️  Stripe Account Deletion Tool'),
    );

    // Parse command line arguments
    const { communityIds, accountIds, dryRun } = parseCommandLineArgs();

    // Load environment variables
    config();

    const env = EnvSchema.parse(process.env);

    // Get confirmation before proceeding
    if (!(await confirmDeletionStart(communityIds, accountIds, dryRun))) {
      logger.warn(
        { script: 'delete-stripe-accounts' },
        'Operation cancelled by user.',
      );
      process.exit(0);
    }

    // Initialize Stripe client
    logger.info(
      { script: 'delete-stripe-accounts' },
      chalk.dim('Initializing Stripe client...'),
    );
    const stripe = new Stripe(env.STRIPE_SECRET_KEY!, {
      apiVersion: env.STRIPE_API_VERSION_V1 as Stripe.LatestApiVersion,
    });
    logger.info({}, chalk.green('✓ Initialized Stripe client.'));

    // Initialize Supabase client with service role key for admin access
    logger.info({}, chalk.dim('Initializing Supabase client...'));
    const client = createClient<Database>(
      env.NEXT_PUBLIC_SUPABASE_URL!,
      env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      },
    );
    logger.info({}, chalk.green('✓ Initialized Supabase client.'));

    logger.info({}, chalk.blue('🔍 Fetching accounts from Stripe...'));

    let accounts: Stripe.V2.Core.Account[] = [];
    try {
      accounts = await listStripeAccountsHelper(stripe, {
        limit: 100,
      });
      // Helper logs success
    } catch {
      // Helper logs error and throws
      logger.error({}, 'Failed to fetch accounts from Stripe. Exiting.');
    }

    if (!accounts || accounts.length === 0) {
      logger.info({}, chalk.green('✅ No accounts found in Stripe.'));
      return;
    }

    logger.info(
      {},
      chalk.cyan(
        `\nFound ${chalk.bold(accounts.length.toString())} accounts.\n`,
      ),
    );

    for (const account of accounts) {
      logger.info({}, chalk.blue('\n🔎 Account Details:'));
      logger.info(
        { id: account.id },
        chalk.dim(`ID: ${chalk.cyan(account.id)}`),
      );
      logger.info(
        { email: account.contact_email },
        chalk.dim(
          `Email: ${account.contact_email ? chalk.cyan(account.contact_email) : chalk.gray('No email')}`,
        ),
      );

      // Handle potentially undefined created timestamp
      const createdDate = account.created
        ? new Date(account.created).toLocaleString()
        : 'Unknown';
      logger.info({}, chalk.dim(`Created: ${chalk.cyan(createdDate)}`));
      logger.info({}, chalk.dim(`Type: ${chalk.cyan(account.configuration)}`));
      logger.info(
        {},
        chalk.dim(
          `Business Type: ${account.identity?.entity_type ? chalk.cyan(account.identity?.entity_type) : chalk.gray('Not specified')}`,
        ),
      );
      logger.info(
        {},
        chalk.dim(
          `Country: ${account.identity?.country ? chalk.cyan(account.identity?.country) : chalk.gray('Not specified')}`,
        ),
      );
      logger.info(
        {},
        chalk.dim(
          `Charges enabled: ${account.configuration?.merchant?.capabilities?.card_payments ? chalk.green('Yes') : chalk.red('No')}`,
        ),
      );
      logger.info(
        {},
        chalk.dim(
          `Payouts enabled: ${account.configuration?.recipient?.capabilities?.stripe_balance ? chalk.green('Yes') : chalk.red('No')}`,
        ),
      );

      if (account.identity?.business_details) {
        logger.info(
          {},
          chalk.dim(
            `Business name: ${account.identity?.business_details?.doing_business_as ? chalk.cyan(account.identity?.business_details?.doing_business_as) : chalk.gray('Not specified')}`,
          ),
        );
        logger.info(
          {},
          chalk.dim(
            `Business URL: ${account.identity.business_details.url ? chalk.cyan(account.identity.business_details.url) : chalk.gray('Not specified')}`,
          ),
        );
      }

      if (account.identity?.individual) {
        logger.info(
          {},
          chalk.dim(
            `Individual name: ${chalk.cyan(`${account.identity?.individual?.given_name || 'Unknown'} ${account.identity?.individual?.surname || 'Unknown'}`)}`,
          ),
        );
      }

      // Check if account exists in Supabase
      logger.info({}, chalk.dim('Checking Supabase records...'));
      let stripeAccount: Awaited<
        ReturnType<typeof getDbStripeAccountByStripeIdHelper>
      > = null;
      try {
        // Use helper to fetch DB record by Stripe ID
        stripeAccount = await getDbStripeAccountByStripeIdHelper(
          client,
          account.id,
        );
        // Helper logs found/not found/errors internally
      } catch (dbFetchError) {
        // Although helper aims to handle errors, catch unexpected ones
        logger.error(
          { error: dbFetchError, stripeAccountId: account.id },
          `❌ Unexpected error checking Supabase for account ${account.id}. Skipping. `,
        );
        continue;
      }

      if (stripeAccount) {
        logger.info(
          {},
          chalk.blue(
            `📦 Found in Supabase (ID: ${chalk.cyan(stripeAccount.id)})`,
          ),
        );
      }

      if (await confirmDeletion(account)) {
        try {
          // Delete from Stripe using helper
          const stripeDeleted = await deleteStripeAccountHelper(
            stripe,
            account.id,
          );

          // Only attempt Supabase deletion if Stripe deletion was successful
          if (stripeDeleted && stripeAccount) {
            logger.info(
              { id: stripeAccount.id },
              chalk.dim('Deleting from Supabase...'),
            );
            // Use helper to delete DB record
            await deleteDbStripeAccountHelper(client, stripeAccount.id);
            // Helper handles logging success/failure
          } else {
            // Stripe deletion failed (error logged by helper)
            // Or the account wasn't found in Supabase to begin with
            if (!stripeDeleted) {
              logger.warn(
                { accountId: account.id },
                'Skipping Supabase deletion because Stripe deletion failed or was unconfirmed.',
              );
            }
          }
        } catch (error) {
          if (error instanceof Error) {
            logger.error(
              { error: error.message, accountId: account.id },
              `❌ Failed to delete account ${account.id}`,
            );
          } else {
            logger.error(
              { error, accountId: account.id },
              `❌ An unknown error occurred while deleting account ${account.id}`,
            );
          }
        }
      } else {
        logger.warn(
          { script: 'delete-stripe-accounts', accountId: account.id },
          `⏭️ Skipping account ${account.id}`,
        );
      }
    }

    logger.success(
      { script: 'delete-stripe-accounts' },
      chalk.bold.green('\n✅ Deletion process complete!'),
    );
  } catch (error) {
    if (error instanceof Error) {
      logger.error(
        { script: 'delete-stripe-accounts', error: error.message },
        '❌ Operation failed',
      );
    } else {
      logger.error(
        { error },
        '❌ An unknown error occurred during the operation',
      );
    }
  }
}

if (require.main === module) {
  main().catch((error) => {
    if (error instanceof Error) {
      logger.error({ error: error.message }, 'Unhandled error');
    } else {
      logger.error({ error }, 'An unknown unhandled error occurred');
    }
    process.exit(1);
  });
}
