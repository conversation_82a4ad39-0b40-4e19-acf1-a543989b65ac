import chalk from 'chalk';
import { execa } from 'execa';
import inquirer from 'inquirer';
import path from 'node:path';

const scriptsDir = __dirname;

// Define the scripts with user-friendly names
const scripts = {
  'delete-stripe-accounts': 'Delete Stripe Accounts',
  'sync-community-products': 'Sync Community Products',
  'sync-customer-subscriptions': 'Sync Customer Subscriptions from Stripe',
  'sync-stripe-accounts': 'Sync Stripe Accounts',
  'sync-stripe-customers': 'Sync Stripe Customers',
  'sync-stripe-products': 'Sync Stripe Products and Prices',
} as const;

type ScriptKey = keyof typeof scripts;

async function runCli() {
  console.log(chalk.cyan('Select a Stripe Billing script to run:'));

  const { selectedScriptKey } = await inquirer.prompt([
    {
      type: 'list',
      name: 'selectedScriptKey',
      message: 'Which script do you want to execute?',
      choices: Object.entries(scripts).map(([key, name]) => ({
        name: name,
        value: key,
      })),
    },
  ]);

  const scriptKey = selectedScriptKey as ScriptKey;

  const scriptFileName = `${scriptKey}.ts`;
  const scriptPath = path.join(scriptsDir, scriptFileName);

  console.log(
    chalk.yellow(
      `\nExecuting script: ${scripts[scriptKey]} (${scriptFileName})...\n`,
    ),
  );

  try {
    // Execute the script using tsx, inheriting stdio
    const subprocess = execa('tsx', [scriptPath], {
      stdio: 'inherit',
      env: {
        ...process.env,
        NODE_ENV: 'development',
      },
      cwd: path.dirname(scriptsDir), // Run from the parent 'stripe' dir
    });

    await subprocess;

    console.log(
      chalk.green(`\n✅ Script ${scriptFileName} finished successfully.`),
    );
  } catch (error) {
    console.error(
      chalk.red(`\n❌ Error executing script ${scriptFileName}:`),
      error,
    );
    process.exit(1);
  }
}

runCli();
