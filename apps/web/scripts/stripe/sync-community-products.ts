#!/usr/bin/env node
import { SupabaseClient, createClient } from '@supabase/supabase-js';

import chalk from 'chalk';
import { config } from 'dotenv';
import { createInterface } from 'node:readline/promises';
import Stripe from 'stripe';

import type { Database } from '../../lib/database.types';
import { createStripeProductHelper } from './helpers/create-stripe-product-helper';
import { listDbCommunitiesHelper } from './helpers/list-db-communities-helper';
import { listStripePricesHelper } from './helpers/list-stripe-prices-helper';
import { logger } from './helpers/logger';
import { searchStripeProductsHelper } from './helpers/search-stripe-products-helper';
import { upsertDbPriceHelper } from './helpers/upsert-db-price-helper';
import { upsertDbProductHelper } from './helpers/upsert-db-product-helper';
import { EnvSchema } from './schema/env.schema';

// Re-import Database type

/**
 * <PERSON>ript to create subscriptions for communities
 * This script will:
 * 1. Look at current communities in the DB
 * 2. Look up products in stripe
 * 3. If products exist in Stripe but not in DB, then link products in Stripe to community products
 * 4. If no products exist in Stripe, then create a new default product in Stripe and link it to the community
 */
class CommunityProductService {
  constructor(
    private readonly stripe: Stripe,
    private readonly client: SupabaseClient,
  ) {}

  /**
   * Main method to create subscriptions for communities
   */
  async syncCommunityProducts(): Promise<{
    processed: number;
    created: number;
    skipped: number;
    failed: number;
    messages: string[];
  }> {
    // Initialize tracking variables
    let processed = 0;
    let created = 0;
    let skipped = 0;
    let failed = 0;
    const messages: string[] = [];

    // 1. Get communities
    console.log(chalk.blue('\n🔍 Finding communities...'));
    const communities = await listDbCommunitiesHelper(this.client);
    if (!communities) {
      logger.error(
        { source: 'syncCommunityProducts' },
        'Failed to fetch communities.',
      );
      return {
        processed: 0,
        created: 0,
        skipped: 0,
        failed: 1,
        messages: ['Failed to fetch communities'],
      };
    }
    console.log(
      chalk.cyan(
        `Found ${chalk.bold(communities.length.toString())} communities`,
      ),
    );

    // 3. Process each community
    console.log(chalk.blue('\n🔄 Processing communities...'));

    for (const community of communities) {
      logger.info({ community: community.name }, 'Processing community');
      process.stdout.write(
        chalk.dim(
          `Processing community ${chalk.cyan(community.id)} (${
            community.name || 'unknown'
          })... `,
        ),
      );

      try {
        // Get any stripe products for the community
        const stripeProducts = await searchStripeProductsHelper({
          stripe: this.stripe,
          communityId: community.id,
        });
        logger.info(
          { stripeProducts: stripeProducts.length },
          'Fetched Stripe products',
        );

        if (!stripeProducts || stripeProducts.length === 0) {
          process.stdout.write(chalk.yellow('no products ⚠️\n'));
          messages.push(
            `Community ${community.id} (${community.name || 'unknown'}) has no products`,
          );
          await this.createCommunityDefaultProduct(community.id);
          created++;
          continue;
        }

        for (const product of stripeProducts) {
          logger.info({ product: product.id }, 'Checking for product');
          const { data: existingProduct } = await this.client
            .from('products')
            .select('id')
            .eq('id', product.id)
            .single();

          if (existingProduct !== null) {
            process.stdout.write(chalk.yellow('already exists ⚠️\n'));
            messages.push(
              `Community ${community.id} (${community.name || 'unknown'}) already has product ${product.id}`,
            );
            skipped++;
            continue;
          }

          // Create product in DB
          const dbProductId = await upsertDbProductHelper(
            this.client,
            product,
            'community',
            undefined,
            community.id,
          );

          process.stdout.write(
            chalk.green(`created product ${product.id} ✓\n`),
          );
          messages.push(
            `Created product ${product.id} for community ${community.id} (${community.name || 'unknown'})`,
          );

          if (product.active) {
            logger.info({ dbProductId }, 'Add default product to community');
            await this.client
              .from('communities')
              .update({ default_product_id: dbProductId })
              .eq('id', community.id);
          }

          const prices = await listStripePricesHelper({
            stripe: this.stripe,
            communityId: community.id,
            productId: product.id,
          });

          if (!prices) {
            process.stdout.write(chalk.yellow('no prices ⚠️\n'));
            messages.push(
              `Community ${community.id} (${community.name || 'unknown'}) has no prices for product ${product.id}`,
            );
            skipped++;
            continue;
          }

          // Create prices in DB
          for (const price of prices) {
            await upsertDbPriceHelper(this.client, price, dbProductId);
          }

          process.stdout.write(
            chalk.green(`created prices for product ${product.id} ✓\n`),
          );
          messages.push(
            `Created prices for product ${product.id} for community ${community.id} (${community.name || 'unknown'})`,
          );

          created++;
        }
      } catch (error) {
        process.stdout.write(chalk.red('failed ✗\n'));
        failed++;
        messages.push(
          `Failed to process community ${community.id} (${community.name || 'unknown'}): ${
            error instanceof Error ? error.message : String(error)
          }`,
        );
      }

      processed++;
    }

    // 4. Summary
    console.log(chalk.blue('\n📊 Creation Summary:'));
    console.log(
      chalk.cyan(`Processed ${chalk.bold(processed.toString())} communities`),
    );
    console.log(
      chalk.green(`Created ${chalk.bold(created.toString())} products`),
    );
    console.log(
      chalk.yellow(`Skipped ${chalk.bold(skipped.toString())} communities`),
    );
    console.log(
      chalk.red(`Failed ${chalk.bold(failed.toString())} operations`),
    );

    return {
      processed,
      created,
      skipped,
      failed,
      messages,
    };
  }

  private async createCommunityDefaultProduct(
    communityId: string,
  ): Promise<Stripe.Product | null> {
    const ctx = {
      source: 'createCommunityDefaultProduct',
      communityId,
    };
    logger.info(ctx, 'Creating default product for community');
    const defaultProduct = await createStripeProductHelper(this.stripe, {
      name: 'Default Product',
      description: 'Default product for community',
      default_price_data: {
        recurring: {
          interval: 'month',
        },
        currency: 'usd',
        unit_amount: 0,
        metadata: { seller_type: 'community' },
      },
      metadata: {
        seller_type: 'community',
        community_id: communityId,
      },
    });

    if (!defaultProduct) {
      logger.error(ctx, 'Failed to create default product');
      return null;
    }

    const dbProductId = await upsertDbProductHelper(
      this.client,
      defaultProduct,
      'community',
      undefined,
      communityId,
    );

    logger.info(
      { ...ctx, dbProductId },
      'Update community with default_product_id',
    );

    await this.client
      .from('communities')
      .update({ default_product_id: dbProductId })
      .eq('id', communityId);

    // Call helper with payload object
    const prices = await listStripePricesHelper({
      stripe: this.stripe,
      productId: defaultProduct.id,
    });

    if (prices.length === 0) {
      process.stdout.write(chalk.yellow('(no prices found)'));
    } else {
      process.stdout.write(chalk.dim(`(${prices.length}) `));

      // Process each price
      for (const price of prices) {
        try {
          // Use helper to upsert price
          await upsertDbPriceHelper(this.client, price, dbProductId);
          process.stdout.write(chalk.green('.'));
        } catch (error) {
          process.stdout.write(chalk.red('✗'));
          const message = `Error processing price ${price.id}: ${error instanceof Error ? error.message : 'Unknown error'}`;

          logger.error({ error, priceId: price.id }, message);
        }
      }
    }

    process.stdout.write('\n');

    return defaultProduct;
  }
}

// Check for -y flag in command line arguments
const hasYesFlag = process.argv.includes('-y');

/**
 * Asks for confirmation before proceeding
 */
async function confirmSync(): Promise<boolean> {
  const readline = createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  try {
    // If -y flag is present, bypass confirmation
    if (hasYesFlag) {
      console.log(
        chalk.yellow(
          'Auto-confirmation enabled via command line argument (-y).',
        ),
      );
      return true;
    }

    const answer = await readline.question(
      chalk.yellow(
        '⚠️  This will sync products from Stripe for each community. Continue? (y/N) ',
      ),
    );
    return answer.toLowerCase() === 'y';
  } finally {
    // Always close the readline interface
    readline.close();
  }
}

// Main function
async function main() {
  // Load environment variables from .env file
  config({ path: '.env.local' });

  // Validate environment variables
  const env = EnvSchema.parse(process.env);

  // Initialize Stripe and Supabase clients
  const stripe = new Stripe(env.STRIPE_SECRET_KEY, {
    apiVersion: env.STRIPE_API_VERSION_V1 as Stripe.LatestApiVersion,
  });

  const supabase = createClient<Database>(
    env.NEXT_PUBLIC_SUPABASE_URL,
    env.SUPABASE_SERVICE_ROLE_KEY,
  );

  // Initialize service
  const service = new CommunityProductService(stripe, supabase);

  // Get confirmation from user
  if (!(await confirmSync())) {
    console.log(chalk.red('Operation canceled'));
    process.exit(0);
  }

  try {
    // Create subscriptions
    logger.info(
      { script: 'sync-community-products' },
      'Starting community product sync...',
    );
    const result = await service.syncCommunityProducts();

    // Show detailed messages
    if (result.messages.length > 0) {
      console.log(chalk.blue('\n📝 Detailed Messages:'));
      result.messages.forEach((msg) => {
        console.log(`  ${msg}`);
      });
    }

    console.log(
      chalk.green('\n✅ Community product sync completed successfully!'),
    );
    // Ensure the script exits after completion
    process.exit(0);
  } catch (error) {
    console.error(
      chalk.red(
        `\n❌ Error syncing community products: ${error instanceof Error ? error.message : String(error)}`,
      ),
    );
    process.exit(1);
  }
}

// Run the main function
main().catch((error) => {
  console.error(
    chalk.red(
      `\n❌ Unhandled error: ${error instanceof Error ? error.message : String(error)}`,
    ),
  );
  process.exit(1);
});
