#!/usr/bin/env node
import { SupabaseClient, createClient } from '@supabase/supabase-js';

import chalk from 'chalk';
import { config } from 'dotenv';
import { createInterface } from 'node:readline/promises';
import Stripe from 'stripe';

import type { Database } from '../../lib/database.types';
import { createStripePriceHelper } from './helpers/create-stripe-price-helper';
import { createStripeProductHelper } from './helpers/create-stripe-product-helper';
import { listDbCommunitiesHelper } from './helpers/list-db-communities-helper';
import { logger } from './helpers/logger';
import { toggleCurrentProductActiveHelper } from './helpers/toggle-current-product-active-helper';
import { upsertDbPriceHelper } from './helpers/upsert-db-price-helper';
import { upsertDbProductHelper } from './helpers/upsert-db-product-helper';
import { EnvSchema } from './schema/env.schema';

/**
 * <PERSON>ript to add specific product/price combinations for communities
 * Community 1: month_year price: $10/$100 with 3-day trial
 * Community 2: month price: $15 with no trial
 * Community 3: one_time price: $20
 */
class CommunityPlansService {
  constructor(
    private readonly stripe: Stripe,
    private readonly client: SupabaseClient<Database>,
  ) {}

  /**
   * Get the first 3 communities ordered by creation date
   */
  async getFirstThreeCommunities() {
    const communities = await listDbCommunitiesHelper(this.client);
    if (!communities) {
      throw new Error('Failed to fetch communities');
    }

    // Sort by creation date and take first 3
    const sortedCommunities = communities
      .sort((a, b) => {
        // Assuming communities have created_at or similar field, or use order as returned
        return a.id.localeCompare(b.id); // Simple ordering by ID if no created_at
      })
      .slice(0, 3);

    return sortedCommunities;
  }

  /**
   * Create product and prices for Community 1: month_year $10/$100 with 3-day trial
   */
  async createCommunity1Plans(communityId: string) {
    const productName = 'Premium Plan - $10 per month / $100 per year';

    console.log(chalk.dim(`  Creating product: ${productName}`));

    // Create product in Stripe
    const product = await createStripeProductHelper(this.stripe, {
      name: productName,
      description: 'Premium plan with monthly and yearly options',
      metadata: {
        community_id: communityId,
        seller_type: 'community',
      },
    });

    if (!product) {
      throw new Error('Failed to create Stripe product');
    }

    // Create product in DB
    const dbProductId = await upsertDbProductHelper(
      this.client,
      product,
      'community',
      3, // trial days
      communityId,
    );

    // Create monthly price in Stripe
    const monthlyPrice = await createStripePriceHelper(this.stripe, {
      product: product.id,
      unit_amount: 10 * 100, // $10 in cents
      currency: 'usd',
      recurring: {
        interval: 'month',
      },
      nickname: 'Premium Plan - Monthly',
      metadata: {
        seller_type: 'community',
        community_id: communityId,
      },
    });

    if (!monthlyPrice) {
      throw new Error('Failed to create monthly price');
    }

    // Create yearly price in Stripe
    const yearlyPrice = await createStripePriceHelper(this.stripe, {
      product: product.id,
      unit_amount: 100 * 100, // $100 in cents
      currency: 'usd',
      recurring: {
        interval: 'year',
      },
      nickname: 'Premium Plan - Yearly',
      metadata: {
        seller_type: 'community',
        community_id: communityId,
      },
    });

    if (!yearlyPrice) {
      throw new Error('Failed to create yearly price');
    }

    // Create prices in DB
    await upsertDbPriceHelper(this.client, monthlyPrice, dbProductId);
    await upsertDbPriceHelper(this.client, yearlyPrice, dbProductId);

    // Activate this product and deactivate others
    await toggleCurrentProductActiveHelper(
      this.stripe,
      this.client,
      product.id,
    );

    return product;
  }

  /**
   * Create product and price for Community 2: month $15 with no trial
   */
  async createCommunity2Plans(communityId: string) {
    const productName = 'Monthly Membership - $15 per month';

    console.log(chalk.dim(`  Creating product: ${productName}`));

    // Create product in Stripe
    const product = await createStripeProductHelper(this.stripe, {
      name: productName,
      description: 'Monthly membership plan',
      metadata: {
        community_id: communityId,
        seller_type: 'community',
      },
    });

    if (!product) {
      throw new Error('Failed to create Stripe product');
    }

    // Create product in DB (no trial days)
    const dbProductId = await upsertDbProductHelper(
      this.client,
      product,
      'community',
      0, // no trial days
      communityId,
    );

    // Create monthly price in Stripe
    const monthlyPrice = await createStripePriceHelper(this.stripe, {
      product: product.id,
      unit_amount: 15 * 100, // $15 in cents
      currency: 'usd',
      recurring: {
        interval: 'month',
      },
      nickname: 'Monthly Membership',
      metadata: {
        seller_type: 'community',
        community_id: communityId,
      },
    });

    if (!monthlyPrice) {
      throw new Error('Failed to create monthly price');
    }

    // Create price in DB
    await upsertDbPriceHelper(this.client, monthlyPrice, dbProductId);

    // Activate this product and deactivate others
    await toggleCurrentProductActiveHelper(
      this.stripe,
      this.client,
      product.id,
    );

    return product;
  }

  /**
   * Create product and price for Community 3: one_time $20
   */
  async createCommunity3Plans(communityId: string) {
    const productName = 'Lifetime Access - $20 one-time';

    console.log(chalk.dim(`  Creating product: ${productName}`));

    // Create product in Stripe
    const product = await createStripeProductHelper(this.stripe, {
      name: productName,
      description: 'One-time payment for lifetime access',
      metadata: {
        community_id: communityId,
        seller_type: 'community',
      },
    });

    if (!product) {
      throw new Error('Failed to create Stripe product');
    }

    // Create product in DB
    const dbProductId = await upsertDbProductHelper(
      this.client,
      product,
      'community',
      0, // no trial days for one-time purchases
      communityId,
    );

    // Create one-time price in Stripe
    const oneTimePrice = await createStripePriceHelper(this.stripe, {
      product: product.id,
      unit_amount: 20 * 100, // $20 in cents
      currency: 'usd',
      nickname: 'Lifetime Access',
      metadata: {
        seller_type: 'community',
        community_id: communityId,
      },
    });

    if (!oneTimePrice) {
      throw new Error('Failed to create one-time price');
    }

    // Create price in DB
    await upsertDbPriceHelper(this.client, oneTimePrice, dbProductId);

    // Activate this product and deactivate others
    await toggleCurrentProductActiveHelper(
      this.stripe,
      this.client,
      product.id,
    );

    return product;
  }

  /**
   * Main method to add community plans
   */
  async addCommunityPlans(): Promise<{
    processed: number;
    created: number;
    failed: number;
    messages: string[];
  }> {
    // Initialize tracking variables
    let processed = 0;
    let created = 0;
    let failed = 0;
    const messages: string[] = [];

    try {
      // Get first 3 communities
      console.log(chalk.blue('\n🔍 Finding first 3 communities...'));
      const communities = await this.getFirstThreeCommunities();

      if (communities.length === 0) {
        console.log(chalk.yellow('No communities found'));
        return {
          processed: 0,
          created: 0,
          failed: 0,
          messages: ['No communities found'],
        };
      }

      console.log(
        chalk.cyan(
          `Found ${chalk.bold(communities.length.toString())} communities to process`,
        ),
      );

      // Process each community with specific plan configurations
      console.log(chalk.blue('\n🔄 Creating community plans...'));

      for (let i = 0; i < communities.length && i < 3; i++) {
        const community = communities[i];
        if (!community) continue; // Skip if community is undefined

        const communityDisplay = `${community.name || 'Unnamed'} (${community.id})`;

        process.stdout.write(
          chalk.dim(`Processing Community ${i + 1}: ${communityDisplay}...\n`),
        );

        try {
          let product;

          // Create specific plans based on community index
          switch (i) {
            case 0: // Community 1: month_year $10/$100 with 3-day trial
              console.log(
                chalk.dim('  Plan: Month/Year $10/$100 with 3-day trial'),
              );
              product = await this.createCommunity1Plans(community.id);
              break;

            case 1: // Community 2: month $15 with no trial
              console.log(chalk.dim('  Plan: Monthly $15 with no trial'));
              product = await this.createCommunity2Plans(community.id);
              break;

            case 2: // Community 3: one_time $20
              console.log(chalk.dim('  Plan: One-time $20'));
              product = await this.createCommunity3Plans(community.id);
              break;
          }

          if (product) {
            process.stdout.write(
              chalk.green(`  ✓ Created product ${product.id}\n`),
            );
            messages.push(
              `Created product for ${communityDisplay}: ${product.name}`,
            );
            created++;
          }
        } catch (error) {
          process.stdout.write(chalk.red(`  ✗ Failed to create plans\n`));
          failed++;
          const errorMsg =
            error instanceof Error ? error.message : String(error);
          messages.push(
            `Failed to create plans for ${communityDisplay}: ${errorMsg}`,
          );
          logger.error(
            { error, communityId: community.id },
            'Failed to create community plans',
          );
        }

        processed++;
      }

      // Summary
      console.log(chalk.blue('\n📊 Plans Creation Summary:'));
      console.log(
        chalk.cyan(`Processed ${chalk.bold(processed.toString())} communities`),
      );
      console.log(
        chalk.green(`Created ${chalk.bold(created.toString())} product plans`),
      );
      console.log(
        chalk.red(`Failed ${chalk.bold(failed.toString())} operations`),
      );

      return {
        processed,
        created,
        failed,
        messages,
      };
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      logger.error({ error }, 'Failed to add community plans');
      return {
        processed: 0,
        created: 0,
        failed: 1,
        messages: [`Failed to add community plans: ${errorMsg}`],
      };
    }
  }
}

// Check for -y flag in command line arguments
const hasYesFlag = process.argv.includes('-y');

/**
 * Asks for confirmation before proceeding
 */
async function confirmOperation(): Promise<boolean> {
  const readline = createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  try {
    // If -y flag is present, bypass confirmation
    if (hasYesFlag) {
      console.log(
        chalk.yellow(
          'Auto-confirmation enabled via command line argument (-y).',
        ),
      );
      return true;
    }

    const answer = await readline.question(
      chalk.yellow(
        '⚠️  This will create specific product/price plans for the first 3 communities. Continue? (y/N) ',
      ),
    );
    return answer.toLowerCase() === 'y';
  } finally {
    // Always close the readline interface
    readline.close();
  }
}

// Main function
async function main() {
  // Load environment variables from .env file
  config({ path: '.env.local' });

  // Validate environment variables
  const env = EnvSchema.parse(process.env);

  // Initialize Stripe and Supabase clients
  const stripe = new Stripe(env.STRIPE_SECRET_KEY, {
    apiVersion: env.STRIPE_API_VERSION_V1 as Stripe.LatestApiVersion,
  });

  const supabase = createClient<Database>(
    env.NEXT_PUBLIC_SUPABASE_URL,
    env.SUPABASE_SERVICE_ROLE_KEY,
  );

  // Initialize service
  const service = new CommunityPlansService(stripe, supabase);

  // Get confirmation from user
  if (!(await confirmOperation())) {
    console.log(chalk.red('Operation canceled'));
    process.exit(0);
  }

  try {
    // Add community plans
    logger.info(
      { script: 'add-community-plans' },
      'Starting community plans creation...',
    );
    const result = await service.addCommunityPlans();

    // Show detailed messages
    if (result.messages.length > 0) {
      console.log(chalk.blue('\n📝 Detailed Messages:'));
      result.messages.forEach((msg) => {
        console.log(`  ${msg}`);
      });
    }

    console.log(
      chalk.green('\n✅ Community plans creation completed successfully!'),
    );
    // Ensure the script exits after completion
    process.exit(0);
  } catch (error) {
    console.error(
      chalk.red(
        `\n❌ Error creating community plans: ${error instanceof Error ? error.message : String(error)}`,
      ),
    );
    process.exit(1);
  }
}

// Run the main function
main().catch((error) => {
  console.error(
    chalk.red(
      `\n❌ Unhandled error: ${error instanceof Error ? error.message : String(error)}`,
    ),
  );
  process.exit(1);
});
