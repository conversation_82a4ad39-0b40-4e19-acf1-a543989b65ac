import { z } from 'zod';

// Validate environment variables
export const EnvSchema = z
  .object({
    STRIPE_SECRET_KEY: z.string().min(1).startsWith('sk_test_'),
    NEXT_PUBLIC_SUPABASE_URL: z.string().min(1),
    SUPABASE_SERVICE_ROLE_KEY: z.string().min(1),
    STRIPE_API_VERSION_V1: z.string().min(1).optional(),
    STRIPE_API_VERSION_V1_BETA: z.string().min(1).optional(),
    STRIPE_API_VERSION_V2: z.string().min(1).optional(),
  })
  .refine(
    (schema) => {
      return (
        schema.STRIPE_API_VERSION_V1 ||
        schema.STRIPE_API_VERSION_V1_BETA ||
        schema.STRIPE_API_VERSION_V2
      );
    },
    {
      message: 'At least one Stripe API version must be provided',
    },
  );
