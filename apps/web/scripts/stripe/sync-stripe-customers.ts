#!/usr/bin/env node
import { SupabaseClient, createClient } from '@supabase/supabase-js';

import chalk from 'chalk';
import { config } from 'dotenv';
import { createInterface } from 'node:readline/promises';
import Stripe from 'stripe';
import { z } from 'zod';

import type { Database } from '../../lib/database.types';
import type { StripeAccountV2 } from '../../lib/stripe/types';
import { createStripeAccountHelper } from './helpers/create-stripe-account-helper';
import { listDbUsersHelper } from './helpers/list-db-users-helper';
import { listStripeAccountsHelper } from './helpers/list-stripe-accounts-helper';
import { logger } from './helpers/logger';
import { updateDbUserHelper } from './helpers/update-db-user-helper';
import { EnvSchema } from './schema/env.schema';

/**
 * Script-friendly version of StripeCustomerScriptService
 * This is a simplified version that implements account syncing for Stripe Accounts v2
 */
class StripeCustomerScriptService {
  constructor(
    private readonly stripe: Stripe,
    private readonly client: SupabaseClient,
  ) {}

  /**
   * Syncs Stripe accounts with our database
   * - Creates database records for Stripe accounts that exist but aren't in our database
   * - Recreates Stripe accounts that are in our database but not in Stripe
   * - Associates accounts with their users based on email matching
   */
  async syncCustomers(): Promise<{
    created: number;
    linked: number;
    failed: number;
    messages: string[];
  }> {
    const messages: string[] = [];
    let created = 0;
    let linked = 0;
    let failed = 0;

    // Set to track user IDs that have already been processed
    const processedUserIds = new Set<string>();

    // 1. Get all accounts from Stripe
    console.log(chalk.blue('\n🔍 Scanning Stripe for accounts...'));
    const stripeCustomers = await listStripeAccountsHelper(this.stripe);
    console.log(
      chalk.cyan(
        `Found ${chalk.bold(stripeCustomers.length.toString())} accounts in Stripe`,
      ),
    );

    // 2. Get all users from our database
    const users = await listDbUsersHelper(this.client);
    console.log(
      chalk.cyan(
        `Found ${chalk.bold((users?.length || 0).toString())} users in database`,
      ),
    );

    // Add existing users with stripe account IDs to processed set
    users.forEach((user) => {
      if (user.stripe_account_id) {
        processedUserIds.add(user.id);
      }
    });

    // 3. Find accounts that are in Stripe but not linked to any user
    const usersWithStripeIds = new Set(
      users.filter((u) => u.stripe_account_id).map((u) => u.stripe_account_id),
    );

    const stripeMissingInDb = stripeCustomers.filter(
      (c: StripeAccountV2) => !usersWithStripeIds.has(c.id),
    );

    console.log(
      chalk.yellow(
        `${chalk.bold(stripeMissingInDb.length.toString())} Stripe accounts not linked to any user`,
      ),
    );

    // 4. Find users with invalid Stripe account IDs
    const stripeCustomerIds = new Set(
      stripeCustomers.map((c: StripeAccountV2) => c.id),
    );
    const usersWithInvalidStripeIds = users.filter(
      (u) => u.stripe_account_id && !stripeCustomerIds.has(u.stripe_account_id),
    );

    console.log(
      chalk.yellow(
        `${chalk.bold(usersWithInvalidStripeIds.length.toString())} users with invalid Stripe account IDs`,
      ),
    );

    // 6. Create a map of email to user ID for easy lookup
    const emailToUser = new Map();
    users.forEach((user) => {
      if (user.email) {
        emailToUser.set(user.email.toLowerCase(), user);
      }
    });

    // 7. Link Stripe accounts not linked to any user
    if (stripeMissingInDb.length > 0) {
      console.log(
        chalk.blue('\n🔄 Linking existing Stripe accounts to users...'),
      );
      let count = 0;

      for (const customer of stripeMissingInDb) {
        try {
          if (customer.contact_email) {
            const user = emailToUser.get(customer.contact_email.toLowerCase());

            if (user) {
              // Skip if user already has an account
              if (processedUserIds.has(user.id)) {
                process.stdout.write(chalk.yellow('⚠️ '));
                messages.push(
                  `User ${user.id} already has an account, skipping link for Stripe account ${customer.id}`,
                );
                continue;
              }

              // We found a matching user, link them
              process.stdout.write(
                chalk.dim(
                  `Linking account ${chalk.cyan(customer.id)} to user... `,
                ),
              );
              const updateSuccess = await updateDbUserHelper(this.client, {
                userId: user.id,
                stripeAccountId: customer.id,
              });
              if (!updateSuccess) {
                throw new Error('Helper failed to link account in DB');
              }
              process.stdout.write(
                chalk.green(`✓ Linked to user ${user.id}\n`),
              );
              messages.push(
                `Linked existing Stripe account ${customer.id} to user ${user.id}`,
              );
              processedUserIds.add(user.id); // Mark user as processed
              linked++;
              count++;
            } else {
              logger.warn(
                {
                  script: 'sync-stripe-customers',
                  accountId: customer.id,
                  email: customer.contact_email,
                },
                `No matching user found for Stripe account ${customer.id} (${customer.contact_email})`,
              );
              messages.push(
                `No matching user found for Stripe account ${customer.id} (${customer.contact_email})`,
              );
            }
          } else {
            logger.warn(
              { script: 'sync-stripe-customers', accountId: customer.id },
              `Stripe account ${customer.id} has no contact email address, cannot link`,
            );
            messages.push(
              `Stripe account ${customer.id} has no contact email address, cannot link`,
            );
          }
        } catch (error) {
          process.stdout.write(chalk.red('✗\n'));
          messages.push(
            `Failed to link Stripe account ${customer.id}: ${error instanceof Error ? error.message : String(error)}`,
          );
          failed++;
        }
      }

      console.log(
        chalk.green(`Linked ${chalk.bold(count.toString())} accounts to users`),
      );
    }

    // 8. Recreate Stripe accounts for users with invalid Stripe IDs
    if (usersWithInvalidStripeIds.length > 0) {
      console.log(chalk.blue('\n🔄 Recreating missing Stripe accounts...'));
      let count = 0;

      for (const user of usersWithInvalidStripeIds) {
        process.stdout.write(
          chalk.yellow(
            `Recreating missing Stripe account for user ${user.id}... `,
          ),
        );

        if (!user || !user.email) {
          process.stdout.write(
            chalk.red('✗ User not found or missing email.\n'),
          );
          messages.push(
            `User ${user.id} is missing email, cannot recreate account`,
          );
          failed++;
          continue;
        }

        const fullName =
          `${user.first_name || ''} ${user.last_name || ''}`.trim();

        try {
          // Create Stripe account using helper
          const accountParams = {
            email: user.email!,
            name: fullName,
            country: 'US', // Default to US, could be made configurable
            metadata: { user_id: user.id },
          };
          const newStripeAccount = await createStripeAccountHelper(
            this.stripe,
            accountParams,
          );

          if (
            !newStripeAccount ||
            typeof newStripeAccount !== 'object' ||
            !('id' in newStripeAccount)
          ) {
            throw new Error('Helper failed to create Stripe account.');
          }

          // Update user record with the new account ID using the update helper
          const updateSuccess = await updateDbUserHelper(this.client, {
            userId: user.id,
            stripeAccountId: newStripeAccount.id as string,
          });

          if (updateSuccess) {
            process.stdout.write(
              chalk.green(`✓ Recreated as ${newStripeAccount.id}\n`),
            );
            messages.push(
              `Recreated missing Stripe account as ${newStripeAccount.id} for user ${user.id}`,
            );
          } else {
            throw new Error(
              'Helper failed to update user record with new account ID.',
            );
          }

          created++;
          count++;
        } catch (error) {
          process.stdout.write(chalk.red('✗\n'));
          messages.push(
            `Failed to recreate Stripe account for ${user.id}: ${error instanceof Error ? error.message : String(error)}`,
          );
          failed++;
        }
      }

      console.log(
        chalk.green(
          `Recreated ${chalk.bold(count.toString())} accounts in Stripe`,
        ),
      );
    }

    // 9. Create accounts for users who don't have one at all
    const usersWithoutCustomers = users.filter(
      (u) => !u.stripe_account_id && u.email && !processedUserIds.has(u.id),
    );

    console.log(
      chalk.yellow(
        `${chalk.bold(usersWithoutCustomers.length.toString())} users without Stripe accounts`,
      ),
    );

    if (usersWithoutCustomers.length > 0) {
      console.log(chalk.blue('\n🆕 Creating new Stripe accounts for users...'));
      let count = 0;

      for (const user of usersWithoutCustomers) {
        process.stdout.write(
          chalk.dim(`Creating account for user ${chalk.cyan(user.id)}... `),
        );
        try {
          const accountParams = {
            email: user.email!,
            name: `${user.first_name || ''} ${user.last_name || ''}`.trim(),
            country: 'US', // Default to US, could be made configurable
            metadata: { user_id: user.id },
          };

          // Create new Stripe account for the user using helper
          const newStripeCustomer = await createStripeAccountHelper(
            this.stripe,
            accountParams,
          );

          if (
            !newStripeCustomer ||
            typeof newStripeCustomer !== 'object' ||
            !('id' in newStripeCustomer)
          ) {
            throw new Error('Helper failed to create Stripe account.');
          }

          // Update the user record with the account ID
          const updateSuccess = await updateDbUserHelper(this.client, {
            userId: user.id,
            stripeAccountId: newStripeCustomer.id as string,
          });

          if (updateSuccess) {
            process.stdout.write(
              chalk.green(`✓ Created as ${newStripeCustomer.id}\n`),
            );
            messages.push(
              `Created new Stripe account ${newStripeCustomer.id} for user ${user.id}`,
            );
            created++;
            count++;
          } else {
            throw new Error(
              'Helper failed to update user record with account ID.',
            );
          }
        } catch (error) {
          process.stdout.write(chalk.red('✗\n'));
          messages.push(
            `Failed to create Stripe account for ${user.id}: ${error instanceof Error ? error.message : String(error)}`,
          );
          failed++;
        }
      }

      console.log(
        chalk.green(
          `Created ${chalk.bold(count.toString())} new accounts in Stripe`,
        ),
      );
    }

    return {
      created,
      linked,
      failed,
      messages,
    };
  }
}

/**
 * This script syncs Stripe accounts with the database.
 * It handles the following scenarios:
 * 1. Accounts in Stripe but not linked to any user (links them to users by email)
 * 2. Users with invalid Stripe account IDs (recreates them)
 * 3. Users without any account (creates new accounts)
 * Only works in development mode.
 */

// Load environment variables from .env file
config({ path: '.env.local' });

// Ensure we're in development
if (process.env.NODE_ENV === 'production') {
  console.error('❌ This script cannot be run in production!');
  process.exit(1);
}

const readline = createInterface({
  input: process.stdin,
  output: process.stdout,
});

// Check for -y flag in command line arguments
const hasYesFlag = process.argv.includes('-y');

/**
 * Asks for confirmation before proceeding
 */
async function confirmSync(): Promise<boolean> {
  const readline = createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  // If -y flag is present, bypass confirmation
  if (hasYesFlag) {
    console.log(
      chalk.yellow('Auto-confirmation enabled via command line argument (-y).'),
    );
    return true;
  }

  const answer = await readline.question(
    chalk.yellow(
      '⚠️  This will sync all customer subscriptions from Stripe to your database. Continue? (y/N) ',
    ),
  );
  readline.close();
  return answer.toLowerCase() === 'y';
}

/**
 * Main function that runs the script
 */
async function main() {
  try {
    // Validate environment variables
    const env = EnvSchema.parse(process.env);

    // Initialize Stripe client
    console.log('🔍 Initializing Stripe client...');
    const stripe = new Stripe(env.STRIPE_SECRET_KEY, {
      apiVersion: env.STRIPE_API_VERSION_V2 as Stripe.LatestApiVersion,
    });

    // Initialize Supabase client with service role key for admin access
    console.log('🔍 Initializing Supabase client...');
    const supabase = createClient<Database>(
      env.NEXT_PUBLIC_SUPABASE_URL,
      env.SUPABASE_SERVICE_ROLE_KEY,
    );

    // Initialize our script-friendly version of StripeBillingService
    const stripeCustomerService = new StripeCustomerScriptService(
      stripe,
      supabase,
    );

    // Confirm sync
    if (!(await confirmSync())) {
      console.log(chalk.yellow('Sync cancelled.'));
      return;
    }

    console.log('\n🔁 Syncing Stripe accounts...\n');

    // Perform the sync
    const { created, linked, failed, messages } =
      await stripeCustomerService.syncCustomers();

    // Display detailed logs
    console.log('\n📝 Detailed Log:');
    messages.forEach((msg) => console.log(`  ${msg}`));

    // Display summary
    console.log('\n📊 Summary:');
    console.log(`✅ Created ${created} new Stripe accounts`);
    console.log(`⛓️ Linked ${linked} existing Stripe accounts`);
    if (failed > 0) {
      console.log(`❌ Failed to process ${failed} accounts`);
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error(
        '❌ Environment Error: Missing or invalid environment variables',
      );
      console.error(
        error.errors.map((e) => `${e.path}: ${e.message}`).join('\n'),
      );
    } else {
      console.error(
        '❌ Error:',
        error instanceof Error ? error.message : String(error),
      );
    }
    process.exit(1);
  } finally {
    readline.close();
  }
}

main().catch(console.error);
