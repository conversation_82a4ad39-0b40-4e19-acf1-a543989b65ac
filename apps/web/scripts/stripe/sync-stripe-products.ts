#!/usr/bin/env node
import { SupabaseClient, createClient } from '@supabase/supabase-js';

import chalk from 'chalk';
import { config } from 'dotenv';
import { createInterface } from 'node:readline/promises';
import Stripe from 'stripe';
import { z } from 'zod';

import type { Database } from '../../lib/database.types';
import { createStripeProductHelper } from './helpers/create-stripe-product-helper';
import { listStripePricesHelper } from './helpers/list-stripe-prices-helper';
import { logger } from './helpers/logger';
import { searchStripeProductsHelper } from './helpers/search-stripe-products-helper';
import { upsertDbPriceHelper } from './helpers/upsert-db-price-helper';
import { upsertDbProductHelper } from './helpers/upsert-db-product-helper';
import { EnvSchema } from './schema/env.schema';

/**
 * Service for Stripe product operations - Script version
 * This is a simplified version of StripeProductsService for CLI scripts
 * without the server-only dependency
 */
class StripeProductScriptService {
  private stripe: Stripe;
  private client: SupabaseClient;

  constructor(stripe: Stripe, client: SupabaseClient) {
    this.stripe = stripe;
    this.client = client;
  }

  async syncProductsFromStripe(limit = 100) {
    const errors: {
      message: string;
      details?: { productId?: string; priceId?: string; code?: string };
    }[] = [];

    try {
      logger.info({}, chalk.bold.blue('\n🔄 Syncing products from Stripe...'));
      logger.info({ limit }, 'Fetching products with limit');

      let products = await searchStripeProductsHelper({
        stripe: this.stripe,
        limit,
      });

      if (products.length === 0) {
        const message = 'No products found in Stripe, adding default';
        logger.info({ script: 'sync-stripe-products' }, message);
        products = [
          await createStripeProductHelper(this.stripe, {
            name: 'Dojo Default Platform',
            metadata: { seller_type: 'platform' },
            default_price_data: {
              currency: 'usd',
              unit_amount: 2900,
              recurring: { interval: 'month' },
              metadata: { seller_type: 'platform' },
            },
          }),
        ] as Stripe.Product[];
      }

      logger.info(
        {},
        chalk.cyan(
          `\nFound ${chalk.bold(products.length.toString())} products to sync`,
        ),
      );

      for (const product of products) {
        process.stdout.write(
          chalk.dim(
            `Processing product ${chalk.cyan(product.id)}: ${chalk.green(product.name || 'Unnamed')} `,
          ),
        );

        try {
          const dbProductId = await upsertDbProductHelper(
            this.client,
            product,
            'platform', // Seller is 'platform' for this script
            14, // Trial period in days
          );
          process.stdout.write(chalk.green('✓'));

          // Fetch and upsert prices for the product using the helper function
          logger.info(
            { product_name: product.name },
            chalk.dim(`   Syncing prices for product ${product.name}...`),
          );
          // Call helper with payload object
          const prices = await listStripePricesHelper({
            stripe: this.stripe,
            productId: product.id,
          });

          if (prices.length === 0) {
            process.stdout.write(chalk.yellow('(no prices found)'));
          } else {
            process.stdout.write(chalk.dim(`(${prices.length}) `));

            // Process each price
            for (const price of prices) {
              try {
                // Use helper to upsert price
                await upsertDbPriceHelper(this.client, price, dbProductId);
                process.stdout.write(chalk.green('.'));
              } catch (error) {
                process.stdout.write(chalk.red('✗'));
                const message = `Error processing price ${price.id}: ${error instanceof Error ? error.message : 'Unknown error'}`;

                errors.push({
                  message,
                  details: {
                    productId: product.id,
                    priceId: price.id,
                  },
                });
              }
            }
          }

          process.stdout.write('\n');
        } catch (error) {
          process.stdout.write(chalk.red('✗\n'));
          const message = `Error processing product ${product.id}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          logger.error({ error, productId: product.id }, message);
          errors.push({
            message,
            details: {
              productId: product.id,
            },
          });
        }
      }

      if (errors.length > 0) {
        logger.info(
          { script: 'sync-stripe-products' },
          chalk.yellow(`\n⚠️ Completed with ${errors.length} errors`),
        );
      } else {
        logger.success(
          { script: 'sync-stripe-products' },
          chalk.bold.green(
            `\n✅ Successfully synced ${products.length} products from Stripe`,
          ),
        );
      }

      return {
        success: true,
        message: `Successfully synced ${products.length} products from Stripe`,
        count: products.length,
        errors: errors.length > 0 ? errors : undefined,
      };
    } catch (error) {
      logger.error({ error }, 'Unexpected error in syncProductsFromStripe');
      return {
        success: false,
        error: 'An unexpected error occurred during sync',
        count: 0,
        errors,
      };
    }
  }
}

/**
 * This script syncs products and prices from Stripe to the database.
 * It uses a script-friendly version of StripeProductsService.
 * Only works in development mode.
 */

config({ path: '.env.local' });

if (process.env.NODE_ENV === 'production') {
  logger.error({}, '❌ This script cannot be run in production!');
  process.exit(1);
}

const readline = createInterface({
  input: process.stdin,
  output: process.stdout,
});

const hasYesFlag = process.argv.includes('-y');

async function confirmSync(): Promise<boolean> {
  if (hasYesFlag) {
    return true;
  }

  const answer = await readline.question(
    chalk.yellow(
      '⚠️ This will sync products and prices from Stripe. Continue? (y/n): ',
    ),
  );
  readline.close();

  return answer.toLowerCase() === 'y';
}

async function main() {
  try {
    const env = EnvSchema.parse(process.env);

    logger.info({}, '🔍 Initializing Stripe client...');
    const stripe = new Stripe(env.STRIPE_SECRET_KEY, {
      apiVersion: env.STRIPE_API_VERSION_V1 as Stripe.LatestApiVersion,
    });

    logger.info({}, '🔍 Initializing Supabase client...');
    const supabase = createClient<Database>(
      env.NEXT_PUBLIC_SUPABASE_URL,
      env.SUPABASE_SERVICE_ROLE_KEY,
    );

    const stripeProductService = new StripeProductScriptService(
      stripe,
      supabase,
    );

    logger.info(
      {},
      '⚠️ This will sync all products and prices from Stripe to the database.\n',
    );

    if (!(await confirmSync())) {
      logger.warn(
        { script: 'sync-stripe-products' },
        '⏭️ Operation cancelled by user.',
      );
      return;
    }

    logger.info(
      { script: 'sync-stripe-products' },
      '\n🔁 Syncing products and prices from Stripe...\n',
    );

    const result = await stripeProductService.syncProductsFromStripe();

    if (result.success) {
      logger.success(
        { script: 'sync-stripe-products' },
        `✅ ${result.message}`,
      );

      if (result.errors && result.errors.length > 0) {
        logger.info(
          { script: 'sync-stripe-products' },
          '\n⚠️ Some issues were encountered:',
        );
        result.errors.forEach((error, index) => {
          logger.info(
            { script: 'sync-stripe-products' },
            `  ${index + 1}. ${error.message}`,
          );
          if (error.details) {
            if (error.details.productId) {
              logger.info(
                { script: 'sync-stripe-products' },
                `     Product ID: ${error.details.productId}`,
              );
            }
            if (error.details.priceId) {
              logger.info(
                { script: 'sync-stripe-products' },
                `     Price ID: ${error.details.priceId}`,
              );
            }
          }
        });
      }
    } else {
      logger.error({ error: result.error }, `\n❌ ${result.error}`);

      if (result.errors && result.errors.length > 0) {
        logger.error({}, '\nDetailed errors:');
        result.errors.forEach((error, index) => {
          logger.error({}, `  ${index + 1}. ${error.message}`);
          if (error.details) {
            if (error.details.productId) {
              logger.error({}, `     Product ID: ${error.details.productId}`);
            }
            if (error.details.priceId) {
              logger.error({}, `     Price ID: ${error.details.priceId}`);
            }
          }
        });
      }
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      logger.error(
        { errors: error.flatten().fieldErrors },
        '❌ Environment Error: Missing or invalid environment variables',
      );
    } else {
      logger.error(
        { error: error instanceof Error ? error.message : String(error) },
        '\n❌ Script failed',
      );
    }
    process.exit(1);
  } finally {
    readline.close();
  }
}

main().catch((error) => {
  logger.error(
    { error: error instanceof Error ? error.message : String(error) },
    'Unhandled error in main',
  );
  process.exit(1);
});
