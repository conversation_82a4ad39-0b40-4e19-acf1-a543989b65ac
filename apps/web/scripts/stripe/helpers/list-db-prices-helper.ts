import type { SupabaseClient } from '@supabase/supabase-js';

import chalk from 'chalk';

import type { Database } from '../../../lib/database.types';
import { logger } from './logger';

// Define the structure of the price data we expect to fetch
export interface DbPrice {
  id: string;
  product_id: string | null;
  active: boolean | null;
  nickname: string | null;
  unit_amount: number | null;
  currency: string | null;
  type: string | null; // Use basic type temporarily
  interval: string | null; // Use basic type temporarily
}

interface PriceFilters {
  product_id?: string;
  active?: boolean;
  // Add other potential filters as needed
}

/**
 * Fetches prices from the database, optionally filtered.
 *
 * @param supabase - Initialized Supabase client instance.
 * @param filters - Optional filters to apply to the query.
 * @returns Promise<DbPrice[] | null> - An array of prices or null if an error occurs.
 */
export async function listDbPricesHelper(
  supabase: SupabaseClient<Database>,
  filters?: PriceFilters,
): Promise<DbPrice[] | null> {
  const logContext = { source: 'listDbPricesHelper', filters };
  logger.info(logContext, 'Fetching prices from database...');
  process.stdout.write(chalk.dim('   Querying prices... '));

  try {
    let query = supabase
      .from('product_prices') // Corrected table name
      .select(
        'id, product_id, active, nickname, unit_amount, currency, type, interval',
      );

    // Apply filters
    if (filters) {
      if (filters.product_id !== undefined) {
        query = query.eq('product_id', filters.product_id);
      }
      if (filters.active !== undefined) {
        query = query.eq('active', filters.active);
      }
      // Add more filters here if needed
    }

    const { data, error } = await query;

    if (error) {
      process.stdout.write(chalk.red('✗ Query Failed\n'));
      logger.error(
        { ...logContext, error },
        'Error fetching prices from database.',
      );
      return null; // Indicate failure
    }

    const prices = (data || []) as DbPrice[];
    process.stdout.write(chalk.green(`✓ Found ${prices.length} prices\n`));
    logger.success(
      { ...logContext, prices: prices.length },
      `Successfully fetched ${prices.length} prices.`,
    );
    return prices;
  } catch (error) {
    process.stdout.write(chalk.red('✗ Unexpected Error\n'));
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(
      { ...logContext, error: errorMessage }, // Correct logger args
      'An unexpected error occurred while fetching prices.',
    );
    return null; // Indicate failure
  }
}
