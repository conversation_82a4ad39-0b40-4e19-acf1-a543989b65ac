import type Stripe from 'stripe';

import { logger } from './logger';

// Define extended Stripe types if needed (e.g., for expanded properties)
type StripeSubscriptionWithExpandedItems = Omit<
  Stripe.Subscription,
  'current_period'
> & {
  items: Stripe.ApiList<Stripe.SubscriptionItem & { price: Stripe.Price }>;
  current_period: {
    start: number;
    end: number;
  };
};

/**
 * Fetches all subscriptions for a given stripe account ID from Stripe, handling pagination.
 *
 * @param stripe - Initialized Stripe client instance.
 * @param stripeAccountId - The Stripe account ID.
 * @returns Promise<StripeSubscriptionWithExpandedItems[]> - An array of subscriptions with expanded items.
 * @throws Error if fetching fails.
 */
export async function listStripeSubscriptionsHelper(
  stripe: Stripe,
  stripeAccountId: string,
): Promise<StripeSubscriptionWithExpandedItems[]> {
  const logContext = { source: 'stripe', stripeAccountId };
  logger.info(logContext, 'Fetching subscriptions from Stripe...');

  const subscriptions: StripeSubscriptionWithExpandedItems[] = [];
  const options: Stripe.SubscriptionListParams = {
    customer_account: stripeAccountId,
    limit: 100, // Max limit per request
    expand: ['data.items.data.price'], // Expand items, prices
    status: 'all', // Fetch subscriptions of all statuses
  };

  try {
    let hasMore = true;
    let startingAfter: string | undefined = undefined;

    while (hasMore) {
      const params: Stripe.SubscriptionListParams = { ...options };
      if (startingAfter) {
        params.starting_after = startingAfter;
      }

      const response: Stripe.ApiList<Stripe.Subscription> =
        await stripe.subscriptions.list(params);

      // Ensure correct typing after expansion
      const fetchedSubscriptions =
        response.data as StripeSubscriptionWithExpandedItems[];
      subscriptions.push(...fetchedSubscriptions);

      hasMore = response.has_more;
      if (hasMore) {
        // Use optional chaining to safely get the ID of the last item for pagination
        startingAfter =
          fetchedSubscriptions[fetchedSubscriptions.length - 1]?.id;
        // If startingAfter becomes undefined (e.g., empty page), stop pagination
        if (!startingAfter) {
          hasMore = false;
        }
      } else {
        startingAfter = undefined; // Reset if no more data or last fetch was empty
      }
    }

    logger.success(
      { ...logContext, subscriptions: subscriptions.length },
      `Successfully fetched ${subscriptions.length} subscriptions from Stripe.`,
    );
    return subscriptions;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(
      { ...logContext, error: errorMessage },
      'Error fetching subscriptions from Stripe.',
    );
    // Re-throw the error to be handled by the calling script
    throw new Error(
      `Failed to fetch subscriptions from Stripe for customer ${stripeAccountId}: ${errorMessage}`,
    );
  }
}
