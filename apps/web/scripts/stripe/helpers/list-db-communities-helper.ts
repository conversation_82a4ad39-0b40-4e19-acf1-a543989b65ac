import type { SupabaseClient } from '@supabase/supabase-js';

import chalk from 'chalk';

import type { Database } from '../../../lib/database.types';
import { logger } from './logger';

interface DbCommunity {
  id: string;
  name: string | null;
  primary_owner_user_id: string | null;
  subscription_id?: string | null;
}

/**
 * Fetches communities from the database
 *
 * @param supabase - Initialized Supabase client instance.
 * @returns Promise<DbCommunity[] | null> - An array of communities or null if an error occurs.
 */
export async function listDbCommunitiesHelper(
  supabase: SupabaseClient<Database>,
): Promise<DbCommunity[] | null> {
  const logContext = { source: 'database' };
  logger.info(logContext, 'Fetching communities from database...');
  process.stdout.write(chalk.dim('   Querying communities... '));

  try {
    const { data, error } = await supabase
      .from('communities')
      .select('id, name, primary_owner_user_id, subscription_id');

    if (error) {
      process.stdout.write(chalk.red('✗ Query Failed\n'));
      logger.error({ ...logContext, error }, 'Error fetching communities.');
      return null; // Indicate failure
    }

    const communities = (data || []) as DbCommunity[];
    process.stdout.write(
      chalk.green(`✓ Found ${communities.length} communities\n`),
    );
    logger.success(
      { ...logContext, communities: communities.length },
      `Successfully fetched ${communities.length} communities.`,
    );
    return communities;
  } catch (error) {
    process.stdout.write(chalk.red('✗ Unexpected Error\n'));
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(
      { ...logContext, error: errorMessage },
      'An unexpected error occurred while fetching communities.',
    );
    return null; // Indicate failure
  }
}
