#!/usr/bin/env node
import type { SupabaseClient } from '@supabase/supabase-js';

import type <PERSON><PERSON> from 'stripe';

import type { Database } from '../../../lib/database.types';
import { logger } from './logger';

// Define specific types for Product insert/update data
type PlatformProductInsert = Database['public']['Tables']['products']['Insert'];

/**
 * Upserts a Stripe product into the products table.
 *
 * @param client - The Supabase client instance.
 * @param product - The Stripe product object.
 * @param seller - Indicates who is selling the product ('platform' or 'community').
 * @param communityId - Optional community ID if the seller is 'community'.
 * @param stripeAccountId - Optional stripe account ID if the seller is 'community'.
 * @returns The database ID of the upserted product.
 * @throws If the Supabase operation fails.
 */
export async function upsertDbProductHelper(
  client: SupabaseClient<Database>,
  product: Stripe.Product,
  seller: 'platform' | 'community',
  trialDays?: number,
  communityId?: string,
  stripeAccountId?: string,
): Promise<string> {
  try {
    const productData: PlatformProductInsert = {
      id: product.id,
      name: product.name,
      description: product.description,
      active: product.active,
      seller: seller,
      // Conditionally add community/stripe account info
      ...(seller === 'community' && {
        community_id: communityId,
      }),
      ...(trialDays && { trial_days: trialDays }),
    };

    const { data: dbProduct, error: productError } = await client
      .from('products')
      .upsert(productData, {
        onConflict: 'id',
        ignoreDuplicates: false, // Ensure we update existing records
      })
      .select('id')
      .single();

    if (productError) {
      logger.error(
        {
          error: productError,
          productId: product.id,
          seller,
          communityId,
          stripeAccountId,
        },
        'Error upserting product in helper',
      );
      throw new Error(`Failed to upsert product: ${productError.message}`);
    }

    if (!dbProduct?.id) {
      throw new Error('Upsert operation did not return a product ID.');
    }

    return dbProduct.id;
  } catch (error) {
    logger.error(
      { error, productId: product.id, seller },
      'Error in upsertDbProductHelper',
    );
    // Re-throw the error after logging
    throw error;
  }
}
