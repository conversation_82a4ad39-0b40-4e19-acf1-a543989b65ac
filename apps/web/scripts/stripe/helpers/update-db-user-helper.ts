import { SupabaseClient } from '@supabase/supabase-js';

import type { Database } from '../../../lib/database.types';
import { logger } from './logger';

type Json =
  Database['public']['Tables']['stripe_accounts']['Row']['configuration'];

interface UpdateDbUserWithStripeAccountParams {
  userId: string;
  stripeAccountId: string;
  stripeAccountData?: {
    applied_configurations?: string[];
    contact_email?: string;
    display_name?: string;
    dashboard?: string;
    configuration?: Json;
    capabilities?: Json;
    requirements?: Json;
    defaults?: Json;
    identity?: Json;
    metadata?: Json;
  };
}

/**
 * Creates or updates a stripe_accounts record and links it to a user
 * @param supabase - Initialized Supabase client.
 * @param params - Object containing userId, stripeAccountId, and optional account data
 * @returns Promise<boolean> - True on success, false on failure.
 */
export async function updateDbUserHelper(
  supabase: SupabaseClient<Database>,
  {
    userId,
    stripeAccountId,
    stripeAccountData,
  }: UpdateDbUserWithStripeAccountParams,
): Promise<boolean> {
  const logContext = { userId, stripeAccountId };
  logger.info(
    logContext,
    'Creating/updating stripe_accounts record and linking to user...',
  );

  try {
    // First, check if a stripe_accounts record already exists for this account ID
    const { data: existingAccount, error: fetchError } = await supabase
      .from('stripe_accounts')
      .select('id, user_id')
      .eq('id', stripeAccountId)
      .single();

    if (fetchError && fetchError.code !== 'PGRST116') {
      // PGRST116 is "not found" error, which is expected for new accounts
      logger.error(
        { error: fetchError, ...logContext },
        'Error checking for existing stripe_accounts record.',
      );
      return false;
    }

    if (existingAccount) {
      // Account exists, update it
      const { error: updateError } = await supabase
        .from('stripe_accounts')
        .update({
          user_id: userId,
          ...(stripeAccountData?.applied_configurations && {
            applied_configurations: stripeAccountData.applied_configurations,
          }),
          ...(stripeAccountData?.contact_email && {
            contact_email: stripeAccountData.contact_email,
          }),
          ...(stripeAccountData?.display_name && {
            display_name: stripeAccountData.display_name,
          }),
          ...(stripeAccountData?.dashboard && {
            dashboard: stripeAccountData.dashboard,
          }),
          ...(stripeAccountData?.configuration && {
            configuration: stripeAccountData.configuration,
          }),
          ...(stripeAccountData?.capabilities && {
            capabilities: stripeAccountData.capabilities,
          }),
          ...(stripeAccountData?.requirements && {
            requirements: stripeAccountData.requirements,
          }),
          ...(stripeAccountData?.defaults && {
            defaults: stripeAccountData.defaults,
          }),
          ...(stripeAccountData?.identity && {
            identity: stripeAccountData.identity,
          }),
          ...(stripeAccountData?.metadata && {
            metadata: stripeAccountData.metadata,
          }),
        })
        .eq('id', stripeAccountId);

      if (updateError) {
        logger.error(
          { error: updateError, ...logContext },
          'Error updating stripe_accounts record.',
        );
        return false;
      }
    } else {
      // Account doesn't exist, create it
      const insertData: Database['public']['Tables']['stripe_accounts']['Insert'] =
        {
          id: stripeAccountId,
          user_id: userId,
          applied_configurations: stripeAccountData?.applied_configurations || [
            'customer',
          ],
          contact_email: stripeAccountData?.contact_email || null,
          display_name: stripeAccountData?.display_name || null,
          dashboard: stripeAccountData?.dashboard || null,
          configuration: stripeAccountData?.configuration || {},
          capabilities: stripeAccountData?.capabilities || {},
          requirements: stripeAccountData?.requirements || {},
          defaults: stripeAccountData?.defaults || {},
          identity: stripeAccountData?.identity || {},
          metadata: stripeAccountData?.metadata || {},
        };

      const { error: insertError } = await supabase
        .from('stripe_accounts')
        .insert(insertData);

      if (insertError) {
        logger.error(
          { error: insertError, ...logContext },
          'Error creating stripe_accounts record.',
        );
        return false;
      }
    }

    // Now update the user record to link to the stripe account
    const { error: userUpdateError } = await supabase
      .from('users')
      .update({
        stripe_account_id: stripeAccountId,
      })
      .eq('id', userId);

    if (userUpdateError) {
      logger.error(
        { error: userUpdateError, ...logContext },
        'Error linking user to stripe_accounts record.',
      );
      return false;
    }

    logger.success(
      { ...logContext },
      `Successfully created/updated stripe_accounts record ${stripeAccountId} and linked to user ${userId}.`,
    );
    return true;
  } catch (err) {
    logger.error(
      { error: err, ...logContext },
      'Unexpected error creating/updating stripe_accounts record.',
    );
    return false;
  }
}
