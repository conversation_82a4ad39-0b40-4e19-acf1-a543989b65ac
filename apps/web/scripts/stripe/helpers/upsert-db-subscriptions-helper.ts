import { SupabaseClient } from '@supabase/supabase-js';

import type { Database, TablesInsert } from '../../../lib/database.types';
import { logger } from './logger';

// Define the payload structure, mirroring the necessary fields for upserting
type UpsertDbSubscriptionPayload = TablesInsert<'subscriptions'>;

/**
 * Upserts a subscription record into the Supabase 'subscriptions' table.
 *
 * @param supabaseClient - The Supabase client instance.
 * @param subscriptionData - The subscription data to upsert.
 * @returns Promise<boolean> - True if the upsert was successful, false otherwise.
 */
export async function upsertDbSubscriptionHelper(
  supabaseClient: SupabaseClient<Database>,
  subscriptionData: UpsertDbSubscriptionPayload,
): Promise<boolean> {
  const logContext = {
    source: 'database',
    subscriptionId: subscriptionData.id,
    userId: subscriptionData.user_id,
  };

  logger.info(logContext, 'Upserting subscription record in database...');

  try {
    const { error } = await supabaseClient
      .from('subscriptions')
      .upsert(subscriptionData, { onConflict: 'id' });

    if (error) {
      logger.error(
        { ...logContext, error },
        'Error upserting subscription record in database.',
      );
      return false; // Indicate failure
    }

    logger.success(
      { ...logContext },
      'Successfully upserted subscription record in database.',
    );
    return true; // Indicate success
  } catch (error) {
    logger.error(
      {
        ...logContext,
        error: error instanceof Error ? error.message : String(error),
      },
      'An unexpected error occurred while upserting subscription record.',
    );
    return false; // Indicate failure
  }
}
