import { SupabaseClient } from '@supabase/supabase-js';

import chalk from 'chalk';
import type Strip<PERSON> from 'stripe';

import type { Database, Json, TablesInsert } from '../../../lib/database.types';
import { logger } from './logger';

interface UpsertDbConnectedAccountPayload {
  userId: string;
  stripeAccount: Stripe.V2.Core.Account; // Contains the .country field (string, e.g., 'US')
}

/**
 * Upserts a record in the `stripe_accounts` table based on Stripe account data.
 * @param supabase - Initialized Supabase client.
 * @param payload - Containing userId and the Stripe Account object.
 * @returns Promise<boolean> - True if the upsert was successful, false otherwise.
 */
export async function upsertDbStripeAccountHelper(
  supabase: SupabaseClient<Database>,
  payload: UpsertDbConnectedAccountPayload,
): Promise<boolean> {
  const logContext = {
    userId: payload.userId,
    stripeAccountId: payload.stripeAccount.id,
  };
  const { userId, stripeAccount } = payload;
  const stripeAccountId = stripeAccount.id;

  logger.info(logContext, `Upserting stripe_accounts record...`);
  process.stdout.write(
    chalk.dim(`   Upserting DB record for account ${stripeAccountId}... `),
  );

  try {
    const stripeCountryCode = stripeAccount.identity?.country; // e.g., 'US', 'GB'

    let countryId: string | undefined = undefined;

    // 1. Look up country_id if stripeCountryCode is provided
    if (stripeCountryCode) {
      logger.info(
        { ...logContext, stripeCountryCode },
        'Looking up country...',
      );
      const { data: countryData, error: countryError } = await supabase
        .from('countries')
        .select('id')
        .eq('iso_code_1', stripeCountryCode.toUpperCase())
        .single();

      if (countryError) {
        logger.warn(
          { ...logContext, countryError },
          `Failed to find country with iso_code_1 '${stripeCountryCode}' for user ${userId} (Account: ${stripeAccountId}). Skipping country_id linkage. Error: ${countryError.message}`,
        );
      } else if (countryData) {
        countryId = countryData.id;
        logger.info({ ...logContext, countryId }, 'Country found.');
      }
    }

    // Explicitly type the data object to match the Supabase table insert type
    const upsertData: TablesInsert<'stripe_accounts'> = {
      id: stripeAccountId,
      user_id: userId,
      contact_email: stripeAccount.contact_email ?? undefined,
      country_id: countryId,
      // applied_configurations should be a string array (TEXT[])
      applied_configurations: stripeAccount.applied_configurations ?? [],
      display_name: stripeAccount.display_name ?? undefined,
      dashboard: stripeAccount.dashboard ?? undefined,
      // configuration, capabilities, requirements, defaults, identity, metadata should be JSONB
      configuration:
        (stripeAccount.configuration as unknown as Json) ?? undefined,
      capabilities:
        (stripeAccount.configuration?.merchant
          ?.capabilities as unknown as Json) ?? undefined,
      requirements:
        (stripeAccount.requirements as unknown as Json) ?? undefined,
      defaults: (stripeAccount.defaults as unknown as Json) ?? undefined,
      identity: (stripeAccount.identity as unknown as Json) ?? undefined,
      metadata: (stripeAccount.metadata as unknown as Json) ?? undefined,
    };

    const { error } = await supabase
      .from('stripe_accounts')
      .upsert(upsertData, { onConflict: 'user_id' });

    if (error) {
      process.stdout.write(chalk.red('✗\n'));
      logger.error(
        { error, ...logContext },
        'Error upserting stripe account in database',
      );
      throw new Error(`Database error: ${error.message}`);
    }

    process.stdout.write(chalk.green('✓\n'));
    logger.info(
      { ...logContext },
      'Successfully upserted stripe account in database.',
    );
    return true;
  } catch (error) {
    if (
      !(error instanceof Error && error.message.startsWith('Database error:'))
    ) {
      logger.error(
        { error, ...logContext },
        'Unexpected error during DB stripe account upsert',
      );
    }
    // Ensure we don't proceed if upsert fails
    return false;
  }
}
