import { SupabaseClient } from '@supabase/supabase-js';

import type { Database } from '../../../lib/database.types';
import { logger } from './logger';

type UserWithStripeAccount = {
  id: string;
  email: string | null;
  first_name: string | null;
  last_name: string | null;
  stripe_account_id: string | null;
  stripe_account?: {
    id: string;
    applied_configurations: string[] | null;
    contact_email: string | null;
    display_name: string | null;
  } | null;
};

/**
 * Helper function to list all users from the database with their stripe account information
 * @param supabase - Initialized Supabase client
 * @returns Promise<UserWithStripeAccount[]> - Array of users with stripe account data
 */
export async function listDbUsersHelper(
  supabase: SupabaseClient<Database>,
): Promise<UserWithStripeAccount[]> {
  logger.info(
    {},
    'Fetching users from database with stripe account information...',
  );

  try {
    const { data: users, error } = await supabase
      .from('users')
      .select(
        `
        id,
        email,
        first_name,
        last_name,
        stripe_account_id,
        stripe_accounts:stripe_account_id (
          id,
          applied_configurations,
          contact_email,
          display_name
        )
      `,
      )
      .order('created_at', { ascending: false });

    if (error) {
      logger.error({ error }, 'Error fetching users from database');
      throw new Error(`Failed to fetch users: ${error.message}`);
    }

    // Transform the data to match our expected structure
    const transformedUsers: UserWithStripeAccount[] = (users || []).map(
      (user) => ({
        id: user.id,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
        stripe_account_id: user.stripe_account_id,
        stripe_account: Array.isArray(user.stripe_accounts)
          ? user.stripe_accounts[0] || null
          : user.stripe_accounts,
      }),
    );

    logger.success(
      { count: transformedUsers.length },
      `Successfully fetched ${transformedUsers.length} users from database.`,
    );
    return transformedUsers;
  } catch (error) {
    logger.error({ error }, 'Unexpected error fetching users from database');
    throw error;
  }
}
