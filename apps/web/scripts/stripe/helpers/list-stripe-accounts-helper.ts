import chalk from 'chalk';
import type <PERSON><PERSON> from 'stripe';

import type {
  StripeAccountV2,
  StripeAccountV2ListParams,
} from '../../../lib/stripe/types';
import { logger } from './logger';

type ListStripeAccountsParams = {
  limit?: number;
  email?: string;
  startingAfter?: string;
};

/**
 * Helper function to list all Stripe Accounts v2 with customer configuration, handling pagination.
 * @param stripe - Initialized Stripe client
 * @param params - Optional parameters like email or limit
 * @returns Promise<StripeAccountV2[]> - Array of Stripe Account v2 objects
 */
export async function listStripeAccountsHelper(
  stripe: Stripe,
  { limit = 20, email, startingAfter }: ListStripeAccountsParams = {},
): Promise<StripeAccountV2[]> {
  logger.info(
    { limit, email, startingAfter },
    'Fetching Stripe Accounts v2 with customer configuration...',
  );
  const accounts: StripeAccountV2[] = [];
  let hasMore = true;
  let lastAccountId: string | undefined = startingAfter;
  let pageCount = 0;

  const listParams: StripeAccountV2ListParams = {
    limit: Math.min(limit, 20), // Stripe v2 API has a max limit of 20
    // Filter for accounts that have customer configuration
    applied_configurations: ['customer'],
  };

  if (email) {
    listParams.contact_email = email;
  }

  process.stdout.write(chalk.dim(`   Page ${++pageCount}...`));

  while (hasMore) {
    if (lastAccountId) {
      listParams.starting_after = lastAccountId;
    }

    try {
      const response = await stripe.v2.core.accounts.list(listParams);
      // @ts-expect-error - Stripe v2 is not fully typed yet
      if (response.data.length > 0) {
        // @ts-expect-error - Stripe v2 is not fully typed yet
        accounts.push(...response.data); // v2 accounts don't have a deleted flag like v1 customers
        // @ts-expect-error - Stripe v2 is not fully typed yet
        const lastItem = response.data[response.data.length - 1];
        if (lastItem) {
          lastAccountId = lastItem.id;
        } else {
          hasMore = false;
        }
      } else {
        hasMore = false;
      }
      // @ts-expect-error - Stripe v2 is not fully typed yet
      hasMore = response.has_more;

      if (hasMore) {
        process.stdout.write(chalk.dim(` ${++pageCount}...`));
      }
    } catch (error) {
      logger.error(
        { error, lastAccountId },
        'Error fetching page of Stripe Accounts v2',
      );
      throw new Error('Failed to list Stripe Accounts v2');
    }
  }
  process.stdout.write(chalk.green('✓ Done.\n'));
  logger.info(
    { count: accounts.length },
    `Fetched a total of ${accounts.length} Stripe Accounts v2 with customer configuration.`,
  );
  return accounts;
}
