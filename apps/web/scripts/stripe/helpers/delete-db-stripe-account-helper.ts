import { SupabaseClient } from '@supabase/supabase-js';

import chalk from 'chalk';

import type { Database } from '../../../lib/database.types';
import { logger } from './logger';

/**
 * Deletes a stripe account record from the database by its primary key (ID).
 *
 * @param supabase - Initialized Supabase client instance.
 * @param dbAccountId - The primary key (id column) of the stripe_accounts record to delete.
 * @returns Promise<boolean> - True if deletion was successful, false otherwise.
 */
export async function deleteDbStripeAccountHelper(
  supabase: SupabaseClient<Database>,
  dbAccountId: string,
): Promise<boolean> {
  const logContext = {
    source: 'database',
    operation: 'deleteStripeAccount',
    dbAccountId,
  };
  logger.info(logContext, `Deleting stripe_accounts record ${dbAccountId}...`);
  process.stdout.write(
    chalk.dim(`   Attempting DB deletion for record ${dbAccountId}... `),
  );

  try {
    const { error: deleteError } = await supabase
      .from('stripe_accounts')
      .delete()
      .eq('id', dbAccountId);

    if (deleteError) {
      process.stdout.write(chalk.red('✗ Deletion Failed\n'));
      logger.error(
        { ...logContext, error: deleteError.message },
        `Failed to delete stripe_accounts record ${dbAccountId}: ${deleteError.message}`,
      );
      return false;
    } else {
      process.stdout.write(chalk.green('✓ Deleted\n'));
      logger.success(
        logContext,
        `Successfully deleted stripe_accounts record ${dbAccountId}.`,
      );
      return true;
    }
  } catch (error) {
    process.stdout.write(chalk.red('✗ Deletion Failed (Unexpected)\n'));
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(
      { ...logContext, error: errorMessage },
      `Unexpected error deleting stripe_accounts record ${dbAccountId}.`,
    );
    return false;
  }
}
