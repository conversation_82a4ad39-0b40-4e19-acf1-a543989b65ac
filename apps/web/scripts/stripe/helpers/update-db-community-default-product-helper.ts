import type { SupabaseClient } from '@supabase/supabase-js';

import chalk from 'chalk';

import type { Database } from '../../../lib/database.types';
import { logger } from './logger';

/**
 * Updates the default_product_id for a specific community in the database.
 *
 * @param supabase - Initialized Supabase client instance.
 * @param communityId - The UUID of the community to update.
 * @param defaultProductId - The UUID of the product to set as default.
 * @returns Promise<boolean> - True if the update was successful, false otherwise.
 */
export async function updateDbCommunityDefaultProductHelper(
  supabase: SupabaseClient<Database>,
  communityId: string,
  defaultProductId: string,
): Promise<boolean> {
  const logContext = { source: 'database', communityId, defaultProductId };
  logger.info(
    logContext,
    `Attempting to update default_product_id for community ${communityId}...`,
  );
  process.stdout.write(
    chalk.dim(`   Updating community ${communityId} default product... `),
  );

  try {
    const { error } = await supabase
      .from('communities')
      .update({ default_product_id: defaultProductId })
      .eq('id', communityId);

    if (error) {
      process.stdout.write(chalk.red('✗ Update Failed\n'));
      logger.error(
        { ...logContext, error },
        `Error updating default_product_id for community ${communityId}.`,
      );
      return false;
    }

    process.stdout.write(chalk.green('✓ Updated\n'));
    logger.success(
      { ...logContext },
      `Successfully updated default_product_id for community ${communityId}.`,
    );
    return true;
  } catch (error) {
    process.stdout.write(chalk.red('✗ Unexpected Error\n'));
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(
      { ...logContext, error: errorMessage },
      `An unexpected error occurred while updating default_product_id for community ${communityId}.`,
    );
    return false;
  }
}
