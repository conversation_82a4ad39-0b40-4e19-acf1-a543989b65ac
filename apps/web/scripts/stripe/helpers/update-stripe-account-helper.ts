import chalk from 'chalk';
import type <PERSON><PERSON> from 'stripe';

import { logger } from './logger';

export type StripeAccountV2UpdateParams = {
  contact_email?: string;
  display_name?: string;
  metadata?: Record<string, string>;
  configuration?: {
    customer?: Record<string, unknown>;
  };
};

/**
 * Updates an existing Stripe Account v2 with customer configuration.
 *
 * @param stripe - Initialized Stripe client instance.
 * @param accountId - The ID of the account to update.
 * @param updateData - The data to update on the account object.
 * @returns Promise<Record<string, unknown> | null> - The updated account object or null on error.
 */
export async function updateStripeAccountHelper(
  stripe: Stripe,
  accountId: string,
  updateData: StripeAccountV2UpdateParams,
): Promise<Record<string, unknown> | null> {
  const logContext = {
    source: 'stripe',
    accountId,
    updateData,
  };
  logger.info(logContext, `Updating Stripe Account v2 ${accountId}...`);
  process.stdout.write(
    chalk.dim(`   Updating Stripe Account v2 ${accountId}... `),
  );

  try {
    const updatedAccount = await stripe.v2.core.accounts.update(
      accountId,
      updateData,
    );

    process.stdout.write(chalk.green('✓ Updated\n'));
    logger.success(
      { ...logContext, accountId: updatedAccount.id },
      `Successfully updated Stripe Account v2 ${updatedAccount.id}.`,
    );
    // @ts-expect-error - Stripe v2 is not fully typed yet
    return updatedAccount;
  } catch (error) {
    process.stdout.write(chalk.red('✗ Update Failed\n'));
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(
      { ...logContext, error: errorMessage },
      'Error updating Stripe Account v2.',
    );
    // Return null to indicate failure, allow caller to handle
    return null;
  }
}
