import chalk from 'chalk';
import type <PERSON><PERSON> from 'stripe';

import { logger } from './logger';

/**
 * Creates a Stripe subscription.
 *
 * @param stripe - Initialized Stripe client instance.
 * @param subscriptionData - Data for creating the subscription.
 * @returns Promise<Stripe.Subscription | null> - The created subscription or null on error.
 */
export async function createStripeSubscriptionHelper(
  stripe: Stripe,
  subscriptionData: Stripe.SubscriptionCreateParams,
): Promise<Stripe.Subscription | null> {
  const logContext = {
    source: 'stripe',
    customerId: subscriptionData.customer,
    itemPriceId: subscriptionData.items?.[0]?.price, // Log primary item price
    metadata: subscriptionData.metadata,
  };

  logger.info(logContext, 'Creating Stripe subscription...');
  process.stdout.write(
    chalk.dim(
      `   Creating Stripe subscription for customer ${subscriptionData.customer}... `,
    ),
  );

  try {
    const subscription = await stripe.subscriptions.create(subscriptionData);

    process.stdout.write(chalk.green(`✓ Created (ID: ${subscription.id})\n`));
    logger.success(
      { ...logContext, subscriptionId: subscription.id },
      `Successfully created Stripe subscription ${subscription.id}.`,
    );
    return subscription;
  } catch (error) {
    process.stdout.write(chalk.red('✗ Creation Failed\n'));
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(
      { ...logContext, error: errorMessage },
      'Error creating Stripe subscription.',
    );
    // Don't re-throw, return null to allow the calling script to decide how to handle.
    return null;
  }
}
