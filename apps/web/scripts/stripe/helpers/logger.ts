import chalk from 'chalk';

// Simple logger for scripts
export interface LogData {
  [key: string]: unknown;
}

export const logger = {
  info: (data: LogData, message: string) =>
    console.log(`${chalk.blue('INFO:')} ${chalk.dim(message)}`, data),
  warn: (data: LogData, message: string) =>
    console.warn(`${chalk.yellow('WARN:')} ${message}`, data),
  error: (data: LogData, message: string) => {
    console.error(`${chalk.red('ERROR:')} ${message}`, data);
  },
  fatal: (data: LogData, message: string) => {
    console.error(`${chalk.red('ERROR:')} ${message}`, data);
    process.exit(1);
  },
  success: (data: LogData, message: string) =>
    console.log(`${chalk.green('✓')} ${message}`, data),
};
