import type { SupabaseClient } from '@supabase/supabase-js';

import chalk from 'chalk';
import type <PERSON><PERSON> from 'stripe';

import type { Database } from '../../../lib/database.types';
import { logger } from './logger';

/**
 * Activates a specific product while deactivating all other products for the community.
 * Also updates the community's default_product_id.
 * This is the seed script equivalent of toggleCurrentProductActive from the frontend services.
 *
 * @param stripe - Stripe SDK instance
 * @param client - Supabase client
 * @param productId - The ID of the product to make active
 * @returns Promise<boolean> - True if successful, false otherwise
 */
export async function toggleCurrentProductActiveHelper(
  stripe: Stripe,
  client: SupabaseClient<Database>,
  productId: string,
): Promise<boolean> {
  const logContext = {
    source: 'toggleCurrentProductActiveHelper',
    productId,
  };

  logger.info(logContext, 'Starting product activation process');
  process.stdout.write(
    chalk.dim(`   Making product ${productId} the active product... `),
  );

  try {
    // 1. Get the community ID for this product
    const { data: product, error: productError } = await client
      .from('products')
      .select('community_id')
      .eq('id', productId)
      .single();

    if (productError || !product || !product.community_id) {
      process.stdout.write(
        chalk.red('✗ Product not found or missing community\n'),
      );
      logger.error(
        { ...logContext, error: productError },
        'Product not found or missing community',
      );
      return false;
    }

    const communityId = product.community_id;
    logger.info({ ...logContext, communityId }, 'Found community for product');

    // 2. Get all active products for this community (excluding the current product)
    const { data: activeProducts, error: activeProductsError } = await client
      .from('products')
      .select('id')
      .eq('community_id', communityId)
      .eq('active', true)
      .neq('id', productId);

    if (activeProductsError) {
      process.stdout.write(chalk.red('✗ Error fetching active products\n'));
      logger.error(
        { ...logContext, error: activeProductsError },
        'Error fetching active products',
      );
      return false;
    }

    // 3. Deactivate all other active products in both Stripe and DB
    for (const activeProduct of activeProducts || []) {
      logger.info(
        { ...logContext, deactivatingProductId: activeProduct.id },
        'Deactivating other product',
      );

      // Deactivate in Stripe
      try {
        await stripe.products.update(activeProduct.id, { active: false });
      } catch (stripeError) {
        logger.error(
          { ...logContext, error: stripeError, productId: activeProduct.id },
          'Error deactivating product in Stripe',
        );
        // Continue with DB update even if Stripe fails
      }

      // Deactivate in DB
      const { error: deactivateError } = await client
        .from('products')
        .update({ active: false })
        .eq('id', activeProduct.id);

      if (deactivateError) {
        logger.error(
          {
            ...logContext,
            error: deactivateError,
            productId: activeProduct.id,
          },
          'Error deactivating product in database',
        );
      }
    }

    // 4. Activate the current product in both Stripe and DB
    logger.info({ ...logContext }, 'Activating current product');

    // Activate in Stripe
    try {
      await stripe.products.update(productId, { active: true });
    } catch (stripeError) {
      process.stdout.write(chalk.red('✗ Error activating in Stripe\n'));
      logger.error(
        { ...logContext, error: stripeError },
        'Error activating product in Stripe',
      );
      return false;
    }

    // Activate in DB
    const { error: activateError } = await client
      .from('products')
      .update({ active: true })
      .eq('id', productId);

    if (activateError) {
      process.stdout.write(chalk.red('✗ Error activating in DB\n'));
      logger.error(
        { ...logContext, error: activateError },
        'Error activating product in database',
      );
      return false;
    }

    // 5. Update the community's default_product_id
    logger.info(
      { ...logContext, communityId },
      'Updating community default product',
    );
    const { error: updateError } = await client
      .from('communities')
      .update({ default_product_id: productId })
      .eq('id', communityId);

    if (updateError) {
      process.stdout.write(chalk.red('✗ Error updating community\n'));
      logger.error(
        { ...logContext, error: updateError },
        'Error updating community default product',
      );
      return false;
    }

    process.stdout.write(chalk.green('✓ Activated\n'));
    logger.success(
      { ...logContext, communityId },
      'Successfully activated product and updated community',
    );
    return true;
  } catch (error) {
    process.stdout.write(chalk.red('✗ Unexpected Error\n'));
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(
      { ...logContext, error: errorMessage },
      'Unexpected error in product activation process',
    );
    return false;
  }
}
