import type Stripe from 'stripe';

import { logger } from './logger';

// Payload for listing prices
interface ListStripePricesPayload {
  stripe: Stripe;
  productId?: string; // Filter by product ID
  communityId?: string; // For connected accounts
  active?: boolean; // Filter by active status
  currency?: string; // Filter by currency
  type?: 'one_time' | 'recurring'; // Filter by price type
  recurring?: Stripe.PriceListParams.Recurring; // Filter by recurring details
  // Pagination is handled internally, so limit/startingAfter are not needed here
}

/**
 * Fetches all prices from Stripe, handling pagination.
 * Can fetch platform prices or prices for a specific stripe account,
 * optionally filtered by product ID and other criteria.
 * @param payload - Object containing the Stripe instance and filter criteria.
 * @returns Promise<Stripe.Price[]> - Array of Stripe Price objects.
 */
export async function listStripePricesHelper(
  payload: ListStripePricesPayload,
): Promise<Stripe.Price[]> {
  const { stripe, productId, communityId, ...filters } = payload;
  const prices: Stripe.Price[] = [];
  const limit = 100; // Max limit per request

  const logContext = {
    productId: productId ?? 'all',
    communityId: communityId ?? 'platform',
    filters,
  };

  logger.info(
    logContext,
    `Fetching all Stripe prices ${communityId ? `for account ${communityId}` : 'for platform'}${productId ? ` for product ${productId}` : ''}...`,
  );

  try {
    while (true) {
      const response: Stripe.ApiList<Stripe.Price> = await stripe.prices.list({
        product: productId,
        ...filters,
        limit,
      });

      prices.push(...response.data);

      if (!response.has_more) {
        break;
      }
    }

    logger.success(
      { ...logContext, prices: prices.length },
      `Successfully fetched ${prices.length} prices ${communityId ? `for account ${communityId}` : 'for platform'}${productId ? ` for product ${productId}` : ''}.`,
    );
    return prices;
  } catch (error) {
    logger.error(
      { ...logContext, error },
      `Failed to fetch Stripe prices ${communityId ? `for account ${communityId}` : 'for platform'}${productId ? ` for product ${productId}` : ''}.`,
    );
    throw new Error('Failed to list prices from Stripe'); // Re-throw
  }
}
