import chalk from 'chalk';
import type <PERSON><PERSON> from 'stripe';

import type { StripeAccountV2 } from '../../../lib/stripe/types';
import { logger } from './logger';

interface SearchStripeAccountsParams {
  query: string;
  limit?: number;
  page?: string; // For pagination if needed in the future
}

interface StripeAccountV2SearchParams {
  applied_configurations: string[];
  limit?: number;
  contact_email?: string;
}

/**
 * Searches for Stripe Accounts v2 with customer configuration based on a query.
 *
 * @param stripe - Initialized Stripe client instance.
 * @param params - Search parameters including query and optional limit.
 * @returns Promise<StripeAccountV2[]> - An array of found accounts (or empty array).
 * @throws Error if searching fails.
 */
export async function searchStripeAccountsHelper(
  stripe: Stripe,
  { query, limit }: SearchStripeAccountsParams,
): Promise<StripeAccountV2[]> {
  const logContext = { source: 'stripe', query, limit };
  logger.info(
    logContext,
    'Searching Stripe Accounts v2 with customer configuration...',
  );
  process.stdout.write(
    chalk.dim(`   Searching Stripe for query "${query}"... `),
  );

  try {
    // For v2 accounts, we'll use the list API with filters instead of search
    // Parse the query to extract email if it's in the format email:'value'
    let contactEmail: string | undefined;
    const emailMatch = query.match(/email:'([^']+)'/);
    if (emailMatch) {
      contactEmail = emailMatch[1];
    }

    const searchParams: StripeAccountV2SearchParams = {
      applied_configurations: ['customer'], // Only get accounts with customer configuration
    };

    if (limit) {
      searchParams.limit = limit;
    }

    if (contactEmail) {
      searchParams.contact_email = contactEmail;
    }

    // @ts-expect-error - Stripe v2 is not fully typed yet
    const searchResult = await stripe.v2.core.accounts.list(searchParams);

    const foundAccounts = searchResult.data || [];

    process.stdout.write(chalk.green(`✓ Found ${foundAccounts.length}\n`));
    logger.success(
      { ...logContext, count: foundAccounts.length },
      `Successfully searched Stripe Accounts v2. Found ${foundAccounts.length}.`,
    );
    return foundAccounts;
  } catch (error) {
    process.stdout.write(chalk.red('✗ Search Failed\n'));
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(
      { ...logContext, error: errorMessage },
      'Error searching Stripe Accounts v2.',
    );
    // Re-throw to allow the calling script to handle the failure
    throw new Error(
      `Failed to search Stripe Accounts v2 with query "${query}": ${errorMessage}`,
    );
  }
}
