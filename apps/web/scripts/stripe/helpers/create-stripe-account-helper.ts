import type <PERSON>e from 'stripe';

import { logger } from './logger';

/**
 * Parameters for creating a Stripe Account v2 with customer configuration
 */
export type CreateStripeAccountParams = {
  email: string;
  name?: string;
  country?: string; // ISO country code like 'US'
  metadata?: Record<string, string>;
};

/**
 * Creates a new Stripe Account v2 with customer configuration.
 * @param stripe - Initialized Stripe client.
 * @param params - Account details (email, name, metadata, country).
 * @returns Promise<Record<string, unknown> | null> - The created Stripe Account v2 or null on error.
 */
export async function createStripeAccountHelper(
  stripe: Stripe,
  params: CreateStripeAccountParams,
): Promise<Record<string, unknown> | null> {
  const ctx = {
    email: params.email,
    name: params.name,
    metadata: params.metadata,
    country: params.country,
  };
  logger.info(
    {
      ctx,
    },
    'Creating new Stripe Account v2 with customer configuration...',
  );

  try {
    const newAccount = await stripe.v2.core.accounts.create({
      configuration: {
        customer: {
          // Basic customer configuration - this enables the account to act as a customer
        },
      },
      contact_email: params.email,
      display_name: params.name || undefined,
      metadata: params.metadata || {},
    });

    logger.success(
      { accountId: newAccount.id },
      `Successfully created Stripe Account v2 ${newAccount.id}`,
    );

    logger.info(ctx, 'Update new customer metadata');
    await stripe.customers.update(newAccount.id, {
      metadata: params.metadata || {},
    });
    // @ts-expect-error - Stripe v2 is not fully typed yet
    return newAccount;
  } catch (error) {
    logger.error(
      {
        error,
        ctx,
      },
      'Error creating Stripe Account v2',
    );
    return null;
  }
}
