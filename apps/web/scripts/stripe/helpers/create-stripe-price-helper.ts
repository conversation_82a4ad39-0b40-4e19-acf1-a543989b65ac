import type Stripe from 'stripe';

import { logger } from './logger';

/**
 * Creates a new price in Stripe.
 * @param stripe - Initialized Stripe client.
 * @param params - Price details (product, currency, recurring, metadata).
 * @returns Promise<Stripe.Price | null> - The created Stripe price or null on error.
 */
export async function createStripePriceHelper(
  stripe: Stripe,
  params: Stripe.PriceCreateParams,
): Promise<Stripe.Price | null> {
  const priceData: Stripe.PriceCreateParams = { ...params };

  logger.info(
    {
      product: params.product,
      currency: params.currency,
      metadata: params.metadata,
    },
    'Creating new Stripe price...',
  );

  try {
    const newPrice = await stripe.prices.create(priceData);
    logger.success(
      { priceId: newPrice.id },
      `Successfully created Stripe price ${newPrice.id}`,
    );
    return newPrice;
  } catch (error) {
    logger.error(
      {
        error,
        product: params.product,
        currency: params.currency,
        metadata: params.metadata,
      },
      'Error creating Stripe price',
    );
    return null;
  }
}
