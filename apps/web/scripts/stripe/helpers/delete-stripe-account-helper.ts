import chalk from 'chalk';
import type <PERSON><PERSON> from 'stripe';

import { logger } from './logger';

/**
 * Deletes a Stripe account.
 *
 * @param stripe - Initialized Stripe client instance.
 * @param accountId - The ID of the stripe account to delete.
 * @returns Promise<boolean> - True if deletion was successful, false otherwise.
 */
export async function deleteStripeAccountHelper(
  stripe: Stripe,
  accountId: string,
): Promise<boolean> {
  const logContext = {
    source: 'stripe',
    accountId,
  };
  logger.info(logContext, `Deleting Stripe account ${accountId}...`);
  process.stdout.write(
    chalk.dim(`   Attempting Stripe deletion for ${accountId}... `),
  );

  try {
    const deletionConfirmation = await stripe.v2.core.accounts.del(accountId);

    if (deletionConfirmation.deleted) {
      process.stdout.write(chalk.green('✓ Deleted\n'));
      logger.success(
        logContext,
        `Successfully deleted Stripe account ${accountId}.`,
      );
      return true;
    } else {
      // This case might indicate an issue if Stripe API confirms deletion but sets deleted=false
      process.stdout.write(chalk.yellow('Deletion Unconfirmed?\n'));
      logger.warn(
        { ...logContext, deletionConfirmation },
        `Stripe deletion API call succeeded for ${accountId}, but 'deleted' flag is false.`,
      );
      return false; // Treat as failure if not explicitly confirmed deleted
    }
  } catch (error) {
    process.stdout.write(chalk.red('✗ Deletion Failed\n'));
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(
      { ...logContext, error: errorMessage },
      `Error deleting Stripe account ${accountId}.`,
    );
    return false; // Indicate failure
  }
}
