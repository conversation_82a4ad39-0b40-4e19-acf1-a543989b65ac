import type { SupabaseClient } from '@supabase/supabase-js';

import chalk from 'chalk';

import type { Database, TablesInsert } from '../../../lib/database.types';
import { logger } from './logger';

// Use TablesInsert for the payload type, ensuring all required fields are present
type DbSubscriptionItemInsert = TablesInsert<'subscription_items'>;

/**
 * Upserts a subscription item record in the Supabase database.
 *
 * @param supabaseClient - The Supabase client instance.
 * @param itemData - The subscription item data to upsert.
 * @returns Promise<boolean> - True if successful, false otherwise.
 */
export async function upsertDbSubscriptionItemHelper(
  supabaseClient: SupabaseClient<Database>,
  itemData: DbSubscriptionItemInsert,
): Promise<boolean> {
  const logContext = {
    source: 'database',
    subscription_id: itemData.subscription_id,
    subscription_item_id: itemData.id,
    product_id: itemData.product_id,
    variant_id: itemData.variant_id,
  };

  logger.info(
    logContext,
    `Upserting subscription item ${itemData.id} for subscription ${itemData.subscription_id}...`,
  );
  process.stdout.write(chalk.dim(`   Upserting item ${itemData.id}... `));

  try {
    const { error } = await supabaseClient
      .from('subscription_items')
      .upsert(itemData, {
        onConflict: 'subscription_id, product_id, variant_id', // Specify conflict target
      });

    if (error) {
      process.stdout.write(chalk.red('✗ Upsert Failed\n'));
      logger.error(
        { ...logContext, error },
        `Error upserting subscription item ${itemData.id}.`,
      );
      return false;
    }

    process.stdout.write(chalk.green('✓ Upserted\n'));
    logger.success(
      { ...logContext },
      `Successfully upserted subscription item ${itemData.id}.`,
    );
    return true;
  } catch (error) {
    process.stdout.write(chalk.red('✗ Upsert Failed (Unexpected)\n'));
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(
      { ...logContext, error: errorMessage },
      `An unexpected error occurred while upserting subscription item ${itemData.id}.`,
    );
    return false;
  }
}
