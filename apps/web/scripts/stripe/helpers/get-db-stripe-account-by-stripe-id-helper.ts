import { SupabaseClient } from '@supabase/supabase-js';

import chalk from 'chalk';

import type { Database } from '../../../lib/database.types';
import { logger } from './logger';

type DbStripeAccount = Pick<
  Database['public']['Tables']['stripe_accounts']['Row'],
  'id' | 'user_id'
>;

/**
 * Fetches a single stripe account record from the database by its Stripe Account ID.
 *
 * @param supabase - Initialized Supabase client instance.
 * @param stripeAccountId - The Stripe Account ID to search for.
 * @returns Promise<DbConnectedAccount | null> - The found record or null if not found or error.
 */
export async function getDbStripeAccountByStripeIdHelper(
  supabase: SupabaseClient<Database>,
  stripeAccountId: string,
): Promise<DbStripeAccount | null> {
  const logContext = {
    source: 'database',
    operation: 'getStripeAccountByStripeId',
    stripeAccountId,
  };
  logger.info(
    logContext,
    `Fetching stripe_accounts record for Stripe ID ${stripeAccountId}...`,
  );
  process.stdout.write(
    chalk.dim(`   Querying DB for Stripe Account ${stripeAccountId}... `),
  );

  try {
    const { data: stripeAccount, error: fetchError } = await supabase
      .from('stripe_accounts')
      .select('id, user_id')
      .eq('id', stripeAccountId)
      .maybeSingle();

    if (fetchError) {
      // Don't treat "not found" as a hard error for the helper's purpose
      if (fetchError.code === 'PGRST116') {
        process.stdout.write(chalk.yellow('Not Found\n'));
        logger.info(
          logContext,
          `No stripe_accounts record found for Stripe Account ID ${stripeAccountId}.`,
        );
        return null;
      } else {
        // Log other errors
        process.stdout.write(chalk.red('✗ DB Query Failed\n'));
        logger.error(
          { ...logContext, error: fetchError.message },
          `Failed to fetch stripe_accounts record: ${fetchError.message}`,
        );
        return null; // Indicate failure
      }
    }

    if (stripeAccount) {
      process.stdout.write(chalk.green('✓ Found\n'));
      logger.success(
        { ...logContext, dbAccountId: stripeAccount.id },
        `Found stripe_accounts record ${stripeAccount.id} for Stripe ID ${stripeAccountId}.`,
      );
      return stripeAccount;
    } else {
      // This case should be covered by maybeSingle returning null or PGRST116
      process.stdout.write(chalk.yellow('Not Found (unexpected path)\n'));
      logger.info(
        logContext,
        `No stripe_accounts record found for Stripe ID ${stripeAccountId} (unexpected path).`,
      );
      return null;
    }
  } catch (error) {
    process.stdout.write(chalk.red('✗ DB Query Failed (Unexpected)\n'));
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(
      { ...logContext, error: errorMessage },
      `Unexpected error fetching stripe_accounts record for Stripe ID ${stripeAccountId}.`,
    );
    return null; // Indicate failure
  }
}
