import type Stripe from 'stripe';

import { logger } from './logger';

interface ListStripeProductsPayload {
  stripe: Stripe;
  communityId?: string;
  limit?: number;
}

/**
 * Helper function to list products directly from Stripe.
 * @param payload - Object containing the Stripe instance and optionally the communityId.
 * @returns Promise<Stripe.Product[]> - Array of Stripe Product objects.
 */
export async function searchStripeProductsHelper(
  payload: ListStripeProductsPayload,
): Promise<Stripe.Product[]> {
  const { stripe, communityId, limit } = payload;
  const products: Stripe.Product[] = [];

  const logContext = {
    source: 'stripe',
    communityId: communityId ?? 'platform',
  };

  logger.info(
    logContext,
    `Fetching Stripe products ${communityId ? `for community ${communityId}` : 'for platform'}...`,
  );

  try {
    while (true) {
      // Get active products
      let query = ``;

      if (communityId) {
        query += `metadata["community_id"]:\"${communityId}\"`;
      } else {
        query = `metadata["seller_type"]:\"platform\"`;
      }

      const response: Stripe.ApiSearchResult<Stripe.Product> =
        await stripe.products.search({
          query: query + ' AND active:\"true\"',
          limit,
        });

      products.push(...response.data);

      // Get archived products
      const archivedResponse: Stripe.ApiSearchResult<Stripe.Product> =
        await stripe.products.search({
          query: query + ' AND active:\"false\"',
          limit,
        });

      products.push(...archivedResponse.data);

      if (!response.has_more && !archivedResponse.has_more) {
        break;
      }
    }

    logger.success(
      { ...logContext, products: products.length },
      `Successfully fetched ${products.length} products ${communityId ? `for community ${communityId}` : 'for platform'}.`,
    );
    return products;
  } catch (error) {
    logger.error(
      { error, communityId: communityId ?? 'platform' },
      `Failed to fetch Stripe products ${communityId ? `for community ${communityId}` : 'for platform'}.`,
    );
    // Depending on requirements, you might want to throw the error
    // or return an empty array / handle it differently.
    throw new Error('Failed to list products from Stripe');
  }
}
