import { SupabaseClient } from '@supabase/supabase-js';

import type { Database } from '../../../lib/database.types';
import { logger } from './logger';

type UserDetails = Pick<
  Database['public']['Tables']['users']['Row'],
  'id' | 'email' | 'first_name' | 'last_name'
> | null;

/**
 * Fetches details for a specific user from the database.
 * @param supabase - Initialized Supabase client.
 * @param userId - The ID of the user to fetch.
 * @returns Promise<UserDetails | null> - User details or null if not found/error.
 */
export async function getDbUserHelper(
  supabase: SupabaseClient<Database>,
  userId: string,
): Promise<UserDetails> {
  logger.info({ userId }, 'Fetching user details from database...');
  try {
    const { data, error } = await supabase
      .from('users')
      .select('id, email, first_name, last_name')
      .eq('id', userId)
      .single();

    if (error) {
      // Log PgrstError 'PGRST116' (Row not found) as info, others as error
      if (error.code === 'PGRST116') {
        logger.info({ userId }, `User not found in database.`);
      } else {
        logger.error({ error, userId }, 'Error fetching user from database.');
      }
      return null; // Return null on error or not found
    }

    logger.info(
      { userId, email: data?.email },
      'Successfully fetched user details.',
    );
    return data;
  } catch (err) {
    logger.error({ error: err, userId }, 'Unexpected error fetching user.');
    return null;
  }
}
