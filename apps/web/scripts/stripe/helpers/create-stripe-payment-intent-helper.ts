import chalk from 'chalk';
import type Stripe from 'stripe';

import { logger } from './logger';

/**
 * Creates and confirms a Stripe Payment Intent, typically for the first invoice
 * of a subscription created with payment_behavior='default_incomplete'.
 * Uses the pm_card_visa test payment method.
 *
 * @param stripe - Initialized Stripe client instance.
 * @param paymentIntentData - Data for creating the Payment Intent. Must include
 *                            amount, currency, customer, and typically confirm=true
 *                            and payment_method='pm_card_visa'.
 * @returns Promise<Stripe.PaymentIntent | null> - The created Payment Intent or null on error.
 */
export async function createAndConfirmTestPaymentIntentHelper(
  stripe: Stripe,
  paymentIntentData: Stripe.PaymentIntentCreateParams,
): Promise<Stripe.PaymentIntent | null> {
  const logContext = {
    source: 'stripe',
    customerId: paymentIntentData.customer,
    amount: paymentIntentData.amount,
    currency: paymentIntentData.currency,
    metadata: paymentIntentData.metadata,
  };

  logger.info(logContext, 'Creating and confirming Stripe Payment Intent...');
  process.stdout.write(
    chalk.dim(
      `   Creating Payment Intent for customer ${paymentIntentData.customer} (Amount: ${paymentIntentData.amount} ${paymentIntentData.currency})... `,
    ),
  );

  try {
    // Ensure test payment method and confirmation are set if not provided
    const dataToCreate: Stripe.PaymentIntentCreateParams = {
      payment_method: 'pm_card_visa', // Default to test card
      confirm: true, // Default to confirming immediately
      ...paymentIntentData, // Allow overrides
      automatic_payment_methods: {
        // Ensure redirects are off for server-side test confirmation
        enabled: true,
        allow_redirects: 'never',
        ...(paymentIntentData.automatic_payment_methods || {}), // Allow overrides
      },
    };

    const paymentIntent = await stripe.paymentIntents.create(dataToCreate);

    process.stdout.write(
      chalk.green(`✓ Created & Confirmed (ID: ${paymentIntent.id})\n`),
    );
    logger.success(
      { ...logContext, paymentIntentId: paymentIntent.id },
      `Successfully created and confirmed Payment Intent ${paymentIntent.id}.`,
    );
    return paymentIntent;
  } catch (error) {
    process.stdout.write(chalk.red('✗ Creation/Confirmation Failed\n'));
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(
      { ...logContext, error: errorMessage },
      'Error creating/confirming Stripe Payment Intent.',
    );
    return null;
  }
}
