import type { SupabaseClient } from '@supabase/supabase-js';

import chalk from 'chalk';

import type { Database, TablesUpdate } from '../../../lib/database.types';
import { logger } from './logger';

interface UpsertDbCommunityPayload {
  communityId: string;
  updateData: TablesUpdate<'communities'>; // Use TablesUpdate for flexibility
}

/**
 * Updates a community record in the Supabase database.
 *
 * @param supabaseClient - The Supabase client instance.
 * @param payload - Object containing communityId and the data to update.
 * @returns Promise<boolean> - True if successful, false otherwise.
 */
export async function upsertDbCommunityHelper(
  supabaseClient: SupabaseClient<Database>,
  payload: UpsertDbCommunityPayload,
): Promise<boolean> {
  const { communityId, updateData } = payload;
  const logContext = {
    source: 'database',
    communityId,
    updateData, // Log the data being updated
  };

  logger.info(logContext, `Updating community ${communityId} in database...`);
  process.stdout.write(chalk.dim(`   Updating community ${communityId}... `));

  try {
    const { error } = await supabaseClient
      .from('communities')
      .update(updateData)
      .eq('id', communityId);

    if (error) {
      process.stdout.write(chalk.red('✗ Update Failed\n'));
      logger.error(
        { ...logContext, error },
        `Error updating community ${communityId}.`,
      );
      return false;
    }

    process.stdout.write(chalk.green('✓ Updated\n'));
    logger.success(
      { ...logContext },
      `Successfully updated community ${communityId}.`,
    );
    return true;
  } catch (error) {
    process.stdout.write(chalk.red('✗ Update Failed (Unexpected)\n'));
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(
      { ...logContext, error: errorMessage },
      `An unexpected error occurred while updating community ${communityId}.`,
    );
    return false;
  }
}
