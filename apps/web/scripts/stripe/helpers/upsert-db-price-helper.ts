import type { SupabaseClient } from '@supabase/supabase-js';

import type Stripe from 'stripe';

import type { Database } from '../../../lib/database.types';
import { logger } from './logger';

// Define specific types for Price insert/update data
type PlatformPriceInsert =
  Database['public']['Tables']['product_prices']['Insert'];

/**
 * Upserts a Stripe price into the product_prices table.
 *
 * @param client - The Supabase client instance.
 * @param price - The Stripe price object.
 * @param dbProductId - The database UUID of the associated product in products.
 * @returns The database ID of the upserted price.
 * @throws If the Supabase operation fails.
 */
export async function upsertDbPriceHelper(
  client: SupabaseClient<Database>,
  price: Stripe.Price,
  dbProductId: string, // Expecting the ID from products table
): Promise<string> {
  try {
    const priceData: PlatformPriceInsert = {
      id: price.id,
      nickname: price.nickname,
      product_id: dbProductId, // Use the database UUID here
      unit_amount: price.unit_amount,
      currency: price.currency,
      active: price.active,
      type: price.type,
      interval:
        price.type === 'one_time' ? 'one_time' : price.recurring?.interval,
    };

    const { data: dbPrice, error: priceError } = await client
      .from('product_prices')
      .upsert(priceData, {
        onConflict: 'id',
        ignoreDuplicates: false, // Ensure we update existing records
      })
      .select('id')
      .single();

    if (priceError) {
      logger.error(
        { error: priceError, priceId: price.id, dbProductId },
        'Error upserting price in helper',
      );
      // Log the error details, including the problematic product_id if possible
      console.error(
        `Attempted to insert price ${price.id} with product_id (UUID): ${dbProductId}`,
      );
      throw new Error(`Failed to upsert price: ${priceError.message}`);
    }

    if (!dbPrice?.id) {
      throw new Error('Upsert operation did not return a price ID.');
    }

    return dbPrice.id;
  } catch (error) {
    logger.error(
      { error, priceId: price.id, dbProductId },
      'Error in upsertPriceHelper',
    );
    // Re-throw the error after logging
    throw error;
  }
}
