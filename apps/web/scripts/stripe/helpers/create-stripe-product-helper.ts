import type Stripe from 'stripe';

import { logger } from './logger';

/**
 * Creates a new product in Stripe.
 * @param stripe - Initialized Stripe client.
 * @param params - Product details (name, metadata).
 * @returns Promise<Stripe.Product | null> - The created Stripe product or null on error.
 */
export async function createStripeProductHelper(
  stripe: Stripe,
  params: Stripe.ProductCreateParams,
): Promise<Stripe.Product | null> {
  const productData: Stripe.ProductCreateParams = { ...params };

  logger.info(
    { name: params.name, metadata: params.metadata },
    'Creating new Stripe product...',
  );

  try {
    const newProduct = await stripe.products.create(productData);
    logger.success(
      { productId: newProduct.id },
      `Successfully created Stripe product ${newProduct.id}`,
    );
    return newProduct;
  } catch (error) {
    logger.error(
      { error, name: params.name, metadata: params.metadata },
      'Error creating Stripe product',
    );
    return null;
  }
}
