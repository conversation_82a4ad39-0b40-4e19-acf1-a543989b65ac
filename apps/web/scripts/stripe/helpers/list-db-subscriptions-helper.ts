import { SupabaseClient } from '@supabase/supabase-js';

import type { Database, Tables } from '../../../lib/database.types';
import { logger } from './logger';

/**
 * Fetches existing subscription records from the Supabase 'subscriptions' table.
 *
 * @param supabaseClient - The Supabase client instance.
 * @param provider - Optional provider to filter by (e.g., 'stripe').
 * @returns Promise<Tables<'subscriptions'>[] | null> - An array of subscription records or null on error.
 */
export async function listDbSubscriptionsHelper(
  supabaseClient: SupabaseClient<Database>,
): Promise<Tables<'subscriptions'>[] | null> {
  const logContext = {
    source: 'database',
  };

  logger.info(logContext, `Fetching subscriptions from database...`);

  try {
    const query = supabaseClient.from('subscriptions').select('*');

    const { data, error } = await query;

    if (error) {
      logger.error(
        { ...logContext, error },
        `Error fetching subscriptions from database.`,
      );
      return null; // Indicate failure
    }

    logger.success(
      { ...logContext, subscriptions: data?.length ?? 0 },
      `Successfully fetched ${data?.length ?? 0} subscriptions.`,
    );
    return data || []; // Return data or empty array if null/undefined
  } catch (error) {
    logger.error(
      {
        ...logContext,
        error: error instanceof Error ? error.message : String(error),
      },
      'An unexpected error occurred while fetching subscriptions.',
    );
    return null; // Indicate failure
  }
}
