import type { SupabaseClient } from '@supabase/supabase-js';

import type { Database } from '../../../lib/database.types';
import { logger } from './logger';

// Define the structure of the product data we expect to fetch
export interface DbProduct {
  id: string;
  seller: 'platform' | 'stripe_account' | null;
  active: boolean | null;
  name: string | null;
}

// Define the payload for the helper function
interface ListDbProductsPayload {
  supabaseClient: SupabaseClient<Database>;
  communityId?: string; // Optional: Filter by community
}

/**
 * Fetches product records from the Supabase database.
 * Can fetch all products or filter by a specific community ID.
 *
 * @param payload - Object containing the Supabase client and optional communityId.
 * @returns Promise<DbProduct[]> - A promise that resolves to an array of product objects.
 */
export async function listDbProductsHelper(
  payload: ListDbProductsPayload,
): Promise<DbProduct[]> {
  const { supabaseClient, communityId } = payload;

  const logContext = {
    source: 'database',
    ...(communityId && { communityId }),
  };

  logger.info(
    logContext,
    `Fetching products from database${communityId ? ` for community ${communityId}` : ''}...`,
  );

  let query = supabaseClient
    .from('products')
    .select('id, seller, active, name');

  // Apply filter if communityId is provided
  if (communityId) {
    query = query.eq('community_id', communityId);
  }

  try {
    const { data, error } = await query;

    if (error) {
      logger.error(
        { ...logContext, error },
        `Error fetching products from database${communityId ? ` for community ${communityId}` : ''}`,
      );
      throw error; // Re-throw the error to be handled by the caller
    }

    const products = (data || []) as DbProduct[];
    logger.success(
      { ...logContext, products: products.length },
      `Successfully fetched ${products.length} products from database${communityId ? ` for community ${communityId}` : ''}.`,
    );
    return products;
  } catch (error) {
    logger.error(
      { ...logContext, error },
      `An unexpected error occurred while fetching products from database${communityId ? ` for community ${communityId}` : ''}. Error: ${error instanceof Error ? error.message : String(error)}`,
    );
    // Depending on requirements, might return empty array or re-throw
    throw new Error('Failed to fetch products from database');
  }
}
