import { SupabaseClient } from '@supabase/supabase-js';

import type { Database, TablesInsert } from '../../../lib/database.types';
import { logger } from './logger';

// Define the payload structure for the helper
type UpsertDbSubscriptionItemPayload = TablesInsert<'subscription_items'>;

/**
 * Upserts a subscription item record into the Supabase 'subscription_items' table.
 *
 * @param supabaseClient - The Supabase client instance.
 * @param itemData - The subscription item data to upsert.
 * @returns Promise<boolean> - True if the upsert was successful, false otherwise.
 */
export async function upsertSubscriptionItemHelper(
  supabaseClient: SupabaseClient<Database>,
  itemData: UpsertDbSubscriptionItemPayload,
): Promise<boolean> {
  const logContext = {
    source: 'database',
    subscriptionId: itemData.subscription_id,
    productId: itemData.product_id,
    variantId: itemData.variant_id,
  };

  logger.info(logContext, 'Upserting subscription item record in database...');

  try {
    const { error } = await supabaseClient
      .from('subscription_items')
      .upsert(itemData, {
        // Define the columns that constitute a unique constraint for conflict resolution
        onConflict: 'subscription_id, product_id, variant_id',
      });

    if (error) {
      logger.error(
        { ...logContext, error },
        'Error upserting subscription item record in database.',
      );
      return false; // Indicate failure
    }

    logger.success(
      { ...logContext },
      'Successfully upserted subscription item record in database.',
    );
    return true; // Indicate success
  } catch (error) {
    logger.error(
      {
        ...logContext,
        error: error instanceof Error ? error.message : String(error),
      },
      'An unexpected error occurred while upserting subscription item record.',
    );
    return false; // Indicate failure
  }
}
