#!/usr/bin/env node
import { SupabaseClient, createClient } from '@supabase/supabase-js';

import chalk from 'chalk';
import { config } from 'dotenv';
import { createInterface } from 'node:readline/promises';
import Stripe from 'stripe';

import type { Database } from '../../lib/database.types';
import { PurchaseTypeEnum } from '../../lib/stripe/types';
import { listDbSubscriptionsHelper } from './helpers/list-db-subscriptions-helper';
import { listDbUsersHelper } from './helpers/list-db-users-helper';
import { listStripeSubscriptionsHelper } from './helpers/list-stripe-subscriptions-helper';
import { logger } from './helpers/logger';
import { upsertSubscriptionItemHelper } from './helpers/upsert-db-subscription-items-helper';
import { upsertDbSubscriptionHelper } from './helpers/upsert-db-subscriptions-helper';
import { EnvSchema } from './schema/env.schema';

/**
 * Script-friendly version of StripeBillingService
 * This is a simplified version that implements account subscription syncing
 */
class StripeBillingSubscriptionService {
  constructor(
    private readonly stripe: Stripe,
    private readonly client: SupabaseClient,
  ) {}

  /**
   * Maps Stripe subscription status to our database enum
   */
  private mapSubscriptionStatus(
    status: Stripe.Subscription.Status,
  ):
    | 'active'
    | 'trialing'
    | 'past_due'
    | 'canceled'
    | 'unpaid'
    | 'incomplete'
    | 'incomplete_expired'
    | 'paused' {
    // Direct mapping for most statuses
    if (
      status === 'active' ||
      status === 'trialing' ||
      status === 'past_due' ||
      status === 'canceled' ||
      status === 'unpaid' ||
      status === 'incomplete' ||
      status === 'incomplete_expired'
    ) {
      return status;
    }

    // Handle 'paused' status
    if (status === 'paused') {
      return 'paused';
    }

    // Default to 'incomplete' for any unexpected statuses
    logger.warn(
      {
        script: 'sync-customer-subscriptions',
        status,
      },
      `Unknown subscription status: ${status}, defaulting to 'incomplete'`,
    );
    return 'incomplete';
  }

  /**
   * Saves a subscription to the database
   */
  private async saveSubscriptionToDatabase(
    stripeAccountId: string,
    subscription: Stripe.Subscription,
    userId?: string,
    communityId?: string,
    memberId?: string,
  ): Promise<void> {
    // Determine subscription type based on provided IDs
    let purchaseType: PurchaseTypeEnum = 'community_membership'; // Default value

    if (communityId) {
      purchaseType = 'community_ownership';
    } else if (memberId) {
      purchaseType = 'community_membership';
    }

    // Map subscription data to our database schema
    const subscriptionData = {
      id: subscription.id,
      user_id: userId,
      community_id: communityId,
      member_id: memberId,
      purchase_type: purchaseType,
      currency: subscription.currency,
      payment_method_id: subscription.default_payment_method as string | null,
      status: this.mapSubscriptionStatus(subscription.status),
      active:
        subscription.status === 'active' || subscription.status === 'trialing',
      metadata: subscription.metadata || {},
      // Handle timestamps safely with fallbacks
      current_period_start: subscription.items.data[0]?.current_period_start
        ? new Date(
            Number(subscription.items.data[0]?.current_period_start) * 1000,
          ).toISOString()
        : new Date().toISOString(),
      current_period_end: subscription.items.data[0]?.current_period_end
        ? new Date(
            Number(subscription.items.data[0]?.current_period_end) * 1000,
          ).toISOString()
        : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now as fallback
      cancel_at_period_end: subscription.cancel_at_period_end || false,
      canceled_at: subscription.canceled_at
        ? new Date(Number(subscription.canceled_at) * 1000).toISOString()
        : null,
      ended_at: subscription.ended_at
        ? new Date(Number(subscription.ended_at) * 1000).toISOString()
        : null,
      trial_starts_at: subscription.trial_start
        ? new Date(Number(subscription.trial_start) * 1000).toISOString()
        : null,
      trial_ends_at: subscription.trial_end
        ? new Date(Number(subscription.trial_end) * 1000).toISOString()
        : null,
    };

    // Upsert the subscription
    const upsertSuccess = await upsertDbSubscriptionHelper(
      this.client,
      subscriptionData,
    );

    if (!upsertSuccess) {
      // Helper logs the error
      throw new Error(`Failed to upsert subscription ${subscription.id}`);
    }

    // Now save subscription items
    await this.saveSubscriptionItems(subscription);
  }

  /**
   * Saves subscription items to the database
   */
  private async saveSubscriptionItems(
    subscription: Stripe.Subscription,
  ): Promise<void> {
    for (const item of subscription.items.data) {
      // Skip if price or product is missing
      if (!item.price || !item.price.product) {
        logger.warn(
          {
            script: 'sync-customer-subscriptions',
            subscriptionId: subscription.id,
            itemId: item.id,
          },
          `Skipping subscription item ${item.id} - missing price or product info`,
        );
        continue;
      }

      // Determine subscription item type
      let itemType: 'flat' | 'per_seat' | 'metered' = 'flat';
      if (item.price.transform_quantity) {
        itemType = 'per_seat';
      } else if (item.price.billing_scheme === 'tiered') {
        itemType = 'metered';
      }

      // Get product ID
      const productId = item.price.product as string;

      // Map subscription item data to our database schema
      const itemData = {
        id: item.id,
        subscription_id: subscription.id,

        product_id: productId,
        variant_id: item.price.id, // Using price ID as variant ID
        type: itemType,
        price_amount: item.price.unit_amount,
        quantity: item.quantity || 1,
        interval: item.price.recurring?.interval || 'month',
        interval_count: item.price.recurring?.interval_count || 1,
      };

      // Upsert the subscription item
      const itemUpsertSuccess = await upsertSubscriptionItemHelper(
        this.client,
        itemData,
      );

      if (!itemUpsertSuccess) {
        // Helper logs the error
        throw new Error(`Failed to upsert item ${item.id}`);
      }
    }
  }

  /**
   * Syncs subscriptions for all customers in our database
   */
  async syncCustomerSubscriptions(): Promise<{
    processed: number;
    created: number;
    updated: number;
    failed: number;
    messages: string[];
  }> {
    const messages: string[] = [];
    let processed = 0;
    let created = 0;
    let updated = 0;
    let failed = 0;

    // 1. Get all users with Stripe customer IDs from the database
    console.log(chalk.blue('\n🔍 Fetching users with Stripe customer IDs...'));
    const users = await listDbUsersHelper(this.client);
    const usersWithStripeIds = users.filter((user) => user.stripe_account_id);

    // The helper throws on error, so we only need to check for empty results
    if (usersWithStripeIds.length === 0) {
      console.log(
        chalk.yellow(
          'No users with Stripe customer IDs found in the database.',
        ),
      );
      logger.info(
        { count: usersWithStripeIds.length },
        `No users with Stripe customer IDs found in the database.`,
      );
      return { processed, created, updated, failed, messages }; // Nothing to sync
    }

    logger.info(
      { count: usersWithStripeIds.length },
      `Found ${usersWithStripeIds.length} users with Stripe customer IDs.`,
    );

    // 2. Get existing subscriptions from our database for comparison
    console.log(
      chalk.blue('\n🔍 Fetching existing subscriptions from database...'),
    );
    const existingSubscriptions = await listDbSubscriptionsHelper(this.client);

    if (existingSubscriptions === null) {
      // Helper logs the error, just need to stop execution
      throw new Error('Failed to fetch existing subscriptions from database.');
    }

    // Create a map of existing subscriptions by ID for quick lookup
    const existingSubscriptionMap = new Map();
    (existingSubscriptions || []).forEach((sub) => {
      existingSubscriptionMap.set(sub.id, sub);
    });

    // 3. Process each user with a Stripe customer ID
    console.log(chalk.blue('\n🔄 Syncing subscriptions for each customer...'));

    for (const user of usersWithStripeIds) {
      process.stdout.write(
        chalk.dim(
          `Processing user ${chalk.cyan(user.id)} (Stripe ID: ${user.stripe_account_id})... `,
        ),
      );

      try {
        // Get all subscriptions for this customer from Stripe
        if (!user.stripe_account_id) {
          continue;
        }
        const subscriptions = await listStripeSubscriptionsHelper(
          this.stripe,
          user.stripe_account_id,
        );
        process.stdout.write(
          chalk.green(`Found ${subscriptions.length} subscriptions\n`),
        );

        // Process each subscription
        for (const subscription of subscriptions) {
          try {
            process.stdout.write(
              chalk.dim(
                `  Processing subscription ${chalk.cyan(subscription.id)}... `,
              ),
            );

            // Check if subscription already exists
            const existingSubscription = existingSubscriptionMap.get(
              subscription.id,
            );

            try {
              // Save subscription to database
              await this.saveSubscriptionToDatabase(
                user.stripe_account_id!,
                subscription,
                user.id,
                subscription.metadata?.community_id,
                subscription.metadata?.member_id,
              );
            } catch (error) {
              // Log the full error details
              console.error('Error details:', error);
              console.error(
                'Subscription data:',
                JSON.stringify(subscription, null, 2),
              );
              throw error;
            }

            if (existingSubscription) {
              process.stdout.write(chalk.green('updated ✓\n'));
              updated++;
              messages.push(`Updated subscription ${subscription.id}`);
            } else {
              process.stdout.write(chalk.green('created ✓\n'));
              created++;
              messages.push(`Created subscription ${subscription.id}`);
            }
          } catch (error) {
            process.stdout.write(chalk.red('✗\n'));
            failed++;
            messages.push(
              `Failed to process subscription ${subscription.id}: ${error instanceof Error ? error.message : String(error)}`,
            );
          }
        }

        processed++;
      } catch (error) {
        process.stdout.write(chalk.red('✗\n'));
        failed++;
        messages.push(
          `Failed to fetch subscriptions for account ${user.stripe_account_id}: ${error instanceof Error ? error.message : String(error)}`,
        );
      }
    }

    // 4. Summary
    console.log(chalk.blue('\n📊 Sync Summary:'));
    console.log(
      chalk.cyan(
        `Processed ${chalk.bold(processed.toString())} users with Stripe IDs`,
      ),
    );
    console.log(
      chalk.green(`Created ${chalk.bold(created.toString())} subscriptions`),
    );
    console.log(
      chalk.yellow(`Updated ${chalk.bold(updated.toString())} subscriptions`),
    );
    console.log(
      chalk.red(`Failed ${chalk.bold(failed.toString())} operations`),
    );

    return {
      processed,
      created,
      updated,
      failed,
      messages,
    };
  }
}

// Check for -y flag in command line arguments
const hasYesFlag = process.argv.includes('-y');

/**
 * Asks for confirmation before proceeding
 */
async function confirmSync(): Promise<boolean> {
  const readline = createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  try {
    // If -y flag is present, bypass confirmation
    if (hasYesFlag) {
      console.log(
        chalk.yellow(
          'Auto-confirmation enabled via command line argument (-y).',
        ),
      );
      return true;
    }

    const answer = await readline.question(
      chalk.yellow(
        '⚠️  This will sync all stripe account subscriptions from Stripe to your database. Continue? (y/N) ',
      ),
    );
    return answer.toLowerCase() === 'y';
  } finally {
    // Always close the readline interface
    readline.close();
  }
}

// Main function
async function main() {
  // Load environment variables from .env file
  config({ path: '.env.local' });

  // Validate environment variables
  const env = EnvSchema.parse(process.env);

  // Initialize Stripe and Supabase clients
  // Initialize Stripe with the API version
  const stripe = new Stripe(env.STRIPE_SECRET_KEY, {
    apiVersion: env.STRIPE_API_VERSION_V2 as Stripe.LatestApiVersion,
  });

  const supabase = createClient<Database>(
    env.NEXT_PUBLIC_SUPABASE_URL,
    env.SUPABASE_SERVICE_ROLE_KEY,
  );

  // Initialize service
  const service = new StripeBillingSubscriptionService(stripe, supabase);

  // Get confirmation from user
  if (!(await confirmSync())) {
    console.log(chalk.red('Operation canceled'));
    process.exit(0);
  }

  try {
    // Sync subscriptions
    logger.info(
      { script: 'sync-customer-subscriptions' },
      'Starting subscription sync...',
    );
    const result = await service.syncCustomerSubscriptions();

    // Show detailed messages
    if (result.messages.length > 0) {
      console.log(chalk.blue('\n📝 Detailed Messages:'));
      result.messages.forEach((msg) => {
        console.log(`  ${msg}`);
      });
    }

    console.log(chalk.green('\n✅ Subscription sync completed successfully!'));
    // Ensure the script exits after completion
    process.exit(0);
  } catch (error) {
    console.error(
      chalk.red(
        `\n❌ Error syncing subscriptions: ${error instanceof Error ? error.message : String(error)}`,
      ),
    );
    process.exit(1);
  }
}

// Run the main function
main().catch((error) => {
  console.error(
    chalk.red(
      `\n❌ Unhandled error: ${error instanceof Error ? error.message : String(error)}`,
    ),
  );
  process.exit(1);
});
