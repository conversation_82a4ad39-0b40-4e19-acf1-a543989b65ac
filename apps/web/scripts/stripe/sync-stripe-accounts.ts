#!/usr/bin/env node
import { SupabaseClient, createClient } from '@supabase/supabase-js';

import chalk from 'chalk';
import { config } from 'dotenv';
import { createInterface } from 'node:readline/promises';
import Stripe from 'stripe';
import { z } from 'zod';

import type { Database } from '../../lib/database.types';
import { listDbUsersHelper } from './helpers/list-db-users-helper';
import { listStripeAccountsHelper } from './helpers/list-stripe-accounts-helper';
import { logger } from './helpers/logger';
// Import DB product helper
import { upsertDbStripeAccountHelper } from './helpers/upsert-db-stripe-account-helper';
import { EnvSchema } from './schema/env.schema';

/**
 * Script-friendly version of StripeConnectedAccountsService
 * This is a simplified version that implements account syncing
 */
class StripeAccountsScriptService {
  constructor(
    private readonly stripe: Stripe,
    private readonly client: SupabaseClient,
  ) {}

  /**
   * Syncs <PERSON>e connected accounts with our database
   * - Creates database records for Stripe accounts that exist but aren't in our database
   */
  async syncAccounts(): Promise<{
    created: number;
    linked: number;
    failed: number;
    messages: string[];
  }> {
    const messages: string[] = [];
    const created = 0;
    let linked = 0;
    let failed = 0;

    // Set to track user IDs that have already been processed
    const processedUserIds = new Set<string>();

    // 1. Get all connected accounts from Stripe using the helper
    const stripeAccounts = await listStripeAccountsHelper(this.stripe);
    messages.push(`Found ${stripeAccounts.length} stripe accounts in Stripe`);

    // 2. Get all users for mapping using the helper
    const users = await listDbUsersHelper(this.client);

    // 3. Create a map of email to user ID for easy lookup
    const emailToUser = new Map();
    users.forEach((user) => {
      if (user.email) {
        emailToUser.set(user.email.toLowerCase(), user);
      }
    });

    // 4. Link Stripe accounts to our database
    for (const account of stripeAccounts) {
      try {
        if (account.contact_email) {
          const user = emailToUser.get(account.contact_email.toLowerCase());

          if (user) {
            // Skip if user already has a stripe account
            if (processedUserIds.has(user.id)) {
              messages.push(
                `User ${user.id} already has a stripe account, skipping link for Stripe account ${account.id}`,
              );
              continue;
            }

            // We found a matching user, link them
            // Upsert the stripe account record using helper
            const upsertSuccess = await upsertDbStripeAccountHelper(
              this.client,
              { userId: user.id, stripeAccount: account }, // Pass the full account object
            );

            // If upsert failed, log and skip
            if (!upsertSuccess) {
              messages.push(
                `Failed to upsert DB record for Stripe account ${account.id} linked to user ${user.id}. Skipping community link.`,
              );
              failed++;
              continue; // Move to the next account
            }

            // Upsert was successful (DB record upserted)
            messages.push(
              `Upserted DB record for Stripe account ${account.id} linked to user ${user.id}`,
            );
            linked++;
            processedUserIds.add(user.id);
          } else {
            messages.push(
              `No matching user found for Stripe account ${account.id} (${account.contact_email})`,
            );
          }
        } else {
          messages.push(
            `Stripe account ${account.id} has no email address, cannot link`,
          );
        }
      } catch (error) {
        messages.push(
          `Failed to link Stripe account ${account.id}: ${error instanceof Error ? error.message : String(error)}`,
        );
        failed++;
      }
    }

    return { created, linked, failed, messages };
  }
}

/**
 * This script syncs Stripe connected accounts with the database.
 * It handles the following scenarios:
 * 1. Connected accounts in Stripe but not in our DB (links them to users by email)
 * 2. Connected accounts in our DB but not in Stripe (flags them for manual recreation)
 * Only works in development mode.
 */

// Load environment variables from .env file
config({ path: '.env.local' });

// Ensure we're in development
if (process.env.NODE_ENV === 'production') {
  console.error(chalk.red('❌ This script cannot be run in production!'));
  process.exit(1);
}

const readline = createInterface({
  input: process.stdin,
  output: process.stdout,
});

// Check for -y flag in command line arguments
const hasYesFlag = process.argv.includes('-y');

/**
 * Asks for confirmation before proceeding
 */
async function confirmSync(): Promise<boolean> {
  // If -y flag is present, bypass confirmation
  if (hasYesFlag) {
    logger.info(
      {},
      chalk.yellow('Auto-confirmation enabled via command line argument (-y).'),
    );
    return true;
  }

  const answer = await readline.question(
    chalk.yellow(
      `Sync Stripe connected accounts with the database? This will link existing accounts and flag missing ones. [y/N] `,
    ),
  );
  return answer.toLowerCase() === 'y';
}

/**
 * Main function that runs the script
 */
async function main() {
  try {
    // Validate environment variables
    const env = EnvSchema.parse(process.env);

    // Initialize Stripe client
    logger.info({}, chalk.blue('🔍 Initializing Stripe client...'));
    const stripe = new Stripe(env.STRIPE_SECRET_KEY, {
      apiVersion: env.STRIPE_API_VERSION_V2 as Stripe.LatestApiVersion,
    });

    // Initialize Supabase client with service role key for admin access
    logger.info({}, chalk.blue('🔍 Initializing Supabase client...'));
    const supabase = createClient<Database>(
      env.NEXT_PUBLIC_SUPABASE_URL,
      env.SUPABASE_SERVICE_ROLE_KEY,
    );

    // Initialize our script-friendly version of StripeConnectedAccountsService
    const stripeConnectedAccountsService = new StripeAccountsScriptService(
      stripe,
      supabase,
    );

    // Confirm sync
    if (!(await confirmSync())) {
      logger.info({}, chalk.yellow('⏭️ Operation cancelled by user.'));
      return;
    }

    logger.info({}, chalk.bold.blue('\n🔁 Syncing Stripe accounts...\n'));

    // Perform the sync
    const { created, linked, failed, messages } =
      await stripeConnectedAccountsService.syncAccounts();

    // Display detailed logs
    logger.info({}, chalk.cyan('\n📝 Detailed Log:'));
    messages.forEach((msg) => logger.info({}, chalk.dim(`  ${msg}`)));

    // Display summary
    logger.info({}, chalk.bold.blue('\n📊 Summary:'));
    logger.info(
      {},
      chalk.green(
        `✅ Created ${chalk.bold(created.toString())} new Stripe accounts`,
      ),
    );
    logger.info(
      {},
      chalk.cyan(
        `⛓️ Linked ${chalk.bold(linked.toString())} existing Stripe accounts`,
      ),
    );
    if (failed > 0) {
      logger.info(
        {},
        chalk.red(
          `❌ Failed to process ${chalk.bold(failed.toString())} accounts`,
        ),
      );
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      logger.error(
        { error },
        chalk.red(
          '❌ Environment Error: Missing or invalid environment variables',
        ),
      );
      logger.error(
        { error },
        chalk.red(
          error.errors.map((e) => `${e.path}: ${e.message}`).join('\n'),
        ),
      );
    } else {
      logger.error({ error }, chalk.red('❌ Error:', error));
    }
    process.exit(1);
  } finally {
    readline.close();
  }
}

main().catch((error) => {
  logger.error({ error }, 'Error in main function');
  process.exit(1);
});
