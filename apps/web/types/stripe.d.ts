declare module 'stripe' {
  namespace Stripe {
    namespace Checkout {
      interface Session {
        /** The stripe account ID (replaces `customer`) */
        customer_account: string;
      }
    }

    namespace BillingPortal {
      interface SessionCreateParams {
        /** The stripe account ID for billing portal */
        customer_account?: string;
      }
    }

    interface SubscriptionCreateParams {
      /** The stripe account ID for subscriptions */
      customer_account?: string;
    }

    interface Customer {
      /** The stripe account ID */
      customer_account?: string;
    }

    interface Response<T> {
      data: T;
      id: string;
    }
  }

  interface Stripe {
    v2: {
      core: {
        accounts: {
          create(
            params: Record<string, unknown>,
          ): Promise<Stripe.Response<Record<string, unknown>>>;
          update(
            id: string,
            params: Record<string, unknown>,
          ): Promise<Stripe.Response<Record<string, unknown>>>;
          list(
            params?: Record<string, unknown>,
          ): Promise<Record<string, unknown>>;
        };
        accountLinks: {
          create(
            params: Record<string, unknown>,
          ): Promise<Stripe.Response<Record<string, unknown>>>;
        };
      };
    };
  }
}
