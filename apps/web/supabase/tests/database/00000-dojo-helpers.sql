CREATE SCHEMA if NOT EXISTS makerkit;

-- anon, authenticated, and service_role should have access to makerkit schema
GRANT usage ON schema makerkit TO anon,
authenticated,
service_role;

-- Don't allow public to execute any functions in the makerkit schema
ALTER DEFAULT PRIVILEGES IN schema makerkit
REVOKE
EXECUTE ON functions
FROM
  public;

-- Grant execute to anon, authenticated, and service_role for testing purposes
ALTER DEFAULT PRIVILEGES IN schema makerkit
GRANT
EXECUTE ON functions TO anon,
authenticated,
service_role;

CREATE OR REPLACE FUNCTION makerkit.set_identifier (identifier TEXT, user_email TEXT) returns TEXT security definer
SET
  search_path = auth,
  pg_temp AS $$
begin
 update auth.users set raw_user_meta_data = jsonb_build_object('test_identifier', identifier)
 where email = user_email;

 return identifier;

end;

$$ language plpgsql;

CREATE OR REPLACE FUNCTION makerkit.get_account_by_slug (account_slug TEXT) returns setof accounts AS $$
begin

    return query
      select
        *
      from
        accounts
      where
        slug = account_slug;

end;

$$ language plpgsql;

CREATE OR REPLACE FUNCTION makerkit.get_user_id_by_slug (account_slug TEXT) returns uuid AS $$

begin

    return
      (select
         id
       from
         accounts
       where
         slug = account_slug);

end;

$$ language plpgsql;

BEGIN;

SELECT
  plan (1);

SELECT
  is_empty (
    $$
  select
    *
  from
    makerkit.get_account_by_slug('test') $$,
    'get_account_by_slug should return an empty set when the account does not exist'
  );

SELECT
  *
FROM
  finish ();

ROLLBACK;
