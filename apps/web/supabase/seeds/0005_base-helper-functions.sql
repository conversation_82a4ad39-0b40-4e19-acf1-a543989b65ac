BEGIN;

-- Function to wrap content in the common JSON structure
CREATE OR REPLACE FUNCTION public.wrap_content_in_json (text_content TEXT) returns JSONB AS $$
BEGIN
  RETURN jsonb_build_object(
    'type', 'doc',
    'content', jsonb_build_array(
      jsonb_build_object(
        'type', 'paragraph',
        'content', jsonb_build_array(
          jsonb_build_object(
            'type', 'text',
            'text', text_content
          )
        )
      )
    )
  );
END;
$$ language plpgsql security definer;

-- Grant execute permission
GRANT
EXECUTE ON function public.wrap_content_in_json (TEXT) TO postgres,
authenticated,
service_role;

-- Helper function to find post id by title
CREATE OR REPLACE FUNCTION public.find_post_by_title (search_title TEXT) returns uuid AS $$
BEGIN
  RETURN (
    SELECT id 
    FROM public.community_forum_posts 
    WHERE title = search_title
    LIMIT 1
  );
END;
$$ language plpgsql security definer;

COMMIT;
