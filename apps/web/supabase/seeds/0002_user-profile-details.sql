-- Update personal accounts with user details
-- Note: Profile images will be uploaded to user_storage by the seed-images.ts script
UPDATE "public"."users"
SET
  user_details = '{"bio": "Martial arts legend and philosopher. The founder of <PERSON><PERSON>", "socials": [{"name": "x-twitter", "url": "https://twitter.com/brucelee"}, {"name": "facebook", "url": "https://facebook.com/brucelee"}, {"name": "instagram", "url": "https://instagram.com/brucelee"}]}'
WHERE
  id = '1f78ef8f-8b2d-44d7-9376-023f9a86ccb0';

UPDATE "public"."users"
SET
  user_details = '{"bio": "Legendary comedic actor known for physical comedy and his role in Beverly Hills Ninja.", "socials": [{"name": "x-twitter", "url": "https://twitter.com/chrisfarley"}, {"name": "facebook", "url": "https://facebook.com/chrisfarley"}]}'
WHERE
  id = '5c064f1b-78ee-4e1c-ac3b-e99aa97c99bf';

UPDATE "public"."users"
SET
  user_details = '{"bio": "Thai martial artist and actor specializing in Muay Thai and known for incredible stunts without wires.", "socials": [{"name": "instagram", "url": "https://instagram.com/tonyjaaofficial"}, {"name": "facebook", "url": "https://facebook.com/tonyjaaofficial"}]}'
WHERE
  id = '31a03e74-1639-45b6-bfa7-77447f1a4762';

UPDATE "public"."users"
SET
  user_details = '{"bio": "Action star with background in martial arts and diving. Known for practical fight scenes and intense roles.", "socials": [{"name": "instagram", "url": "https://instagram.com/jasonstatham"}, {"name": "facebook", "url": "https://facebook.com/jasonstatham"}]}'
WHERE
  id = '1a52fbd1-cf26-4507-9808-5097a088cb93';

UPDATE "public"."users"
SET
  user_details = '{"bio": "Hong Kong martial artist, actor, stuntman and filmmaker known for his acrobatic fighting style.", "socials": [{"name": "x-twitter", "url": "https://twitter.com/eyeofjackiechan"}, {"name": "facebook", "url": "https://facebook.com/jackie"}, {"name": "instagram", "url": "https://instagram.com/jackiechan"}]}'
WHERE
  id = 'c807e002-d92f-458f-8525-19e9b45b12a0';

UPDATE "public"."users"
SET
  user_details = '{"bio": "Wushu champion and actor known for his lightning-fast martial arts skills.", "socials": [{"name": "facebook", "url": "https://facebook.com/jetli"}, {"name": "instagram", "url": "https://instagram.com/jetli"}]}'
WHERE
  id = '6b83d656-e4ab-48e3-a062-c0c54a427368';

UPDATE "public"."users"
SET
  user_details = '{"bio": "Actor, martial artist and action director known for the Ip Man series and Wing Chun mastery.", "socials": [{"name": "instagram", "url": "https://instagram.com/donnieyenofficial"}, {"name": "facebook", "url": "https://facebook.com/donnieyen"}]}'
WHERE
  id = '392bcae5-479a-4b85-90ff-f1af746f255e';

UPDATE "public"."users"
SET
  user_details = '{"bio": "Award-winning actress and martial artist known for her elegance and precision.", "socials": [{"name": "instagram", "url": "https://instagram.com/michelleyeoh_official"}, {"name": "x-twitter", "url": "https://twitter.com/michelleyeoh_official"}]}'
WHERE
  id = '61c12650-82d0-4cbf-bff7-566e9e9a3f24';

UPDATE "public"."users"
SET
  user_details = '{"bio": "Martial artist, actor, and internet meme legend. Master of several martial arts disciplines.", "socials": [{"name": "x-twitter", "url": "https://twitter.com/chucknorris"}, {"name": "facebook", "url": "https://facebook.com/officialchucknorrispage"}]}'
WHERE
  id = '5870c4b1-3881-4399-ba9e-ad1ac0c958c7';

UPDATE "public"."users"
SET
  user_details = '{"bio": "Known as the \"Muscles from Brussels\". Actor and martial artist famous for his splits and kickboxing.", "socials": [{"name": "instagram", "url": "https://instagram.com/jcvd"}, {"name": "facebook", "url": "https://facebook.com/jcvd"}]}'
WHERE
  id = 'b73eb03e-fb7a-424d-84ff-18e2791ce0b4';
