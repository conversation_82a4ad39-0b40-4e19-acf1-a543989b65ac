/* -------------------------------------------------------
* Section: Community Storage - Lessons
* -------------------------------------------------------
*/
-- Function to validate if a path is a lesson storage path
CREATE OR REPLACE FUNCTION kit.is_lesson_storage_path (storage_path TEXT) returns BOOLEAN language sql security definer
SET
  search_path = '' AS $$
  SELECT 
    (storage.foldername(storage_path))[2] = 'courses' 
    AND array_length(storage.foldername(storage_path), 1) >= 4;
$$;

GRANT
EXECUTE ON function kit.is_lesson_storage_path (TEXT) TO authenticated,
service_role;

-- Function to check if user has permission-based access to lesson in storage
CREATE OR REPLACE FUNCTION kit.has_permission_on_lesson_storage (storage_path TEXT, permission_name TEXT) returns BOOLEAN language plpgsql security definer
SET
  search_path = '' AS $$
DECLARE
  v_community_id UUID;
  v_course_id UUID;
  v_lesson_id UUID;
BEGIN
  -- Extract IDs using storage.foldername
  v_community_id := (storage.foldername(storage_path))[1]::UUID;
  v_course_id := (storage.foldername(storage_path))[3]::UUID;
  v_lesson_id := (storage.foldername(storage_path))[4]::UUID;
  
  -- Special case for lesson creation - check course exists and user has permission on course
  IF permission_name = 'community.courses.lessons.create' THEN
    RETURN EXISTS (
      SELECT 1 
      FROM public.community_courses c
      WHERE c.id = v_course_id
      AND c.community_id = v_community_id
      AND public.has_community_permission(
        auth.uid(),
        c.community_id,
        permission_name::public.community_permissions
      )
    );
  END IF;
  
  -- For other operations, check if lesson exists and user has permission
  RETURN EXISTS (
    SELECT 1 
    FROM public.community_course_lessons l
    JOIN public.community_courses c ON c.id = l.course_id
    WHERE l.id = v_lesson_id
    AND c.id = v_course_id
    AND public.has_community_permission(
      auth.uid(),
      c.community_id,
      permission_name::public.community_permissions
    )
    AND c.community_id = v_community_id
  );
END;
$$;

GRANT
EXECUTE ON function kit.has_permission_on_lesson_storage (TEXT, TEXT) TO authenticated,
service_role;

-- Lesson read policy (SELECT) - authenticated users with role on the community
CREATE POLICY "community_lessons_read" ON storage.objects FOR
SELECT
  TO authenticated USING (
    bucket_id = 'community_storage'
    AND kit.is_lesson_storage_path (name)
    AND public.has_role_on_community (kit.get_community_id_from_storage_path (name))
  );

-- Lesson write policy (INSERT) - authenticated users with lesson create permission
CREATE POLICY "community_lessons_insert" ON storage.objects FOR insert TO authenticated
WITH
  CHECK (
    bucket_id = 'community_storage'
    AND kit.is_lesson_storage_path (name)
    AND kit.has_permission_on_lesson_storage (name, 'community.courses.lessons.create')
  );

-- Lesson update policy (UPDATE) - authenticated users with lesson update permission
CREATE POLICY "community_lessons_update" ON storage.objects
FOR UPDATE
  TO authenticated USING (
    bucket_id = 'community_storage'
    AND kit.is_lesson_storage_path (name)
    AND kit.has_permission_on_lesson_storage (name, 'community.courses.lessons.update')
  )
WITH
  CHECK (
    bucket_id = 'community_storage'
    AND kit.is_lesson_storage_path (name)
    AND kit.has_permission_on_lesson_storage (name, 'community.courses.lessons.update')
  );

-- Lesson delete policy (DELETE) - authenticated users with lesson delete permission
CREATE POLICY "community_lessons_delete" ON storage.objects FOR delete TO authenticated USING (
  bucket_id = 'community_storage'
  AND kit.is_lesson_storage_path (name)
  AND kit.has_permission_on_lesson_storage (name, 'community.courses.lessons.delete')
);

-- Cleanup function
-- Function to delete lesson storage objects when lesson is deleted
CREATE OR REPLACE FUNCTION public.delete_community_lesson_storage_objects () returns trigger language plpgsql security invoker
SET
  search_path = '' AS $$
DECLARE
    community_id UUID;
BEGIN
    -- Get the community_id from the course
    SELECT c.community_id INTO community_id
    FROM public.community_courses c
    WHERE c.id = OLD.course_id;

    -- Delete all objects in the lesson's directory using the exact path pattern
    -- Pattern: /<community_id>/courses/<course_id>/<lesson_id>/* 
    DELETE FROM storage.objects
    WHERE bucket_id = 'community_storage'
    AND name LIKE community_id::TEXT || '/courses/' || OLD.course_id::TEXT || '/' || OLD.id::TEXT || '/%';

    RETURN OLD;
END;
$$;

-- Grant execute permissions for the trigger function
GRANT
EXECUTE ON function public.delete_community_lesson_storage_objects () TO postgres,
authenticated;

-- Create trigger to delete lesson storage objects when lesson is deleted
CREATE TRIGGER delete_community_lesson_storage_objects
AFTER delete ON public.community_course_lessons FOR each ROW
EXECUTE function public.delete_community_lesson_storage_objects ();
