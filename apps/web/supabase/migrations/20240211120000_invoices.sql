/*
 * -------------------------------------------------------
 * Section: Invoices
 * Create the schema for the Invoices. 
 * Invoices are one-time purchases for an community or other digital one-time products
 * -------------------------------------------------------
 */
/*
* Payment Status
- Create the payment status for the Dojo. These statuses are used to manage the status of the payments
*/
CREATE TYPE public.payment_status AS ENUM('pending', 'succeeded', 'failed');

/*
* Purchase type
- Enum to track what this invoice belongs to a community, member or user.
- community_product is a invoice of a user to a community for a product
- platform_product is a invoice of a user to the platform for a product
*/
ALTER TYPE public.purchase_type
ADD value 'community_product';

ALTER TYPE public.purchase_type
ADD value 'platform_product';

CREATE TABLE IF NOT EXISTS public.invoices (
  id TEXT NOT NULL PRIMARY KEY,
  user_id uuid REFERENCES public.users (id) ON DELETE CASCADE,
  community_id uuid REFERENCES public.communities (id) ON DELETE CASCADE,
  status public.payment_status NOT NULL,
  purchase_type public.purchase_type NOT NULL,
  total_amount NUMERIC NOT NULL,
  currency VARCHAR(3) NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT current_timestamp,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT current_timestamp
);

comment ON TABLE public.invoices IS 'The one-time invoices for a user';

comment ON COLUMN public.invoices.id IS 'The invoice ID for the invoice from the billing provider';

comment ON COLUMN public.invoices.user_id IS 'The user the invoice is for';

comment ON COLUMN public.invoices.community_id IS 'The community the invoice is for';

comment ON COLUMN public.invoices.status IS 'The status of the invoice';

comment ON COLUMN public.invoices.purchase_type IS 'The type of purchase for the invoice';

comment ON COLUMN public.invoices.total_amount IS 'The total amount for the invoice';

comment ON COLUMN public.invoices.currency IS 'The currency for the invoice';

-- Indexes
-- Indexes on the invoices table
CREATE INDEX idx_invoices_user_id ON public.invoices (user_id);

-- Add latest_invoice_id to community_memberships table
ALTER TABLE public.community_memberships
ADD COLUMN latest_invoice_id TEXT REFERENCES public.invoices (id) ON DELETE CASCADE;

comment ON COLUMN public.community_memberships.latest_invoice_id IS 'The latest invoice the membership is for';

-- Revoke all access to invoices table for authenticated users and service_role
REVOKE ALL ON public.invoices
FROM
  authenticated,
  service_role;

-- Open up access to invoices table for authenticated users and service_role
GRANT
SELECT
  ON TABLE public.invoices TO authenticated;

GRANT
SELECT
,
  insert,
UPDATE,
delete ON TABLE public.invoices TO service_role;

-- RLS
ALTER TABLE public.invoices enable ROW level security;

-- SELECT(invoices)
-- Users can read invoices on a user they are a member of or the user is their own
CREATE POLICY "policy_invoices_select_self_or_member" ON public.invoices FOR
SELECT
  TO authenticated USING (
    (
      user_id = (
        SELECT
          auth.uid ()
      )
    )
    OR (has_role_on_community (community_id))
  );

/**
 * -------------------------------------------------------
 * Section: Invoice Items
 * Create the schema for the invoice items. Invoice items are the items in an invoice.
 * -------------------------------------------------------
 */
CREATE TABLE IF NOT EXISTS public.invoice_items (
  id TEXT NOT NULL PRIMARY KEY,
  invoice_id TEXT REFERENCES public.invoices (id) ON DELETE CASCADE NOT NULL,
  product_id TEXT NOT NULL,
  variant_id TEXT NOT NULL,
  price_amount NUMERIC,
  quantity INTEGER NOT NULL DEFAULT 1,
  created_at TIMESTAMPTZ NOT NULL DEFAULT current_timestamp,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT current_timestamp,
  UNIQUE (invoice_id, product_id, variant_id)
);

comment ON TABLE public.invoice_items IS 'The items in an invoice';

comment ON COLUMN public.invoice_items.id IS 'The ID for the item from billing provider';

comment ON COLUMN public.invoice_items.invoice_id IS 'The invoice the item is for';

comment ON COLUMN public.invoice_items.product_id IS 'The product ID for the item';

comment ON COLUMN public.invoice_items.variant_id IS 'The variant ID for the item';

comment ON COLUMN public.invoice_items.price_amount IS 'The price amount for the item';

comment ON COLUMN public.invoice_items.quantity IS 'The quantity of the item';

comment ON COLUMN public.invoice_items.created_at IS 'The creation date of the item';

comment ON COLUMN public.invoice_items.updated_at IS 'The last update date of the item';

-- Revoke all access to invoice_items table for authenticated users and service_role
REVOKE ALL ON public.invoice_items
FROM
  authenticated,
  service_role;

-- Open up relevant access to invoice_items table for authenticated users and service_role
GRANT
SELECT
  ON TABLE public.invoice_items TO authenticated,
  service_role;

GRANT insert,
UPDATE,
delete ON TABLE public.invoice_items TO service_role;

-- Indexes on the invoice_items table
CREATE INDEX idx_invoice_items_invoice_id ON public.invoice_items (invoice_id);

-- RLS
ALTER TABLE public.invoice_items enable ROW level security;

-- SELECT(invoice_items):
-- Users can read invoice items on an invoice they are a member of
CREATE POLICY "policy_invoice_items_select_self_or_member" ON public.invoice_items FOR
SELECT
  TO authenticated USING (
    EXISTS (
      SELECT
        1
      FROM
        public.invoices
      WHERE
        id = invoice_id
        AND (
          user_id = (
            SELECT
              auth.uid ()
          )
          OR has_role_on_community (community_id)
        )
    )
  );

-- Function "public.upsert_invoice"
-- Insert or update an invoice and its items when receiving a webhook from the billing provider
CREATE OR REPLACE FUNCTION public.upsert_invoice (
  p_target_user_id uuid,
  p_target_community_id uuid,
  p_target_invoice_id TEXT,
  p_status public.payment_status,
  p_purchase_type public.purchase_type,
  p_total_amount NUMERIC,
  p_currency VARCHAR(3),
  p_line_items JSONB
) returns public.invoices
SET
  search_path = '' AS $$
DECLARE
    v_new_invoice public.invoices;
BEGIN
    INSERT INTO public.invoices(
        id,
        user_id,
        community_id,
        status,
        purchase_type,
        total_amount,
        currency)
    VALUES (
        p_target_invoice_id,
        p_target_user_id,
        p_target_community_id,
        p_status,
        p_purchase_type,
        p_total_amount,
        p_currency)
    ON CONFLICT (
        id
    )
    DO UPDATE SET
        status = excluded.status,
        purchase_type = excluded.purchase_type,
        total_amount = excluded.total_amount,
        currency = excluded.currency
    RETURNING
        * INTO v_new_invoice;

    -- Upsert invoice items and delete ones that are not in the line_items array
    WITH item_data AS (
        SELECT
            (line_item ->> 'id')::VARCHAR as line_item_id,
            (line_item ->> 'product_id')::VARCHAR as prod_id,
            (line_item ->> 'variant_id')::VARCHAR as var_id,
            (line_item ->> 'price_amount')::NUMERIC as price_amt,
            (line_item ->> 'quantity')::INTEGER as qty
        FROM
            jsonb_array_elements(p_line_items) as line_item
    ),
    line_item_ids AS (
        SELECT line_item_id FROM item_data
    ),
    deleted_items AS (
        DELETE FROM
            public.invoice_items
        WHERE
            public.invoice_items.invoice_id = v_new_invoice.id
            AND public.invoice_items.id NOT IN (
                SELECT
                    line_item_id
                FROM
                    line_item_ids
            )
        RETURNING
            *
    )
    INSERT INTO public.invoice_items(
        id,
        invoice_id,
        product_id,
        variant_id,
        price_amount,
        quantity)
    SELECT
        line_item_id,
        v_new_invoice.id,
        prod_id,
        var_id,
        price_amt,
        qty
    FROM
        item_data
    ON CONFLICT (
        id
    )
    DO UPDATE SET
            price_amount = excluded.price_amount,
            product_id = excluded.product_id,
            variant_id = excluded.variant_id,
            quantity = excluded.quantity;

    RETURN v_new_invoice;

END;

$$ language plpgsql;

GRANT
EXECUTE ON function public.upsert_invoice (
  uuid,
  uuid,
  TEXT,
  public.payment_status,
  public.purchase_type,
  NUMERIC,
  VARCHAR,
  JSONB
) TO service_role;
