/*
 * -------------------------------------------------------
 * Section: Update Community Policy
 * Update the community policy to allow the owners to update the community
 * -------------------------------------------------------
 */
-- RLS on the accounts table
-- UPDATE(user):
-- Community owners can update their communities
DROP POLICY if EXISTS "policy_communities_update_owner" ON public.communities;

CREATE POLICY "policy_communities_update_owner" ON public.communities
FOR UPDATE
  TO authenticated USING (
    (
      SELECT
        auth.uid ()
    ) = primary_owner_user_id
    OR has_role_on_community (id, 'owner')
  )
WITH
  CHECK (
    (
      SELECT
        auth.uid ()
    ) = primary_owner_user_id
    OR has_role_on_community (id, 'owner')
  );
