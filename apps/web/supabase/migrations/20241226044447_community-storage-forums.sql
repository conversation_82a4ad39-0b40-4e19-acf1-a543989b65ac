/* -------------------------------------------------------
* Section: Community Storage - Forums
* -------------------------------------------------------
*/
-- Function to check if user has read access to forum post in storage
CREATE OR REPLACE FUNCTION kit.has_read_access_to_forum_storage (storage_path TEXT) returns BOOLEAN language sql security definer
SET
  search_path = '' AS $$
  SELECT public.has_role_on_community(kit.get_community_id_from_storage_path(storage_path));
$$;

GRANT
EXECUTE ON function kit.has_read_access_to_forum_storage (TEXT) TO anon,
authenticated,
service_role;

-- Function to check if user has insert access to forum post in storage
CREATE OR REPLACE FUNCTION kit.has_insert_access_to_forum_storage (storage_path TEXT) returns BOOLEAN language sql security definer
SET
  search_path = '' AS $$
  SELECT EXISTS (
    SELECT 1 FROM public.community_forum_posts p
    WHERE p.id = (storage.foldername(storage_path))[3]::UUID
    AND p.created_by_user_id = auth.uid()
    AND p.community_id = kit.get_community_id_from_storage_path(storage_path)
  );
$$;

GRANT
EXECUTE ON function kit.has_insert_access_to_forum_storage (TEXT) TO authenticated,
service_role;

-- Function to check if user has update/delete access to forum post in storage
CREATE OR REPLACE FUNCTION kit.has_modify_access_to_forum_storage (storage_path TEXT, permission_name TEXT) returns BOOLEAN language sql security definer
SET
  search_path = '' AS $$
  SELECT EXISTS (
    SELECT 1 FROM public.community_forum_posts p
    WHERE p.id = (storage.foldername(storage_path))[3]::UUID
    AND (
      -- Either the user is the creator of the post
      p.created_by_user_id = auth.uid()
      -- Or the user has the permission to modify posts in this community
      OR public.has_community_permission(
        auth.uid(),
        p.community_id,
        permission_name::public.community_permissions
      )
    )
    AND p.community_id = kit.get_community_id_from_storage_path(storage_path)
  );
$$;

GRANT
EXECUTE ON function kit.has_modify_access_to_forum_storage (TEXT, TEXT) TO authenticated,
service_role;

-- Forum read policy (SELECT) - authenticated users with role on the community
CREATE POLICY "policy_community_storage_select_forums_member" ON storage.objects FOR
SELECT
  TO authenticated USING (
    bucket_id = 'community_storage'
    AND (storage.foldername (name)) [2] = 'forums'
    AND kit.has_read_access_to_forum_storage (name)
  );

-- Forum write policy (INSERT) - authenticated users who can create posts
CREATE POLICY "policy_community_storage_insert_forums_author" ON storage.objects FOR insert TO authenticated
WITH
  CHECK (
    bucket_id = 'community_storage'
    AND (storage.foldername (name)) [2] = 'forums'
    AND kit.has_insert_access_to_forum_storage (name)
  );

-- Forum update policy (UPDATE) - limited to post creator or users with update permission
CREATE POLICY "policy_community_storage_update_forums_author_or_can_update" ON storage.objects
FOR UPDATE
  TO authenticated USING (
    bucket_id = 'community_storage'
    AND (storage.foldername (name)) [2] = 'forums'
    AND kit.has_modify_access_to_forum_storage (name, 'community.forums.post.update')
  )
WITH
  CHECK (
    bucket_id = 'community_storage'
    AND (storage.foldername (name)) [2] = 'forums'
    AND kit.has_modify_access_to_forum_storage (name, 'community.forums.post.update')
  );

-- Forum delete policy (DELETE) - limited to post creator or users with delete permission
CREATE POLICY "policy_community_storage_delete_forums_author_or_can_delete" ON storage.objects FOR delete TO authenticated USING (
  bucket_id = 'community_storage'
  AND (storage.foldername (name)) [2] = 'forums'
  AND kit.has_modify_access_to_forum_storage (name, 'community.forums.post.delete')
);

-- Cleanup functions
-- Function to delete post storage objects when post is deleted
CREATE OR REPLACE FUNCTION public.delete_community_forum_post_storage_objects () returns trigger language plpgsql security invoker
SET
  search_path = '' AS $$
BEGIN
    -- Delete all objects in the post's directory using the exact path pattern
    -- Pattern: /<community_id>/forums/<post_id>/* - using community_id and post_id match
    DELETE FROM storage.objects
    WHERE bucket_id = 'community_storage'
    AND name LIKE OLD.community_id::TEXT || '/forums/' || OLD.id::TEXT || '/%';

    RETURN OLD;
END;
$$;

-- Grant execute permissions for the trigger function
GRANT
EXECUTE ON function public.delete_community_forum_post_storage_objects () TO postgres,
authenticated;

-- Create trigger to delete post storage objects when post is deleted
CREATE TRIGGER delete_community_forum_post_storage_objects
AFTER delete ON public.community_forum_posts FOR each ROW
EXECUTE function public.delete_community_forum_post_storage_objects ();

-- Function to delete comment storage objects when comment is deleted
CREATE OR REPLACE FUNCTION public.delete_community_forum_comment_storage_objects () returns trigger language plpgsql security invoker
SET
  search_path = '' AS $$
DECLARE
    v_community_id UUID;
BEGIN
    -- Get community ID from the post
    SELECT community_id INTO v_community_id
    FROM public.community_forum_posts
    WHERE id = OLD.post_id;

    IF v_community_id IS NULL THEN
        RETURN OLD;
    END IF;

    -- Delete all objects matching the comment pattern
    -- Pattern: /<community_id>/forums/<post_id>/<comment_id>-* 
    DELETE FROM storage.objects
    WHERE bucket_id = 'community_storage'
    AND name LIKE v_community_id::TEXT || '/forums/' || OLD.post_id::TEXT || '/' || OLD.id::TEXT || '-%';

    RETURN OLD;
END;
$$;

-- Grant execute permissions for the trigger function
GRANT
EXECUTE ON function public.delete_community_forum_comment_storage_objects () TO postgres,
authenticated;

-- Create trigger to delete comment storage objects when comment is deleted
CREATE TRIGGER delete_community_forum_comment_storage_objects
AFTER delete ON public.community_forum_comments FOR each ROW
EXECUTE function public.delete_community_forum_comment_storage_objects ();
