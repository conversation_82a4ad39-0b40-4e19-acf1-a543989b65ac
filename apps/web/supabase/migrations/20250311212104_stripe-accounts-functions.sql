/* -------------------------------------------------------
* Section: Stripe Accounts v2 Operations SQL Functions
* This migration creates database functions for Stripe Accounts v2 operations
* -------------------------------------------------------
*/
-- Function to create a stripe account v2
-- This function is used to create a stripe account in the database, linked to a user
CREATE OR REPLACE FUNCTION public.create_stripe_account (
  p_stripe_account_id TEXT,
  p_user_id uuid,
  p_country_iso_code_1 TEXT DEFAULT NULL,
  p_applied_configurations TEXT[] DEFAULT '{}',
  p_contact_email TEXT DEFAULT NULL,
  p_display_name TEXT DEFAULT NULL,
  p_dashboard TEXT DEFAULT NULL,
  p_configuration JSONB DEFAULT '{}'::JSON<PERSON>,
  p_capabilities JSONB DEFAULT '{}'::JSONB,
  p_requirements JSONB DEFAULT '{}'::JSONB,
  p_defaults JSONB DEFAULT '{}'::JSONB,
  p_identity JSONB DEFAULT '{}'::JSONB,
  p_metadata JSONB DEFAULT '{}'::JSONB
) returns TEXT language plpgsql security definer
SET
  search_path = '' AS $$
DECLARE
  v_stripe_account_id TEXT;
  v_country_id UUID;
BEGIN
  -- Get the country ID from the country ISO code if provided
  IF p_country_iso_code_1 IS NOT NULL THEN
    SELECT id INTO v_country_id
    FROM public.countries
    WHERE iso_code_1 = UPPER(p_country_iso_code_1);

    IF v_country_id IS NULL THEN
      RAISE EXCEPTION 'Country with ISO code % not found', p_country_iso_code_1;
    END IF;
  END IF;

  INSERT INTO public.stripe_accounts (
    id,
    user_id, 
    country_id,
    applied_configurations,
    contact_email,
    display_name,
    dashboard,
    configuration,
    capabilities,
    requirements,
    defaults,
    identity,
    metadata
  )
  VALUES (
    p_stripe_account_id,
    p_user_id,
    v_country_id,
    p_applied_configurations,
    p_contact_email,
    p_display_name,
    p_dashboard,
    p_configuration,
    p_capabilities,
    p_requirements,
    p_defaults,
    p_identity,
    p_metadata
  )
  RETURNING id INTO v_stripe_account_id;

  RETURN v_stripe_account_id;
END;
$$;

GRANT
EXECUTE ON function public.create_stripe_account (
  TEXT,
  uuid,
  TEXT,
  TEXT[],
  TEXT,
  TEXT,
  TEXT,
  JSONB,
  JSONB,
  JSONB,
  JSONB,
  JSONB,
  JSONB
) TO authenticated,
service_role;

-- Function to update a stripe account v2
-- This function is used to update a stripe account in the database
-- It is called when a stripe account is updated via webhook
CREATE OR REPLACE FUNCTION public.update_stripe_account (
  p_stripe_account_id TEXT,
  p_applied_configurations TEXT[] DEFAULT NULL,
  p_contact_email TEXT DEFAULT NULL,
  p_display_name TEXT DEFAULT NULL,
  p_dashboard TEXT DEFAULT NULL,
  p_configuration JSONB DEFAULT NULL,
  p_capabilities JSONB DEFAULT NULL,
  p_requirements JSONB DEFAULT NULL,
  p_defaults JSONB DEFAULT NULL,
  p_identity JSONB DEFAULT NULL,
  p_metadata JSONB DEFAULT NULL
) returns TEXT language plpgsql security definer
SET
  search_path = '' AS $$
DECLARE
  v_stripe_account_id TEXT;
BEGIN
  
  -- Update the stripe account with only non-null parameters
  UPDATE public.stripe_accounts
  SET 
    applied_configurations = COALESCE(p_applied_configurations, applied_configurations),
    contact_email = COALESCE(p_contact_email, contact_email),
    display_name = COALESCE(p_display_name, display_name),
    dashboard = COALESCE(p_dashboard, dashboard),
    configuration = COALESCE(p_configuration, configuration),
    capabilities = COALESCE(p_capabilities, capabilities),
    requirements = COALESCE(p_requirements, requirements),
    defaults = COALESCE(p_defaults, defaults),
    identity = COALESCE(p_identity, identity),
    metadata = COALESCE(p_metadata, metadata),
    updated_at = CURRENT_TIMESTAMP
  WHERE public.stripe_accounts.id = p_stripe_account_id
  RETURNING id INTO v_stripe_account_id;
  
  RETURN v_stripe_account_id;
END;
$$;

GRANT
EXECUTE ON function public.update_stripe_account (
  TEXT,
  TEXT[],
  TEXT,
  TEXT,
  TEXT,
  JSONB,
  JSONB,
  JSONB,
  JSONB,
  JSONB,
  JSONB
) TO authenticated,
service_role;

-- Function to get the Stripe Account status for a community
-- This function returns the stripe account details linked to the community owner
CREATE OR REPLACE FUNCTION public.get_community_stripe_account_status (p_community_id uuid) returns TABLE (
  id TEXT,
  country_id uuid,
  applied_configurations TEXT[],
  contact_email TEXT,
  display_name TEXT,
  dashboard TEXT,
  configuration JSONB,
  capabilities JSONB,
  requirements JSONB,
  defaults JSONB,
  identity JSONB,
  metadata JSONB
) language plpgsql security definer
SET
  search_path = '' AS $$
BEGIN

  RETURN QUERY
  SELECT
    sa.id,
    sa.country_id,
    sa.applied_configurations,
    sa.contact_email,
    sa.display_name,
    sa.dashboard,
    sa.configuration,
    sa.capabilities,
    sa.requirements,
    sa.defaults,
    sa.identity,
    sa.metadata
  FROM public.communities c
  LEFT JOIN public.stripe_accounts sa ON c.primary_owner_user_id = sa.user_id
  WHERE c.id = p_community_id
  LIMIT 1;

END;
$$;

GRANT
EXECUTE ON function public.get_community_stripe_account_status (uuid) TO authenticated,
service_role;

-- Function to get stripe account by user ID
-- Useful for getting a user's stripe account directly
CREATE OR REPLACE FUNCTION public.get_user_stripe_account (p_user_id uuid) returns TABLE (
  id TEXT,
  country_id uuid,
  applied_configurations TEXT[],
  contact_email TEXT,
  display_name TEXT,
  dashboard TEXT,
  configuration JSONB,
  capabilities JSONB,
  requirements JSONB,
  defaults JSONB,
  identity JSONB,
  metadata JSONB,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ
) language plpgsql security definer
SET
  search_path = '' AS $$
BEGIN

  RETURN QUERY
  SELECT
    sa.id,
    sa.country_id,
    sa.applied_configurations,
    sa.contact_email,
    sa.display_name,
    sa.dashboard,
    sa.configuration,
    sa.capabilities,
    sa.requirements,
    sa.defaults,
    sa.identity,
    sa.metadata,
    sa.created_at,
    sa.updated_at
  FROM public.stripe_accounts sa
  WHERE sa.user_id = p_user_id
  LIMIT 1;

END;
$$;

GRANT
EXECUTE ON function public.get_user_stripe_account (uuid) TO authenticated,
service_role;
