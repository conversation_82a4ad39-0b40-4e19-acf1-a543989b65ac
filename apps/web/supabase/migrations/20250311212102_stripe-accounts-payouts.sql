/* -------------------------------------------------------
* Section: Stripe Account Payouts
* Create the tables and functions to support Stripe Connect integration
* -------------------------------------------------------
*/
-- Create stripe_account_payouts table for Stripe Connect integration
-- Description: This migration creates the stripe_account_payouts table to track payouts for stripe accounts.
-- Add community_permissions to support Stripe Connect payouts
BEGIN;

ALTER TYPE public.community_permissions
ADD value 'community.stripe_account.payouts.update';

ALTER TYPE public.community_permissions
ADD value 'community.stripe_account.payouts.delete';

ALTER TYPE public.community_permissions
ADD value 'community.stripe_account.payouts.request';

COMMIT;

-- ROLES
BEGIN;

INSERT INTO
  public.community_role_permissions (role, permission)
VALUES
  (
    'owner',
    'community.stripe_account.payouts.update'
  ),
  (
    'owner',
    'community.stripe_account.payouts.delete'
  ),
  (
    'owner',
    'community.stripe_account.payouts.request'
  );

COMMIT;

-- Create enum type for payout status
CREATE TYPE public.stripe_account_payout_status_type AS ENUM('pending', 'paid', 'failed', 'canceled');

-- Create stripe_account_payouts table
CREATE TABLE IF NOT EXISTS public.stripe_account_payouts (
  id TEXT NOT NULL PRIMARY KEY,
  stripe_account_id TEXT NOT NULL REFERENCES public.stripe_accounts (id) ON DELETE CASCADE,
  stripe_payout_id TEXT NOT NULL,
  amount INTEGER NOT NULL,
  currency TEXT NOT NULL,
  status stripe_account_payout_status_type NOT NULL,
  arrival_date TIMESTAMPTZ,
  payout_type TEXT,
  method TEXT,
  destination TEXT,
  failure_message TEXT,
  failure_code TEXT,
  created_at TIMESTAMPTZ DEFAULT current_timestamp NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT current_timestamp NOT NULL
);

-- Add comments for documentation
comment ON TABLE public.stripe_account_payouts IS 'Tracks payouts for Stripe Connect accounts';

comment ON COLUMN public.stripe_account_payouts.id IS 'Unique identifier for the payout record';

comment ON COLUMN public.stripe_account_payouts.stripe_account_id IS 'Reference to the stripe account receiving the payout';

comment ON COLUMN public.stripe_account_payouts.stripe_payout_id IS 'The Stripe payout ID';

comment ON COLUMN public.stripe_account_payouts.amount IS 'The payout amount in smallest currency unit (e.g. cents)';

comment ON COLUMN public.stripe_account_payouts.currency IS 'ISO currency code for the payout';

comment ON COLUMN public.stripe_account_payouts.status IS 'The status of the payout (pending, paid, failed, canceled)';

comment ON COLUMN public.stripe_account_payouts.arrival_date IS 'When the payout is expected to arrive in the bank account';

comment ON COLUMN public.stripe_account_payouts.payout_type IS 'The type of payout';

comment ON COLUMN public.stripe_account_payouts.method IS 'The payout method (e.g. standard, instant)';

comment ON COLUMN public.stripe_account_payouts.destination IS 'The destination for the payout (e.g. bank account)';

comment ON COLUMN public.stripe_account_payouts.failure_message IS 'Message explaining reason for failure, if applicable';

comment ON COLUMN public.stripe_account_payouts.failure_code IS 'Error code for failed payouts, if applicable';

-- Create unique constraint to ensure each Stripe payout ID is only used once
ALTER TABLE public.stripe_account_payouts
ADD CONSTRAINT stripe_account_payouts_stripe_payout_id_unique UNIQUE (stripe_payout_id);

-- Set up timestamps trigger
CREATE TRIGGER set_stripe_account_payouts_timestamps before
UPDATE ON public.stripe_account_payouts FOR each ROW
EXECUTE function public.trigger_set_timestamps ();

-- Create indexes for frequently queried columns
CREATE INDEX idx_stripe_account_payouts_stripe_account_id ON public.stripe_account_payouts (stripe_account_id);

CREATE INDEX idx_stripe_account_payouts_status ON public.stripe_account_payouts (status);

CREATE INDEX idx_stripe_account_payouts_arrival_date ON public.stripe_account_payouts (arrival_date);

CREATE INDEX idx_stripe_account_payouts_created_at ON public.stripe_account_payouts (created_at);

-- RLS policies for Payouts
ALTER TABLE public.stripe_account_payouts enable ROW level security;

ALTER TABLE public.stripe_account_payouts force ROW level security;

-- Grant base permissions
GRANT
SELECT
  ON public.stripe_account_payouts TO authenticated;

GRANT
SELECT
,
  insert,
UPDATE,
delete ON public.stripe_account_payouts TO service_role;

-- Create new policy: Users can read payouts linked to their own stripe account
CREATE POLICY "policy_stripe_account_payouts_select_user" ON public.stripe_account_payouts FOR
SELECT
  TO authenticated USING (
    EXISTS (
      SELECT
        1
      FROM
        public.stripe_accounts sa
      WHERE
        sa.id = stripe_account_payouts.stripe_account_id
        AND sa.user_id = auth.uid ()
    )
  );

-- Block direct INSERT, UPDATE, DELETE from authenticated users (should go via functions/service_role if needed)
CREATE POLICY "policy_stripe_account_payouts_block_direct_modification" ON public.stripe_account_payouts FOR ALL -- Applies to INSERT, UPDATE, DELETE
TO authenticated USING (FALSE)
WITH
  CHECK (FALSE);

-- Functions to handle payouts will be implemented in the webhook handler service
