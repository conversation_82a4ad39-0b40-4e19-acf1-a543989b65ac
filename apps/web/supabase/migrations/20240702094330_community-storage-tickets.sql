/* -------------------------------------------------------
* Section: Community Storage - Tickets Migration
* -------------------------------------------------------
*/
-- Function to validate if the authenticated user has access to the ticket message in storage
CREATE OR REPLACE FUNCTION kit.has_access_to_ticket_message_storage (storage_path TEXT) returns BOOLEAN language sql security definer
SET
  search_path = '' AS $$
  SELECT EXISTS (
    SELECT 1 
    FROM public.community_ticket_messages m
    JOIN public.community_tickets t ON t.id = m.ticket_id
    WHERE m.id = split_part((storage.foldername(storage_path))[3], '-', 1)::UUID
    AND public.has_role_on_ticket_community(t.id)
    AND kit.get_community_id_from_storage_path(storage_path) = t.community_id
  );
$$;

GRANT
EXECUTE ON function kit.has_access_to_ticket_message_storage (TEXT) TO authenticated,
service_role;

-- Ticket read policy (SELECT) - authenticated users with role on the ticket community
CREATE POLICY "policy_community_storage_select_tickets_member" ON storage.objects FOR
SELECT
  TO authenticated USING (
    bucket_id = 'community_storage'
    AND (storage.foldername (name)) [2] = 'tickets'
    AND kit.has_access_to_ticket_message_storage (name)
  );

-- Ticket write policy (INSERT) - authenticated users with role on the ticket community
CREATE POLICY "policy_community_storage_insert_tickets_member" ON storage.objects FOR insert TO authenticated
WITH
  CHECK (
    bucket_id = 'community_storage'
    AND (storage.foldername (name)) [2] = 'tickets'
    AND kit.has_access_to_ticket_message_storage (name)
  );

-- Ticket update policy (UPDATE) - authenticated users with role on the ticket community
CREATE POLICY "policy_community_storage_update_tickets_member" ON storage.objects
FOR UPDATE
  TO authenticated USING (
    bucket_id = 'community_storage'
    AND (storage.foldername (name)) [2] = 'tickets'
    AND kit.has_access_to_ticket_message_storage (name)
  )
WITH
  CHECK (
    bucket_id = 'community_storage'
    AND (storage.foldername (name)) [2] = 'tickets'
    AND kit.has_access_to_ticket_message_storage (name)
  );

-- Ticket delete policy (DELETE) - authenticated users with role on the ticket community
CREATE POLICY "policy_community_storage_delete_tickets_member" ON storage.objects FOR delete TO authenticated USING (
  bucket_id = 'community_storage'
  AND (storage.foldername (name)) [2] = 'tickets'
  AND kit.has_access_to_ticket_message_storage (name)
);

-- Cleanup function
-- Function to delete message attachments when a message is deleted
CREATE OR REPLACE FUNCTION public.delete_community_ticket_message_attachments () returns trigger language plpgsql security invoker
SET
  search_path = '' AS $$
DECLARE
    community_id UUID;
BEGIN
    -- Get the community_id from the ticket
    SELECT t.community_id INTO community_id
    FROM public.community_tickets t
    WHERE t.id = OLD.ticket_id;

    -- Delete all attachments for this message
    -- Pattern: /<community_id>/tickets/<ticket_id>/<message_id>-* 
    DELETE FROM storage.objects
    WHERE bucket_id = 'community_storage'
    AND name LIKE community_id::TEXT || '/tickets/' || OLD.ticket_id::TEXT || '/' || OLD.id::TEXT || '-%';

    RETURN OLD;
END;
$$;

-- Grant execute permissions for the trigger function
GRANT
EXECUTE ON function public.delete_community_ticket_message_attachments () TO postgres,
authenticated;

-- Create trigger to delete message attachments when a message is deleted
CREATE TRIGGER delete_community_ticket_message_attachments
AFTER delete ON public.community_ticket_messages FOR each ROW
EXECUTE function public.delete_community_ticket_message_attachments ();
