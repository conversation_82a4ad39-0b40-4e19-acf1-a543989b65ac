/*
 * -------------------------------------------------------
 * Section: Toggle Community Listing
 * Create the trigger to toggle the listing of a community.
 * -------------------------------------------------------
 */
/*
 * Create a custom type to return all requirement details in one structure
 */
CREATE TYPE public.community_listing_requirements_result AS (
  meets_requirements BOOLEAN,
  name_valid BOOLEAN,
  description_length_sufficient BOOLEAN,
  category_changed BOOLEAN,
  logo_exists BOOLEAN,
  cover_exists BOOLEAN,
  about_page_exists BOOLEAN,
  about_page_content_length INTEGER,
  about_page_media_count INTEGER
);

/**
 * ----
 * FUNCTION: check_community_listing_requirements
 * This function checks if a community meets the listing requirements
 * and returns both the overall status and individual requirements.
 * 
 * Logo image must be present.
 * Cover image must be present.
 * Name must be present and at least 5 characters.
 * Description must be present and at least 100 characters.
 * Category must be changed from default `new_community` to a valid category.
 * About page must exist.
 * About page must have at least 1 image or 1 video.
 * ----
 */
CREATE OR REPLACE FUNCTION public.check_community_listing_requirements (p_community_id uuid) returns public.community_listing_requirements_result AS $$
DECLARE
    result public.community_listing_requirements_result;
    v_name_length INTEGER;
    v_description_length INTEGER;
    v_category_id UUID;
    v_default_community_category_id UUID;
    v_about_page_id UUID;
    v_about_page_content_length INTEGER;
    v_about_page_content JSONB;
BEGIN
    -- Initialize result with defaults
    result.meets_requirements := TRUE;
    result.name_valid := FALSE;
    result.description_length_sufficient := FALSE;
    result.category_changed := FALSE;
    result.logo_exists := FALSE;
    result.cover_exists := FALSE;
    result.about_page_exists := FALSE;
    result.about_page_content_length := 0;
    result.about_page_media_count := 0;

    -- Check if community exists
    IF NOT EXISTS (SELECT 1 FROM public.communities WHERE id = p_community_id) THEN
        RAISE EXCEPTION 'Community not found';
    END IF;
    
    -- Get default category id
    SELECT id INTO v_default_community_category_id 
    FROM public.community_categories 
    WHERE name = 'new_community';
    
    -- Get community about page
    SELECT id INTO v_about_page_id
    FROM public.community_pages
    WHERE community_id = p_community_id 
    AND slug = 'about' 
    AND page_type = 'system';

    -- Check if about page exists
    result.about_page_exists := v_about_page_id IS NOT NULL;
    
    -- Get character count from community_pages table instead of calculating from JSON content
    IF result.about_page_exists THEN
        -- Simply get the character_count column value
        SELECT character_count INTO result.about_page_content_length
        FROM public.community_pages
        WHERE id = v_about_page_id;
        
        -- If NULL, default to 0
        IF result.about_page_content_length IS NULL THEN
            result.about_page_content_length := 0;
        END IF;
    ELSE
        result.about_page_content_length := 0;
    END IF;
    
    -- Check about page media count
    IF result.about_page_exists THEN
        SELECT COUNT(*) INTO result.about_page_media_count
        FROM public.community_page_media
        WHERE page_id = v_about_page_id
        AND media_type IN ('image', 'video');
    END IF;
    
    -- Get community details in a single query
    SELECT 
        logo_url IS NOT NULL AND logo_url != '',
        cover_url IS NOT NULL AND cover_url != '',
        category_id,
        LENGTH(COALESCE(description, '')),
        LENGTH(COALESCE(name, ''))
    INTO 
        result.logo_exists,
        result.cover_exists,
        v_category_id,
        v_description_length,
        v_name_length
    FROM public.communities
    WHERE id = p_community_id;
    
    result.category_changed := v_category_id IS NOT NULL AND v_category_id != v_default_community_category_id;
    result.description_length_sufficient := v_description_length >= 100;
    result.name_valid := v_name_length >= 5;
    
    -- Calculate overall status
    result.meets_requirements := 
        result.name_valid AND
        result.description_length_sufficient AND
        result.category_changed AND
        result.logo_exists AND
        result.cover_exists AND
        result.about_page_exists AND
        result.about_page_content_length > 0 AND
        result.about_page_media_count > 0;
    
    RETURN result;
END;
$$ language plpgsql;

GRANT
EXECUTE ON function public.check_community_listing_requirements (uuid) TO service_role,
authenticated;

/**
 * -------------------------------------------------------
 * FUNCTION: toggle_community_listing
 * This function toggles the listing of a community.
 * -------------------------------------------------------
 */
CREATE OR REPLACE FUNCTION public.toggle_community_listing (p_community_id uuid) returns void AS $$
DECLARE
    v_community_exists BOOLEAN;
    v_requirements public.community_listing_requirements_result;
BEGIN
    -- Use the helper function to check if user is super admin
    IF NOT public.is_super_admin() THEN
        RAISE EXCEPTION 'User does not have permission to toggle community listing';
    END IF;
    
    -- Get community 
    SELECT EXISTS(SELECT 1 FROM public.communities WHERE id = p_community_id) 
    INTO v_community_exists;
    
    -- Check if community exists
    IF NOT v_community_exists THEN
        RAISE EXCEPTION 'Community not found';
    END IF;

    -- Check community meets listing requirements
    SELECT * INTO v_requirements FROM public.check_community_listing_requirements(p_community_id);
    
    IF NOT v_requirements.meets_requirements THEN
        RAISE EXCEPTION 'Community does not meet listing requirements';
    END IF;

    -- Toggle the listing of the community
    UPDATE public.communities c
    SET is_listed = NOT is_listed
    WHERE id = p_community_id;

END;
$$ language plpgsql;

GRANT
EXECUTE ON function public.toggle_community_listing (uuid) TO service_role,
authenticated;

/**
 * -------------------------------------------------------
 * FUNCTION: get_community_explore_page_request_status
 * This function gets the explore page request status and current requirements for a community.
 * Returns a JSON object with the status and requirements.
 * -------------------------------------------------------
 */
CREATE OR REPLACE FUNCTION public.get_community_explore_page_request_status (p_community_id uuid) returns JSON AS $$
DECLARE
    v_requirements public.community_listing_requirements_result;
    v_is_listed BOOLEAN;
    v_status TEXT;
    v_result json;
    v_request_submitted BOOLEAN;
    v_request_status TEXT := 'not_submitted';
    v_request_reason TEXT := NULL;
    v_can_reapply BOOLEAN := TRUE;
    v_latest_request RECORD;
    v_next_reapply_date TIMESTAMPTZ := NULL;
BEGIN
    -- Get current listing status
    SELECT is_listed INTO v_is_listed
    FROM public.communities
    WHERE id = p_community_id;
    
    -- If community doesn't exist, raise exception
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Community not found';
    END IF;
    
    -- Get detailed requirements
    SELECT * INTO v_requirements 
    FROM public.check_community_listing_requirements(p_community_id);
    
    -- Determine status
    IF v_is_listed THEN
        v_status := 'listed';
    ELSIF v_requirements.meets_requirements THEN
        v_status := 'ready';
    ELSE
        v_status := 'not_ready';
    END IF;

    -- Check if request is submitted and get latest request details
    SELECT EXISTS(SELECT 1 FROM public.community_explore_requests WHERE community_id = p_community_id)
    INTO v_request_submitted;
    
    -- Get the latest request status and reason if a request exists
    IF v_request_submitted THEN
        SELECT status::TEXT, reason, updated_at, created_at
        INTO v_latest_request
        FROM public.community_explore_requests
        WHERE community_id = p_community_id
        ORDER BY created_at DESC
        LIMIT 1;
        
        v_request_status := v_latest_request.status;
        v_request_reason := v_latest_request.reason;
        
        -- Calculate reapplication eligibility for rejected requests
        IF v_request_status = 'rejected' THEN
            v_next_reapply_date := v_latest_request.updated_at + INTERVAL '7 days';
            v_can_reapply := v_latest_request.updated_at <= (NOW() - INTERVAL '7 days');
        ELSIF v_request_status = 'pending' THEN
            v_can_reapply := FALSE;
        ELSIF v_request_status = 'approved' THEN
            v_can_reapply := FALSE;
        END IF;
    END IF;
    
    -- Build the result JSON
    SELECT 
      json_build_object(
        'status', v_status,
        'requirements', json_build_object(
            'name_valid', v_requirements.name_valid,
            'description_length', v_requirements.description_length_sufficient,
            'category_changed', v_requirements.category_changed,
            'logo_exists', v_requirements.logo_exists,
            'cover_exists', v_requirements.cover_exists,
            'about_page_exists', v_requirements.about_page_exists,
            'about_page_content_length', v_requirements.about_page_content_length,
            'about_page_media_count', v_requirements.about_page_media_count
        ),
        'request_submitted', v_request_submitted,
        'request_status', v_request_status,
        'request_reason', v_request_reason,
        'can_reapply', v_can_reapply,
        'next_reapply_date', v_next_reapply_date
      ) INTO v_result;
    
    RETURN v_result;
END;
$$ language plpgsql;

GRANT
EXECUTE ON function public.get_community_explore_page_request_status (uuid) TO service_role,
authenticated;
