/* -------------------------------------------------------
* Section: Community Functions
* Create the functions to support community
* -------------------------------------------------------
*/
-- Function "public.create_community"
-- Create a community
CREATE OR REPLACE FUNCTION public.create_community (
  p_community_name TEXT,
  p_primary_owner_user_id uuid,
  p_community_id uuid DEFAULT NULL
) returns public.communities
SET
  search_path = '' AS $$
DECLARE
    v_new_community public.communities;
BEGIN
    IF p_community_id IS NULL THEN
        p_community_id := uuid_generate_v4();
    END IF;

    INSERT INTO public.communities(
        id,
        name,
        primary_owner_user_id
        )
    VALUES (
        p_community_id,
        p_community_name,
        p_primary_owner_user_id
    )
    RETURNING * INTO v_new_community;

    RETURN v_new_community;

END;

$$ language plpgsql security definer;

GRANT
EXECUTE ON function public.create_community (TEXT, uuid, uuid) TO authenticated,
service_role;

-- Function "public.create_community_server_actions"
-- Create a community through server actions
-- Differs from create_community by accepting explicit primary_owner_user_id instead of using auth.uid()
CREATE OR REPLACE FUNCTION public.create_community_server_actions (
  p_community_name TEXT,
  p_primary_owner_user_id uuid,
  p_subscription_id TEXT,
  p_is_enabled BOOLEAN DEFAULT FALSE,
  p_community_id uuid DEFAULT NULL
) returns public.communities
SET
  search_path = '' AS $$
DECLARE
    v_new_community public.communities;
    v_community_id uuid;
BEGIN
    -- Use provided community ID or generate a new one
    IF p_community_id IS NULL THEN
        v_community_id := uuid_generate_v4();
    ELSE
        v_community_id := p_community_id;
    END IF;

    INSERT INTO public.communities(
        id,
        name,
        primary_owner_user_id,
        subscription_id,
        is_enabled
        )
    VALUES (
        v_community_id,
        p_community_name,
        p_primary_owner_user_id,
        p_subscription_id,
        p_is_enabled
    )
    RETURNING * INTO v_new_community;

    RETURN v_new_community;

END;

$$ language plpgsql;

GRANT
EXECUTE ON function public.create_community_server_actions (TEXT, uuid, TEXT, BOOLEAN, uuid) TO service_role;
