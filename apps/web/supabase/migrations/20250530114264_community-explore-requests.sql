/*
 * -------------------------------------------------------
 * Section: Community Explore Requests
 * Create the table to store the requests for communities to be added to the explore page.
 * -------------------------------------------------------
 */
--
-- ENUM: public.community_explore_request_status
-- The status of a community explore request.
--
CREATE TYPE public.community_explore_request_status AS ENUM(
  'not_submitted',
  'pending',
  'approved',
  'rejected'
);

--
-- TABLE: public.community_explore_requests
-- Stores the requests for communities to be added to the explore page.
--
CREATE TABLE IF NOT EXISTS public.community_explore_requests (
  id uuid PRIMARY KEY DEFAULT extensions.uuid_generate_v4 (),
  created_by_user_id uuid REFERENCES public.users ON DELETE SET NULL,
  created_by_member_id uuid REFERENCES public.community_memberships ON DELETE SET NULL,
  community_id uuid REFERENCES public.communities (id) ON DELETE CASCADE,
  status public.community_explore_request_status NOT NULL DEFAULT 'pending',
  reason TEXT,
  updated_by_user_id uuid REFERENCES public.users,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create partial unique index to allow only one pending request per community
-- This allows multiple requests per community but only one can be pending at a time
CREATE UNIQUE INDEX idx_community_explore_requests_pending_unique ON public.community_explore_requests (community_id)
WHERE
  status = 'pending';

-- Table comments & descriptions
comment ON TABLE public.community_explore_requests IS 'Community explore requests';

comment ON COLUMN public.community_explore_requests.created_by_user_id IS 'The user that created the request';

comment ON COLUMN public.community_explore_requests.created_by_member_id IS 'The member that created the request';

comment ON COLUMN public.community_explore_requests.community_id IS 'The community that the request is for';

comment ON COLUMN public.community_explore_requests.status IS 'The status of the request';

comment ON COLUMN public.community_explore_requests.reason IS 'The reason for the request approval or rejection';

-- Add trigger to automatically update timestamps on users table
CREATE TRIGGER set_timestamps_on_community_explore_requests before insert
OR
UPDATE ON public.community_explore_requests FOR each ROW
EXECUTE function public.trigger_set_timestamps ();

-- RLS
ALTER TABLE public.community_explore_requests enable ROW level security;

-- Super admin policies (separate for each operation)
CREATE POLICY "policy_community_explore_requests_select_super_admin" ON public.community_explore_requests FOR
SELECT
  TO authenticated USING (public.is_super_admin ());

CREATE POLICY "policy_community_explore_requests_insert_super_admin" ON public.community_explore_requests FOR insert TO authenticated
WITH
  CHECK (public.is_super_admin ());

CREATE POLICY "policy_community_explore_requests_update_super_admin" ON public.community_explore_requests
FOR UPDATE
  TO authenticated USING (public.is_super_admin ())
WITH
  CHECK (public.is_super_admin ());

CREATE POLICY "policy_community_explore_requests_delete_super_admin" ON public.community_explore_requests FOR delete TO authenticated USING (public.is_super_admin ());

-- Owner policies (separate for each operation)
CREATE POLICY "policy_community_explore_requests_select_owner" ON public.community_explore_requests FOR
SELECT
  TO authenticated USING (
    EXISTS (
      SELECT
        1
      FROM
        public.community_memberships cm
      WHERE
        cm.community_id = community_explore_requests.community_id
        AND cm.user_id = auth.uid ()
        AND has_role_on_community (cm.community_id, 'owner')
    )
  );

CREATE POLICY "policy_community_explore_requests_insert_owner" ON public.community_explore_requests FOR insert TO authenticated
WITH
  CHECK (
    EXISTS (
      SELECT
        1
      FROM
        public.community_memberships cm
      WHERE
        cm.community_id = community_explore_requests.community_id
        AND cm.user_id = auth.uid ()
        AND has_role_on_community (cm.community_id, 'owner')
    )
  );

CREATE POLICY "policy_community_explore_requests_update_owner" ON public.community_explore_requests
FOR UPDATE
  TO authenticated USING (
    EXISTS (
      SELECT
        1
      FROM
        public.community_memberships cm
      WHERE
        cm.community_id = community_explore_requests.community_id
        AND cm.user_id = auth.uid ()
        AND has_role_on_community (cm.community_id, 'owner')
    )
  )
WITH
  CHECK (
    EXISTS (
      SELECT
        1
      FROM
        public.community_memberships cm
      WHERE
        cm.community_id = community_explore_requests.community_id
        AND cm.user_id = auth.uid ()
        AND has_role_on_community (cm.community_id, 'owner')
    )
  );

CREATE POLICY "policy_community_explore_requests_delete_owner" ON public.community_explore_requests FOR delete TO authenticated USING (
  EXISTS (
    SELECT
      1
    FROM
      public.community_memberships cm
    WHERE
      cm.community_id = community_explore_requests.community_id
      AND cm.user_id = auth.uid ()
      AND has_role_on_community (cm.community_id, 'owner')
  )
);

/**
 * -------------------------------------------------------
 * FUNCTION: can_reapply_for_explore_listing
 * This function checks if a community can reapply for explore listing
 * after being rejected. Communities must wait 7 days after rejection.
 * -------------------------------------------------------
 */
CREATE OR REPLACE FUNCTION public.can_reapply_for_explore_listing (p_community_id uuid) returns BOOLEAN AS $$
DECLARE
    v_latest_request RECORD;
    v_seven_days_ago TIMESTAMPTZ;
BEGIN
    -- Calculate 7 days ago
    v_seven_days_ago := NOW() - INTERVAL '7 days';
    
    -- Get the most recent request for this community
    SELECT status, updated_at
    INTO v_latest_request
    FROM public.community_explore_requests
    WHERE community_id = p_community_id
    ORDER BY created_at DESC
    LIMIT 1;
    
    -- If no previous request exists, can apply
    IF NOT FOUND THEN
        RETURN TRUE;
    END IF;
    
    -- If latest request is pending, cannot apply again
    IF v_latest_request.status = 'pending' THEN
        RETURN FALSE;
    END IF;
    
    -- If latest request was approved, cannot apply again (already listed)
    IF v_latest_request.status = 'approved' THEN
        RETURN FALSE;
    END IF;
    
    -- If latest request was rejected, check if 7 days have passed
    IF v_latest_request.status = 'rejected' THEN
        RETURN v_latest_request.updated_at <= v_seven_days_ago;
    END IF;
    
    -- Default: allow application
    RETURN TRUE;
END;
$$ language plpgsql;

GRANT
EXECUTE ON function public.can_reapply_for_explore_listing (uuid) TO service_role,
authenticated;
