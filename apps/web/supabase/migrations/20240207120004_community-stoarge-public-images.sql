/*
 * -------------------------------------------------------
 * Section: Community Story Public image policies
 * -------------------------------------------------------
 */
-- Public Images Read Policy (accessible to everyone)
CREATE POLICY "policy_community_storage_select_public_images_public" ON storage.objects FOR
SELECT
  USING (
    bucket_id = 'community_storage'
    AND (storage.foldername (name)) [2] = 'public_images'
  );

-- Public Images Insert Policy (restricted to community managers)
CREATE POLICY "policy_community_storage_insert_public_images_manager" ON storage.objects FOR insert TO authenticated
WITH
  CHECK (
    bucket_id = 'community_storage'
    AND (storage.foldername (name)) [2] = 'public_images'
    AND public.has_community_permission (
      auth.uid (),
      kit.get_community_id_from_storage_path (name),
      'settings.manage'
    )
  );

-- Public Images Update Policy (restricted to community managers)
CREATE POLICY "policy_community_storage_update_public_images_manager" ON storage.objects
FOR UPDATE
  TO authenticated USING (
    bucket_id = 'community_storage'
    AND (storage.foldername (name)) [2] = 'public_images'
    AND public.has_community_permission (
      auth.uid (),
      kit.get_community_id_from_storage_path (name),
      'settings.manage'
    )
  )
WITH
  CHECK (
    bucket_id = 'community_storage'
    AND (storage.foldername (name)) [2] = 'public_images'
    AND public.has_community_permission (
      auth.uid (),
      kit.get_community_id_from_storage_path (name),
      'settings.manage'
    )
  );

-- Public Images Delete Policy (restricted to community managers)
CREATE POLICY "policy_community_storage_delete_public_images_manager" ON storage.objects FOR delete TO authenticated USING (
  bucket_id = 'community_storage'
  AND (storage.foldername (name)) [2] = 'public_images'
  AND public.has_community_permission (
    auth.uid (),
    kit.get_community_id_from_storage_path (name),
    'settings.manage'
  )
);
