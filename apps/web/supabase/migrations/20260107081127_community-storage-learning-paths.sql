/* -------------------------------------------------------
* Section: Community Storage - Learning Paths
* -------------------------------------------------------
*/
-- Function to check if user has read access to learning path in storage
CREATE OR REPLACE FUNCTION kit.has_read_access_to_learning_path_storage (storage_path TEXT) returns BOOLEAN language sql security definer
SET
  search_path = '' AS $$
  SELECT EXISTS (
    SELECT 1 
    FROM public.community_learning_paths lp
    WHERE lp.id = (storage.foldername(storage_path))[3]::UUID
    AND public.has_role_on_learning_path_community(lp.id)
    AND lp.community_id = kit.get_community_id_from_storage_path(storage_path)
  );
$$;

GRANT
EXECUTE ON function kit.has_read_access_to_learning_path_storage (TEXT) TO authenticated,
service_role;

-- Function to check if user has write access to learning path in storage
CREATE OR REPLACE FUNCTION kit.has_write_access_to_learning_path_storage (storage_path TEXT) returns BOOLEAN language sql security definer
SET
  search_path = '' AS $$
  SELECT EXISTS (
    SELECT 1 
    FROM public.community_learning_paths lp
    WHERE lp.id = (storage.foldername(storage_path))[3]::UUID
    AND public.has_role_on_learning_path_community(lp.id)
    AND public.has_community_permission(
      auth.uid(),
      lp.community_id,
      'community.learning_paths.settings'::public.community_permissions
    )
    AND lp.community_id = kit.get_community_id_from_storage_path(storage_path)
  );
$$;

GRANT
EXECUTE ON function kit.has_write_access_to_learning_path_storage (TEXT) TO authenticated,
service_role;

-- Learning path read policy (SELECT)
CREATE POLICY "policy_community_storage_select_learning_paths_member" ON storage.objects FOR
SELECT
  TO authenticated USING (
    bucket_id = 'community_storage'
    AND (storage.foldername (name)) [2] = 'learning_paths'
    AND kit.has_read_access_to_learning_path_storage (name)
  );

-- Learning path write policy (INSERT, UPDATE, DELETE)
CREATE POLICY "policy_community_storage_all_learning_paths_can_manage_settings" ON storage.objects FOR ALL TO authenticated USING (
  bucket_id = 'community_storage'
  AND (storage.foldername (name)) [2] = 'learning_paths'
  AND kit.has_write_access_to_learning_path_storage (name)
)
WITH
  CHECK (
    bucket_id = 'community_storage'
    AND (storage.foldername (name)) [2] = 'learning_paths'
    AND kit.has_write_access_to_learning_path_storage (name)
  );

-- Function to delete learning path cover image when a learning path is deleted
CREATE OR REPLACE FUNCTION public.delete_community_learning_path_cover_image () returns trigger language plpgsql security invoker
SET
  search_path = '' AS $$
DECLARE
    community_id UUID;
BEGIN
    -- Get the community_id from the learning path
    SELECT lp.community_id INTO community_id
    FROM public.community_learning_paths lp
    WHERE lp.id = OLD.id;

    -- Delete all objects in the learning path's directory using the exact path pattern
    -- Pattern: /<community_id>/learning_paths/<learning_path_id>/* 
    DELETE FROM storage.objects
    WHERE bucket_id = 'community_storage'
    AND name LIKE community_id::TEXT || '/learning_paths/' || OLD.id::TEXT || '/%';

    RETURN OLD;
END;
$$;

-- Grant execute permissions for the trigger function
GRANT
EXECUTE ON function public.delete_community_learning_path_cover_image () TO postgres,
authenticated;

-- Create trigger to delete learning path cover image when a learning path is deleted
CREATE TRIGGER delete_community_learning_path_cover_image
AFTER delete ON public.community_learning_paths FOR each ROW
EXECUTE function public.delete_community_learning_path_cover_image ();
