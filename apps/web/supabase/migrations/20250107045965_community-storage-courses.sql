/* -------------------------------------------------------
* Section: Community Storage - Courses
* -------------------------------------------------------
*/
-- Function to check if user has read access to course in storage
CREATE OR REPLACE FUNCTION kit.has_read_access_to_course_storage (storage_path TEXT) returns BOOLEAN language sql security definer
SET
  search_path = '' AS $$
  SELECT public.has_role_on_community(kit.get_community_id_from_storage_path(storage_path));
$$;

GRANT
EXECUTE ON function kit.has_read_access_to_course_storage (TEXT) TO authenticated,
service_role;

-- Function to check if user has permission-based access to course in storage
CREATE OR REPLACE FUNCTION kit.has_permission_on_course_storage (storage_path TEXT, permission_name TEXT) returns BOOLEAN language plpgsql security definer
SET
  search_path = '' AS $$
DECLARE
  v_community_id UUID;
  v_course_id UUID;
BEGIN
  -- Extract IDs using storage.foldername
  v_community_id := (storage.foldername(storage_path))[1]::UUID;
  v_course_id := (storage.foldername(storage_path))[3]::UUID;
  
  -- Special case for course creation - only check community permission
  IF permission_name = 'community.courses.create' THEN
    RETURN public.has_community_permission(
      auth.uid(),
      v_community_id,
      permission_name::public.community_permissions
    );
  END IF;
  
  -- For other operations, check if course exists and user has permission
  RETURN EXISTS (
    SELECT 1 FROM public.community_courses c
    WHERE c.id = v_course_id
    AND public.has_community_permission(
      auth.uid(),
      c.community_id,
      permission_name::public.community_permissions
    )
    AND c.community_id = v_community_id
  );
END;
$$;

GRANT
EXECUTE ON function kit.has_permission_on_course_storage (TEXT, TEXT) TO authenticated,
service_role;

/* -------------------------------------------------------
* Policies: storage.objects (community_storage/courses)
* ------------------------------------------------------- */
-- Read policy
CREATE POLICY "policy_community_storage_select_courses_member" ON storage.objects FOR
SELECT
  TO authenticated USING (
    bucket_id = 'community_storage'
    AND (storage.foldername (name)) [2] = 'courses'
    AND kit.has_read_access_to_course_storage (name)
  );

-- Insert policy
CREATE POLICY "policy_community_storage_insert_courses_member" ON storage.objects FOR insert TO authenticated
WITH
  CHECK (
    bucket_id = 'community_storage'
    AND (storage.foldername (name)) [2] = 'courses'
    AND kit.has_permission_on_course_storage (name, 'community.courses.create')
  );

-- Update policy
CREATE POLICY "policy_community_storage_update_courses_member" ON storage.objects
FOR UPDATE
  TO authenticated USING (
    bucket_id = 'community_storage'
    AND (storage.foldername (name)) [2] = 'courses'
    AND kit.has_permission_on_course_storage (name, 'community.courses.update')
  )
WITH
  CHECK (
    bucket_id = 'community_storage'
    AND (storage.foldername (name)) [2] = 'courses'
    AND kit.has_permission_on_course_storage (name, 'community.courses.update')
  );

-- Delete policy
CREATE POLICY "policy_community_storage_delete_courses_member" ON storage.objects FOR delete TO authenticated USING (
  bucket_id = 'community_storage'
  AND (storage.foldername (name)) [2] = 'courses'
  AND kit.has_permission_on_course_storage (name, 'community.courses.delete')
);

-- Cleanup function for course images
-- Function to delete course storage objects when a course is deleted
CREATE OR REPLACE FUNCTION public.delete_community_course_storage_objects () returns trigger language plpgsql security invoker
SET
  search_path = '' AS $$
BEGIN
    -- Delete all objects in the course's directory
    -- Pattern: /<community_id>/courses/<course_id>/* - using community_id and course_id
    DELETE FROM storage.objects
    WHERE bucket_id = 'community_storage'
    AND name LIKE OLD.community_id::TEXT || '/courses/' || OLD.id::TEXT || '/%';

    RETURN OLD;
END;
$$;

-- Grant execute permissions for the trigger function
GRANT
EXECUTE ON function public.delete_community_course_storage_objects () TO postgres,
authenticated;

-- Create trigger to delete course storage objects when a course is deleted
CREATE TRIGGER delete_community_course_storage_objects
AFTER delete ON public.community_courses FOR each ROW
EXECUTE function public.delete_community_course_storage_objects ();
