/* -------------------------------------------------------
* Section: Community Team Payout Percentage
* Create the tables and functions to support Stripe Connect integration
* -------------------------------------------------------
*/
-- Create community_team_payouts table for Stripe Connect integration
-- Description: This migration creates the community_team_payouts table to track payouts for community teams.
-- Add community_permissions to support Stripe Connect payouts
BEGIN;

ALTER TYPE public.community_permissions
ADD value 'community.team.payouts';

COMMIT;

-- ROLES
BEGIN;

INSERT INTO
  public.community_role_permissions (role, permission)
VALUES
  ('owner', 'community.team.payouts');

COMMIT;

-- Create community_team_payouts table
CREATE TABLE IF NOT EXISTS public.community_team_payouts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid (),
  member_id uuid NOT NULL UNIQUE REFERENCES public.community_memberships (id) ON DELETE CASCADE,
  payout_percentage INTEGER NOT NULL, -- i.e. 10000 = 100%, 1150 = 11.5%
  created_at TIMESTAMPTZ DEFAULT current_timestamp NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT current_timestamp NOT NULL
);

comment ON TABLE public.community_team_payouts IS 'Tracks payout percentages for community members';

comment ON COLUMN public.community_team_payouts.id IS 'Unique identifier for the payout record';

comment ON COLUMN public.community_team_payouts.member_id IS 'Reference to the community member';

comment ON COLUMN public.community_team_payouts.payout_percentage IS 'The payout percentage for the community member';

ALTER TABLE public.community_team_payouts
ADD CONSTRAINT check_payout_percentage_range CHECK (
  payout_percentage >= 0
  AND payout_percentage <= 10000
);

-- Set up timestamps trigger
CREATE TRIGGER set_community_team_payouts_timestamps before
UPDATE ON public.community_team_payouts FOR each ROW
EXECUTE function public.trigger_set_timestamps ();

CREATE INDEX idx_community_team_payouts_member_id ON public.community_team_payouts (member_id);

-- RLS policies for Payouts
ALTER TABLE public.community_team_payouts enable ROW level security;

ALTER TABLE public.community_team_payouts force ROW level security;

GRANT
SELECT
,
  insert,
UPDATE,
delete ON public.community_team_payouts TO authenticated,
service_role;

-- Create new policy: Users can adjust payouts linked to their own community team
CREATE POLICY "policy_community_team_payouts_crud_user" ON public.community_team_payouts FOR ALL TO authenticated USING (
  public.has_community_permission (
    auth.uid (),
    (
      SELECT
        cm.community_id
      FROM
        public.community_memberships cm
      WHERE
        cm.id = member_id
    ),
    'community.team.payouts'::public.community_permissions
  )
)
WITH
  CHECK (
    public.has_community_permission (
      auth.uid (),
      (
        SELECT
          cm.community_id
        FROM
          public.community_memberships cm
        WHERE
          cm.id = member_id
      ),
      'community.team.payouts'::public.community_permissions
    )
  );

-- Function to check the total payout percentage for a community
CREATE OR REPLACE FUNCTION public.check_community_payout_total () returns trigger AS $$
DECLARE
  v_community_id UUID;
  total_percentage INTEGER;
BEGIN
  -- Determine the community_id based on the member_id involved in the change
  IF TG_OP = 'DELETE' THEN
    -- For DELETE, use the old member_id to find the community
    SELECT community_id INTO v_community_id
    FROM public.community_memberships
    WHERE id = OLD.member_id;
  ELSE
    -- For INSERT or UPDATE, use the new member_id
    SELECT community_id INTO v_community_id
    FROM public.community_memberships
    WHERE id = NEW.member_id;
  END IF;

  -- If community_id couldn't be found (should not happen due to FKs, but safe check)
  IF v_community_id IS NULL THEN
     RETURN COALESCE(NEW, OLD); -- Exit gracefully
  END IF;

  -- Calculate the current total payout percentage for this community *after* the change
  -- This query runs within the transaction's snapshot of the data
  SELECT COALESCE(SUM(ctp.payout_percentage), 0) INTO total_percentage
  FROM public.community_team_payouts ctp
  JOIN public.community_memberships cm ON ctp.member_id = cm.id
  WHERE cm.community_id = v_community_id;

  -- Check if the total is exactly 10000 (100.00%)
  IF total_percentage <> 10000 THEN
    RAISE EXCEPTION 'Total payout percentage for community % must equal exactly 10000 (100.00%%). Current total: %', v_community_id, total_percentage;
  END IF;

  -- Return appropriate value based on trigger type
  IF TG_OP = 'DELETE' THEN
    RETURN OLD;
  ELSE
    RETURN NEW;
  END IF;
END;
$$ language plpgsql;

-- Constraint trigger to enforce the total payout percentage limit
CREATE CONSTRAINT TRIGGER trigger_check_community_payout_total
AFTER insert
OR
UPDATE
OR delete ON public.community_team_payouts -- Include DELETE for completeness, though it won't cause violation
DEFERRABLE INITIALLY deferred -- IMPORTANT: Check at commit time
FOR each ROW
EXECUTE function public.check_community_payout_total ();
