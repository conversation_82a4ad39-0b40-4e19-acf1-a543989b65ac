/*
 * -------------------------------------------------------
 * Section: Update Community Explore Request Status
 * Create function to update explore request status with reason
 * -------------------------------------------------------
 */
/**
 * -------------------------------------------------------
 * FUNCTION: update_community_explore_request_status
 * This function updates the status of a community explore request
 * with an optional reason (required for rejection)
 * -------------------------------------------------------
 */
CREATE OR REPLACE FUNCTION public.update_community_explore_request_status (
  p_community_id uuid,
  p_status public.community_explore_request_status,
  p_reason TEXT DEFAULT NULL
) returns void AS $$
DECLARE
    v_request_exists BOOLEAN;
BEGIN
    -- Check if user is super admin
    IF NOT public.is_super_admin() THEN
        RAISE EXCEPTION 'User does not have permission to update explore request status';
    END IF;
    
    -- Check if request exists
    SELECT EXISTS(SELECT 1 FROM public.community_explore_requests WHERE community_id = p_community_id) 
    INTO v_request_exists;
    
    IF NOT v_request_exists THEN
        RAISE EXCEPTION 'Community explore request not found';
    END IF;

    -- If approving, update community to be listed
    IF p_status = 'approved' THEN        
        -- Update community to be listed
        UPDATE public.communities 
        SET is_listed = TRUE 
        WHERE id = p_community_id;
    END IF;

    -- If rejecting, reason is required
    IF p_status = 'rejected' AND (p_reason IS NULL OR LENGTH(TRIM(p_reason)) = 0) THEN
        RAISE EXCEPTION 'Reason is required when rejecting a request';
    END IF;

    -- Update the request status
    UPDATE public.community_explore_requests 
    SET 
        status = p_status,
        reason = p_reason,
        updated_by_user_id = auth.uid(),
        updated_at = NOW()
    WHERE community_id = p_community_id;

END;
$$ language plpgsql;

GRANT
EXECUTE ON function public.update_community_explore_request_status (
  uuid,
  public.community_explore_request_status,
  TEXT
) TO service_role,
authenticated;
