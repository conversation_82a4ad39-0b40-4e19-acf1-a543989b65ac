/* -------------------------------------------------------
* Section: Community Storage - Pages
* -------------------------------------------------------
*/
-- Function to check if user has read access to page in storage
CREATE OR REPLACE FUNCTION kit.has_read_access_to_page_storage (storage_path TEXT) returns BOOLEAN language sql security definer
SET
  search_path = '' AS $$
  SELECT EXISTS (
    SELECT 1  
    FROM public.community_pages cp
    WHERE cp.id = (storage.foldername(storage_path))[3]::UUID
    AND public.has_role_on_community(cp.community_id)
    AND cp.community_id = kit.get_community_id_from_storage_path(storage_path)
  );
$$;

GRANT
EXECUTE ON function kit.has_read_access_to_page_storage (TEXT) TO authenticated,
service_role;

-- Function to check if user has write access to page in storage
CREATE OR REPLACE FUNCTION kit.has_write_access_to_page_storage (storage_path TEXT) returns BOOLEAN language sql security definer
SET
  search_path = '' AS $$
  SELECT EXISTS (
    SELECT 1 
    FROM public.community_pages cp
    WHERE cp.id = (storage.foldername(storage_path))[3]::UUID
    AND public.has_role_on_community(cp.community_id)
    AND public.has_community_permission(
      auth.uid(),
      cp.community_id,
      'community.pages.settings'::public.community_permissions
    )
    AND cp.community_id = kit.get_community_id_from_storage_path(storage_path)
  );
$$;

GRANT
EXECUTE ON function kit.has_write_access_to_page_storage (TEXT) TO authenticated,
service_role;

-- Page read policy (SELECT)
CREATE POLICY "policy_community_storage_select_pages_member" ON storage.objects FOR
SELECT
  TO authenticated USING (
    bucket_id = 'community_storage'
    AND (storage.foldername (name)) [2] = 'pages'
    AND kit.has_read_access_to_page_storage (name)
  );

-- Page write policy (INSERT, UPDATE, DELETE)
CREATE POLICY "policy_community_storage_all_pages_can_manage_settings" ON storage.objects FOR ALL TO authenticated USING (
  bucket_id = 'community_storage'
  AND (storage.foldername (name)) [2] = 'pages'
  AND kit.has_write_access_to_page_storage (name)
)
WITH
  CHECK (
    bucket_id = 'community_storage'
    AND (storage.foldername (name)) [2] = 'pages'
    AND kit.has_write_access_to_page_storage (name)
  );

-- Function to delete page cover image when a page is deleted
CREATE OR REPLACE FUNCTION public.delete_community_page_cover_image () returns trigger language plpgsql security invoker
SET
  search_path = '' AS $$
DECLARE
    community_id UUID;
BEGIN
    -- Get the community_id from the page
    SELECT cp.community_id INTO community_id
    FROM public.community_pages cp
    WHERE cp.id = OLD.id;

    -- Delete all objects in the page's directory using the exact path pattern
    -- Pattern: /<community_id>/pages/<page_id>/* 
    DELETE FROM storage.objects
    WHERE bucket_id = 'community_storage'
    AND name LIKE community_id::TEXT || '/pages/' || OLD.id::TEXT || '/%';

    RETURN OLD;
END;
$$;

-- Grant execute permissions for the trigger function
GRANT
EXECUTE ON function public.delete_community_page_cover_image () TO postgres,
authenticated;

-- Create trigger to delete page cover image when a page is deleted
CREATE TRIGGER delete_community_page_cover_image
AFTER delete ON public.community_pages FOR each ROW
EXECUTE function public.delete_community_page_cover_image ();
