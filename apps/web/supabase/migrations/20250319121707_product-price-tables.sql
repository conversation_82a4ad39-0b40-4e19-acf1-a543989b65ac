/* -------------------------------------------------------
* Section: Platform Billing Tables
* Tables to store Stripe product and pricing information for platform billing
* These tables store product catalog data synced from Stripe
* -------------------------------------------------------
*/
-- Add community_permissions to support community products
BEGIN;

ALTER TYPE public.community_permissions
ADD value 'community.products.settings';

ALTER TYPE public.community_permissions
ADD value 'community.products.create';

ALTER TYPE public.community_permissions
ADD value 'community.products.update';

ALTER TYPE public.community_permissions
ADD value 'community.products.delete';

ALTER TYPE public.community_permissions
ADD value 'community.prices.create';

ALTER TYPE public.community_permissions
ADD value 'community.prices.update';

ALTER TYPE public.community_permissions
ADD value 'community.prices.delete';

ALTER TYPE public.community_permissions
ADD value 'community.prices.toggle';

COMMIT;

-- ROLES
-- Update the community_role_permissions table with the new community products permissions
BEGIN;

INSERT INTO
  public.community_role_permissions (role, permission)
VALUES
  ('owner', 'community.products.settings'),
  ('owner', 'community.products.create'),
  ('owner', 'community.products.update'),
  ('owner', 'community.products.delete'),
  ('owner', 'community.prices.create'),
  ('owner', 'community.prices.update'),
  ('owner', 'community.prices.delete'),
  ('owner', 'community.prices.toggle');

COMMIT;

CREATE TYPE public.seller_type AS ENUM('platform', 'community', 'user');

CREATE TYPE public.product_type AS ENUM(
  'platform_plan',
  'community_plan',
  'digital_product'
);

-- Platform products table
CREATE TABLE public.products (
  id TEXT NOT NULL PRIMARY KEY,
  community_id uuid REFERENCES public.communities (id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  active BOOLEAN NOT NULL DEFAULT TRUE,
  trial_days INTEGER NOT NULL DEFAULT 0,
  seller public.seller_type NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

comment ON TABLE public.products IS 'Products to be used within the platform';

-- Add default_product_id to communities table
ALTER TABLE public.communities
ADD COLUMN default_product_id TEXT REFERENCES public.products (id) ON DELETE SET NULL;

comment ON COLUMN public.communities.default_product_id IS 'The default product for the community';

REVOKE ALL ON public.products
FROM
  authenticated,
  service_role;

GRANT
SELECT
,
  insert,
UPDATE,
delete ON public.products TO authenticated,
service_role;

ALTER TABLE public.products enable ROW level security;

CREATE POLICY "policy_products_all_super_admin" ON public.products FOR ALL USING (is_super_admin ());

CREATE POLICY "policy_products_all_service_role" ON public.products FOR ALL TO service_role USING (TRUE);

CREATE POLICY "policy_products_select_public" ON public.products FOR
SELECT
  TO anon,
  authenticated USING (TRUE);

CREATE POLICY "policy_products_insert_community_can_create" ON public.products FOR insert TO authenticated
WITH
  CHECK (
    CASE
      WHEN seller = 'community' THEN community_id IS NOT NULL
      AND public.has_community_permission (
        auth.uid (),
        community_id,
        'community.products.create'::public.community_permissions
      )
      ELSE public.is_super_admin ()
    END
  );

CREATE POLICY "policy_products_update_community_can_update" ON public.products
FOR UPDATE
  TO authenticated USING (
    CASE
      WHEN seller = 'community' THEN community_id IS NOT NULL
      AND public.has_community_permission (
        auth.uid (),
        community_id,
        'community.products.update'::public.community_permissions
      )
      ELSE public.is_super_admin ()
    END
  );

CREATE POLICY "policy_products_delete_community_can_delete" ON public.products FOR delete TO authenticated USING (
  CASE
    WHEN seller = 'community' THEN community_id IS NOT NULL
    AND public.has_community_permission (
      auth.uid (),
      community_id,
      'community.products.delete'::public.community_permissions
    )
  END
);

CREATE TRIGGER set_timestamp_products before
UPDATE ON public.products FOR each ROW
EXECUTE procedure public.trigger_set_timestamps ();

CREATE TYPE public.price_type AS ENUM('one_time', 'recurring');

CREATE TYPE public.price_interval AS ENUM('month', 'year', 'week', 'day', 'one_time');

-- Platform prices table
CREATE TABLE public.product_prices (
  id TEXT NOT NULL PRIMARY KEY,
  nickname TEXT,
  product_id TEXT NOT NULL, -- Will be linked to products after creation  
  unit_amount INTEGER, -- Amount in cents
  currency TEXT NOT NULL,
  active BOOLEAN NOT NULL DEFAULT TRUE,
  type public.price_type NOT NULL DEFAULT 'recurring', -- one_time or recurring
  INTERVAL public.price_interval NOT NULL DEFAULT 'month', -- month, year, week, day, one_time
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

comment ON TABLE public.product_prices IS 'Prices to be used within the platform';

ALTER TABLE public.product_prices
ADD CONSTRAINT fk_product_prices_product_id FOREIGN key (product_id) REFERENCES public.products (id) ON DELETE CASCADE;

CREATE INDEX idx_product_prices_product_id ON public.product_prices (product_id);

-- Add price_id to community_memberships table
ALTER TABLE public.community_memberships
ADD COLUMN price_id TEXT REFERENCES public.product_prices (id);

comment ON COLUMN public.community_memberships.price_id IS 'The price the user purchased to join the community';

-- Revoke all access to product_prices table for authenticated users and service_role
REVOKE ALL ON public.product_prices
FROM
  authenticated,
  service_role;

GRANT
SELECT
,
  insert,
UPDATE,
delete ON public.product_prices TO authenticated,
service_role;

ALTER TABLE public.product_prices enable ROW level security;

CREATE OR REPLACE FUNCTION public.has_community_price_permission (
  p_product_id TEXT,
  p_permission community_permissions
) returns BOOLEAN AS $$
DECLARE
  v_community_id UUID;
BEGIN
  -- Get the community_id from the price
  SELECT community_id INTO v_community_id
  FROM public.products
  WHERE id = p_product_id;

  RETURN public.has_community_permission(auth.uid(), v_community_id, p_permission);
END;
$$ language plpgsql security definer;

GRANT
EXECUTE ON function public.has_community_price_permission (TEXT, community_permissions) TO authenticated,
service_role;

CREATE POLICY "policy_product_prices_all_super_admin" ON public.product_prices FOR ALL USING (is_super_admin ());

CREATE POLICY "policy_product_prices_all_service_role" ON public.product_prices FOR ALL TO service_role USING (TRUE);

CREATE POLICY "policy_product_prices_select_public" ON public.product_prices FOR
SELECT
  TO anon,
  authenticated USING (TRUE);

CREATE POLICY "policy_product_prices_insert_community_can_create_price" ON public.product_prices FOR insert TO authenticated
WITH
  CHECK (
    public.has_community_price_permission (
      product_id,
      'community.prices.create'::public.community_permissions
    )
    OR public.is_super_admin ()
  );

CREATE POLICY "policy_product_prices_update_community_can_update_price" ON public.product_prices
FOR UPDATE
  TO authenticated USING (
    public.has_community_price_permission (
      product_id,
      'community.prices.update'::public.community_permissions
    )
    OR public.is_super_admin ()
  );

CREATE POLICY "policy_product_prices_delete_community_can_delete_price" ON public.product_prices FOR delete TO authenticated USING (
  public.has_community_price_permission (
    product_id,
    'community.prices.delete'::public.community_permissions
  )
  OR public.is_super_admin ()
);

CREATE TRIGGER set_timestamp_product_prices before
UPDATE ON public.product_prices FOR each ROW
EXECUTE procedure public.trigger_set_timestamps ();
