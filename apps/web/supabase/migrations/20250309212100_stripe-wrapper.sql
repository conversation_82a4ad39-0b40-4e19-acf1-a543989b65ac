-- /* -------------------------------------------------------
-- * Section: Stripe Wrapper setup
-- * Enable Stripe wrapper 
-- * Docs: https://supabase.com/docs/guides/database/extensions/wrappers/stripe
-- * -------------------------------------------------------
-- */
-- CREATE EXTENSION if NOT EXISTS wrappers
-- WITH
--   schema extensions;
-- CREATE FOREIGN DATA WRAPPER stripe_wrapper handler stripe_fdw_handler validator stripe_fdw_validator;
-- -- Add test Stripe API key in Vault and retrieve the created `key_id`
-- SELECT
--   vault.create_secret (
--     '',
--     'stripe',
--     'Stripe API key for Wrappers'
--   );
-- CREATE SERVER stripe_server FOREIGN data wrapper stripe_wrapper options (
--   -- api_key_id 'stripe', -- The Key ID from above, required if api_key_name is not specified.
--   api_key_name 'stripe', -- The Key Name from above, required if api_key_id is not specified.
--   api_url 'https://api.stripe.com/v1/' -- Stripe API base URL, optional. Default is 'https://api.stripe.com/v1/'
--   -- api_version '2024-06-20'  -- Stripe API version, optional. Default is your Stripe account’s default API version.
-- );
-- CREATE SCHEMA if NOT EXISTS stripe;
-- -- create all the foreign tables
-- IMPORT FOREIGN SCHEMA stripe
-- FROM
--   server stripe_server INTO stripe;
-- -- Add Stripe accounts table
-- CREATE FOREIGN TABLE stripe.accounts (
--   id TEXT,
--   business_type TEXT,
--   country TEXT,
--   email TEXT,
--   type TEXT,
--   created TIMESTAMP,
--   attrs JSONB
-- ) server stripe_server options (object 'accounts');
-- -- Add Stripe customers table
-- CREATE FOREIGN TABLE stripe.customers (
--   id TEXT,
--   email TEXT,
--   name TEXT,
--   description TEXT,
--   created TIMESTAMP,
--   attrs JSONB
-- ) server stripe_server options (object 'customers', rowid_column 'id');
-- -- Add Stripe invoices table
-- CREATE FOREIGN TABLE stripe.invoices (
--   id TEXT,
--   customer TEXT,
--   subscription TEXT,
--   status TEXT,
--   total BIGINT,
--   currency TEXT,
--   period_start TIMESTAMP,
--   period_end TIMESTAMP,
--   attrs JSONB
-- ) server stripe_server options (object 'invoices');
-- -- Add Stripe payment intents table
-- CREATE FOREIGN TABLE stripe.payment_intents (
--   id TEXT,
--   customer TEXT,
--   amount BIGINT,
--   currency TEXT,
--   payment_method TEXT,
--   created TIMESTAMP,
--   attrs JSONB
-- ) server stripe_server options (object 'payment_intents');
-- -- Add Stripe payouts table
-- CREATE FOREIGN TABLE stripe.payouts (
--   id TEXT,
--   amount BIGINT,
--   currency TEXT,
--   arrival_date TIMESTAMP,
--   description TEXT,
--   statement_descriptor TEXT,
--   status TEXT,
--   created TIMESTAMP,
--   attrs JSONB
-- ) server stripe_server options (object 'payouts');
-- -- Add Stripe prices table
-- CREATE FOREIGN TABLE stripe.prices (
--   id TEXT,
--   active BOOL,
--   currency TEXT,
--   product TEXT,
--   unit_amount BIGINT,
--   type TEXT,
--   created TIMESTAMP,
--   attrs JSONB
-- ) server stripe_server options (object 'prices');
-- -- Add Stripe products table
-- CREATE FOREIGN TABLE stripe.products (
--   id TEXT,
--   name TEXT,
--   active BOOL,
--   default_price TEXT,
--   description TEXT,
--   created TIMESTAMP,
--   updated TIMESTAMP,
--   attrs JSONB
-- ) server stripe_server options (object 'products', rowid_column 'id');
-- -- Add Stripe subscriptions table
-- CREATE FOREIGN TABLE stripe.subscriptions (
--   id TEXT,
--   customer TEXT,
--   currency TEXT,
--   current_period_start TIMESTAMP,
--   current_period_end TIMESTAMP,
--   attrs JSONB
-- ) server stripe_server options (object 'subscriptions', rowid_column 'id');
-- -- Add Stripe transfers table
-- CREATE FOREIGN TABLE stripe.transfers (
--   id TEXT,
--   amount BIGINT,
--   currency TEXT,
--   description TEXT,
--   destination TEXT,
--   created TIMESTAMP,
--   attrs JSONB
-- ) server stripe_server options (object 'transfers');
