/*
 * -------------------------------------------------------
 * Section: Setup New Community Trigger
 * Create the trigger to setup a new community.
 * -------------------------------------------------------
 */
/**
 * -------------------------------------------------------
 * FUNCTION: setup_new_community
 * This function orchestrates the complete setup of a new community by executing steps in sequence:
 * 1. Add owner as member
 * 2. Create default pages
 * -------------------------------------------------------
 */
CREATE OR REPLACE FUNCTION public.setup_new_community () returns trigger AS $$
DECLARE
    v_owner_member_id UUID;
BEGIN
    -- First add the owner as a member
    INSERT INTO public.community_memberships(
        community_id,
        user_id,
        community_role)
    VALUES (
        NEW.id,
        NEW.primary_owner_user_id,
        public.get_upper_system_role())
    RETURNING id INTO v_owner_member_id;

    -- Add payout percentage for owner
    INSERT INTO public.community_team_payouts(
        member_id,
        payout_percentage)
    VALUES (
        v_owner_member_id,
        10000);

    -- Add General forums category
    PERFORM public.community_create_forum_category(
        NEW.id,
        'General',
        'General discussions',
        '💬'
    );

    -- Then create the default pages using the new membership
    INSERT INTO public.community_pages (
        community_id, 
        slug, 
        title, 
        description, 
        content,
        page_type,
        is_published,
        created_by_user_id,
        created_by_member_id
    ) VALUES (
        NEW.id,
        'about',
        'About',
        'Learn more about our community',
        '{"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "Learn more about our community"}]}]}',
        'system',
        true,
        NEW.primary_owner_user_id,
        v_owner_member_id
    );
    
    RETURN NEW;
END;
$$ language plpgsql;

-- Create the single orchestrator trigger that handles everything
CREATE TRIGGER setup_new_community
AFTER insert ON public.communities FOR each ROW
EXECUTE function public.setup_new_community ();

-- Drop the individual triggers since we're combining them
-- DROP TRIGGER if EXISTS add_current_user_to_new_community ON public.communities;
