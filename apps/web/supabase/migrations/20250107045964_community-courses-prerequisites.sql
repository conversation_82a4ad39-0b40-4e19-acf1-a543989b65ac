/* -------------------------------------------------------
* Section: Community Course Prerequisites
* Create the tables and functions to support community course prerequisites
* -------------------------------------------------------
*/
CREATE TABLE IF NOT EXISTS public.community_course_prerequisites (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4 (),
  course_id uuid NOT NULL REFERENCES public.community_courses (id) ON DELETE CASCADE,
  prerequisite_id uuid NOT NULL REFERENCES public.community_courses (id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE (course_id, prerequisite_id),
  CONSTRAINT no_self_reference CHECK (course_id != prerequisite_id)
);

-- Add RLS, indexes and grants to the prerequisites table
ALTER TABLE public.community_course_prerequisites enable ROW level security;

GRANT
SELECT
,
  insert,
UPDATE,
delete ON public.community_course_prerequisites TO authenticated,
service_role;

-- Create indexes for better performance
CREATE INDEX idx_course_prerequisites_prerequisite_id ON public.community_course_prerequisites (prerequisite_id);

-- RLS policies for prerequisites table
CREATE POLICY "policy_community_course_prerequisites_select" ON public.community_course_prerequisites FOR
SELECT
  TO authenticated USING (
    EXISTS (
      SELECT
        1
      FROM
        public.community_courses c
      WHERE
        c.id = course_id
        AND public.has_role_on_community (c.community_id)
    )
  );

CREATE POLICY "policy_community_course_prerequisites_modify" ON public.community_course_prerequisites FOR ALL TO authenticated USING (
  EXISTS (
    SELECT
      1
    FROM
      public.community_courses c
    WHERE
      c.id = course_id
      AND public.has_role_on_community (c.community_id)
      AND public.has_community_permission (
        auth.uid (),
        c.community_id,
        'community.courses.update'::public.community_permissions
      )
  )
);

-- Add functions for managing multiple prerequisites at once
-- Function to set course prerequisites (replaces all existing prerequisites)
CREATE OR REPLACE FUNCTION public.set_community_course_prerequisites (p_course_id uuid, p_prerequisite_ids uuid[]) returns BOOLEAN AS $$
BEGIN
  -- First delete all existing prerequisites
  DELETE FROM public.community_course_prerequisites
  WHERE course_id = p_course_id;
  
  -- Skip if the array is null or empty
  IF p_prerequisite_ids IS NULL OR array_length(p_prerequisite_ids, 1) IS NULL THEN
    RETURN TRUE;
  END IF;
  
  -- Add all prerequisites in a single operation
  INSERT INTO public.community_course_prerequisites (course_id, prerequisite_id)
  SELECT p_course_id, pr
  FROM unnest(p_prerequisite_ids) AS pr
  ON CONFLICT DO NOTHING;
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    -- Roll back any changes if an error occurs
    RAISE;
END;
$$ language plpgsql;

GRANT
EXECUTE ON function public.set_community_course_prerequisites (uuid, uuid[]) TO authenticated;

-- Updated create_community_course function that accepts prerequisites array
CREATE OR REPLACE FUNCTION public.create_community_course_with_prerequisites (
  p_community_id uuid,
  p_title VARCHAR,
  p_description VARCHAR DEFAULT NULL,
  p_cover_url VARCHAR DEFAULT NULL,
  p_prerequisite_ids uuid[] DEFAULT NULL,
  p_access community_course_access_enum DEFAULT 'standard'
) returns public.community_courses AS $$
DECLARE
    v_new_course public.community_courses;
BEGIN
    -- Create the course
    INSERT INTO community_courses (
        community_id,
        title,
        description,
        cover_url,
        access,
        sequence_order
    )
    VALUES (
        p_community_id,
        p_title,
        p_description,
        p_cover_url,
        p_access,
        (
            SELECT COALESCE(MAX(sequence_order), 0) + 1
            FROM public.community_courses 
            WHERE public.community_courses.community_id = p_community_id
        )
    )
    RETURNING * INTO v_new_course;
    
    -- Set prerequisites if provided
    IF p_prerequisite_ids IS NOT NULL AND array_length(p_prerequisite_ids, 1) > 0 THEN
        PERFORM public.set_community_course_prerequisites(v_new_course.id, p_prerequisite_ids);
    END IF;
    
    RETURN v_new_course;
END;
$$ language plpgsql;

GRANT
EXECUTE ON function public.create_community_course_with_prerequisites (
  uuid,
  VARCHAR,
  VARCHAR,
  VARCHAR,
  uuid[],
  community_course_access_enum
) TO authenticated;

-- Updated update_community_course function that accepts prerequisites array
CREATE OR REPLACE FUNCTION public.update_community_course_with_prerequisites (
  p_course_id uuid,
  p_title VARCHAR,
  p_description VARCHAR DEFAULT NULL,
  p_cover_url VARCHAR DEFAULT NULL,
  p_prerequisite_ids uuid[] DEFAULT NULL,
  p_access community_course_access_enum DEFAULT 'standard'
) returns public.community_courses AS $$
DECLARE
    v_updated_course public.community_courses;
BEGIN
    -- Update the course
    UPDATE public.community_courses
    SET title = p_title,
        description = p_description,
        cover_url = p_cover_url,
        access = p_access
    WHERE id = p_course_id
    RETURNING * INTO v_updated_course;

    -- Update prerequisites if provided
    IF p_prerequisite_ids IS NOT NULL THEN
        PERFORM public.set_community_course_prerequisites(p_course_id, p_prerequisite_ids);
    END IF;

    RETURN v_updated_course;
END;
$$ language plpgsql;

GRANT
EXECUTE ON function public.update_community_course_with_prerequisites (
  uuid,
  VARCHAR,
  VARCHAR,
  VARCHAR,
  uuid[],
  community_course_access_enum
) TO authenticated;

-- Create a function TO validate prerequisite courses exist
CREATE OR REPLACE FUNCTION public.validate_prerequisite_community_courses () returns trigger AS $$
DECLARE
    v_max_prerequisites CONSTANT INT := 5;
    v_prereq_count INT;
    v_course_community_id UUID;
    v_valid_count INT;
BEGIN
    -- Get the community_id for the course
    SELECT c.community_id INTO v_course_community_id
    FROM public.community_courses c
    WHERE c.id = NEW.course_id;
    
    IF v_course_community_id IS NULL THEN
        RAISE EXCEPTION 'Course with ID % not found', NEW.course_id;
    END IF;

    -- Check maximum number of prerequisites
    SELECT COUNT(*)
    INTO v_prereq_count
    FROM public.community_course_prerequisites
    WHERE course_id = NEW.course_id;
    
    IF v_prereq_count > v_max_prerequisites THEN
        RAISE EXCEPTION 'Maximum of % prerequisite courses allowed', v_max_prerequisites;
    END IF;

    -- No self-referencing allowed
    IF NEW.course_id = NEW.prerequisite_id THEN
        RAISE EXCEPTION 'Course cannot be a prerequisite for itself';
    END IF;

    -- Ensure exactly one valid prerequisite exists
    SELECT COUNT(*) INTO v_valid_count
    FROM public.community_courses c
    WHERE c.id = NEW.prerequisite_id
      AND c.community_id = v_course_community_id
      AND c.status = 'published';
      
    IF v_valid_count = 0 THEN
      RAISE EXCEPTION 'Invalid prerequisite: must exist in same community and be published';
    END IF;
    
    RETURN NEW;
END;
$$ language plpgsql;

GRANT
EXECUTE ON function public.validate_prerequisite_community_courses () TO authenticated;

-- Create trigger FOR prerequisite validation
CREATE TRIGGER validate_community_course_prerequisites_trigger
AFTER insert
OR
UPDATE ON public.community_course_prerequisites FOR each ROW
EXECUTE function public.validate_prerequisite_community_courses ();

CREATE OR REPLACE FUNCTION check_circular_community_course_prerequisites () returns trigger AS $$
DECLARE
    v_visited_courses UUID[];
    v_current_prerequisites UUID[];
    v_max_depth CONSTANT INT := 10;
    v_current_depth INT := 0;
BEGIN
    -- Start with the direct prerequisites
    SELECT ARRAY[NEW.prerequisite_id] INTO v_visited_courses;
    
    -- Keep checking prerequisites of prerequisites
    LOOP
        -- Prevent extremely deep prerequisite chains
        v_current_depth := v_current_depth + 1;
        IF v_current_depth > v_max_depth THEN
            RAISE EXCEPTION 'Prerequisite chain too deep (max depth: %)', v_max_depth;
        END IF;
        
        -- Get prerequisites of all courses we've seen
        WITH RECURSIVE prereqs AS (
            SELECT DISTINCT cp.prerequisite_id AS course_id
            FROM public.community_course_prerequisites cp
            WHERE cp.course_id = ANY(v_visited_courses)
        )
        SELECT array_agg(DISTINCT course_id) INTO v_current_prerequisites
        FROM prereqs
        WHERE course_id IS NOT NULL;
        
        -- If no more prerequisites OR we've seen them all, we're done
        IF v_current_prerequisites IS NULL OR (
            SELECT bool_and(elem = ANY(v_visited_courses))
            FROM unnest(v_current_prerequisites) AS elem
        ) THEN
            EXIT;
        END IF;
        
        -- If we find our starting course, we have a cycle
        IF NEW.course_id = ANY(v_current_prerequisites) THEN
            RAISE EXCEPTION 'Circular prerequisite dependency detected: Course % is part of a prerequisite chain that leads back to itself', NEW.course_id;
        END IF;
        
        -- Add these prerequisites to seen courses
        v_visited_courses := array_cat(v_visited_courses, v_current_prerequisites);
    END LOOP;
    
    RETURN NEW;
END;
$$ language plpgsql;

GRANT
EXECUTE ON function public.check_circular_community_course_prerequisites () TO authenticated;

CREATE TRIGGER prevent_circular_community_course_prerequisites_trigger
AFTER insert
OR
UPDATE ON public.community_course_prerequisites FOR each ROW
EXECUTE function public.check_circular_community_course_prerequisites ();
