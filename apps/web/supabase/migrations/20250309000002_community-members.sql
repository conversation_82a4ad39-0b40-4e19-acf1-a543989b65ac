/* -------------------------------------------------------
* Section: Community Members
* Create the functions to support getting community members
* -------------------------------------------------------
*/
-- INSERT new permissions
BEGIN;

ALTER TYPE public.community_permissions
ADD value 'community.members.remove';

ALTER TYPE public.community_permissions
ADD value 'community.members.send_email';

ALTER TYPE public.community_permissions
ADD value 'community.members.ban_member';

COMMIT;

-- GRANT permissions TO the owner role
INSERT INTO
  public.community_role_permissions (role, permission)
VALUES
  ('owner', 'community.members.remove'),
  ('owner', 'community.members.send_email'),
  ('owner', 'community.members.ban_member'),
  ('admin', 'community.members.remove'),
  ('admin', 'community.members.send_email'),
  ('admin', 'community.members.ban_member'),
  ('moderator', 'community.members.ban_member');

-- Functions "public.get_community_team_members"
-- Function to get the team members of a community by the community slug
CREATE OR REPLACE FUNCTION public.get_community_team_members (p_community_id uuid) returns TABLE (
  user_id uuid,
  community_id uuid,
  role VARCHAR(50),
  role_hierarchy_level INT,
  primary_owner_user_id uuid,
  first_name VARCHAR,
  last_name VARCHAR,
  username TEXT,
  picture_url VARCHAR,
  user_details JSONB,
  country VARCHAR(100),
  country_icon VARCHAR(100),
  last_sign_in_at TIMESTAMPTZ,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE
)
SET
  search_path = '' AS $$
BEGIN
    RETURN QUERY
    SELECT
        pcm.user_id,
        pcm.community_id,
        pcm.community_role,
        cr.hierarchy_level,
        pc.primary_owner_user_id,
        pp.first_name,
        pp.last_name,
        pp.username,
        pp.picture_url,
        pp.user_details,
        pp.country,
        pp.country_icon,
        pp.last_sign_in_at,
        pcm.created_at,
        pcm.updated_at
    FROM
        public.public_community_memberships pcm
        JOIN public.public_communities_view pc ON pc.id = pcm.community_id
        JOIN public.community_roles cr ON cr.name = pcm.community_role
        JOIN public.public_profiles pp ON pp.id = pcm.user_id
    WHERE
        pc.id = p_community_id
        AND pcm.community_role NOT IN ('free_member', 'paid_member');

END;
$$ language plpgsql;

GRANT
EXECUTE ON function public.get_community_team_members (uuid) TO anon,
authenticated,
service_role;

-- Functions "public.get_community_members_only"
-- Function to get the members of a community by the community slug
CREATE OR REPLACE FUNCTION public.get_community_members_only (p_community_id uuid) returns TABLE (
  user_id uuid,
  community_id uuid,
  role VARCHAR(50),
  role_hierarchy_level INT,
  primary_owner_user_id uuid,
  first_name VARCHAR,
  last_name VARCHAR,
  username TEXT,
  picture_url VARCHAR,
  user_details JSONB,
  country VARCHAR(100),
  country_icon VARCHAR(100),
  last_sign_in_at TIMESTAMPTZ,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE
)
SET
  search_path = '' AS $$
BEGIN
    RETURN QUERY
    SELECT
        pcm.user_id,
        pcm.community_id,
        pcm.community_role,
        r.hierarchy_level,
        pc.primary_owner_user_id,
        pp.first_name,
        pp.last_name,
        pp.username,
        pp.picture_url,
        pp.user_details,
        pp.country,
        pp.country_icon,
        pp.last_sign_in_at,
        pcm.created_at,
        pcm.updated_at
    FROM
        public.public_community_memberships pcm
        JOIN public.public_communities_view pc ON pc.id = pcm.community_id
        JOIN public.community_roles r ON r.name = pcm.community_role
        JOIN public.public_profiles pp ON pp.id = pcm.user_id
    WHERE
        pc.id = p_community_id
        AND pcm.community_role IN ('free_member', 'paid_member');

END;
$$ language plpgsql;

GRANT
EXECUTE ON function public.get_community_members_only (uuid) TO anon,
authenticated,
service_role;
