/* -------------------------------------------------------
* Section: Membership Helper Functions
* Create the functions to support membership
* -------------------------------------------------------
*/
/*
 * Function: get_user_from_membership
 * 
 * Retrieves the user details associated with a given membership ID.
 * 
 * Parameters:
 *   p_member_id - UUID of the community membership
 * 
 * Returns:
 *   A single record from the public.users table corresponding to the membership
 */
CREATE OR REPLACE FUNCTION public.get_user_from_membership (p_member_id uuid) returns public.users AS $$
BEGIN
    RETURN (
        SELECT u.*
        FROM public.users u
        JOIN public.community_memberships cm ON cm.user_id = u.id
        WHERE cm.id = p_member_id
    );
END;
$$ language plpgsql;

GRANT
EXECUTE ON function public.get_user_from_membership (uuid) TO service_role,
authenticated;

/*
 * Function: get_membership_from_user_and_community
 * 
 * Retrieves the membership ID for a given user and community combination.
 * 
 * Parameters:
 *   p_user_id - UUID of the user
 *   p_community_id - UUID of the community
 * 
 * Returns:
 *   UUID of the membership if found, null otherwise
 */
CREATE OR REPLACE FUNCTION public.get_membership_from_user_and_community (p_user_id uuid, p_community_id uuid) returns uuid AS $$
DECLARE
    v_member_id UUID;
BEGIN
    SELECT id INTO v_member_id
    FROM public.community_memberships
    WHERE user_id = p_user_id
    AND community_id = p_community_id;
    
    RETURN v_member_id;
END;
$$ language plpgsql;

GRANT
EXECUTE ON function public.get_membership_from_user_and_community (uuid, uuid) TO service_role,
authenticated;

/*
 * Function: get_active_membership_details
 * 
 * Retrieves detailed membership information including role hierarchy for an active user in a community.
 * 
 * Parameters:
 *   p_user_id - UUID of the user
 *   p_community_id - UUID of the community
 * 
 * Returns:
 *   Table containing:
 *     - member_id: UUID of the member
 *     - user_id: UUID of the user
 *     - community_id: UUID of the community
 *     - role: VARCHAR(50) representing the user's role name
 *     - role_hierarchy_level: INT indicating the role's position in hierarchy
 *     - created_at: TIMESTAMPTZ when the membership was created
 */
CREATE OR REPLACE FUNCTION public.get_active_membership_details (p_user_id uuid, p_community_id uuid) returns TABLE (
  member_id uuid,
  user_id uuid,
  community_id uuid,
  role VARCHAR(50),
  role_hierarchy_level INT,
  created_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cm.id AS member_id,
        cm.user_id,
        cm.community_id,
        cm.community_role AS role,
        r.hierarchy_level AS role_hierarchy_level,
        cm.created_at
    FROM public.community_memberships cm
    JOIN public.community_roles r ON r.name = cm.community_role
    WHERE cm.user_id = p_user_id
    AND cm.community_id = p_community_id
    AND cm.is_active = TRUE;
END;
$$ language plpgsql;

GRANT
EXECUTE ON function public.get_active_membership_details (uuid, uuid) TO service_role,
authenticated;

/*
 * Function: compare_user_role_to_content_role
 * 
 * Compares a user's role level against a content role level to determine access rights.
 * Lower hierarchy_level numbers indicate higher privileges.
 * 
 * Parameters:
 *   p_user_id - UUID of the user
 *   p_community_id - UUID of the community
 *   p_content_role - VARCHAR(50) role name required for the content
 * 
 * Returns:
 *   Boolean:
 *     - true if user's role has equal or higher privileges than the content role
 *     - false if user's role has lower privileges
 *     - null if either role is not found
 */
CREATE OR REPLACE FUNCTION public.compare_user_role_to_content_role (
  p_user_id uuid,
  p_community_id uuid,
  p_content_role VARCHAR(50)
) returns BOOLEAN AS $$
DECLARE
    v_user_role VARCHAR(50);
    v_user_role_level INT;
    v_content_role_level INT;
BEGIN
    -- Get user's current role and level
    SELECT 
        cm.community_role, 
        r.hierarchy_level
    INTO 
        v_user_role, 
        v_user_role_level
    FROM 
        public.community_memberships cm
    JOIN 
        public.community_roles r ON r.name = cm.community_role
    WHERE 
        cm.user_id = p_user_id
    AND 
        cm.community_id = p_community_id;
    
    -- Get content role level
    SELECT 
        hierarchy_level 
    INTO 
        v_content_role_level
    FROM 
        public.community_roles
    WHERE 
        name = p_content_role;

    IF v_user_role_level IS NULL OR v_content_role_level IS NULL THEN
        RETURN NULL;
    END IF;
    
    -- Return true if user role has higher or equal privileges
    RETURN v_user_role_level <= v_content_role_level;
END;
$$ language plpgsql;

GRANT
EXECUTE ON function public.compare_user_role_to_content_role (uuid, uuid, VARCHAR) TO service_role,
authenticated;

/*
 * Function: add_user_to_community
 * 
 * Adds a user to a community based on the community's product_price.
 * Checks that the user has a subscription or invoice to the community.
 * 
 * Parameters:
 *   p_community_id - UUID of the community
 *   p_user_id - UUID of the user
 *   p_latest_invoice_id - TEXT of the invoice id (always present for both types)
 *   p_subscription_id - TEXT of the subscription id (only for subscription type, null for one-time)
 *   p_price_id - TEXT of the price id
 *   p_order_type - 'subscription' | 'one-time'
 */
CREATE TYPE public.order_type AS ENUM('subscription', 'one-time');

comment ON type public.order_type IS 'The type of order for a community membership';

CREATE OR REPLACE FUNCTION public.add_user_to_community (
  p_community_id uuid,
  p_user_id uuid,
  p_order_type public.order_type,
  p_price_id TEXT,
  p_latest_invoice_id TEXT,
  p_subscription_id TEXT DEFAULT NULL
) returns uuid language plpgsql security invoker
SET
  search_path = '' AS $$
DECLARE
    v_existing_member_id UUID;
    v_unit_amount INT;
    v_community_role VARCHAR(50);
    v_subscription_id TEXT;
    v_invoice_id TEXT;
BEGIN
    -- Check if the required parameters are not null
    IF p_community_id IS NULL OR p_user_id IS NULL THEN
        RAISE EXCEPTION 'Required parameters cannot be NULL';
    END IF;

    -- Check if the user is already a member
    SELECT id INTO v_existing_member_id
    FROM public.community_memberships
    WHERE user_id = p_user_id
    AND community_id = p_community_id;
    
    IF v_existing_member_id IS NOT NULL THEN
        RETURN v_existing_member_id; -- User is already a member, silently succeed
    END IF;

    -- Check if the community has non-free product_price
    SELECT pp.unit_amount
    INTO v_unit_amount
    FROM public.communities c
    JOIN public.product_prices pp ON pp.product_id = c.default_product_id
    WHERE c.id = p_community_id;

    -- Handle missing default product
    IF v_unit_amount IS NULL THEN
        RAISE EXCEPTION 'Community % has no default product configured', p_community_id;
    ELSIF v_unit_amount = 0 THEN
        -- Give the role for free membership
        v_community_role = 'free_member';
        v_subscription_id = NULL;
        v_invoice_id = NULL;
        -- For free communities, no payment validation needed
    ELSE
        -- Give the role for paid membership
        v_community_role = 'paid_member';
        
        -- Always validate that the invoice exists (both subscription and one-time have invoices)
        SELECT id INTO v_invoice_id
        FROM public.invoices
        WHERE user_id = p_user_id
        AND community_id = p_community_id
        AND id = p_latest_invoice_id
        AND status = 'succeeded';

        IF v_invoice_id IS NULL THEN
            RAISE EXCEPTION 'User % does not have a valid invoice % to community %', 
                p_user_id, p_latest_invoice_id, p_community_id;
        END IF;

        IF p_order_type = 'subscription' THEN
            -- For subscriptions, also validate that the subscription exists
            IF p_subscription_id IS NULL THEN
                RAISE EXCEPTION 'Subscription ID is required for subscription order type';
            END IF;

            SELECT id INTO v_subscription_id
            FROM public.subscriptions
            WHERE user_id = p_user_id
            AND community_id = p_community_id
            AND id = p_subscription_id
            AND active = TRUE
            AND status IN ('active', 'trialing');

            IF v_subscription_id IS NULL THEN
                RAISE EXCEPTION 'User % does not have an active subscription % to community %', 
                    p_user_id, p_subscription_id, p_community_id;
            END IF;
        END IF;
    END IF;

    -- Insert membership with both subscription_id and latest_invoice_id as appropriate
    INSERT INTO public.community_memberships (
        user_id,
        community_id,
        community_role,
        status,
        subscription_id,
        latest_invoice_id,
        price_id
    ) VALUES (
        p_user_id,
        p_community_id,
        v_community_role,
        'active',
        p_subscription_id, -- Will be null for one-time purchases
        p_latest_invoice_id, -- Always present for both types
        p_price_id
    ) RETURNING id INTO v_existing_member_id;

    RETURN v_existing_member_id;
END;
$$;

GRANT
EXECUTE ON function public.add_user_to_community (uuid, uuid, public.order_type, TEXT, TEXT, TEXT) TO service_role,
authenticated;
