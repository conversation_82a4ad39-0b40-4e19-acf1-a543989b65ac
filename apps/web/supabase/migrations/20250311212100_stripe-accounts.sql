/* -------------------------------------------------------
* Section: Stripe Accounts v2
* Create the tables and functions to support Stripe Accounts v2 integration
* -------------------------------------------------------
*/
-- Create stripe_accounts table for Stripe Accounts v2 integration
-- Description: This migration creates the stripe_accounts table to store information about
-- Stripe Accounts v2 associated with Users. Supports unified customer/merchant/recipient accounts.
-- Add community_permissions to support Stripe Connect
BEGIN;

ALTER TYPE public.community_permissions
ADD value 'community.stripe_account.settings';

COMMIT;

-- ROLES
-- Update the community_role_permissions table with the new Stripe Connect permissions
BEGIN;

INSERT INTO
  public.community_role_permissions (role, permission)
VALUES
  ('owner', 'community.stripe_account.settings');

COMMIT;

-- Create stripe_accounts table for Stripe Accounts v2
CREATE TABLE IF NOT EXISTS public.stripe_accounts (
  id TEXT NOT NULL PRIMARY KEY,
  user_id uuid NOT NULL UNIQUE REFERENCES public.users (id) ON DELETE CASCADE,
  country_id uuid REFERENCES public.countries (id),
  applied_configurations TEXT[] DEFAULT '{}',
  contact_email TEXT,
  display_name TEXT,
  dashboard TEXT CHECK (dashboard IN ('express', 'full', 'none')),
  configuration JSONB DEFAULT '{}'::JSONB,
  capabilities JSONB DEFAULT '{}'::JSONB,
  requirements JSONB DEFAULT '{}'::JSONB,
  defaults JSONB DEFAULT '{}'::JSONB,
  identity JSONB DEFAULT '{}'::JSONB,
  metadata JSONB DEFAULT '{}'::JSONB,
  created_at TIMESTAMPTZ DEFAULT current_timestamp NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT current_timestamp NOT NULL
);

-- Table and column comments
comment ON TABLE public.stripe_accounts IS 'Stores information about Stripe Accounts v2, supporting unified customer/merchant/recipient accounts';

comment ON COLUMN public.stripe_accounts.id IS 'Stripe Account ID from Stripe v2 API';

comment ON COLUMN public.stripe_accounts.user_id IS 'The user that owns this stripe account';

comment ON COLUMN public.stripe_accounts.country_id IS 'Reference to the country where the stripe account is registered';

comment ON COLUMN public.stripe_accounts.applied_configurations IS 'Array of configurations: customer, merchant, recipient';

comment ON COLUMN public.stripe_accounts.contact_email IS 'The default contact email address for the Account';

comment ON COLUMN public.stripe_accounts.display_name IS 'Descriptive name shown in Stripe Dashboard and invoices';

comment ON COLUMN public.stripe_accounts.dashboard IS 'Stripe dashboard access level: express, full, or none';

comment ON COLUMN public.stripe_accounts.configuration IS 'Account Configuration object containing customer/merchant/recipient settings';

comment ON COLUMN public.stripe_accounts.capabilities IS 'Account capabilities with status information for each configuration';

comment ON COLUMN public.stripe_accounts.requirements IS 'Information about account requirements and collection deadlines';

comment ON COLUMN public.stripe_accounts.defaults IS 'Default values used on Account Configurations (currency, responsibilities, etc.)';

comment ON COLUMN public.stripe_accounts.identity IS 'Information about the company, individual, and business represented by the Account';

comment ON COLUMN public.stripe_accounts.metadata IS 'Custom key-value pairs attached to the account';

-- Set up timestamps trigger
CREATE TRIGGER set_stripe_accounts_timestamps before
UPDATE ON public.stripe_accounts FOR each ROW
EXECUTE function public.trigger_set_timestamps ();

-- Create indexes for frequently queried columns
CREATE INDEX idx_stripe_accounts_user_id ON public.stripe_accounts (user_id);

CREATE INDEX idx_stripe_accounts_country_id ON public.stripe_accounts (country_id);

-- Index on applied_configurations array for efficient querying
CREATE INDEX idx_stripe_accounts_applied_configurations ON public.stripe_accounts USING gin (applied_configurations);

-- JSONB indexes for commonly queried fields
-- Use btree for text extraction from JSONB, gin for full JSONB operations
CREATE INDEX idx_stripe_accounts_entity_type ON public.stripe_accounts USING btree ((identity ->> 'entity_type'));

CREATE INDEX idx_stripe_accounts_business_name ON public.stripe_accounts USING btree (
  (
    identity -> 'business_details' ->> 'registered_name'
  )
);

-- GIN index on full identity JSONB for complex queries
CREATE INDEX idx_stripe_accounts_identity_gin ON public.stripe_accounts USING gin (identity);

-- Add a new column to users table to store the stripe account id
ALTER TABLE public.users
ADD COLUMN stripe_account_id TEXT REFERENCES public.stripe_accounts (id);

comment ON COLUMN public.users.stripe_account_id IS 'The stripe account id for the user';

ALTER TABLE public.users
ADD CONSTRAINT users_stripe_account_id_unique UNIQUE (stripe_account_id);

-- RLS policies for stripe_accounts
ALTER TABLE public.stripe_accounts enable ROW level security;

ALTER TABLE public.stripe_accounts force ROW level security;

-- Grant necessary base permissions before RLS
-- Service role needs full access
GRANT
SELECT
,
  insert,
UPDATE,
delete ON public.stripe_accounts TO service_role;

-- Authenticated users need base permissions; RLS will restrict further
GRANT
SELECT
,
  insert,
UPDATE,
delete ON public.stripe_accounts TO authenticated;

-- RLS Policies
-- User can fully manage their OWN stripe account
CREATE POLICY "stripe_accounts_manage_owner" ON public.stripe_accounts FOR ALL TO authenticated USING (user_id = auth.uid ())
WITH
  CHECK (user_id = auth.uid ());

-- Service role can access all accounts (for webhooks and admin operations)
CREATE POLICY "stripe_accounts_service_role_all" ON public.stripe_accounts FOR ALL TO service_role USING (TRUE)
WITH
  CHECK (TRUE);

-- Helper functions for country conversion
CREATE OR REPLACE FUNCTION public.get_country_id_by_iso (iso_code TEXT) returns uuid language plpgsql security invoker
SET
  search_path = '' AS $$
DECLARE
    country_uuid uuid;
BEGIN
    SELECT id INTO country_uuid 
    FROM public.countries 
    WHERE iso_code_1 = UPPER(iso_code);
    
    RETURN country_uuid;
END;
$$;

GRANT
EXECUTE ON function public.get_country_id_by_iso (TEXT) TO authenticated,
service_role;

CREATE OR REPLACE FUNCTION public.get_iso_code_by_country_id (country_uuid uuid) returns TEXT language plpgsql security invoker
SET
  search_path = '' AS $$
DECLARE
    iso_code TEXT;
BEGIN
    SELECT iso_code_1 INTO iso_code 
    FROM public.countries 
    WHERE id = country_uuid;
    
    RETURN iso_code;
END;
$$;

GRANT
EXECUTE ON function public.get_iso_code_by_country_id (uuid) TO authenticated,
service_role;
