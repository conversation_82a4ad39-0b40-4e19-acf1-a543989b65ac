/* -------------------------------------------------------
* Section: Checkout Sessions Tables
* Tables to store Stripe checkout sessions
* -------------------------------------------------------
*/
-- Create a type for session status
CREATE TYPE public.checkout_session_status AS ENUM('open', 'complete', 'expired');

-- Checkout sessions table
CREATE TABLE checkout_sessions (
  id TEXT PRIMARY KEY,
  user_id uuid REFERENCES public.users (id) NOT NULL,
  community_id uuid,
  community_name TEXT,
  price_id TEXT REFERENCES public.product_prices (id) NOT NULL,
  purchase_type public.purchase_type NOT NULL,
  trial_days INTEGER NOT NULL DEFAULT 0,
  metadata JSONB NOT NULL,
  status public.checkout_session_status NOT NULL DEFAULT 'open',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT current_timestamp,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT current_timestamp
);

comment ON TABLE public.checkout_sessions IS 'Checkout sessions from stripe';

comment ON COLUMN public.checkout_sessions.id IS 'The id of the checkout session from stripe';

comment ON COLUMN public.checkout_sessions.user_id IS 'The id of the user who created the checkout session';

comment ON COLUMN public.checkout_sessions.community_id IS 'The id of the community that the checkout session is for';

comment ON COLUMN public.checkout_sessions.price_id IS 'The id of the price that the checkout session is for';

comment ON COLUMN public.checkout_sessions.purchase_type IS 'The type of purchase that the checkout session is for';

comment ON COLUMN public.checkout_sessions.trial_days IS 'The number of trial days that the checkout session is for';

comment ON COLUMN public.checkout_sessions.metadata IS 'The metadata of the checkout session';

REVOKE ALL ON public.checkout_sessions
FROM
  authenticated,
  service_role;

GRANT
SELECT
,
  insert,
UPDATE,
delete ON public.checkout_sessions TO authenticated,
service_role;

ALTER TABLE public.checkout_sessions enable ROW level security;

-- RLS policies for checkout sessions
CREATE POLICY "checkout_sessions_select_owner" ON public.checkout_sessions FOR
SELECT
  TO authenticated USING (auth.uid () = user_id);

CREATE POLICY "checkout_sessions_insert_owner" ON public.checkout_sessions FOR insert TO authenticated
WITH
  CHECK (auth.uid () = user_id);

CREATE POLICY "checkout_sessions_update_owner" ON public.checkout_sessions
FOR UPDATE
  TO authenticated USING (auth.uid () = user_id)
WITH
  CHECK (auth.uid () = user_id);

CREATE POLICY "checkout_sessions_delete_owner" ON public.checkout_sessions FOR delete TO authenticated USING (auth.uid () = user_id);

CREATE TRIGGER set_timestamp_checkout_sessions before
UPDATE ON public.checkout_sessions FOR each ROW
EXECUTE procedure public.trigger_set_timestamps ();
