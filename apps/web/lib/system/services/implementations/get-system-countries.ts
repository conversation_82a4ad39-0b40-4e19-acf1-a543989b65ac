import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

import { Country } from '../../types';

const NAMESPACE = 'core.service';

/**
 * Gets the system countries
 */
export async function getSystemCountries(
  client: SupabaseClient<Database>,
): Promise<{ countries: Country[] }> {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    function: 'getSystemCountries',
  };

  logger.info(ctx, 'Getting system countries...');
  const { data, error } = await client.rpc('get_countries');

  if (error) {
    logger.error({ ...ctx, error }, 'Error getting system countries:');
    throw error;
  }

  return {
    countries: objectToCamel(data) ?? [],
  };
}
