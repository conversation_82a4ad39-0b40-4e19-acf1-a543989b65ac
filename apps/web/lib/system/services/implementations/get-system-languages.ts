import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

import { Language } from '../../types';

const NAMESPACE = 'core.service';

/**
 * Gets the system languages
 */
export async function getSystemLanguages(
  client: SupabaseClient<Database>,
): Promise<{ languages: Language[] }> {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    function: 'getSystemLanguages',
  };

  logger.info(ctx, 'Getting system languages...');
  const { data, error } = await client.rpc('get_languages');

  if (error) {
    logger.error({ ...ctx, error }, 'Error getting system languages:');
    throw error;
  }

  return {
    languages: objectToCamel(data) ?? [],
  };
}
