import { SupabaseClient } from '@supabase/supabase-js';

import type { Database } from '~/lib/database.types';

import { Country, Language } from '../types';
import * as implementations from './implementations';

/**
 * @name createCoreService
 * @description Creates a new instance of the CoreService
 * @param client The Supabase client instance
 * @returns A new CoreService instance
 */
export function createSystemService(client: SupabaseClient<Database>) {
  return new SystemService(client);
}

/**
 * @name SystemService
 * @description Service for managing system data in the application
 * @example
 * const client = getSupabaseClient();
 * const systemService = createSystemService(client);
 */
export class SystemService {
  constructor(private readonly client: SupabaseClient<Database>) {}

  /**
   * @name getSystemLanguages
   * @description Get the system languages
   */
  async getSystemLanguages(): Promise<{ languages: Language[] }> {
    return implementations.getSystemLanguages(this.client);
  }

  /**
   * @name getSystemCountries
   * @description Get the system countries
   */
  async getSystemCountries(): Promise<{ countries: Country[] }> {
    return implementations.getSystemCountries(this.client);
  }
}
