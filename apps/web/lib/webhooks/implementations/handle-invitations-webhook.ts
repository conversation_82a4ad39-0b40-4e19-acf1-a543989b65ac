import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { objectToCamel } from '@kit/shared/utils';
import type { Database } from '@kit/supabase/database';

import { createCommunityInvitationsWebhookService } from '../../communities/invitations/webhooks';
import { RecordChange } from '../record-change.type';

/**
 * Handles webhook events for community invitations
 * @param adminClient - The Supabase admin client
 * @param body - The webhook payload
 */
export async function handleInvitationsWebhookImpl(
  adminClient: SupabaseClient<Database>,
  body: RecordChange<'community_invitations'>,
): Promise<unknown> {
  const service = createCommunityInvitationsWebhookService(adminClient);

  return service.handleInvitationWebhook(objectToCamel(body.record));
}
