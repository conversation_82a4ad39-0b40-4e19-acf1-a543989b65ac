import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';
import type { Database } from '@kit/supabase/database';

import { createCommunityMailerService } from '../../communities/mailer';
import { RecordChange } from '../record-change.type';

/**
 * Handles webhook events for communities table
 * @param adminClient - The Supabase admin client
 * @param body - The webhook payload
 */
export async function handleCommunitiesWebhookImpl(
  adminClient: SupabaseClient<Database>,
  body: RecordChange<'communities'>,
): Promise<unknown> {
  const logger = await getLogger();
  try {
    if (body.type === 'DELETE' && body.old_record) {
      const service = createCommunityMailerService();

      const primaryOwnerUser = await adminClient
        .from('users')
        .select('*')
        .eq('id', body.old_record.primary_owner_user_id)
        .single();

      // Check if primaryOwnerUser.data exists before proceeding
      if (!primaryOwnerUser.data) {
        logger.error(
          {
            communityId: body.old_record.id,
            userId: body.old_record.primary_owner_user_id,
          },
          'Failed to find primary owner user for deleted community',
        );
        return;
      }

      return service.handleCommunityDeletedWebhook(
        objectToCamel(body.old_record),
        objectToCamel(primaryOwnerUser.data),
      );
    }
  } catch (error) {
    logger.error({ error }, 'Failed to handle communities webhook');
  }
}
