import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import type { Database } from '@kit/supabase/database';

import { getStripeSubscriptionService } from '~/lib/stripe/services';

import { RecordChange } from '../record-change.type';

/**
 * Handles webhook events for community memberships table
 * @param adminClient - The Supabase admin client
 * @param body - The webhook payload
 */
export async function handleCommunityMembershipsWebhookImpl(
  adminClient: SupabaseClient<Database>,
  body: RecordChange<'community_memberships'>,
): Promise<void> {
  const logger = await getLogger();
  if (
    body.type === 'DELETE' &&
    body.old_record &&
    body.old_record.subscription_id
  ) {
    const stripeSubscriptionService = getStripeSubscriptionService(adminClient);

    try {
      await stripeSubscriptionService.cancelSubscription({
        subscriptionId: body.old_record.subscription_id,
        invoiceNow: false,
      });
    } catch (error) {
      logger.error(
        {
          subscriptionId: body.old_record.subscription_id,
          communityId: body.old_record.community_id,
          error,
          name: 'database-webhook-router',
          action: 'handleCommunityMembershipsWebhook',
        },
        'Failed to cancel Stripe subscription for membership deletion',
      );
    }
  }
}
