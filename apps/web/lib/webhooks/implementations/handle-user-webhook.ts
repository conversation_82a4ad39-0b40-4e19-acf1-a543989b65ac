import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import type { Database } from '@kit/supabase/database';

import { getStripeCustomerService } from '~/lib/stripe/services';

import { RecordChange } from '../record-change.type';

/**
 * Handles webhook events for users table
 * Creates a Stripe account for new users
 * @param adminClient - The Supabase admin client
 * @param body - The webhook payload
 */
export async function handleUserWebhookImpl(
  adminClient: SupabaseClient<Database>,
  body: RecordChange<'users'>,
): Promise<void> {
  // Handle public.users table events (new user registration)
  if (body.type === 'INSERT' && body.record) {
    const userRecord = body.record;

    const logger = await getLogger();
    const logContext = {
      userId: userRecord.id,
      name: 'database-webhook-router',
      action: 'handleUserCreatedWebhook',
    };

    try {
      logger.info(logContext, 'Creating Stripe account for new user');

      const stripeCustomerService = getStripeCustomerService(adminClient);

      // Check if stripe account already exists in our database
      if (userRecord.stripe_account_id) {
        logger.info(
          { ...logContext, stripeAccountId: userRecord.stripe_account_id },
          'User already has a Stripe account',
        );
        return;
      }

      // Create a stripe account in Stripe for the new user
      const stripeAccountId =
        await stripeCustomerService.getOrCreateStripeAccount({
          userId: userRecord.id,
        });

      logger.info(
        { ...logContext, stripeAccountId },
        'Successfully created Stripe account for new user',
      );
    } catch (error) {
      logger.error(
        { ...logContext, error },
        'Failed to create Stripe account for new user',
      );

      // Even if this fails, the Stripe webhook handler will catch the account.created event
      // and save the account to the database, ensuring this is eventually consistent
    }
  }
}
