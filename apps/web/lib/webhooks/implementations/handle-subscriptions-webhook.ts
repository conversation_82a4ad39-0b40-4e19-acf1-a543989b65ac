import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import type { Database } from '@kit/supabase/database';

import { getStripeSubscriptionService } from '~/lib/stripe/services';

import { RecordChange } from '../record-change.type';

/**
 * Handles webhook events for subscriptions table
 * @param adminClient - The Supabase admin client
 * @param body - The webhook payload
 */
export async function handleSubscriptionsWebhookImpl(
  adminClient: SupabaseClient<Database>,
  body: RecordChange<'subscriptions'>,
): Promise<unknown> {
  if (body.type === 'DELETE' && body.old_record) {
    const stripeSubscriptionService = getStripeSubscriptionService(adminClient);

    return stripeSubscriptionService.cancelSubscription({
      subscriptionId: body.old_record.id,
    });
  }
}
