import { SupabaseClient } from '@supabase/supabase-js';

import type { Database } from '@kit/supabase/database';

import { handleCommunitiesWebhookImpl } from './implementations/handle-communities-webhook';
import { handleCommunityMembershipsWebhookImpl } from './implementations/handle-community-memberships-webhook';
import { handleInvitationsWebhookImpl } from './implementations/handle-invitations-webhook';
import { handleSubscriptionsWebhookImpl } from './implementations/handle-subscriptions-webhook';
import { handleUserWebhookImpl } from './implementations/handle-user-webhook';
import { RecordChange, Tables } from './record-change.type';

export function createDatabaseWebhookRouterService(
  adminClient: SupabaseClient<Database>,
) {
  return new DatabaseWebhookRouterService(adminClient);
}

/**
 * @name DatabaseWebhookRouterService
 * @description Service that routes the webhook event to the appropriate service
 */
class DatabaseWebhookRouterService {
  constructor(private readonly adminClient: SupabaseClient<Database>) {}

  /**
   * @name handleWebhook
   * @description Handle the webhook event
   * @param body
   */
  async handleWebhook(body: RecordChange<keyof Tables>) {
    switch (body.table) {
      case 'community_invitations': {
        const payload = body as RecordChange<typeof body.table>;
        return handleInvitationsWebhookImpl(this.adminClient, payload);
      }

      case 'subscriptions': {
        const payload = body as RecordChange<typeof body.table>;
        return handleSubscriptionsWebhookImpl(this.adminClient, payload);
      }

      case 'communities': {
        const payload = body as RecordChange<typeof body.table>;
        return handleCommunitiesWebhookImpl(this.adminClient, payload);
      }

      case 'community_memberships': {
        const payload = body as RecordChange<typeof body.table>;
        return handleCommunityMembershipsWebhookImpl(this.adminClient, payload);
      }

      case 'users': {
        const payload = body as RecordChange<typeof body.table>;
        return handleUserWebhookImpl(this.adminClient, payload);
      }

      default: {
        return;
      }
    }
  }
}
