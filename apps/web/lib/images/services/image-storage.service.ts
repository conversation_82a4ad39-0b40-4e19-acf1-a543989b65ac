'server-only';

import type {
  ImageServiceClient,
  ImageUploadResult,
  UploadImageContext,
} from '../types';
import * as implementations from './implementations';

/**
 * Creates a new image storage service instance
 * @param client The image service client
 * @returns A new ImageStorageService instance
 */
export function createImageStorageService(client: ImageServiceClient) {
  return new ImageStorageService(client);
}

/**
 * Service for managing image storage operations
 */
export class ImageStorageService {
  constructor(private readonly client: ImageServiceClient) {}

  /**
   * Uploads an image file to storage
   * @param file The file to upload
   * @param context The context for the upload
   * @param bucketName Optional bucket name override
   * @returns The upload result containing the public URL and path
   */
  async uploadImage(
    file: File,
    context: UploadImageContext,
    bucketName?: string,
  ): Promise<ImageUploadResult> {
    return implementations.uploadImage(this.client, file, context, bucketName);
  }

  /**
   * Deletes an image from storage
   * @param path The path to the image
   * @param bucket The bucket containing the image
   */
  async deleteImage(path: string, bucket: string): Promise<void> {
    return implementations.deleteImage(this.client, path, bucket);
  }
}
