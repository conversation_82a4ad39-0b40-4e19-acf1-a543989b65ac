import 'server-only';

import { getLogger } from '@kit/shared/logger';

import type { ImageServiceClient } from '../../types';

const NAMESPACE = 'image-storage.service';

/**
 * Deletes an image from storage
 *
 * @param client The image service client
 * @param path The path to the image
 * @param bucket The bucket containing the image
 */
export async function deleteImage(
  client: ImageServiceClient,
  path: string,
  bucket: string,
): Promise<void> {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    path,
    bucket,
  };

  logger.info(ctx, 'Deleting image...');

  try {
    const { error } = await client.storage.from(bucket).remove([path]);

    if (error) {
      logger.error({ ...ctx, error }, 'Failed to delete image');
      throw error;
    }

    logger.info(ctx, 'Successfully deleted image');
  } catch (error) {
    logger.error({ ...ctx, error }, 'Failed to delete image');
    throw error;
  }
}
