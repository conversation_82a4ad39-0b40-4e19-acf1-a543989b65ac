import 'server-only';

import { getLogger } from '@kit/shared/logger';

import { STORAGE_CONFIG } from '../../constants';
import type {
  ImageServiceClient,
  ImageUploadResult,
  UploadImageContext,
} from '../../types';
import { generateUniqueFilename, validateImage } from '../../utils/validation';

const NAMESPACE = 'image-storage.service';

/**
 * Uploads an image file to storage
 *
 * @param client The image service client
 * @param file The file to upload
 * @param context The context for the upload
 * @param bucketName Optional bucket name override
 * @returns The upload result containing the public URL and path
 */
export async function uploadImage(
  client: ImageServiceClient,
  file: File,
  context: UploadImageContext,
  bucketName?: string,
): Promise<ImageUploadResult> {
  const logger = await getLogger();

  const targetBucket = bucketName ?? STORAGE_CONFIG.bucket;

  const ctx = {
    namespace: NAMESPACE,
    bucket: targetBucket,
    context,
  };

  logger.info(ctx, 'Uploading image...');

  try {
    const config = STORAGE_CONFIG;

    validateImage(file, config);

    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    const filename = context.targetFilename
      ? context.targetFilename
      : generateUniqueFilename(file.name);

    const storagePath = config.pathBuilder(context, filename);

    const { error: uploadError } = await client.storage
      .from(targetBucket)
      .upload(storagePath, buffer, {
        contentType: file.type,
        upsert: true,
      });

    if (uploadError) {
      logger.error({ ...ctx, error: uploadError }, 'Failed to upload image');
      throw uploadError;
    }

    const { data: publicUrlData } = client.storage
      .from(targetBucket)
      .getPublicUrl(storagePath);

    const finalUrl = `${publicUrlData.publicUrl}?t=${Date.now()}`;

    const result: ImageUploadResult = {
      url: finalUrl,
      path: storagePath,
      bucket: targetBucket,
    };

    logger.info({ ...ctx, result }, 'Successfully uploaded image');

    return result;
  } catch (error) {
    logger.error({ ...ctx, error }, 'Failed to upload image');
    throw error;
  }
}
