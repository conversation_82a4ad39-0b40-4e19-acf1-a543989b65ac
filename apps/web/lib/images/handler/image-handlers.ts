'use client';

import type { TipTapImageUploadContext } from '@kit/ui/dojo/organisms/tiptap-editor';
import {
  createTipTapImageDeleteHandler as createBaseImageDeleteHandler,
  createTipTapImageUploadHandler as createBaseImageUploadHandler,
} from '@kit/ui/dojo/organisms/tiptap-editor';

import { deleteImageAction } from '../actions/delete-image';
import { uploadImageAction } from '../actions/upload-image';
import { COMMUNITY_STORAGE_BUCKET } from '../constants';
import type { DeleteImageContext } from '../types';

/**
 * Simplified context type for image handlers
 */
export type StoragePathContext = {
  featureDir?: string;
  storageDirPath?: string;
  communityId?: string;
};

/**
 * Extracts the path and bucket from a Supabase storage URL
 * @param url - The URL to extract from
 * @returns The path and bucket, or null if extraction fails
 */
export function extractPathAndBucketFromUrl(
  url: string,
): DeleteImageContext | null {
  try {
    const urlObj = new URL(url);
    // URL structure is like: https://domain.com/storage/v1/object/public/community_storage/path/to/image.jpg
    const pathParts = urlObj.pathname.split('/');
    const bucketIndex = pathParts.findIndex((part) => part === 'public') + 1;

    if (bucketIndex > 0 && bucketIndex < pathParts.length) {
      const bucket = pathParts[bucketIndex];
      const path = pathParts.slice(bucketIndex + 1).join('/');

      if (bucket && path) {
        return { bucket, path };
      }
    }
    return null;
  } catch (error) {
    console.error('Failed to parse image URL:', error);
    return null;
  }
}

/**
 * Creates an image upload handler for the TipTap editor
 * @param context - The storage path context with storageDirPath and communitySlug
 * @returns A function that handles image uploads
 */
export function createImageUploadHandler(context: StoragePathContext) {
  // Adapt our context to the TipTap context type
  const adaptedContext: TipTapImageUploadContext = {
    contextId: context.storageDirPath ?? '',
    communityId: context.communityId ?? '',
  };

  // Call the renamed base handler from the UI package
  return createBaseImageUploadHandler(adaptedContext, {
    onUpload: async (file: File) => {
      // Use our full context structure when calling the action
      const result = await uploadImageAction({
        file,
        context: {
          featureDir: context.featureDir,
          storageDirPath: context.storageDirPath,
          communityId: context.communityId,
        },
      });

      if (!result?.url) {
        throw new Error(
          'Image upload failed: Server did not return a valid URL',
        );
      }

      return { url: result.url };
    },
  });
}

/**
 * Creates an image delete handler for the TipTap editor
 * @param context - The storage path context with storageDirPath and communitySlug
 * @returns A function that handles image deletions
 */
export function createImageDeleteHandler(context: StoragePathContext) {
  // Adapt our context to the TipTap context type
  const adaptedContext: TipTapImageUploadContext = {
    contextId: context.storageDirPath ?? '',
    communityId: context.communityId ?? '',
  };

  // Call the renamed base handler from the UI package
  return createBaseImageDeleteHandler(adaptedContext, {
    onDelete: async (src: string) => {
      const pathAndBucket = extractPathAndBucketFromUrl(src);

      if (!pathAndBucket) {
        console.error('Failed to extract path and bucket from URL:', src);
        throw new Error('Failed to extract path and bucket from URL');
      }

      // Always use community_storage bucket regardless of what's in the URL
      // This ensures compatibility as we transition to the new storage structure
      if (pathAndBucket.bucket !== COMMUNITY_STORAGE_BUCKET) {
        console.warn(
          `URL uses bucket '${pathAndBucket.bucket}' but we're using '${COMMUNITY_STORAGE_BUCKET}' instead.`,
        );
        pathAndBucket.bucket = COMMUNITY_STORAGE_BUCKET;
      }

      await deleteImageAction(pathAndBucket);
    },
  });
}
