import type {
  ImageStorageConfig,
  ImageValidationError,
  // UploadImageContext, // Removed as it's no longer used
} from '../types';

/**
 * Validates an image file against the storage configuration
 * @param file The file to validate
 * @param config The storage configuration
 * @param context The image context
 * @returns void if valid, throws ImageValidationError if invalid
 */
export function validateImage(file: File, config: ImageStorageConfig): void {
  const errors: ImageValidationError[] = [];

  // Check file type
  if (!config.allowedTypes.includes(file.type)) {
    errors.push({
      code: 'INVALID_TYPE',
      message: `Invalid file type. Allowed types: ${config.allowedTypes.join(
        ', ',
      )}`,
    });
  }

  // Check file size
  if (file.size > config.maxSize) {
    errors.push({
      code: 'INVALID_SIZE',
      message: `File size exceeds ${config.maxSize / (1024 * 1024)}MB limit.`,
    });
  }

  if (errors.length > 0) {
    throw errors[0]; // Throw the first error encountered
  }
}

/**
 * Generates a unique filename for an image
 * @param originalName The original filename
 * @returns A unique filename with timestamp
 */
export function generateUniqueFilename(originalName: string): string {
  const timestamp = Date.now();
  const sanitizedName = originalName.replace(/[^a-zA-Z0-9.-]/g, '_');
  return `${timestamp}-${sanitizedName}`;
}
