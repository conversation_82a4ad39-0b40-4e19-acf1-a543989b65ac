import type { SupabaseClient } from '@supabase/supabase-js';

import type { Database } from '~/lib/database.types';

/**
 * Represents the complete upload context for an image, including its storage path and community.
 * @property storageDirPath - The directory path where the image will be stored
 * @property communityId - Optional community identifier associated with the image
 * @property subPath - Optional sub-path for organizing images within a context
 * @property targetFilename - Optional specific filename to use
 */
export type UploadImageContext = {
  // The ID of the community the image belongs to. Made optional again for non-community uploads.
  communityId?: string;
  // The top-level feature directory. Made optional again for flexibility.
  featureDir?: string;
  // Represents the main directory identifier within the feature/community scope (e.g., a course ID, page ID, or user ID).
  storageDirPath?: string;
  // Optional sub-path for further nesting within the storageDirPath.
  subPath?: string;
  // Optional specific filename to use instead of a generated one.
  targetFilename?: string;
};

/**
 * Represents the complete delete context for an image, including its bucket and path.
 * @property bucket - The storage bucket where the image is saved
 * @property path - The storage path where the image is saved
 */
export type DeleteImageContext = {
  bucket: string;
  path: string;
};

/**
 * Represents the result of an image upload operation.
 * @property url - The public URL where the uploaded image can be accessed
 * @property path - The storage path where the image is saved
 * @property bucket - The storage bucket containing the image
 */
export type ImageUploadResult = {
  url: string;
  path: string;
  bucket: string;
};

export type ImageValidationError = {
  code:
    | 'INVALID_TYPE'
    | 'INVALID_SIZE'
    | 'MISSING_COMMUNITY'
    | 'MISSING_STORAGE_PATH'
    | 'UPLOAD_FAILED';
  message: string;
  details?: unknown;
};

/**
 * Enumeration of possible image validation error codes
 */
export const ImageErrorCode = {
  INVALID_TYPE: 'INVALID_TYPE',
  INVALID_SIZE: 'INVALID_SIZE',
  MISSING_COMMUNITY: 'MISSING_COMMUNITY',
  MISSING_STORAGE_PATH: 'MISSING_STORAGE_PATH',
  UPLOAD_FAILED: 'UPLOAD_FAILED',
} as const;

export type ImageErrorCode =
  (typeof ImageErrorCode)[keyof typeof ImageErrorCode];

/**
 * Configuration for image storage and validation.
 * @property bucket - The storage bucket where images will be saved
 * @property allowedTypes - List of permitted MIME types for images
 * @property maxSize - Maximum allowed file size in bytes
 * @property requiresAuth - Whether authentication is required for upload
 * @property pathBuilder - Function to generate storage path for images
 */
export type ImageStorageConfig = {
  bucket: string;
  allowedTypes: ReadonlyArray<string>;
  maxSize: number;
  requiresAuth: boolean;
  pathBuilder: (context: UploadImageContext, filename: string) => string;
};

/**
 * Type alias for a Supabase client configured with our database schema.
 * Used for image-related operations that require database access.
 */
export type ImageServiceClient = SupabaseClient<Database>;
