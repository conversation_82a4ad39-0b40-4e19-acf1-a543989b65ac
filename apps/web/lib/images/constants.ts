import type { ImageStorageConfig } from './types';

export const ALLOWED_IMAGE_TYPES = [
  'image/jpeg',
  'image/png',
  'image/webp',
  'image/svg+xml',
  'image/gif',
] as const;
export const MAX_IMAGE_SIZE = 5 * 1024 * 1024; // 5MB

// Single bucket for all community storage
export const COMMUNITY_STORAGE_BUCKET = 'community_storage';

// Simplified storage config that uses the community_storage bucket
export const STORAGE_CONFIG: ImageStorageConfig = {
  bucket: COMMUNITY_STORAGE_BUCKET,
  allowedTypes: ALLOWED_IMAGE_TYPES,
  maxSize: MAX_IMAGE_SIZE,
  requiresAuth: true,
  pathBuilder: (context, filename) => {
    // Construct the path segments
    const segments = [
      context.communityId,
      context.featureDir,
      context.storageDirPath,
    ];

    // Add subPath if it exists and is not empty
    if (context.subPath && context.subPath.trim() !== '') {
      segments.push(context.subPath.trim());
    }

    // Add the filename
    segments.push(filename);

    // Join segments with forward slashes
    return segments.join('/');
  },
};
