'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createImageStorageService } from '../services/image-storage.service';
import type { UploadImageContext } from '../types';

const UploadImageSchema = z.object({
  file: z.instanceof(File),
  context: z.object({
    communityId: z.string().optional(),
    featureDir: z.string().optional(),
    storageDirPath: z.string().optional(),
    subPath: z.string().optional(),
    targetFilename: z.string().optional(),
  }) satisfies z.ZodType<UploadImageContext>,
});

/**
 * Server action to handle image uploads for any context
 * Uses the ImageStorageService to handle the upload with proper configuration
 */
export const uploadImageAction = enhanceAction(
  async function (data) {
    const client = getSupabaseServerClient();
    const service = createImageStorageService(client);

    try {
      const result = await service.uploadImage(data.file, data.context);
      return result;
    } catch (error) {
      console.error('Image upload failed:', error);
      throw {
        code: 'UPLOAD_FAILED',
        message: 'Failed to upload image. Please try again.',
        details: error instanceof Error ? error.message : undefined,
      };
    }
  },

  {
    auth: true,
    schema: UploadImageSchema,
  },
);
