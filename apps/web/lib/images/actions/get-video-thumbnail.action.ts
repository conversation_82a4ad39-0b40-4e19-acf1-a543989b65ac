'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';

const GetVideoThumbnailSchema = z.object({
  videoUrl: z.string().url(),
});

/**
 * Fetches the thumbnail URL for a given video URL (YouTube, Vimeo, Loom) via oEmbed.
 */
export const getVideoThumbnailAction = enhanceAction(
  async function (data) {
    const logger = await getLogger();
    const ctx = { name: 'getVideoThumbnailAction', videoUrl: data.videoUrl };
    logger.info(ctx, 'Fetching video thumbnail...');

    let oEmbedUrl = '';

    try {
      const url = new URL(data.videoUrl);

      if (
        url.hostname.includes('youtube.com') ||
        url.hostname.includes('youtu.be')
      ) {
        oEmbedUrl = `https://www.youtube.com/oembed?url=${encodeURIComponent(data.videoUrl)}&format=json`;
      } else if (url.hostname.includes('vimeo.com')) {
        oEmbedUrl = `https://vimeo.com/api/oembed.json?url=${encodeURIComponent(data.videoUrl)}`;
      } else if (url.hostname.includes('loom.com')) {
        oEmbedUrl = `https://www.loom.com/v1/oembed?url=${encodeURIComponent(data.videoUrl)}`;
      } else {
        logger.warn(ctx, 'Unsupported video provider for thumbnail fetching.');
        return {
          success: true, // Still success, just no thumbnail found
          data: null,
        };
      }

      const response = await fetch(oEmbedUrl);
      if (!response.ok) {
        logger.error(
          { ...ctx, status: response.status },
          `Failed to fetch oEmbed data.`,
        );
        // Return success but null data if oEmbed fetch fails
        return { success: true, data: null };
      }

      const oEmbedData = await response.json();
      const thumbnailUrl = oEmbedData?.thumbnail_url || null;

      if (thumbnailUrl) {
        logger.info({ ...ctx }, 'Thumbnail URL fetched successfully.');
      } else {
        logger.warn({ ...ctx }, 'oEmbed data did not contain a thumbnail URL.');
      }

      return {
        success: true,
        data: thumbnailUrl, // Return the URL or null
      };
    } catch (error) {
      logger.error({ ...ctx, error }, 'Error fetching thumbnail');
      return {
        success: false, // Indicate failure due to unexpected error
        error:
          error instanceof Error
            ? error.message
            : 'Unknown error fetching thumbnail',
        data: null,
      };
    }
  },
  {
    // This action doesn't inherently require authentication
    auth: false,
    schema: GetVideoThumbnailSchema,
  },
);
