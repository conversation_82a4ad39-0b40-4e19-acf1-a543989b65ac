'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createImageStorageService } from '../services/image-storage.service';
import type { DeleteImageContext } from '../types';

const DeleteImageSchema = z.object({
  path: z.string(),
  bucket: z.string(),
}) satisfies z.ZodType<DeleteImageContext>;

/**
 * Server action to handle image deletions for any context
 * Uses the ImageStorageService to handle the deletion with proper configuration
 */
export const deleteImageAction = enhanceAction(
  async function (data) {
    const client = getSupabaseServerClient();
    const service = createImageStorageService(client);

    try {
      await service.deleteImage(data.path, data.bucket);
      return true;
    } catch (error) {
      console.error('Image deletion failed:', error);
      throw {
        code: 'DELETE_FAILED',
        message: 'Failed to delete image. Please try again.',
        details: error instanceof Error ? error.message : undefined,
      };
    }
  },

  {
    auth: true,
    schema: DeleteImageSchema,
  },
);
