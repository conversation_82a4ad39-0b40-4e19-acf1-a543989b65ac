'use server';

import { SupabaseClient } from '@supabase/supabase-js';

import { enhanceAction } from '@kit/next/actions';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { DbCountry } from '~/lib/communities/billing/types';
import { createSystemService } from '~/lib/system/services/system.service';

import type { Database } from '../database.types';

export const getCountriesAction = enhanceAction(
  async function (): Promise<DbCountry[]> {
    const client = getSupabaseServerClient();
    return await loadAllCountries(client);
  },
  {
    auth: false,
  },
);

/**
 * Load all countries from the database
 */
async function loadAllCountries(
  client: SupabaseClient<Database>,
): Promise<DbCountry[]> {
  const coreService = createSystemService(client);
  const { countries } = await coreService.getSystemCountries();

  if (!countries) {
    return [];
  }

  return countries;
}
