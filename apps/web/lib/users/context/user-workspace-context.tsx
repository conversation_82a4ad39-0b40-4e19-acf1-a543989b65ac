'use client';

import { createContext } from 'react';

import type { UserWorkspace } from '~/lib/users/user-workspace.loader';

export const UserWorkspaceContext = createContext<UserWorkspace>(
  {} as UserWorkspace,
);

export function UserWorkspaceContextProvider(
  props: React.PropsWithChildren<{
    value: UserWorkspace;
  }>,
) {
  return (
    <UserWorkspaceContext.Provider value={props.value}>
      {props.children}
    </UserWorkspaceContext.Provider>
  );
}
