import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import type { Database } from '~/lib/database.types';
import { UserById, UserByUsername } from '~/lib/users/types';

import * as implementations from './implementations';

/**
 * Creates a new instance of the UsersService
 * @param client Supabase client
 * @returns A new UsersService instance
 */
export function createUsersService(client: SupabaseClient<Database>) {
  return new UsersService(client);
}

/**
 * @name UsersService
 * @description Service for managing users in the application
 * @example
 * const client = getSupabaseClient();
 * const usersService = createUsersService(client);
 */
class UsersService {
  constructor(private readonly client: SupabaseClient<Database>) {}

  /**
   * Get a user by their ID from the public profiles view via a DB function
   */
  async getUserById(id: string): Promise<UserById | null> {
    return implementations.getUserById(this.client, id);
  }

  /**
   * Get a user by their username from the public profiles view via a DB function
   */
  async getUserByUsername(username: string): Promise<UserByUsername | null> {
    return implementations.getUserByUsername(this.client, username);
  }

  /**
   * Get the user account data for the given ID
   */
  async getUserAccount(id: string) {
    return implementations.getUserAccount(this.client, id);
  }

  /**
   * Get the user subscription workspace data
   */
  async getUserSubscriptionWorkspace() {
    return implementations.getUserSubscriptionWorkspace(this.client);
  }

  /**
   * Load the user communities
   */
  async loadUserCommunities() {
    return implementations.loadUserCommunities(this.client);
  }

  /**
   * Toggle the visibility of a user's community membership
   */
  async toggleUserCommunityVisibility(
    userId: string,
    communityId: string,
    visibleOnProfile: boolean,
  ) {
    return implementations.toggleUserCommunityVisibility(
      this.client,
      userId,
      communityId,
      visibleOnProfile,
    );
  }

  /**
   * Get the subscription data for the given user
   */
  async getUserSubscriptions(userId: string) {
    return implementations.getUserSubscriptions(this.client, userId);
  }

  /**
   * Get the invoices data for the given user
   */
  async getUserInvoices(userId: string) {
    return implementations.getUserInvoices(this.client, userId);
  }

  /**
   * Get the billing stripe account ID for the given user
   * If the user does not have a billing stripe account ID, it will return null
   */
  async getUserStripeAccountId(userId: string) {
    return implementations.getUserStripeAccountId(this.client, userId);
  }

  /**
   * Delete user account of a user.
   * This will delete the user from the authentication provider and cancel all subscriptions.
   *
   * Permissions are not checked here, as they are checked in the server action.
   * USE WITH CAUTION. THE USER MUST HAVE THE NECESSARY PERMISSIONS.
   * TODO: Add permissions check, RLS check, only the user or super-admin can delete a user.
   */
  async deleteUserAccount(params: {
    adminClient: SupabaseClient<Database>;
    userId: string;
    userEmail: string | null;
  }) {
    return implementations.deleteUserAccount(this.client, params);
  }

  /**
   * Get the stripe account for the given user
   */
  async getUserStripeAccountByUserId(userId: string) {
    return implementations.getUserStripeAccountByUserId(this.client, userId);
  }
}
