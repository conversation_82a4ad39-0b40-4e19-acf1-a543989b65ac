import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'users.service.getUserStripeAccountByUserId';

/**
 * Get the stripe account for the given user
 *
 * @param client Supabase client
 * @param userId User ID to get stripe account for
 * @returns Stripe account data
 */
export async function getUserStripeAccountByUserId(
  client: SupabaseClient<Database>,
  userId: string,
) {
  const logger = await getLogger();
  const ctx = { userId, name: NAMESPACE };

  logger.info(ctx, 'Getting user stripe account');

  const response = await client
    .from('stripe_accounts')
    .select('*')
    .eq('user_id', userId)
    .maybeSingle();

  if (response.error) {
    logger.error(
      { ...ctx, response: response.error },
      'Error getting user stripe account',
    );
    throw response.error;
  }

  return objectToCamel(response);
}
