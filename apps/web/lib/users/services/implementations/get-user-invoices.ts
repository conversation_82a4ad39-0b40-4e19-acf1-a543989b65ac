import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'users.service';

/**
 * Get the invoices data for the given user
 *
 * @param client Supabase client
 * @param userId User ID to get invoices for
 * @returns Invoice data or null if not found
 */
export async function getUserInvoices(
  client: SupabaseClient<Database>,
  userId: string,
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    function: 'getUserInvoices',
    userId,
  };

  logger.info(ctx, 'Getting user invoices...');

  const response = await client
    .from('invoices')
    .select('*, items: invoice_items !inner (*)')
    .eq('user_id', userId)
    .order('created_at', { ascending: false });

  if (response.error) {
    logger.error(ctx, 'Error getting user invoices:', {
      error: response.error,
    });
    throw response.error;
  }

  if (!response.data) {
    return null;
  }

  return objectToCamel(response.data);
}
