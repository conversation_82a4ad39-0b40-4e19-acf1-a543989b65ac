import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'users.service';

/**
 * Get the subscription data for the given user
 *
 * @param client Supabase client
 * @param userId User ID to get subscriptions for
 * @returns Subscription data or null if not found
 */
export async function getUserSubscriptions(
  client: SupabaseClient<Database>,
  userId: string,
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    function: 'getUserSubscriptions',
    userId,
  };

  logger.info(ctx, 'Getting user subscriptions...');

  const response = await client
    .from('subscriptions')
    .select('*, items: subscription_items !inner (*)')
    .eq('user_id', userId)
    .maybeSingle();

  if (response.error) {
    logger.error(ctx, 'Error getting user subscriptions:', {
      error: response.error,
    });
    throw response.error;
  }

  if (!response.data) {
    return null;
  }

  return objectToCamel(response.data);
}
