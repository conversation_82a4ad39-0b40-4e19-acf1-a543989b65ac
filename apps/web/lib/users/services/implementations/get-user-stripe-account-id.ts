import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'users.service';

/**
 * Get the stripe account ID for the given user
 * If the user does not have a stripe account ID, it will return null
 *
 * @param client Supabase client
 * @param userId User ID to get customer ID for
 * @returns Stripe account ID or null if not found
 */
export async function getUserStripeAccountId(
  client: SupabaseClient<Database>,
  userId: string,
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    function: 'getUserStripeAccountId',
    userId,
  };

  logger.info(ctx, 'Getting user stripe account ID...');

  const response = await client
    .from('users')
    .select('stripe_account_id')
    .eq('id', userId)
    .maybeSingle();

  if (response.error) {
    logger.error(ctx, 'Error getting user stripe account ID:', {
      error: response.error,
    });
    throw response.error;
  }

  if (!response.data) {
    return null;
  }

  const stripeAccountId = response.data?.stripe_account_id;

  return stripeAccountId;
}
