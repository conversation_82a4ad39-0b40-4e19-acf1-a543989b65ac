import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'users.service';

/**
 * Load the user communities
 *
 * @param client Supabase client
 * @returns Array of user communities
 */
export async function loadUserCommunities(client: SupabaseClient<Database>) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    function: 'loadUserCommunities',
  };

  logger.info(ctx, 'Loading user communities...');
  const { data: communities, error } = await client
    .from('user_communities')
    .select('*');

  if (error) {
    logger.error({ ...ctx, error }, 'Error loading user communities:');
    throw error;
  }

  return objectToCamel(communities);
}
