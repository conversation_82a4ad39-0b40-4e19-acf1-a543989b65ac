import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'users.service';

/**
 * Get the user subscription workspace data
 *
 * @param client Supabase client
 * @returns Subscription workspace data or null if not found
 */
export async function getUserSubscriptionWorkspace(
  client: SupabaseClient<Database>,
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    function: 'getUserSubscriptionWorkspace',
  };

  logger.info(ctx, 'Getting user subscription workspace...');
  const { data: subscriptions, error } = await client
    .from('user_subscriptions_workspace')
    .select(`*`)
    .single();

  if (!subscriptions) {
    logger.info(ctx, 'No user subscription workspace found');
    return null;
  }

  if (error) {
    logger.error(ctx, 'Error getting user subscription workspace:', {
      error,
    });
    throw error;
  }

  return objectToCamel(subscriptions);
}
