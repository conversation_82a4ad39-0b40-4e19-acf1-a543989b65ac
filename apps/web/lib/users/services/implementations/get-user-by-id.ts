import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';
import { UserById } from '~/lib/users/types';

const NAMESPACE = 'users.service';

/**
 * Get a user by their ID from the public profiles view via a DB function
 *
 * @param client Supabase client
 * @param id User ID to look up
 * @returns User data or null if not found
 */
export async function getUserById(
  client: SupabaseClient<Database>,
  id: string,
): Promise<UserById | null> {
  const logger = await getLogger();
  const ctx = {
    name: NAMESPACE,
    id,
  };

  try {
    logger.info(ctx, 'Getting user by id');

    let result;
    let error;

    try {
      const response = await client
        .rpc('get_user_by_id', {
          p_id: id,
        })
        .single();
      result = response.data as unknown as UserById;
      error = response.error;

      if (!error) {
        logger.info(ctx, 'Successfully got user via ID');
      }
    } catch (e) {
      logger.warn(
        { ...ctx, error: e },
        'Error calling function get_user_by_id via ID',
      );
      error = e;
    }

    if (!result) {
      logger.info({ ...ctx }, 'No user found with this id');
      return null;
    }

    logger.info(ctx, 'Successfully got user by ID');

    return objectToCamel(result);
  } catch (error) {
    logger.error({ ...ctx, error }, 'Unexpected error getting user by id');
    return null;
  }
}
