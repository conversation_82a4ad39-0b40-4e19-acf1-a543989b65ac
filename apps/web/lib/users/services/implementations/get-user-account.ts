import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'users.service';

/**
 * Get the user account data for the given ID
 *
 * @param client Supabase client
 * @param id User ID to look up
 * @returns User account data
 */
export async function getUserAccount(
  client: SupabaseClient<Database>,
  id: string,
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    function: 'getUserAccount',
    id,
  };

  logger.info(ctx, 'Getting user account...');

  const { data, error } = await client
    .from('users')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    logger.error({ ...ctx, error }, 'Error getting user account:');
    throw error;
  }

  return objectToCamel(data);
}
