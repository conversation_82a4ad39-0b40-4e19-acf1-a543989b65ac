import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';
import { UserByUsername } from '~/lib/users/types';

const NAMESPACE = 'users.service';

/**
 * Get a user by their username from the public profiles view via a DB function
 *
 * @param client Supabase client
 * @param username Username to look up
 * @returns User data or null if not found
 */
export async function getUserByUsername(
  client: SupabaseClient<Database>,
  username: string,
): Promise<UserByUsername | null> {
  const logger = await getLogger();
  const ctx = {
    name: NAMESPACE,
    username,
  };

  try {
    logger.info(ctx, 'Getting user by username');

    const result = await client
      .rpc('get_user_by_username', {
        p_username: username,
      })
      .single();

    if (!result || !result.data) {
      logger.info({ ...ctx }, 'No user found with this username');
      return null;
    }

    logger.info(ctx, 'Successfully got user by username');

    return objectToCamel(result.data);
  } catch (error) {
    logger.error(
      { ...ctx, error },
      'Unexpected error getting user by username',
    );
    return null;
  }
}
