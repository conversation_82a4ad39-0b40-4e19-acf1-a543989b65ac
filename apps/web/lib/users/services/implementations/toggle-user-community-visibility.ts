import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'users.service';

/**
 * Toggle the visibility of a user's community membership
 *
 * @param client Supabase client
 * @param userId The ID of the user
 * @param communityId The ID of the community
 * @param visibleOnProfile Whether the membership is visible on the user's profile
 */
export async function toggleUserCommunityVisibility(
  client: SupabaseClient<Database>,
  userId: string,
  communityId: string,
  visibleOnProfile: boolean,
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    function: 'toggleUserCommunityVisibility',
    userId,
    communityId,
    visibleOnProfile,
  };

  logger.info(ctx, 'Toggling user community visibility...');
  const { data: communityMembership, error } = await client
    .from('community_memberships')
    .update({ visible_on_profile: visibleOnProfile })
    .eq('user_id', userId)
    .eq('community_id', communityId)
    .select('*')
    .single();

  if (error) {
    logger.error(
      { ...ctx, error },
      'Error toggling user community visibility:',
    );
    throw error;
  }

  return objectToCamel(communityMembership);
}
