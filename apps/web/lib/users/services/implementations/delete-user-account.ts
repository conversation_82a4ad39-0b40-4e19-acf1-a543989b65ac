import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'users.service';

/**
 * Delete user account of a user.
 * This will delete the user from the authentication provider and cancel all subscriptions.
 *
 * Permissions are not checked here, as they are checked in the server action.
 * USE WITH CAUTION. THE USER MUST HAVE THE NECESSARY PERMISSIONS.
 * TODO: Add permissions check, RLS check, only the user or super-admin can delete a user.
 *
 * @param client Supabase client
 * @param params Parameters for deleting the user account
 * @returns void
 */
export async function deleteUserAccount(
  client: SupabaseClient<Database>,
  params: {
    adminClient: SupabaseClient<Database>;
    userId: string;
    userEmail: string | null;
  },
) {
  const logger = await getLogger();

  const userId = params.userId;
  const ctx = { userId, name: NAMESPACE };

  logger.info(ctx, 'User requested to delete their account. Processing...');

  // execute the deletion of the user
  try {
    await params.adminClient.auth.admin.deleteUser(userId);
  } catch (error) {
    logger.error(
      {
        ...ctx,
        error,
      },
      'Encountered an error deleting user',
    );

    throw new Error('Error deleting user');
  }

  logger.info(ctx, 'User successfully deleted!');
}
