import { z } from 'zod';

import type { Social } from '@kit/ui/dojo/organisms/socials-form';

// Database type references

import { SubscriptionStatusEnum } from '~/lib/communities/members/types';

// For user_details which is a JSONB field in the users table
export type DbUserDetails = {
  bio: string | null;
  socials: Social[] | null;
};

// Shared type for frontend components
export type UserData = {
  id: string;
  firstName: string;
  lastName: string | null;
  bio: string | null;
  socials: Social[] | null;
  username: string | null;
  pictureUrl: string | null;
  countryId: string | null;
  subscriptionStatus?: SubscriptionStatusEnum | null;
};

/**
 * Schema for validating UserData at runtime
 */
const UserDataSchema = z.object({
  id: z.string(),
  firstName: z.string(),
  lastName: z.string().nullable(),
  bio: z.string().nullable(),
  socials: z.array(z.any()).nullable(),
  username: z.string().nullable(),
  pictureUrl: z.string().nullable(),
  countryId: z.string().nullable(),
  subscriptionStatus: z
    .enum(['active', 'trialing', 'past_due', 'canceled', 'unpaid'])
    .nullable()
    .optional(),
});

/**
 * Helper to safely cast and validate data to UsertData type
 * Uses Zod schema validation to ensure data integrity at runtime
 */
export function castToUserData(data: unknown): UserData {
  return UserDataSchema.parse(data);
}
