import { cache } from 'react';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createUsersService } from './services/users.service';

export type UserWorkspace = Awaited<ReturnType<typeof loadUserWorkspace>>;

/**
 * @name loadUserWorkspace
 * @description
 * Load the user workspace data. It's a cached per-request function that fetches the user workspace data.
 * It can be used across the server components to load the user workspace data.
 */
export const loadUserWorkspace = cache(workspaceLoader);

async function workspaceLoader() {
  const client = getSupabaseServerClient();
  const usersService = createUsersService(client);

  const communitiesPromise = usersService.loadUserCommunities();

  const subscriptionPromise = usersService.getUserSubscriptionWorkspace();

  const {
    data: { user },
  } = await client.auth.getUser();

  const [communities, subscription] = await Promise.all([
    communitiesPromise,
    subscriptionPromise,
  ]);

  return {
    communities,
    subscription,
    user,
  };
}
