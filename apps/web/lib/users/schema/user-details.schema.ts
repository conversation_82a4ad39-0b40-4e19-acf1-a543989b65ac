import { z } from 'zod';

type Social = {
  name: string;
  url: string;
};

export type UserFormValues = {
  firstName: string;
  lastName: string;
  bio: string;
  socials: Social[];
  username: string;
  countryId: string;
};

export const UserDetailsSchema = z.object({
  firstName: z.string().min(2).max(100),
  lastName: z.string().min(2).max(100).optional().or(z.literal('')),
  socials: z.array(
    z.object({
      name: z.string().min(2).max(100),
      url: z.string().min(2).max(100),
    }),
  ),
  bio: z.string().min(2).max(1000).optional().or(z.literal('')),
  countryId: z.string().uuid(),
  username: z
    .string()
    .min(4)
    .max(30)
    .regex(
      /^[a-z0-9][a-z0-9-]*[a-z0-9]$/,
      'Must start and end with letter/number, and no consecutive hyphens',
    )
    .transform((val) => val.toLowerCase()),
});
