import { z } from 'zod';

// Define the schema with a generic string that gets refined at validation time
export const DeleteUserAccountSchema = z.object({
  // We'll use a regular string type in the form and add the refinement for validation
  confirmation: z.string().refine((value) => value === 'DELETE', {
    message: "Please type 'DELETE' to confirm account deletion",
  }),
});

// Export the type used in the form
export type DeleteUserAccountFormValues = {
  confirmation: string;
};
