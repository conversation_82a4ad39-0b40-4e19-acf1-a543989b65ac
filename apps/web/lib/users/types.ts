import { ObjectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

export type SubscriptionStatus =
  Database['public']['Tables']['subscriptions']['Row']['status'];

type UserRow = Database['public']['Tables']['users']['Row'];

export type User = ObjectToCamel<UserRow>;

type UserByIdReturn =
  Database['public']['Functions']['get_user_by_id']['Returns'][0];

export type UserById = ObjectToCamel<UserByIdReturn>;

type UserUpdateParams = Database['public']['Tables']['users']['Update'];

export type UserUpdate = ObjectToCamel<UserUpdateParams>;

type UserByUsernameReturn =
  Database['public']['Functions']['get_user_by_username']['Returns'][0];

export type UserByUsername = ObjectToCamel<UserByUsernameReturn>;
