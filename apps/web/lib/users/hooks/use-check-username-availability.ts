import { useState } from 'react';

import { useDebouncedCallback } from 'use-debounce';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';

type DebouncedCheckFunction = {
  (username: string, currentUserId: string): Promise<void> | undefined;
  subscribe(callback: (result: boolean | null) => void): void;
  unsubscribe(callback: (result: boolean | null) => void): void;
};

export function useCheckUsernameAvailability() {
  const client = useSupabase();
  const [subscribers] = useState<Set<(result: boolean | null) => void>>(
    new Set(),
  );

  const debouncedCheck = useDebouncedCallback(
    async (username: string, currentUserId: string) => {
      if (!username) {
        subscribers.forEach((cb) => cb(null));
        return;
      }

      try {
        const { data, error } = await client.rpc(
          'check_user_username_availability',
          {
            p_username_to_check: username,
            p_current_user_id: currentUserId,
          },
        );

        if (error) {
          console.error('Error checking username availability:', error);
          subscribers.forEach((cb) => cb(null));
          return;
        }

        subscribers.forEach((cb) => cb(data));
      } catch (error) {
        console.error('Error checking username availability:', error);
        subscribers.forEach((cb) => cb(null));
      }
    },
    300,
  );

  const checkAvailability: DebouncedCheckFunction = Object.assign(
    debouncedCheck,
    {
      subscribe: (callback: (result: boolean | null) => void) => {
        subscribers.add(callback);
      },
      unsubscribe: (callback: (result: boolean | null) => void) => {
        subscribers.delete(callback);
      },
    },
  );

  return {
    checkAvailability,
  };
}
