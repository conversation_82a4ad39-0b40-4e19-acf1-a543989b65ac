import { useQuery } from '@tanstack/react-query';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';

type CountryData = {
  id: string;
  name: string;
  icon: string;
};

export function useAllCountries() {
  const client = useSupabase();
  const queryKey = ['countries:data'];

  const queryFn = async (): Promise<CountryData[]> => {
    const response = await client
      .from('countries')
      .select(
        `
        id,
        name,
        icon
    `,
      )
      .order('name', { ascending: true });

    if (response.error) {
      throw response.error;
    }

    return response.data as CountryData[];
  };

  return useQuery<CountryData[], Error>({
    queryKey,
    queryFn,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });
}
