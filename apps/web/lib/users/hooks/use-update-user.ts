import { useMutation } from '@tanstack/react-query';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';

import type { Json } from '~/lib/database.types';
import { UserUpdate } from '~/lib/users/types';

export function useUpdateUserData(userId: string) {
  const client = useSupabase();

  const mutationKey = ['user:data', userId];

  const mutationFn = async (
    data: UserUpdate & { userDetails?: Record<string, unknown> },
  ) => {
    // If updating user_details, fetch current data first to preserve existing fields
    if (data.userDetails) {
      const { data: currentData, error: fetchError } = await client
        .from('users')
        .select('user_details')
        .eq('id', userId)
        .single();

      if (fetchError) {
        console.error('Error fetching current user data:', fetchError);
      } else if (currentData?.user_details) {
        // Merge new user_details with existing fields
        const existingDetails =
          typeof currentData.user_details === 'object'
            ? (currentData.user_details as Record<string, unknown>)
            : {};

        const newDetails =
          typeof data.userDetails === 'object'
            ? (data.userDetails as Record<string, unknown>)
            : {};

        data.userDetails = {
          ...existingDetails,
          ...newDetails,
        } as Json & Record<string, unknown>;
      }
    }

    // Perform the update
    const response = await client.from('users').update(data).match({
      id: userId,
    });

    if (response.error) {
      throw response.error;
    }

    return response.data;
  };

  return useMutation({
    mutationKey,
    mutationFn,
  });
}
