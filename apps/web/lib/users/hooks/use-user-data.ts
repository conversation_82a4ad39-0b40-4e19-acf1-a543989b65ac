import { useCallback } from 'react';

import { useQuery, useQueryClient } from '@tanstack/react-query';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';

import { User } from '~/lib/users/types';

import { DbUserDetails, UserData } from '../utils/type-helpers';

export function useUserData(
  userId: string,
  partialUser?: {
    id: string;
    firstName: string;
    lastName: string | null;
    pictureUrl: string | null;
    userDetails?: DbUserDetails | null;
    username?: string;
    countryId?: string;
  },
) {
  const client = useSupabase();
  const queryKey = ['user:data', userId];

  const queryFn = async (): Promise<UserData | null> => {
    if (!userId) {
      return null;
    }

    const response = await client
      .from('users')
      .select(
        `
        id,
        first_name,
        last_name,
        picture_url,
        user_details,
        username,
        country_id
    `,
      )
      .eq('id', userId)
      .single<User>();

    if (response.error) {
      throw response.error;
    }

    // Transform database response to AccountData
    const userDetails = response.data.user_details
      ? (response.data.user_details as unknown as DbUserDetails)
      : null;

    return {
      id: response.data.id,
      firstName: response.data.first_name,
      lastName: response.data.last_name,
      bio: userDetails?.bio ?? null,
      socials: userDetails?.socials ?? null,
      username: response.data.username,
      pictureUrl: response.data.picture_url,
      countryId: response.data.country_id,
      subscriptionStatus: null,
    };
  };

  // Convert the partial account to AccountData format if it exists
  const initialData = partialUser?.id
    ? ({
        id: partialUser.id,
        firstName: partialUser.firstName,
        lastName: partialUser.lastName ?? null,
        bio: partialUser.userDetails?.bio ?? null,
        socials: partialUser.userDetails?.socials ?? null,
        username: partialUser.username ?? '',
        pictureUrl: partialUser.pictureUrl,
        countryId: partialUser.countryId ?? '',
        subscriptionStatus: null,
      } as UserData)
    : undefined;

  return useQuery<UserData | null>({
    queryKey,
    queryFn,
    enabled: !!userId,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    initialData,
  });
}

export function useRevalidateUserDataQuery() {
  const queryClient = useQueryClient();

  return useCallback(
    (userId: string) =>
      queryClient.invalidateQueries({
        queryKey: ['user:data', userId],
      }),
    [queryClient],
  );
}
