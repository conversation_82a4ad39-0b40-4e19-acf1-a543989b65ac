'use server';

import { cache } from 'react';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createUsersService } from '~/lib/users/services/users.service';
import { UserById } from '~/lib/users/types';

/**
 * Schema for getting a user by id
 */
const GetUserByIdSchema = z.object({
  id: z.string(),
});

export type GetUserByIdInput = z.infer<typeof GetUserByIdSchema>;

/**
 * Action to get a user by id
 * This uses the public profiles view through a function in either the public or public_api schema
 */
export const getUserByIdAction = cache(
  enhanceAction(
    async function (data: GetUserByIdInput): Promise<UserById | null> {
      const logger = await getLogger();
      const ctx = {
        name: 'get-user-by-id',
        id: data.id,
      };

      try {
        logger.info(ctx, 'Getting user by id');

        const supabase = getSupabaseServerClient();
        const usersService = createUsersService(supabase);

        const user = await usersService.getUserById(data.id);

        return user;
      } catch (error) {
        logger.error({ ...ctx, error }, 'Unexpected error getting user by id');
        return null;
      }
    },
    {
      schema: GetUserByIdSchema,
      auth: false, // Allow anonymous access
    },
  ),
);
