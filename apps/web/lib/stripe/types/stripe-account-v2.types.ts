/**
 * Shared types for Stripe Accounts v2 API
 */
import type <PERSON><PERSON> from 'stripe';

export type StripeAccountV2 = Stripe.V2.Core.Account;

export type StripeAccountV2Params = {
  applied_configurations?: string[];
  display_name?: string;
  dashboard?: string;
  configuration?: Record<string, unknown>;
  defaults?: Record<string, unknown>;
  identity?: Record<string, unknown>;
};

export type StripeAccountV2CreateParams = {
  configuration?: {
    customer?: Record<string, unknown>;
  };
  contact_email: string;
  display_name?: string;
  metadata?: Record<string, string>;
  identity?: {
    country?: string;
  };
};

export type StripeAccountV2ListParams = {
  limit?: number;
  applied_configurations?: string[];
  contact_email?: string;
  starting_after?: string;
};

// Type for Stripe v2 Account response
export type StripeV2Account = {
  id: string;
  object: string;
  applied_configurations: string[];
  configuration: unknown;
  contact_email: string;
  created: string;
  dashboard: string | null;
  identity: unknown;
  defaults: unknown;
  display_name: string;
  metadata: Record<string, string>;
  requirements: unknown;
  livemode: boolean;
};
