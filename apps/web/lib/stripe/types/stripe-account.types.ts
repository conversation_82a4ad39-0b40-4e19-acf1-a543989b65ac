import type Stripe from 'stripe';

import { ObjectToCamel } from '@kit/shared/utils';
import type { Database } from '@kit/supabase/database';

type StripeAccountRow = Database['public']['Tables']['stripe_accounts']['Row'];

export type DbStripeAccount = ObjectToCamel<StripeAccountRow>;

export type StripeAccount = Stripe.Account;

export type StripeRequirements = Stripe.Account.Requirements;

type CommunityStripeAccountStatusRow =
  Database['public']['Functions']['get_community_stripe_account_status']['Returns'][number];

export type CommunityStripeAccountStatus =
  ObjectToCamel<CommunityStripeAccountStatusRow>;
