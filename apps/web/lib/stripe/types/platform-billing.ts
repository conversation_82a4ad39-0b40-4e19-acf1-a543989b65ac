import { Enums } from '@kit/supabase/database';

export type SubscriptionStatusEnum = Enums<'subscription_status'>;
export type PaymentStatusEnum = Enums<'payment_status'>;

/**
 * Type definition for platform products
 */
export type PlatformProduct = {
  id: string;
  name: string;
  description: string | null;
  active: boolean;
  defaultPriceId: string | null;
  createdAt: string;
  updatedAt: string;
  productPrices?: ProductPrice[];
  trialDays: number | null;
};

// Add helper type for price types from DB enum
export type PriceType = 'one_time' | 'recurring';

// Add helper type for price intervals from DB enum
export type PriceInterval = 'month' | 'year' | 'week' | 'day';

/**
 * Type definition for platform prices
 */
export type ProductPrice = {
  id: string;
  nickname: string | null;
  productId: string;
  unitAmount: number;
  currency: string;
  active: boolean;
  type: PriceType;
  interval: PriceInterval;
  createdAt: string;
  updatedAt: string;
};

// Add helper type for currency formatting
export type CurrencyFormatOptions = {
  value: number;
  currency: string;
  locale: string;
};

// UI/Component specific types
export type PricingItemInterval = 'month' | 'year';
