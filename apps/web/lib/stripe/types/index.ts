import { ObjectToCamel } from '@kit/shared/utils';
import type { Database } from '@kit/supabase/database';

// Export platform billing types
export * from './platform-billing';
export * from './types';
export * from './stripe-account.types';
export * from './stripe-account-v2.types';

/**
 * Parameters for upserting a subscription
 */
export type UpsertSubscriptionParams =
  Database['public']['Functions']['upsert_subscription']['Args'];

export type UpsertSubscriptionReturn = ObjectToCamel<
  Database['public']['Functions']['upsert_subscription']['Returns']
>;

export type PurchaseTypeEnum = Database['public']['Enums']['purchase_type'];

export type LineItem = {
  id: string;
  quantity: number;
  subscription_id: string;
  subscription_item_id: string;
  product_id: string;
  variant_id: string;
  price_amount: number | null | undefined;
  interval: string;
  interval_count: number;
  // Only flat rate is supported now
  type: 'flat';
};

/**
 * Parameters for upserting an invoice (one-time payment)
 */
export type UpsertInvoiceParams =
  Database['public']['Functions']['upsert_invoice']['Args'];

/**
 * Data for a stripe account
 */
export type ConnectAccountData = {
  id: string;
  charges_enabled: boolean;
  details_submitted: boolean;
  payouts_enabled: boolean;
  requirements: {
    currently_due: string[];
    eventually_due: string[];
    past_due: string[];
    pending_verification: string[];
  };
};
