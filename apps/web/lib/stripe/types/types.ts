import type Stripe from 'stripe';

export type ConnectAccountData = {
  id: string;
  charges_enabled: boolean;
  details_submitted: boolean;
  payouts_enabled: boolean;
  requirements: {
    currently_due: string[];
    eventually_due: string[];
    past_due: string[];
    pending_verification: string[];
  };
};

export type UpsertSubscriptionParams = {
  [key: string]: unknown;
  id: string;
  communityId: string;
  accountId: string;
  customerId: string;
  lineItems: Stripe.SubscriptionItem[];
  status: string;
  currency: string;
  cancelAtPeriodEnd: boolean;
  periodStartsAt: number;
  periodEndsAt: number;
  trialStartsAt: number | null;
  trialEndsAt: number | null;
};

export type UpsertOrderParams = {
  [key: string]: unknown;
  target_invoice_id: string;
  target_community_id: string;
  target_account_id: string;
  status: string;
  currency: string;
  total_amount: number;
  line_items: Array<{
    id: string;
    product_id: string;
    variant_id: string;
    quantity: number;
    price_amount: number;
    invoice_id: string;
  }>;
};
