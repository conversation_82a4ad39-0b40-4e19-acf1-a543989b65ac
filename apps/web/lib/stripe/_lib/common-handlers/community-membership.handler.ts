import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { UUID } from 'crypto';

import { getLogger } from '@kit/shared/logger';

import type { CommunityMembershipStatusEnum } from '~/lib/communities/members/types';
import type { Database } from '~/lib/database.types';

import { PurchaseTypeEnum } from '../../types';

export async function handleCommunityMembershipSubscription(
  client: SupabaseClient<Database>,
  communityId: UUID,
  userId: UUID,
  purchaseType: PurchaseTypeEnum,
  type: 'subscription' | 'one-time',
  priceId: string,
  status: CommunityMembershipStatusEnum,
  invoiceId: string,
  subscriptionId?: string,
): Promise<null> {
  const logger = await getLogger();

  const ctx = {
    name: 'handleCommunityMembershipSubscription',
    communityId,
    userId,
    purchaseType,
    priceId,
    status,
    invoiceId,
    subscriptionId,
  };

  if (!userId || !communityId) {
    logger.error(
      { ...ctx },
      'Missing user_id or community_id in subscription metadata',
    );
    return null;
  }

  const { data: existingCommunityMembership, error } = await client
    .from('community_memberships')
    .select('id')
    .eq('user_id', userId)
    .eq('community_id', communityId)
    .maybeSingle();

  if (error) {
    logger.error({ ...ctx, error }, 'Failed to fetch community membership');
    throw error;
  }
  if (!existingCommunityMembership) {
    logger.info(ctx, 'Creating new community membership for subscription');

    // Determine the correct invoice ID and subscription ID based on type
    const actualInvoiceId = invoiceId; // Always use the invoice ID
    const actualSubscriptionId =
      type === 'subscription' ? subscriptionId : null;

    logger.info(
      { ...ctx, actualInvoiceId, actualSubscriptionId, type },
      'Determined invoice and subscription IDs for RPC call',
    );

    const rpcParams = {
      p_community_id: communityId,
      p_user_id: userId,
      p_order_type: type,
      p_price_id: priceId,
      p_latest_invoice_id: actualInvoiceId || '',
      p_subscription_id: actualSubscriptionId || undefined, // Convert null to undefined for TypeScript compatibility
    };

    logger.info(
      { ...ctx, rpcParams },
      'Calling add_user_to_community RPC with parameters',
    );

    const { error: rpcError } = await client.rpc(
      'add_user_to_community',
      rpcParams,
    );
    if (rpcError) {
      logger.error(
        { ...ctx, error: rpcError },
        'Failed to add user to community',
      );
      throw rpcError;
    }
    logger.info(ctx, 'Successfully created community membership');
  } else {
    logger.info(
      ctx,
      'Community membership already exists for subscription updating status',
    );

    if (type === 'subscription') {
      const { error: updateError } = await client
        .from('community_memberships')
        .update({
          subscription_id: subscriptionId,
          status: status,
        })
        .eq('id', existingCommunityMembership.id);

      if (updateError) {
        logger.error(ctx, 'Failed to update community membership');
        throw updateError;
      }
    } else if (type === 'one-time') {
      const { error: updateError } = await client
        .from('community_memberships')
        .update({
          latest_invoice_id: invoiceId,
        })
        .eq('id', existingCommunityMembership.id);

      if (updateError) {
        logger.error(ctx, 'Failed to update community membership');
        throw updateError;
      }
    }
  }
  return null;
}
