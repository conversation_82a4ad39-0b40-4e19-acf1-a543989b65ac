import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import type Strip<PERSON> from 'stripe';

import { getLogger } from '@kit/shared/logger';

import type { Database } from '~/lib/database.types';

import { getStripeProductsService } from '../../services';

export async function handleCommunityOwnershipSubscription(
  stripeSubscription: Stripe.Subscription,
  client: SupabaseClient<Database>,
  communityName?: string,
  primaryOwnerUserId?: string,
): Promise<null> {
  const logger = await getLogger();

  const ctx = {
    name: 'handleCommunityOwnershipSubscription',
    subscriptionId: stripeSubscription.id,
  };

  const communityId = stripeSubscription.metadata?.community_id;

  if (!communityId) {
    logger.error(ctx, 'Community ID not found in subscription metadata');
    return null;
  }

  const isEnabled = ['active', 'trialing'].includes(stripeSubscription.status);

  const { data: existingCommunityWithSubscription } = await client
    .from('communities')
    .select('id')
    .eq('subscription_id', stripeSubscription.id)
    .maybeSingle();

  logger.info(ctx, 'Checked for existing community with subscription', {
    subscriptionId: stripeSubscription.id,
    foundCommunity: !!existingCommunityWithSubscription,
    communityId: existingCommunityWithSubscription?.id,
  });

  if (!existingCommunityWithSubscription) {
    logger.info(ctx, 'Community not found, creating new community');

    const { data: existingCommunityNoSubscription } = await client
      .from('communities')
      .select('id')
      .eq('id', communityId)
      .maybeSingle();

    logger.info(ctx, 'Checked for existing community by ID', {
      communityId,
      foundCommunity: !!existingCommunityNoSubscription,
    });

    if (!existingCommunityNoSubscription) {
      const communityNameCurrent =
        communityName || stripeSubscription.metadata?.community_name;
      const primaryOwnerUserIdCurrent =
        primaryOwnerUserId ||
        stripeSubscription.metadata?.primary_owner_user_id;

      // If community name is missing, try to look it up from checkout_sessions
      let finalCommunityName = communityNameCurrent;
      let finalPrimaryOwnerUserId = primaryOwnerUserIdCurrent;

      if (!finalCommunityName || !finalPrimaryOwnerUserId) {
        logger.info(
          ctx,
          'Missing metadata, attempting to lookup from checkout_sessions',
        );

        const { data: checkoutSession } = await client
          .from('checkout_sessions')
          .select('community_name, user_id')
          .eq('community_id', communityId)
          .eq('purchase_type', 'community_ownership')
          .order('created_at', { ascending: false })
          .limit(1)
          .maybeSingle();

        if (checkoutSession) {
          finalCommunityName =
            finalCommunityName || checkoutSession.community_name || undefined;
          finalPrimaryOwnerUserId =
            finalPrimaryOwnerUserId || checkoutSession.user_id || undefined;
          logger.info(
            ctx,
            'Successfully found community data from checkout_sessions',
            {
              communityName: finalCommunityName,
              userId: finalPrimaryOwnerUserId,
            },
          );
        }
      }

      if (!finalCommunityName) {
        logger.warn(
          ctx,
          'Missing community name even after checkout_sessions lookup - skipping community creation',
        );
        return null;
      }

      if (!finalPrimaryOwnerUserId) {
        logger.warn(
          ctx,
          'Missing primary owner user ID even after checkout_sessions lookup - skipping community creation',
        );
        return null;
      }

      const { data: newCommunity, error: createError } = await client.rpc(
        'create_community_server_actions',
        {
          p_community_name: finalCommunityName,
          p_primary_owner_user_id: finalPrimaryOwnerUserId,
          p_subscription_id: stripeSubscription.id,
          p_is_enabled: isEnabled,
          p_community_id: communityId,
        },
      );

      if (createError) {
        logger.error(
          { ...ctx, error: createError },
          'Failed to create community for subscription',
        );
        throw createError;
      }

      logger.info(
        {
          ctx,
          newCommunity,
        },
        'Successfully created new community for subscription',
      );

      const stripeProductService = await getStripeProductsService(client);

      // Create a default product
      const { product, price } =
        await stripeProductService.createCommunityDefaultProduct(
          newCommunity.id,
        );

      if (!product || !price) {
        logger.error(ctx, 'Error creating default product');
        throw new Error('Error creating default product');
      }
      return null;
    } else {
      logger.info(ctx, 'Community already exists, updating subscription ID');

      const { error: updateError } = await client
        .from('communities')
        .update({
          subscription_id: stripeSubscription.id,
          is_enabled: isEnabled,
        })
        .eq('id', existingCommunityNoSubscription.id);

      if (updateError) {
        logger.error(
          { ...ctx, error: updateError },
          'Failed to update community subscription ID',
        );
        throw updateError;
      }
    }
  } else {
    logger.info(ctx, 'Community already exists for subscription');

    const { error: updateError } = await client
      .from('communities')
      .update({
        subscription_id: stripeSubscription.id,
        is_enabled: isEnabled,
      })
      .eq('id', existingCommunityWithSubscription.id);

    if (updateError) {
      logger.error(
        { ...ctx, error: updateError },
        'Failed to update community subscription ID',
      );
      throw updateError;
    }
  }
  return null;
}
