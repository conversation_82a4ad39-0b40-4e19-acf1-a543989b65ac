import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import type <PERSON><PERSON> from 'stripe';

import { getLogger } from '@kit/shared/logger';

import type { Database } from '~/lib/database.types';

export async function handlePaymentMethodUpdate(
  stripeSubscription: Stripe.Subscription,
  client: SupabaseClient<Database>,
): Promise<null> {
  const logger = await getLogger();

  const paymentMethod = stripeSubscription.default_payment_method as
    | string
    | null;

  if (!paymentMethod) {
    return null;
  }

  const ctx = {
    name: 'handlePaymentMethodUpdate',
    subscriptionId: stripeSubscription.id,
    paymentMethodId: paymentMethod,
  };

  logger.info(ctx, 'Updating payment method for subscription');

  try {
    await client
      .from('subscriptions')
      .update({ payment_method_id: paymentMethod })
      .eq('id', stripeSubscription.id);
  } catch (error) {
    logger.error(
      {
        error,
        ...ctx,
      },
      'Error updating payment method for subscription',
    );

    throw error;
  }

  logger.info(ctx, 'Successfully updated payment method for subscription');

  return null;
}
