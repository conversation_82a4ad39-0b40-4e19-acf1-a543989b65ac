import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import type { Stripe } from 'stripe';

import { getLogger } from '@kit/shared/logger';

import { DbStripeAccount } from '../../types';

export async function syncStripeAccountV2ToDb(
  client: SupabaseClient,
  stripe: Stripe,
  stripeAccountId: string,
) {
  const logger = await getLogger();
  const ctx = {
    name: 'syncStripeAccountV2ToDb',
    stripeAccountId,
  };

  logger.info(ctx, 'Syncing Stripe account v2 to database');

  // Fetch latest subscription data from Stripe
  try {
    const stripeAccount = (await stripe.v2.core.accounts.retrieve(
      stripeAccountId,
      {
        include: [
          'configuration.customer',
          'configuration.merchant',
          'configuration.recipient',
          'identity',
          'requirements',
        ],
      },
    )) as Stripe.V2.Core.Account & {
      data: Stripe.V2.Core.Account;
    } & Stripe.V2.Core.AccountRetrieveParams;

    if (!stripeAccount) {
      throw new Error('Stripe account not found');
    }

    logger.info({ ctx, stripeAccount }, 'Stripe account');

    const stripeAccountPayload = {
      p_stripe_account_id: stripeAccount.id,
      p_applied_configurations:
        stripeAccount.applied_configurations as string[],
      p_contact_email: stripeAccount.contact_email as string,
      p_display_name: stripeAccount.display_name,
      p_dashboard: stripeAccount.dashboard,
      p_configuration: stripeAccount.configuration,
      p_capabilities: stripeAccount.configuration?.merchant?.capabilities,
      p_requirements: stripeAccount.requirements,
      p_defaults: stripeAccount.defaults,
      p_identity: stripeAccount.identity,
      p_metadata: stripeAccount.metadata,
    };

    try {
      const { data: dbAccount, error } = await client.rpc(
        'update_stripe_account',
        stripeAccountPayload,
      );

      if (error) {
        logger.error(
          {
            ...ctx,
            error: error.message,
            details: error.details,
            hint: error.hint,
            code: error.code,
          },
          'Error updating stripe account to database',
        );
        throw error;
      }

      logger.info(
        { ...ctx, stripeAccountId: dbAccount?.id },
        'Successfully updated stripe account',
      );
      return {
        stripeAccount: stripeAccount.data as Stripe.V2.Core.Account,
        dbAccount: dbAccount as DbStripeAccount,
      };
    } catch (dbError) {
      logger.error(
        { ...ctx, error: dbError, payload: stripeAccountPayload },
        'Error calling update_stripe_account RPC',
      );
      throw dbError;
    }
  } catch (error) {
    logger.error({ ...ctx, error }, 'Error syncing Stripe account to database');
    throw error;
  }
}
