import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import type Stripe from 'stripe';

import { getLogger } from '@kit/shared/logger';

import { PurchaseTypeEnum, UpsertSubscriptionReturn } from '../../types';
import { buildSubscriptionPayloadFromStripe } from '../../utils';

export async function syncStripeSubscriptionToDb(
  client: SupabaseClient,
  stripe: Stripe,
  subscriptionId: string,
) {
  const logger = await getLogger();
  const ctx = {
    name: 'syncStripeSubscriptionToDb',
    subscriptionId,
  };

  logger.info(ctx, 'Syncing Stripe subscription to database');

  // Fetch latest subscription data from Stripe
  try {
    const stripeSubscription = await stripe.subscriptions.retrieve(
      subscriptionId,
      { expand: ['items.data.price', 'items.data.price.product'] },
    );

    if (!stripeSubscription.metadata.community_id) {
      throw new Error('Missing community_id in subscription metadata');
    }

    const userId = stripeSubscription.metadata.user_id;

    if (!userId) {
      throw new Error(
        'Missing user_id or primary_owner_user_id in subscription metadata',
      );
    }

    const subscriptionPayload = buildSubscriptionPayloadFromStripe(
      stripeSubscription,
      {
        communityId: stripeSubscription.metadata.community_id,
        userId: userId,
        paymentMethodId:
          typeof stripeSubscription.default_payment_method === 'string'
            ? stripeSubscription.default_payment_method
            : null,
        purchaseType: stripeSubscription.metadata
          .purchase_type as PurchaseTypeEnum,
      },
    );
    logger.info({ ctx, subscriptionPayload }, 'Subscription payload');

    try {
      const { data: dbSubscription, error } = await client.rpc(
        'upsert_subscription',
        subscriptionPayload,
      );

      if (error) {
        logger.error(
          {
            ...ctx,
            error: error.message,
            details: error.details,
            hint: error.hint,
            code: error.code,
          },
          'Error upserting subscription to database',
        );
        throw error;
      }

      logger.info(
        { ...ctx, subscriptionId: dbSubscription?.id },
        'Successfully upserted subscription',
      );
      return {
        stripeSubscription: stripeSubscription as Stripe.Subscription,
        dbSubscription: dbSubscription as UpsertSubscriptionReturn,
      };
    } catch (dbError) {
      logger.error(
        { ...ctx, error: dbError, payload: subscriptionPayload },
        'Error calling upsert_subscription RPC',
      );
      throw dbError;
    }
  } catch (error) {
    logger.error(ctx, 'Error syncing Stripe subscription to database', error);
    throw error;
  }
}
