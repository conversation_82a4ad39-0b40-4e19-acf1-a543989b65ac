import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { UUID } from 'crypto';
import type Stripe from 'stripe';

import { getLogger } from '@kit/shared/logger';

import { PurchaseTypeEnum } from '../../types';
import { syncStripeSubscriptionToDb } from './sync-stripe-subscription-to-db';

export async function syncStripeInvoiceToDb(
  client: SupabaseClient,
  stripe: Stripe,
  invoiceId: string,
) {
  const logger = await getLogger();
  const ctx = {
    name: 'syncStripeInvoiceToDb',
    invoiceId,
  };

  logger.info(ctx, 'Syncing Stripe invoice to database');

  // Fetch latest invoice data from Stripe
  try {
    const stripeInvoice = await stripe.invoices.retrieve(invoiceId);
    console.log(`🚀 ~ stripeInvoice:`, stripeInvoice);

    // If the invoice is for a subscription,
    // sync the subscription to the database
    if (stripeInvoice.parent?.subscription_details?.subscription) {
      await syncStripeSubscriptionToDb(
        client,
        stripe,
        stripeInvoice.parent.subscription_details.subscription as string,
      );
    }

    // upsert the invoice to the database
    const lineItems = stripeInvoice.lines?.data ?? [];
    console.log(`🚀 ~ lineItems:`, lineItems);
    console.log(`🚀 ~ lineItems[0].pricing:`, lineItems[0].pricing);

    const pLineItems = lineItems.map((item) => {
      console.log(`🚀 ~ item:`, item);
      const pricing = item.pricing as Stripe.InvoiceLineItem.Pricing;

      return {
        id: item.id,
        product_id: pricing?.price_details?.product as string,
        variant_id: pricing?.price_details?.price,
        quantity: item.quantity ?? 1,
        price_amount: pricing?.unit_amount ?? 0,
        invoice_id: stripeInvoice.id,
      };
    });

    console.log(`🚀 ~ pLineItems:`, pLineItems);

    const userId = lineItems[0].metadata?.user_id as UUID;
    console.log(`🚀 ~ userId:`, userId);
    const communityId = lineItems[0].metadata?.community_id as UUID;
    console.log(`🚀 ~ communityId:`, communityId);

    const purchaseType = lineItems[0].metadata
      ?.purchase_type as PurchaseTypeEnum;
    console.log(`🚀 ~ purchaseType:`, purchaseType);

    const paymentStatus = stripeInvoice.status;
    console.log(`🚀 ~ paymentStatus:`, paymentStatus);

    const status = paymentStatus === 'paid' ? 'succeeded' : 'pending';
    console.log(`🚀 ~ status:`, status);

    const currency = stripeInvoice.currency as string;
    console.log(`🚀 ~ currency:`, currency);

    // Create the invoice params
    const invoiceUpsert = {
      p_target_user_id: userId,
      p_target_community_id: communityId,
      p_target_invoice_id: stripeInvoice.id,
      p_status: status,
      p_purchase_type: purchaseType,
      p_total_amount: stripeInvoice.total ?? 0,
      p_currency: currency,
      p_line_items: pLineItems,
    };

    // Add the invoice to the database
    logger.info(ctx, 'Adding invoice to database');

    const { error } = await client.rpc(
      'upsert_invoice',
      invoiceUpsert as unknown as Record<string, unknown>,
    );

    if (error) {
      logger.error({ ...ctx, error }, 'Error upserting invoice to database');
      throw error;
    }

    return stripeInvoice;
  } catch (error) {
    logger.error({ ...ctx, error }, 'Error syncing Stripe invoice to database');
    throw error;
  }
}
