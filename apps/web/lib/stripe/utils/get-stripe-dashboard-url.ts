import { isStripeTestMode } from './is-stripe-test-mode';

type ResourceType = 'product' | 'price' | 'customer' | 'subscription';

/**
 * Constructs a Stripe dashboard URL for a resource
 * @param resourceType The type of resource (product, price, customer, subscription)
 * @param resourceId The ID of the resource
 * @returns The URL to the resource in the Stripe dashboard
 */
export function getStripeDashboardUrl(
  resourceType: ResourceType,
  resourceId: string,
): string {
  // Check if we're in test mode
  const isTestMode = isStripeTestMode();

  // Base URL for the Stripe dashboard
  const baseUrl = 'https://dashboard.stripe.com';

  // Add '/test' path segment for test mode
  const modePath = isTestMode ? '/test' : '';

  // Map resource types to their plural form for the URL
  const resourcePaths: Record<ResourceType, string> = {
    product: 'products',
    price: 'prices',
    customer: 'customers',
    subscription: 'subscriptions',
  };

  // Get the correct resource path
  const resourcePath = resourcePaths[resourceType];

  // Construct and return the full URL
  return `${baseUrl}${modePath}/${resourcePath}/${resourceId}`;
}
