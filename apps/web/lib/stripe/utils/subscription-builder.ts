import 'server-only';

import type <PERSON><PERSON> from 'stripe';

import { LineItem, PurchaseTypeEnum, UpsertSubscriptionParams } from '../types';
import { getISOString } from './get-iso-string';

/**
 * Builds a subscription payload directly from a Stripe subscription object
 */
export function buildSubscriptionPayloadFromStripe(
  subscription: Stripe.Subscription,
  params: {
    communityId: string;
    userId: string;
    paymentMethodId: string | null;
    purchaseType: PurchaseTypeEnum;
  },
): UpsertSubscriptionParams {
  const active =
    subscription.status === 'active' || subscription.status === 'trialing';

  const lineItems = subscription.items.data.map((item) => {
    const quantity = item.quantity ?? 1;
    const variantId = item.price?.id as string;
    const productId = (item.price?.product as Stripe.Product).id;

    return {
      id: item.id,
      quantity,
      subscription_id: subscription.id,
      subscription_item_id: item.id,
      product_id: productId,
      variant_id: variantId,
      price_amount: item.price?.unit_amount,
      interval: item.price?.recurring?.interval as string,
      interval_count: item.price?.recurring?.interval_count as number,
      type: 'flat' as string, // Only flat rate is supported for now
    } as LineItem;
  });

  // Build the subscription payload
  return {
    p_target_user_id: params.userId || subscription.metadata?.user_id || '',
    p_target_community_id: params.communityId,
    p_target_subscription_id: subscription.id,
    p_purchase_type:
      params.purchaseType || subscription.metadata?.purchase_type,
    p_active: active,
    p_status: subscription.status,
    p_payment_method_id:
      params.paymentMethodId ||
      (subscription.default_payment_method as string) ||
      undefined,
    p_currency: subscription.currency,
    p_line_items: lineItems,
    p_cancel_at_period_end: subscription.cancel_at_period_end ?? false,
    p_current_period_start: getISOString(
      subscription.items.data[0]?.current_period_start ?? 0,
    ) as string,
    p_current_period_end: getISOString(
      subscription.items.data[0]?.current_period_end ?? 0,
    ) as string,
    p_trial_starts_at: getISOString(subscription.trial_start),
    p_trial_ends_at: getISOString(subscription.trial_end),
  } as UpsertSubscriptionParams;
}
