import 'server-only';

import <PERSON><PERSON> from 'stripe';

import { StripeServerEnvSchema } from '../schema/stripe-server-env.schema';

/**
 * Stripe SDK Utility
 *
 * This utility provides functions to create Stripe SDK instances with different API versions.
 *
 * Environment Variables Required:
 * - STRIPE_SECRET_KEY: Your Stripe secret key
 * - STRIPE_WEBHOOK_SECRET: Your Stripe webhook secret
 * - At least one of the following API version variables:
 *   - STRIPE_API_VERSION_V1: Standard API version (e.g., '2025-05-28.basil')
 *   - STRIPE_API_VERSION_V1_BETA: Beta API version (e.g., '2025-05-28.basil; checkout_server_update_beta=v1;')
 *   - STRIPE_API_VERSION_V2: Preview API version (e.g., '2025-05-28.preview')
 *
 * Usage Examples:
 * Use default V1 API
 * const stripeV1 = await createStripeClient();
 *
 * Use V1 Beta API with checkout features
 * const stripeV1Beta = await createStripeClient('V1_BETA');
 *
 * Use V2 Preview API
 * const stripeV2 = await createStripeClient('V2');
 *
 * Synchronous version (for compatibility)
 * const stripe = createStripeSdk('V1');
 */

// Define the constructor interface for the beta version
interface StripeConstructor {
  new (apiKey: string, config?: { apiVersion?: string }): Stripe;
}

// Define the API version types that correspond to our environment variables
export type StripeApiVersionType = 'V1' | 'V1_BETA' | 'V2';

// Helper function to get the correct environment variable for the API version
function getApiVersionFromEnv(
  versionType: StripeApiVersionType,
): string | undefined {
  switch (versionType) {
    case 'V1':
      return process.env.STRIPE_API_VERSION_V1;
    case 'V1_BETA':
      return process.env.STRIPE_API_VERSION_V1_BETA;
    case 'V2':
      return process.env.STRIPE_API_VERSION_V2;
    default:
      return process.env.STRIPE_API_VERSION_V1; // fallback to V1
  }
}

/**
 * Creates a Stripe Client SDK instance
 * @param versionType - The API version type to use (defaults to 'V1')
 * @returns A Stripe instance
 */
export async function createStripeClient(
  versionType: StripeApiVersionType = 'V1',
) {
  // Get the API version from environment variables
  const apiVersion = getApiVersionFromEnv(versionType);

  // Throw error if the requested API version is not available
  if (!apiVersion) {
    const envVarName = `STRIPE_API_VERSION_${versionType}`;
    throw new Error(`Missing environment variable: ${envVarName}`);
  }

  // Parse the environment variables and validate them (only validate what we need)
  const stripeServerEnv = StripeServerEnvSchema.parse({
    secretKey: process.env.STRIPE_SECRET_KEY,
    webhooksSecret: process.env.STRIPE_WEBHOOK_SECRET,
    // Only include the API versions that are actually set
    ...(process.env.STRIPE_API_VERSION_V1 && {
      apiVersionV1: process.env.STRIPE_API_VERSION_V1,
    }),
    ...(process.env.STRIPE_API_VERSION_V1_BETA && {
      apiVersionV1Beta: process.env.STRIPE_API_VERSION_V1_BETA,
    }),
    ...(process.env.STRIPE_API_VERSION_V2 && {
      apiVersionV2: process.env.STRIPE_API_VERSION_V2,
    }),
  });

  // Type assertion to handle beta version export structure changes
  const StripeClass = Stripe as StripeConstructor;

  return new StripeClass(stripeServerEnv.secretKey, {
    apiVersion,
  });
}

/**
 * Creates a Stripe SDK instance synchronously
 * This is used in compatibility functions where we can't use async/await
 * @param versionType - The API version type to use (defaults to 'V1')
 * @returns A Stripe instance
 */
export function createStripeSdk(versionType: StripeApiVersionType = 'V1') {
  // We're using the static import for Stripe in synchronous contexts

  // We're not validating the env vars here because this is a compatibility function
  // The original code didn't validate them either in the sync version
  const secretKey = process.env.STRIPE_SECRET_KEY;
  if (!secretKey) {
    throw new Error('STRIPE_SECRET_KEY is required');
  }

  // Get the API version from environment variables
  const apiVersion = getApiVersionFromEnv(versionType);

  // Throw error if the requested API version is not available
  if (!apiVersion) {
    const envVarName = `STRIPE_API_VERSION_${versionType}`;
    throw new Error(`Missing environment variable: ${envVarName}`);
  }

  // Type assertion to handle beta version export structure changes
  const StripeClass = Stripe as StripeConstructor;

  return new StripeClass(secretKey, {
    apiVersion,
  });
}
