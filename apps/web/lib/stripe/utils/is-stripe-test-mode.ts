/**
 * Checks if <PERSON><PERSON> is in test mode based on the environment variables
 * @returns boolean indicating if <PERSON><PERSON> is in test mode
 */
export function isStripeTestMode(): boolean {
  // First check for the public environment variable which works in both client and server
  const publicMode = process.env.NEXT_PUBLIC_STRIPE_MODE;
  if (publicMode) {
    return publicMode === 'test';
  }

  // Fallback to checking the secret key (only works server-side)
  const secretKey = process.env.STRIPE_SECRET_KEY;

  // If the secret key starts with sk_test_, we're in test mode
  return secretKey?.startsWith('sk_test_') ?? false;
}
