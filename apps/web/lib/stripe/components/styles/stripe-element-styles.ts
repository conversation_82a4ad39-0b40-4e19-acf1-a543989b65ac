/**
 * Utility for consistent Stripe Element styling across the application
 * example usage:
 * const elementStyles = getStripeElementStyles();
 * <CardNumberElement options={elementStyles} />
 */

type Theme = 'light' | 'dark';

const THEME_STYLES = {
  light: {
    color: '#0a0a0a', // Match --foreground (Hex)
    placeholderColor: '#737373', // Match --muted-foreground (Hex)
  },
  dark: {
    color: '#fafafa', // Match dark --foreground (Hex)
    placeholderColor: '#a3a3a3', // Match dark --muted-foreground (Hex)
  },
} as const;

const BASE_STYLES = {
  fontSize: '14px',
  backgroundColor: 'transparent', // Keep transparent to inherit page background
  fontFamily:
    '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
};

// Function now accepts an optional theme parameter, defaulting to 'light'
export function getStripeElementStyles(theme: Theme = 'light') {
  const currentTheme = THEME_STYLES[theme];

  return {
    style: {
      base: {
        ...BASE_STYLES,
        color: currentTheme.color,
        '::placeholder': {
          color: currentTheme.placeholderColor,
        },

        ':focus': {
          color: currentTheme.color,
        },
      },
      invalid: {
        color: '#ef4444',
        iconColor: '#ef4444',
      },

      empty: {
        color: '#6b7280',
      },
    },
  };
}
