'use server';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { getStripeCustomerService } from '../../services/stripe-factory.service';

/**
 * @name getStripeAccountBilling
 * @description Get stripe account billing data including stripe_account_id and default_payment_method
 * @param userId User ID to get billing data for
 */
export async function getStripeAccountBilling(userId: string) {
  const supabase = getSupabaseServerClient();
  const stripeCustomerService = getStripeCustomerService(supabase);
  return stripeCustomerService.getStripeAccountBilling(userId);
}

/**
 * @name listPaymentMethods
 * @description List payment methods for a customer
 * @param stripeAccountId Customer ID to list payment methods for
 */
export async function listPaymentMethods(stripeAccountId: string) {
  const supabase = getSupabaseServerClient();
  const stripeCustomerService = getStripeCustomerService(supabase);
  return stripeCustomerService.listPaymentMethods(stripeAccountId);
}

/**
 * @name detachPaymentMethod
 * @description Detach a payment method from a customer
 * @param paymentMethodId Payment method ID to detach
 */
export async function detachPaymentMethod(paymentMethodId: string) {
  const supabase = getSupabaseServerClient();
  const stripeCustomerService = getStripeCustomerService(supabase);
  return stripeCustomerService.detachPaymentMethod(paymentMethodId);
}

export async function attachPaymentMethod(
  stripeAccountId: string,
  paymentMethodId: string,
) {
  const supabase = getSupabaseServerClient();
  const stripeCustomerService = getStripeCustomerService(supabase);
  return stripeCustomerService.attachPaymentMethodToCustomer(
    stripeAccountId,
    paymentMethodId,
  );
}
