'use client';

import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import { formatCurrency } from '@kit/shared/utils';
import { If } from '@kit/ui/if';

import type { LineItemSchema } from '../schema/products-schema';

export function LineItemDetails(
  props: React.PropsWithChildren<{
    lineItems: z.infer<typeof LineItemSchema>[];
    currency: string;
    selectedInterval?: string | undefined;
  }>,
) {
  const locale = useTranslation().i18n.language;
  const currencyCode = props.currency.toLowerCase();
  const className = 'flex justify-between gap-3 py-1';

  return (
    <div className={'flex flex-col'}>
      {props.lineItems.map((item) => {
        // If the item has a description, we render it as a simple text
        const Description = () => (
          <>
            <If condition={item.description}>
              <p className={'text-muted-foreground mt-1 text-xs'}>
                {item.description}
              </p>
            </If>
          </>
        );

        return (
          <div key={item.id} className={'flex flex-col'}>
            <div className={className}>
              <span className={'flex items-center space-x-1'}>
                <span>{item.name}</span>
              </span>

              <If condition={!item.tiers?.length}>
                <span className={'font-semibold'}>
                  {formatCurrency({
                    currencyCode,
                    value: item.cost / 100,
                    locale,
                  })}
                </span>
              </If>
            </div>

            <Description />
          </div>
        );
      })}
    </div>
  );
}

LineItemDetails.displayName = 'LineItemDetails';
