import { BadgeCheck } from 'lucide-react';

import { Tables } from '@kit/supabase/database';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { Trans } from '@kit/ui/trans';

import {
  PlatformProducts,
  getProductPricePairByVariantId,
} from '../../schema/products-schema';
import { CurrentPriceBadge } from './current-price-badge';
import { LineItemDetails } from './line-item-details';

type Invoice = Tables<'invoices'>;
type LineItem = Tables<'invoice_items'>;

type CurrentLifetimeOrderCardProps = {
  invoice: Invoice & {
    items: LineItem[];
  };

  config: PlatformProducts;
};

/**
 * Card component displaying details about a user's lifetime invoice
 */
export function CurrentLifetimeOrderCard({
  invoice,
  config,
}: React.PropsWithChildren<CurrentLifetimeOrderCardProps>) {
  const lineItems = invoice.items;
  const firstLineItem = lineItems[0];

  if (!firstLineItem) {
    throw new Error('No line items found in subscription');
  }

  const { product, price } = getProductPricePairByVariantId(
    config,
    firstLineItem.variant_id,
  );

  if (!product || !price) {
    throw new Error(
      'Product or price not found. Did you forget to add it to the billing config?',
    );
  }

  const productLineItems = price.lineItems;

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          <Trans i18nKey="billing:priceCardTitle" />
        </CardTitle>

        <CardDescription>
          <Trans i18nKey="billing:priceCardDescription" />
        </CardDescription>
      </CardHeader>

      <CardContent className={'space-y-3 text-sm'}>
        <div className={'flex flex-col space-y-1'}>
          <div className={'flex items-center space-x-2 text-lg font-semibold'}>
            <BadgeCheck
              className={
                's-6 fill-green-500 text-white dark:fill-white dark:text-black'
              }
            />

            <span>{product.name}</span>

            <CurrentPriceBadge status={invoice.status} />
          </div>
        </div>

        <div>
          <div className="flex flex-col space-y-0.5">
            <span className="font-semibold">
              <Trans i18nKey="billing:detailsLabel" />
            </span>

            <LineItemDetails
              lineItems={productLineItems}
              currency={invoice.currency}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

CurrentLifetimeOrderCard.displayName = 'CurrentLifetimeOrderCard';
