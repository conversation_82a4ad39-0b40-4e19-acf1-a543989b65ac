'use client';

import { useMemo } from 'react';

import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowRight, CheckCircle } from 'lucide-react';
import { useForm, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import { formatCurrency } from '@kit/shared/utils';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { If } from '@kit/ui/if';
import { RadioGroup, RadioGroupItem } from '@kit/ui/radio-group';
import { Trans } from '@kit/ui/trans';
import { cn } from '@kit/ui/utils';

import {
  PriceInterval,
  PriceWithLineItems,
  ProductWithPrices,
} from '../../types';

/**
 * Get unique price intervals from products
 */
const getIntervalsFromProducts = (
  products: ProductWithPrices[],
): PriceInterval[] => {
  const intervals = new Set<PriceInterval>();

  products.forEach((product) => {
    product.productPrices?.forEach((price) => {
      if (price.type === 'recurring' && price.interval) {
        intervals.add(price.interval);
      }
    });
  });

  return Array.from(intervals);
};

/**
 * Helper function to extract features from product or metadata
 */
function getProductFeatures(product: ProductWithPrices): string[] {
  if ('features' in product && Array.isArray(product.features)) {
    return product.features;
  }

  if (
    product.metadata &&
    typeof product.metadata === 'object' &&
    'features' in product.metadata
  ) {
    if (Array.isArray(product.metadata.features)) {
      return product.metadata.features;
    }
    if (typeof product.metadata.features === 'string') {
      try {
        return JSON.parse(product.metadata.features);
      } catch {
        return [];
      }
    }
  }

  return [];
}

/**
 * Helper function to determine if product should be highlighted
 */
function isProductHighlighted(product: ProductWithPrices): boolean {
  return Boolean(
    ('highlighted' in product && product.highlighted) ||
      (product.metadata &&
        typeof product.metadata === 'object' &&
        'highlighted' in product.metadata &&
        String(product.metadata.highlighted) === 'true'),
  );
}

/**
 * Helper function to get badge text
 */
function getProductBadge(product: ProductWithPrices): string {
  if ('badge' in product && typeof product.badge === 'string') {
    return product.badge;
  }

  if (
    product.metadata &&
    typeof product.metadata === 'object' &&
    'badge' in product.metadata
  ) {
    return String(product.metadata.badge);
  }

  return 'Popular';
}

/**
 * Find product and price by priceId in products array
 */
function findProductAndPriceByPriceId(
  products: ProductWithPrices[],
  priceId: string,
): {
  product: ProductWithPrices | null;
  price: PriceWithLineItems | null;
} {
  for (const product of products) {
    if (!product.productPrices) continue;

    for (const price of product.productPrices) {
      if (price.id === priceId) {
        return { product, price };
      }
    }
  }

  return { product: null, price: null };
}

/**
 * Component for selecting a billing price
 *
 * This is a simplified version of the price picker component.
 * The full implementation includes more features and UI elements.
 */
export function PricePicker(
  props: React.PropsWithChildren<{
    productWithPrices: ProductWithPrices[];
    onSubmit: (data: { priceId: string; productId: string }) => void;
    canStartTrial?: boolean;
    pending?: boolean;
  }>,
) {
  const { t } = useTranslation(`billing`);

  // Get intervals based on approach
  const intervals = useMemo(() => {
    return getIntervalsFromProducts(props.productWithPrices);
  }, [props.productWithPrices]) as PriceInterval[];

  const form = useForm({
    reValidateMode: 'onChange',
    mode: 'onChange',
    resolver: zodResolver(
      z
        .object({
          priceId: z.string(),
          productId: z.string(),
          interval: z.string().optional(),
        })
        .refine(
          (data) => {
            try {
              const { product, price } = findProductAndPriceByPriceId(
                props.productWithPrices,
                data.priceId,
              );
              return product && price;
            } catch {
              return false;
            }
          },
          { message: t('noPriceChosen'), path: ['priceId'] },
        ),
    ),
    defaultValues: {
      interval: intervals[0] || 'month',
      priceId: '',
      productId: '',
    },
  });

  const selectedInterval = useWatch({
    name: 'interval',
    control: form.control,
  }) as PriceInterval;

  const priceId = form.getValues('priceId');

  // Get selected price and product
  const { price: selectedPrice, product: selectedProduct } = useMemo(() => {
    try {
      return findProductAndPriceByPriceId(props.productWithPrices, priceId);
    } catch {
      return {
        price: null,
        product: null,
      };
    }
  }, [props.productWithPrices, priceId]);

  // display the period picker if the selected price is recurring or if no price is selected
  const isRecurringPrice = selectedPrice
    ? (selectedPrice as PriceWithLineItems).type === 'recurring'
    : true;

  const locale = useTranslation().i18n.language;

  // If no products are provided, show an error
  if (!props.productWithPrices || props.productWithPrices.length === 0) {
    return <div>No billing configuration or products available</div>;
  }

  return (
    <Form {...form}>
      <div
        className={
          'flex flex-col space-y-4 lg:flex-row lg:space-y-0 lg:space-x-4'
        }
      >
        <form
          className={'flex w-full max-w-xl flex-col space-y-4'}
          onSubmit={form.handleSubmit(props.onSubmit)}
        >
          {/* Interval Selection */}
          <If condition={intervals.length}>
            <div
              className={cn('transition-all', {
                ['pointer-events-none opacity-50']: !isRecurringPrice,
              })}
            >
              <FormField
                name={'interval'}
                render={({ field }) => (
                  <FormItem className={'rounded-md border p-4'}>
                    <FormLabel>
                      <Trans i18nKey={'common:billingInterval.label'} />
                    </FormLabel>

                    <FormControl>
                      <RadioGroup name={field.name} value={field.value}>
                        <div className={'flex space-x-2.5'}>
                          {intervals.map((interval) => {
                            const selected = field.value === interval;

                            return (
                              <label
                                key={interval}
                                className={cn(
                                  'flex items-center space-x-2 rounded-md border border-transparent px-4 py-2 transition-colors',
                                  {
                                    ['border-primary']: selected,
                                    ['hover:border-primary']: !selected,
                                  },
                                )}
                              >
                                <RadioGroupItem
                                  value={interval}
                                  onClick={() => {
                                    form.setValue('interval', interval, {
                                      shouldValidate: true,
                                    });

                                    if (selectedProduct) {
                                      const platformProduct =
                                        selectedProduct as ProductWithPrices;
                                      const price =
                                        platformProduct.productPrices?.find(
                                          (item) =>
                                            item.type === 'recurring' &&
                                            item.interval === interval,
                                        );

                                      form.setValue(
                                        'priceId',
                                        price?.id ?? '',
                                        {
                                          shouldValidate: true,
                                          shouldDirty: true,
                                          shouldTouch: true,
                                        },
                                      );
                                    }
                                  }}
                                />
                                <span
                                  className={cn('text-sm', {
                                    ['cursor-pointer']: !selected,
                                  })}
                                >
                                  <Trans
                                    i18nKey={`billing:billingInterval.${interval}`}
                                  />
                                </span>
                              </label>
                            );
                          })}
                        </div>
                      </RadioGroup>
                    </FormControl>

                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </If>

          {/* Price Selection */}
          <FormField
            name={'priceId'}
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  <Trans i18nKey={'common:pricePickerLabel'} />
                </FormLabel>

                <FormControl>
                  <RadioGroup value={field.value} name={field.name}>
                    {props.productWithPrices.map((product) => {
                      const price = product.productPrices?.find((p) => {
                        if (p.type !== 'recurring') {
                          return true; // One-time payment
                        }
                        return p.interval === selectedInterval;
                      });

                      if (!price || !product.active) {
                        return null;
                      }

                      const priceId = price.id;
                      const selected = field.value === priceId;

                      return (
                        <label
                          key={priceId}
                          className={cn(
                            'hover:border-primary relative m-0 flex cursor-pointer flex-col space-y-2 rounded-lg border p-4',
                            { ['border-primary']: selected },
                          )}
                          onClick={() => {
                            form.setValue('priceId', priceId, {
                              shouldValidate: true,
                            });

                            form.setValue('productId', product.id, {
                              shouldValidate: true,
                            });
                          }}
                        >
                          <div className={'flex flex-col'}>
                            <div
                              className={'flex items-center justify-between'}
                            >
                              <RadioGroupItem
                                value={priceId}
                                className={'sr-only'}
                              />

                              <div className={'flex items-center space-x-2'}>
                                {/* Product name and badge */}
                                <div className={'mb-4 flex justify-between'}>
                                  <span
                                    className={
                                      'text-foreground text-lg font-semibold'
                                    }
                                  >
                                    {product.name}
                                  </span>

                                  {isProductHighlighted(product) && (
                                    <Badge>{getProductBadge(product)}</Badge>
                                  )}
                                </div>
                              </div>

                              <If condition={selected}>
                                <CheckCircle
                                  className={'text-primary h-5 w-5 stroke-2'}
                                />
                              </If>
                            </div>

                            <div
                              className={'flex items-center justify-between'}
                            >
                              <p className={'text-muted-foreground text-sm'}>
                                {product.description}
                              </p>

                              <span className={'text-2xl font-bold'}>
                                {formatCurrency({
                                  value: (price.unitAmount || 0) / 100,
                                  currencyCode: price.currency,
                                  locale,
                                })}
                                <span className={'ml-1 text-sm font-normal'}>
                                  {price.type === 'recurring'
                                    ? `/${price.interval}`
                                    : ''}
                                </span>
                              </span>
                            </div>
                          </div>

                          {/* Product features */}
                          <If
                            condition={
                              ('features' in product &&
                                Array.isArray(product.features) &&
                                product.features.length > 0) ||
                              (product.metadata &&
                                typeof product.metadata === 'object' &&
                                'features' in product.metadata)
                            }
                          >
                            <div className={'flex flex-col space-y-1'}>
                              {getProductFeatures(product).map(
                                (feature: string, i: number) => (
                                  <div
                                    key={i}
                                    className={'flex items-center space-x-2'}
                                  >
                                    <span>
                                      <CheckCircle className="size-4 text-green-500" />
                                    </span>
                                    <span className={'text-sm'}>
                                      {typeof feature === 'string'
                                        ? feature
                                        : ''}
                                    </span>
                                  </div>
                                ),
                              )}
                            </div>
                          </If>
                        </label>
                      );
                    })}
                  </RadioGroup>
                </FormControl>

                <FormMessage />
              </FormItem>
            )}
          />

          <div className={'flex justify-end'}>
            <Button type={'submit'} disabled={props.pending}>
              <span>
                <Trans i18nKey={'common:checkout'} />
              </span>
              <ArrowRight className={'ml-2 h-4 w-4'} />
            </Button>
          </div>
        </form>
      </div>
    </Form>
  );
}

PricePicker.displayName = 'PricePicker';
