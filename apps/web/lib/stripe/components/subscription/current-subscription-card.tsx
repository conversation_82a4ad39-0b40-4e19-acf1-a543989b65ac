import { formatDate } from 'date-fns';
import { BadgeCheck } from 'lucide-react';

import { Tables } from '@kit/supabase/database';
import { Alert, AlertDescription, AlertTitle } from '@kit/ui/alert';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { If } from '@kit/ui/if';
import { Trans } from '@kit/ui/trans';

import {
  PlatformProducts,
  getProductPricePairByVariantId,
} from '../../schema/products-schema';
import {
  LineItemType,
  PlatformLineItem,
  PlatformProduct,
  ProductPrice,
} from '../../types/platform-billing';
import { CurrentPriceAlert } from './current-price-alert';
import { CurrentPriceBadge } from './current-price-badge';
import { LineItemDetails } from './line-item-details';

type Subscription = Tables<'subscriptions'>;
type LineItem = Tables<'subscription_items'>;

// Extended types for internal use
type ProductWithPrices = PlatformProduct & {
  productPrices?: PriceWithLineItems[];
};

type PriceWithLineItems = ProductPrice & {
  lineItems?: PlatformLineItem[];
};

type CurrentSubscriptionCardProps = {
  subscription: Subscription & {
    items: LineItem[];
  };
  // Support both old and new approaches for backwards compatibility
  config?: PlatformProducts;
  products?: ProductWithPrices[];
};

// Helper to convert string type to LineItemType enum
function mapStringToLineItemType(type: string): LineItemType {
  switch (type) {
    case 'flat':
      return 'flat';
    case 'perSeat':
      return 'perSeat';
    case 'metered':
      return 'metered';
    default:
      return 'flat'; // Default to flat as a fallback
  }
}

/**
 * Find a product and price by variant ID using the platform products data
 */
function findProductAndPriceByVariantId(
  products: ProductWithPrices[],
  variantId: string,
): { product: ProductWithPrices | null; price: PriceWithLineItems | null } {
  for (const product of products) {
    if (!product.productPrices) continue;

    for (const price of product.productPrices) {
      if (price.id === variantId) {
        return { product, price };
      }
    }
  }

  return { product: null, price: null };
}

/**
 * Card component displaying details about a user's current subscription
 */
export function CurrentSubscriptionCard({
  subscription,
  config,
  products,
}: React.PropsWithChildren<CurrentSubscriptionCardProps>) {
  const lineItems = subscription.items;
  const firstLineItem = lineItems[0];

  if (!firstLineItem) {
    throw new Error('No line items found in subscription');
  }

  // Use either the new approach with products or fall back to the old approach with config
  let product;
  let price;
  let productLineItems;

  if (products && products.length > 0) {
    // New approach using platform products
    const result = findProductAndPriceByVariantId(
      products,
      firstLineItem.variant_id,
    );

    if (!result.product || !result.price) {
      throw new Error('Product or price not found in the platform products');
    }

    product = {
      id: result.product.id,
      name: result.product.name,
      description: result.product.description || 'No description',
    };

    price = {
      id: result.price.id,
      name: result.price.nickname || result.price.id,
      interval:
        result.price.type === 'recurring'
          ? (result.price.interval as 'month' | 'year' | undefined)
          : undefined,
      lineItems:
        result.price.lineItems?.map((item) => ({
          id: item.id,
          name: item.name,
          type: mapStringToLineItemType(item.type),
          cost: Number(item.cost) / 100,
        })) || [],
    };

    productLineItems = price.lineItems;
  } else if (config) {
    // Legacy approach using billingConfig
    const result = getProductPricePairByVariantId(
      config,
      firstLineItem.variant_id,
    );

    if (!result.product || !result.price) {
      throw new Error('Product or price not found in the billing config');
    }

    product = result.product;
    price = result.price;
    productLineItems = price.lineItems;
  } else {
    throw new Error('Either products or config must be provided');
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          <Trans i18nKey="billing:priceCardTitle" />
        </CardTitle>

        <CardDescription>
          <Trans i18nKey="billing:priceCardDescription" />
        </CardDescription>
      </CardHeader>

      <CardContent className={'space-y-4 border-t pt-4 text-sm'}>
        <div className={'flex flex-col space-y-1'}>
          <div className={'flex items-center space-x-2 text-lg font-semibold'}>
            <BadgeCheck
              className={
                's-6 fill-green-500 text-white dark:fill-white dark:text-black'
              }
            />

            <span data-test={'current-price-card-product-name'}>
              <Trans i18nKey={product.name} defaults={product.name} />
            </span>

            <CurrentPriceBadge status={subscription.status} />
          </div>

          <div>
            <p className={'text-muted-foreground'}>
              <Trans
                i18nKey={product.description}
                defaults={product.description}
              />
            </p>
          </div>
        </div>

        {/*
         Only show the alert if the subscription requires action
          (e.g. trial ending soon, subscription canceled, etc.)
        */}
        <If condition={!subscription.active}>
          <div data-test={'current-price-card-status-alert'}>
            <CurrentPriceAlert status={subscription.status} />
          </div>
        </If>

        <If condition={subscription.status === 'trialing'}>
          <div className="flex flex-col space-y-0.5">
            <span className="font-semibold">
              <Trans i18nKey="billing:trialEndsOn" />
            </span>

            <div className={'text-muted-foreground'}>
              <span>
                {subscription.trial_ends_at
                  ? formatDate(subscription.trial_ends_at, 'P')
                  : ''}
              </span>
            </div>
          </div>
        </If>

        <If condition={subscription.cancel_at_period_end}>
          <Alert variant={'warning'}>
            <AlertTitle>
              <Trans i18nKey="billing:subscriptionCancelled" />
            </AlertTitle>

            <AlertDescription>
              <Trans i18nKey="billing:cancelSubscriptionDate" />:
              <span className={'ml-1'}>
                {formatDate(subscription.current_period_end ?? '', 'P')}
              </span>
            </AlertDescription>
          </Alert>
        </If>

        <div className="flex flex-col space-y-0.5">
          <span className="font-semibold">
            <Trans i18nKey="billing:detailsLabel" />
          </span>

          <LineItemDetails
            lineItems={productLineItems}
            currency={subscription.currency}
            selectedInterval={firstLineItem.interval}
          />
        </div>
      </CardContent>
    </Card>
  );
}

CurrentSubscriptionCard.displayName = 'CurrentSubscriptionCard';
