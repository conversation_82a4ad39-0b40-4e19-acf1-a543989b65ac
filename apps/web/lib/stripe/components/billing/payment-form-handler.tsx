'use client';

import { useConfirmPayment } from './confirm-payment';

type PaymentFormHandlerProps = {
  children: (
    confirmPayment: (
      clientSecret: string,
      isSetupIntent?: boolean,
    ) => Promise<{ error?: unknown }>,
  ) => React.ReactNode;
};

export function PaymentFormHandler({ children }: PaymentFormHandlerProps) {
  const confirmPayment = useConfirmPayment();
  return <>{children(confirmPayment)}</>;
}

PaymentFormHandler.displayName = 'PaymentFormHandler';
