'use client';

import { useCallback, useEffect, useState, useTransition } from 'react';

import {
  CardCvcElement,
  CardExpiryElement,
  CardNumberElement,
  useElements,
  useStripe,
} from '@stripe/react-stripe-js';
import { useTranslation } from 'react-i18next';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@kit/ui/alert-dialog';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardFooter } from '@kit/ui/card';
import { FaIcon } from '@kit/ui/dojo/atoms/fa-icon';
import { Spinner } from '@kit/ui/spinner';
import { Trans } from '@kit/ui/trans';
import { cn } from '@kit/ui/utils';

import {
  attachPaymentMethod,
  detachPaymentMethod,
  getStripeAccountBilling,
  listPaymentMethods,
} from '../_lib/payment-method.loader';
import { getStripeElementStyles } from '../styles/stripe-element-styles';
import { StripeElementsProvider } from './stripe-elements-provider';

// TODO: This type is duplicated, we should move it to the types folder
type PaymentMethodDisplay = {
  id: string;
  card: {
    brand: string;
    last4: string;
    expMonth: number;
    expYear: number;
  };
};

type PaymentMethodsListProps = {
  userId: string;
  containerClassName?: string;
  onPaymentMethodAdded?: () => void;
  onPaymentMethodAddError?: (error: string) => void;
  onPaymentMethodRemoved?: () => void;
  onPaymentMethodRemoveError?: (error: string) => void;
};

export function PaymentMethodsList({
  userId,
  containerClassName,
  onPaymentMethodAdded,
  onPaymentMethodAddError,
  onPaymentMethodRemoved,
  onPaymentMethodRemoveError,
}: PaymentMethodsListProps) {
  return (
    <div className={containerClassName}>
      <StripeElementsProvider>
        <PaymentMethodsContent
          userId={userId}
          onPaymentMethodAdded={onPaymentMethodAdded}
          onPaymentMethodAddError={onPaymentMethodAddError}
          onPaymentMethodRemoved={onPaymentMethodRemoved}
          onPaymentMethodRemoveError={onPaymentMethodRemoveError}
        />
      </StripeElementsProvider>
    </div>
  );
}

function PaymentMethodsContent({
  userId,
  onPaymentMethodAdded,
  onPaymentMethodAddError,
  onPaymentMethodRemoved,
  onPaymentMethodRemoveError,
}: {
  userId: string;
  onPaymentMethodAdded?: () => void;
  onPaymentMethodAddError?: (error: string) => void;
  onPaymentMethodRemoved?: () => void;
  onPaymentMethodRemoveError?: (error: string) => void;
}) {
  useTranslation();
  const stripeJs = useStripe();
  const elements = useElements();
  const [isPending, startTransition] = useTransition();
  const [showNewCard, setShowNewCard] = useState(false);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethodDisplay[]>(
    [],
  );
  const [error, setError] = useState<string | null>(null);
  const [focusedElement, setFocusedElement] = useState<string>('');
  const [paymentMethodToDelete, setPaymentMethodToDelete] = useState<
    string | null
  >(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const loadPaymentMethods = useCallback(async () => {
    startTransition(async () => {
      const billingData = await getStripeAccountBilling(userId);
      if (billingData?.stripeAccountId) {
        const paymentMethodsResult = await listPaymentMethods(
          billingData.stripeAccountId,
        );
        if (paymentMethodsResult.data) {
          setPaymentMethods(paymentMethodsResult.data);
        }
      }
    });
  }, [userId]);

  useEffect(() => {
    loadPaymentMethods();
  }, [userId, loadPaymentMethods]);

  const handleSubmit = async () => {
    if (!stripeJs || !elements) {
      setError('Payment form is not ready yet. Please try again.');
      return;
    }

    const cardNumberElement = elements.getElement(CardNumberElement);
    if (!cardNumberElement) {
      setError('Card number element is not ready. Please try again.');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // First, create the payment method with Stripe
      const { paymentMethod, error: stripeError } =
        await stripeJs.createPaymentMethod({
          type: 'card',
          card: cardNumberElement,
        });

      if (stripeError) {
        setError(stripeError.message ?? 'An error occurred');
        onPaymentMethodAddError?.(stripeError.message ?? 'An error occurred');
        return;
      }

      if (paymentMethod) {
        // Get customer billing data
        const billingData = await getStripeAccountBilling(userId);
        if (!billingData?.stripeAccountId) {
          throw new Error('Stripe account not found');
        }

        // Attach the payment method to the customer
        await attachPaymentMethod(
          billingData.stripeAccountId,
          paymentMethod.id,
        );

        // Update local state
        setPaymentMethods((prev) => [
          ...prev,
          {
            id: paymentMethod.id,
            card: {
              brand: paymentMethod.card?.brand ?? '',
              last4: paymentMethod.card?.last4 ?? '',
              expMonth: paymentMethod.card?.exp_month ?? 0,
              expYear: paymentMethod.card?.exp_year ?? 0,
            },
          },
        ]);

        onPaymentMethodAdded?.();
        await loadPaymentMethods();
        setShowNewCard(false);
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'An error occurred';
      setError(errorMessage);
      onPaymentMethodAddError?.(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeletePaymentMethod = async (paymentMethodId: string) => {
    startTransition(async () => {
      try {
        await detachPaymentMethod(paymentMethodId);
        setPaymentMethods((prev) =>
          prev.filter((pm) => pm.id !== paymentMethodId),
        );
        onPaymentMethodRemoved?.();
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : String(error);
        console.error('Failed to remove payment method:', errorMessage);
        onPaymentMethodRemoveError?.(errorMessage);
      }
      setPaymentMethodToDelete(null);
    });
  };

  const getCardIcon = (brand: string) => {
    switch (brand.toLowerCase()) {
      case 'visa':
        return 'brands/cc-visa';
      case 'mastercard':
        return 'brands/cc-mastercard';
      case 'jcb':
        return 'brands/cc-jcb';
      case 'amex':
        return 'brands/cc-amex';
      case 'discover':
        return 'brands/cc-discover';
      case 'diners':
        return 'brands/cc-diners-club';
      default:
        return 'solid/credit-card';
    }
  };

  // Use the utility function instead of inline styles
  const elementStyles = getStripeElementStyles();

  const handleCancelNewCard = () => {
    if (!isSubmitting) {
      if (elements) {
        const cardNumber = elements.getElement(CardNumberElement);
        const cardExpiry = elements.getElement(CardExpiryElement);
        const cardCvc = elements.getElement(CardCvcElement);

        cardNumber?.clear();
        cardExpiry?.clear();
        cardCvc?.clear();
      }
      setError(null);
      setShowNewCard(false);
    }
  };

  if (isPending && paymentMethods.length === 0) {
    return (
      <div className="flex items-center justify-center py-8">
        <Spinner />
      </div>
    );
  }

  return (
    <div className="space-y-4" data-test="payment-methods-list">
      {/* List of existing payment methods */}
      {paymentMethods.length === 0 && !showNewCard ? (
        <div className="py-6 text-center">
          <FaIcon
            icon="solid/credit-card"
            className="text-muted-foreground mx-auto mb-4 h-12 w-12"
          />
          <p className="text-muted-foreground">
            <Trans i18nKey="billing:paymentMethods.noPaymentMethods" />
          </p>
          <Button
            onClick={() => setShowNewCard(true)}
            className="mt-4"
            data-test="add-payment-method-button"
          >
            <FaIcon icon="solid/plus" className="mr-2 h-4 w-4" />
            <Trans i18nKey="billing:paymentMethods.addPaymentMethod" />
          </Button>
        </div>
      ) : (
        <>
          {paymentMethods.map((method) => (
            <Card key={method.id} className="overflow-hidden">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <FaIcon
                      icon={getCardIcon(method.card.brand)}
                      className="text-primary h-8 w-8"
                    />
                    <div>
                      <p className="font-medium">
                        {method.card.brand.toUpperCase()} ••••{' '}
                        {method.card.last4}
                      </p>
                      <p className="text-muted-foreground text-sm">
                        <Trans
                          i18nKey="billing:paymentMethods.cardExpiry"
                          values={{
                            month: method.card.expMonth
                              .toString()
                              .padStart(2, '0'),
                            year: method.card.expYear,
                          }}
                        />
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-destructive hover:bg-destructive/10 hover:text-destructive/90"
                    onClick={() => setPaymentMethodToDelete(method.id)}
                    data-test={`delete-payment-method-${method.id}`}
                  >
                    <FaIcon icon="solid/trash" className="mr-2 h-4 w-4" />
                    <Trans i18nKey="billing:paymentMethods.remove" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}

          {!showNewCard && (
            <Button
              onClick={() => setShowNewCard(true)}
              variant="outline"
              className="mt-4 w-full"
              data-test="add-payment-method-button"
            >
              <FaIcon icon="solid/plus" className="mr-2 h-4 w-4" />
              <Trans i18nKey="billing:paymentMethods.addPaymentMethod" />
            </Button>
          )}
        </>
      )}

      {/* Add new card form */}
      {showNewCard && (
        <Card>
          <CardContent className="space-y-4 p-4">
            <h3 className="text-lg font-medium">
              <Trans i18nKey="billing:paymentMethods.addNewCard" />
            </h3>
            <div className="space-y-3">
              <div
                className={cn(
                  'border-border rounded-md border p-3',
                  focusedElement === 'cardNumber' && 'border-primary',
                )}
              >
                <label className="text-foreground text-sm font-medium">
                  <Trans i18nKey="billing:paymentMethods.cardNumber" />
                </label>
                <CardNumberElement
                  onFocus={() => setFocusedElement('cardNumber')}
                  onBlur={() => setFocusedElement('')}
                  options={elementStyles}
                />
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div
                  className={cn(
                    'border-border rounded-md border p-3',
                    focusedElement === 'cardExpiry' && 'border-primary',
                  )}
                >
                  <label className="text-foreground text-sm font-medium">
                    <Trans i18nKey="billing:paymentMethods.expiration" />
                  </label>
                  <CardExpiryElement
                    onFocus={() => setFocusedElement('cardExpiry')}
                    onBlur={() => setFocusedElement('')}
                    options={elementStyles}
                  />
                </div>

                <div
                  className={cn(
                    'border-border rounded-md border p-3',
                    focusedElement === 'cardCvc' && 'border-primary',
                  )}
                >
                  <label className="text-foreground text-sm font-medium">
                    <Trans i18nKey="billing:paymentMethods.cvc" />
                  </label>
                  <CardCvcElement
                    onFocus={() => setFocusedElement('cardCvc')}
                    onBlur={() => setFocusedElement('')}
                    options={elementStyles}
                  />
                </div>
              </div>
            </div>

            {error && (
              <div className="text-destructive mt-2 text-sm">{error}</div>
            )}
          </CardContent>
          <CardFooter className="flex justify-between border-t px-4 py-3">
            <Button
              variant="ghost"
              onClick={handleCancelNewCard}
              disabled={isSubmitting}
              data-test="cancel-add-card"
            >
              <Trans i18nKey="common:cancel" />
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting || !stripeJs || !elements}
              data-test="save-payment-method"
            >
              {isSubmitting ? (
                <>
                  <Spinner className="mr-2 h-4 w-4" />
                  <Trans i18nKey="common:saving" />
                </>
              ) : (
                <Trans i18nKey="billing:paymentMethods.saveCard" />
              )}
            </Button>
          </CardFooter>
        </Card>
      )}

      {/* Confirmation dialog for deleting payment method
       * TODO: Move this to a separate component
       */}
      <AlertDialog
        open={!!paymentMethodToDelete}
        onOpenChange={(isOpen) => !isOpen && setPaymentMethodToDelete(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              <Trans i18nKey="billing:paymentMethods.removePaymentMethodTitle" />
            </AlertDialogTitle>
            <AlertDialogDescription>
              <Trans i18nKey="billing:paymentMethods.removePaymentMethodDescription" />
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>
              <Trans i18nKey="common:cancel" />
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() =>
                paymentMethodToDelete &&
                handleDeletePaymentMethod(paymentMethodToDelete)
              }
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              data-test="confirm-delete-payment-method"
            >
              {isPending ? (
                <>
                  <Spinner className="mr-2 h-4 w-4" />
                  <Trans i18nKey="billing:paymentMethods.removingCard" />
                </>
              ) : (
                <Trans i18nKey="billing:paymentMethods.removePaymentMethod" />
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

PaymentMethodsList.displayName = 'PaymentMethodsList';
PaymentMethodsContent.displayName = 'PaymentMethodsContent';
