import { FaIcon } from '@kit/ui/dojo/atoms/fa-icon';
import { IconTextSelector } from '@kit/ui/dojo/molecules/icon-text-selector';

type PaymentMethod = {
  id: string;
  card: {
    brand: string;
    last4: string;
    expMonth: number;
    expYear: number;
  };
};

type PaymentMethodDropdownProps = {
  paymentMethods: PaymentMethod[];
  selectedPaymentMethod: string;
  onSelect: (paymentMethodId: string) => void;
};

export function PaymentMethodDropdown({
  paymentMethods,
  selectedPaymentMethod,
  onSelect,
}: PaymentMethodDropdownProps) {
  const getCardIcon = (brand: string) => {
    switch (brand.toLowerCase()) {
      case 'visa':
        return 'brands/cc-visa';
      case 'mastercard':
        return 'brands/cc-mastercard';
      case 'jcb':
        return 'brands/cc-jcb';
      case 'amex':
        return 'brands/cc-amex';
      case 'discover':
        return 'brands/cc-discover';
      case 'diners':
        return 'brands/cc-diners-club';
      default:
        return 'solid/credit-card';
    }
  };

  const formatPaymentMethodItems = () => {
    const items = paymentMethods.map((method) => ({
      id: method.id,
      name: `${method.card.brand.toUpperCase()} •••• ${method.card.last4} (Expires ${method.card.expMonth}/${method.card.expYear})`,
      iconNode: (
        <FaIcon icon={getCardIcon(method.card.brand)} className="h-6 w-6" />
      ),
    }));

    // Add the "Add new card" option
    items.push({
      id: 'new',
      name: 'Add new card',
      iconNode: <FaIcon icon="solid/credit-card" className="h-6 w-6" />,
    });

    return items;
  };

  return (
    <div className="flex items-center gap-2">
      <IconTextSelector
        placeholder="Select a payment method"
        description="Search payment methods"
        currentId={selectedPaymentMethod}
        items={formatPaymentMethodItems()}
        onChange={onSelect}
      />
    </div>
  );
}

PaymentMethodDropdown.displayName = 'PaymentMethodDropdown';
