'use client';

import { PropsWithChildren, useEffect, useState } from 'react';

import { Elements } from '@stripe/react-stripe-js';
import { StripeElementsOptions, loadStripe } from '@stripe/stripe-js';

import { StripeClientEnvSchema } from '../../schema/stripe-client-env.schema';

const { publishableKey } = StripeClientEnvSchema.parse({
  publishableKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,
});

const stripePromise = loadStripe(publishableKey);

const ELEMENTS_OPTIONS: StripeElementsOptions = {
  appearance: {
    theme: 'flat',
    variables: {
      colorDanger: 'red',
      borderRadius: '6px',
      fontSizeBase: '14px',
      fontFamily: 'inherit',
      spacingUnit: '4px',
    },
    rules: {
      '.Input': {
        padding: '8px 12px',
        transition: 'all 150ms ease',
      },
      '.Input:focus': {
        outline: 'none',
      },
      '.Input--invalid': {
        border: '1px solid red',
        boxShadow: '0 0 0 1px red',
      },
    },
  },
  locale: 'auto' as const,
};

export function StripeElementsProvider({ children }: PropsWithChildren) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  if (!mounted) {
    return (
      <div className="py-4">
        <p className="text-muted-foreground text-center">
          Loading payment form...
        </p>
      </div>
    );
  }

  return (
    <Elements stripe={stripePromise} options={ELEMENTS_OPTIONS}>
      {children}
    </Elements>
  );
}

StripeElementsProvider.displayName = 'StripeElementsProvider';
