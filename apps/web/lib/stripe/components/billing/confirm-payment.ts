import { useStripe } from '@stripe/react-stripe-js';

export function useConfirmPayment() {
  const stripe = useStripe();

  const confirmPayment = async (
    clientSecret: string,
    isSetupIntent = false,
  ) => {
    if (!stripe) {
      throw new Error('Stripe not initialized');
    }

    if (isSetupIntent) {
      // For SetupIntent (trial periods)

      const { error } = await stripe.confirmCardSetup(clientSecret);

      return { error };
    } else {
      // For PaymentIntent (immediate payments)

      const { error } = await stripe.confirmCardPayment(clientSecret);

      return { error };
    }
  };

  return confirmPayment;
}
