'use client';

import { Suspense, forwardRef, lazy, memo, useMemo } from 'react';

import { LoadingOverlay } from '@kit/ui/loading-overlay';

/**
 * Embedded checkout component that uses Stripe checkout
 * This version maintains the same API as the original component but always uses Stripe
 */
export function EmbeddedCheckout({
  checkoutToken,
  onClose,
}: {
  checkoutToken: string;
  onClose?: () => void;
}) {
  const StripeCheckoutComponent = useMemo(() => {
    return buildLazyComponent(() => {
      return import('./stripe-embedded-checkout').then(({ StripeCheckout }) => {
        return {
          default: StripeCheckout,
        };
      });
    });
  }, []);

  return (
    <>
      <StripeCheckoutComponent
        checkoutToken={checkoutToken}
        onClose={onClose}
      />
      <BlurryBackdrop />
    </>
  );
}

const Fallback = <LoadingOverlay fullPage={false} />;

function buildLazyComponent<
  Component extends React.ComponentType<{
    onClose: (() => unknown) | undefined;
    checkoutToken: string;
  }>,
>(
  load: () => Promise<{
    default: Component;
  }>,
  fallback = Fallback,
) {
  let LoadedComponent: ReturnType<typeof lazy<Component>> | null = null;

  const LazyComponent = forwardRef<
    React.ElementRef<'div'>,
    {
      onClose: (() => unknown) | undefined;
      checkoutToken: string;
    }
  >(function LazyDynamicComponent(props, ref) {
    if (!LoadedComponent) {
      LoadedComponent = lazy(load);
    }

    return (
      <Suspense fallback={fallback}>
        {/* @ts-expect-error: Stripe component might use slightly different props */}
        <LoadedComponent
          onClose={props.onClose}
          checkoutToken={props.checkoutToken}
          ref={ref}
        />
      </Suspense>
    );
  });

  return memo(LazyComponent);
}

function BlurryBackdrop() {
  return (
    <div
      className={
        'bg-background/30 fixed top-0 left-0 !m-0 h-full w-full backdrop-blur-xs'
      }
    />
  );
}

EmbeddedCheckout.displayName = 'EmbeddedCheckout';
BlurryBackdrop.displayName = 'BlurryBackdrop';
