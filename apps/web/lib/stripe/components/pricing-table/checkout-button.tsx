'use client';

import React from 'react';

import Link from 'next/link';

import { Button } from '@kit/ui/button';
import { If } from '@kit/ui/if';
import { Trans } from '@kit/ui/trans';

type Paths = {
  signUp: string;
  return: string;
};

type CheckoutButtonProps = {
  CheckoutButtonComponent?: React.ComponentType<{
    priceId: string;
    productId: string;
    highlighted?: boolean;
  }>;
  priceId: string;
  productId: string;
  paths: Paths;
  highlighted?: boolean;
  redirectToCheckout?: boolean;
};

export function CheckoutButton({
  CheckoutButtonComponent,
  priceId,
  productId,
  paths,
  highlighted,
  redirectToCheckout,
}: React.PropsWithChildren<CheckoutButtonProps>) {
  return (
    <div className={'flex justify-center'}>
      <If
        condition={!!CheckoutButtonComponent}
        fallback={
          <DefaultCheckoutButton
            highlighted={highlighted}
            priceId={priceId}
            paths={paths}
            redirectToCheckout={redirectToCheckout}
          />
        }
      >
        {() =>
          CheckoutButtonComponent && (
            <CheckoutButtonComponent
              priceId={priceId}
              productId={productId}
              highlighted={highlighted}
            />
          )
        }
      </If>
    </div>
  );
}

type DefaultCheckoutButtonProps = {
  highlighted?: boolean;
  priceId: string;
  paths: Paths;
  redirectToCheckout?: boolean;
};

function DefaultCheckoutButton({
  highlighted,
  priceId,
  paths,
  redirectToCheckout,
}: React.PropsWithChildren<DefaultCheckoutButtonProps>) {
  const signUpHref = `${paths.signUp}?price=${priceId}`;
  const returnHref = `${paths.return}?price=${priceId}`;

  const href = redirectToCheckout ? returnHref : signUpHref;

  return (
    <div className={'w-full'}>
      <Button
        variant={highlighted ? 'default' : 'outline'}
        size={'lg'}
        data-cy={'checkout-button'}
        asChild
      >
        <Link href={href} className={'relative w-full'}>
          <span>
            <Trans i18nKey={'billing:checkout'} />
          </span>
        </Link>
      </Button>
    </div>
  );
}

DefaultCheckoutButton.displayName = 'DefaultCheckoutButton';
CheckoutButton.displayName = 'CheckoutButton';
