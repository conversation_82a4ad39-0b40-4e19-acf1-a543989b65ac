import {
  LineItemType,
  PriceWithLineItems,
  ProductWithPrices,
} from '../../types/platform-billing';

export function mapTypeToLineItemType(type: string): LineItemType {
  // For now, safely map to supported types
  if (type === 'perSeat') return 'perSeat';
  if (type === 'metered') return 'metered';
  return 'flat'; // Default to flat
}

// Define the return type for price objects with specific shape
type PriceOutput = {
  id: string;
  lineItems: {
    id: string;
    name: string;
    cost: number;
    type: LineItemType;
  }[];
  interval?: 'month' | 'year';
  paymentType?: string;
  isValidType: boolean;
  currency: string;
};

export function mapPlatformProductToPricingProduct(
  product: ProductWithPrices,
  interval: string,
): {
  id: string;
  name: string;
  currency: string;
  description: string;
  badge?: string;
  highlighted?: boolean;
  features: string[];
  prices: Omit<PriceOutput, 'isValidType'>[];
} {
  // Ensure we have line items
  const prices = product.productPrices?.map((price) => {
    // Choose metered or flat rate, but flat line items should have type: flat
    const lineItems = price.lineItems?.map((lineItem) => {
      return {
        id: lineItem.id,
        name: lineItem.name,
        cost: Number(lineItem.cost), // Use cost field from line_items
        type: mapTypeToLineItemType(lineItem.type),
      };
    });

    // Get paymentType (recurring or one-time) from the price object
    const paymentType = price.type;

    // Get interval directly from the price object
    const isValidType =
      price.type !== 'recurring' || price.interval === interval;

    // Safely handle currency which exists in product_prices table
    const priceCurrency =
      typeof price.currency === 'string' ? price.currency : 'usd';

    return {
      id: price.id,
      lineItems: lineItems ?? [],
      interval: price.type === 'recurring' ? price.interval : undefined,
      paymentType,
      isValidType,
      currency: priceCurrency,
    } as PriceOutput;
  });

  // Filter out invalid types/intervals
  const validPrices = prices?.filter((price) => price.isValidType) ?? [];

  // Safely handle the features array - it's stored as TEXT[] in DB
  let featuresArray: string[] = [];
  if ('features' in product) {
    // If it's already an array, use it directly
    if (Array.isArray(product.features)) {
      featuresArray = product.features;
    } else {
      // Otherwise try to parse from string with fallback to empty array
      try {
        featuresArray = JSON.parse(product.features as unknown as string);
      } catch {
        featuresArray = [];
      }
    }
  }

  // Get badge and highlighted from the product fields directly or metadata
  // They might be stored directly or in metadata
  const badge =
    'badge' in product
      ? (product.badge as string | undefined)
      : product.metadata &&
          typeof product.metadata === 'object' &&
          'badge' in product.metadata
        ? String(product.metadata.badge)
        : undefined;

  const highlighted =
    'highlighted' in product
      ? (product.highlighted as boolean | undefined)
      : product.metadata &&
          typeof product.metadata === 'object' &&
          'highlighted' in product.metadata
        ? String(product.metadata.highlighted) === 'true'
        : undefined;

  // Get currency from the first valid price or default to USD
  const defaultCurrency =
    validPrices.length > 0 ? (validPrices[0]?.currency ?? 'usd') : 'usd';

  return {
    id: product.id,
    name: product.name,
    description: product.description || '',
    currency: defaultCurrency,
    badge,
    highlighted,
    features: featuresArray,
    prices: validPrices.map((price) => {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { isValidType, ...rest } = price;
      return rest;
    }),
  };
}

export function getPlatformProductIntervals(
  products: ProductWithPrices[],
): string[] {
  const intervals = products
    .flatMap((product) => {
      return (
        product.productPrices?.map((price) =>
          price.type === 'recurring' ? price.interval : undefined,
        ) ?? []
      );
    })
    .filter(Boolean) as string[];

  return Array.from(new Set(intervals));
}

export function findProductAndPriceByPriceId(
  products: ProductWithPrices[],
  priceId: string,
): { product: ProductWithPrices | null; price: PriceWithLineItems | null } {
  for (const product of products) {
    if (Array.isArray(product.productPrices)) {
      for (const price of product.productPrices) {
        if (price.id === priceId) {
          return { product, price };
        }
      }
    }
  }

  return { product: null, price: null };
}
