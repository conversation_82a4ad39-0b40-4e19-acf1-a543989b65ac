'use client';

import React from 'react';

import { Trans } from '@kit/ui/trans';

import { PricingLineItem } from '../../types/platform-billing';

type LineItemDetailsProps = {
  lineItems: PricingLineItem[];
  currency: string;
  selectedInterval: string;
};

export function LineItemDetails({
  lineItems,
  currency,
  selectedInterval,
}: React.PropsWithChildren<LineItemDetailsProps>) {
  return (
    <ul className={'space-y-1'} data-cy={'line-item-details'}>
      {lineItems.map((item) => {
        return (
          <li key={item.id} className={'text-xs'}>
            <span>
              <Trans i18nKey={item.name} defaults={item.name} />:
            </span>

            <span className={'ml-1'}>
              {item.cost} {currency}
            </span>

            <span className={'ml-1 capitalize'}>
              <Trans i18nKey={`billing:billingInterval.${selectedInterval}`} />
            </span>

            <span className={'ml-1'}>
              <Trans i18nKey={item.unit ?? 'billing:count'} defaults={'each'} />
            </span>
          </li>
        );
      })}
    </ul>
  );
}

LineItemDetails.displayName = 'LineItemDetails';
