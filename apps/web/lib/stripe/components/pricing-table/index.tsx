'use client';

import React, { useState } from 'react';

import { z } from 'zod';

import { If } from '@kit/ui/if';

import {
  LineItemSchema,
  PlatformProducts,
  getPriceIntervals,
  getPrimaryLineItem,
} from '../../schema/products-schema';
import { ProductWithPrices } from '../../types/platform-billing';
import { PriceIntervalSwitcher } from './price-interval-switcher';
import { PricingItem } from './pricing-item';
import {
  getPlatformProductIntervals,
  mapPlatformProductToPricingProduct,
} from './pricing-utils';

type Paths = {
  signUp: string;
  return: string;
};

type Interval = 'month' | 'year';

type PriceDetails = {
  interval?: Interval;
  paymentType?: string;
  lineItems: z.infer<typeof LineItemSchema>[];
  id: string;
};

type PricingTableProps = {
  config?: PlatformProducts;
  products?: ProductWithPrices[];
  paths: Paths;
  displayPriceDetails?: boolean;
  alwaysDisplayMonthlyPrice?: boolean;
  redirectToCheckout?: boolean;

  CheckoutButtonRenderer?: React.ComponentType<{
    priceId: string;
    productId: string;
    highlighted?: boolean;
  }>;
};

export function PricingTable({
  config,
  products,
  paths,
  CheckoutButtonRenderer,
  redirectToCheckout = true,
  displayPriceDetails = true,
  alwaysDisplayMonthlyPrice = true,
}: PricingTableProps) {
  const productPrices = products
    ? products.filter((product) => !!product.productPrices?.length)
    : undefined;

  // Default to month for most use cases
  const platformIntervals = productPrices
    ? getPlatformProductIntervals(productPrices)
    : config
      ? getPriceIntervals(config)
      : [];

  // Initialize useState before any conditionals
  const [interval, setInterval] = useState<Interval>(
    (platformIntervals[0] as Interval) ?? 'month',
  );

  if (!productPrices && !config) {
    return null;
  }

  if (config) {
    return (
      <div className={'w-full'}>
        <If condition={platformIntervals.length > 1}>
          <div className={'mx-auto mb-8 flex justify-center gap-2'}>
            <PriceIntervalSwitcher
              intervals={platformIntervals as Interval[]}
              interval={interval}
              setInterval={setInterval}
            />
          </div>
        </If>

        <div
          className={
            'flex flex-col items-center justify-center gap-6 md:flex-row md:items-stretch md:gap-4'
          }
        >
          {config.products.map((product) => {
            const prices = product.prices.filter((price) => {
              function isPriceValidForInterval(
                price: PriceDetails,
                interval: string,
              ) {
                return price.interval === interval || !price.interval;
              }

              if (isPriceValidForInterval(price, interval)) {
                if (price.paymentType === 'recurring') {
                  return price.interval === interval;
                }

                return true;
              }

              return false;
            });

            if (!prices.length) {
              return null;
            }

            return prices.map((price) => {
              const primaryLineItem = !price.lineItems.length
                ? price.lineItems[0]
                : getPrimaryLineItem(config!, price.id);

              if (!price.lineItems.length && !primaryLineItem) {
                return null;
              }

              return (
                <PricingItem
                  selectable
                  key={price.id}
                  price={price}
                  redirectToCheckout={redirectToCheckout}
                  primaryLineItem={primaryLineItem}
                  product={product}
                  paths={paths}
                  displayPriceDetails={displayPriceDetails}
                  alwaysDisplayMonthlyPrice={alwaysDisplayMonthlyPrice}
                  CheckoutButton={CheckoutButtonRenderer}
                />
              );
            });
          })}
        </div>
      </div>
    );
  }

  if (productPrices) {
    const mappedProducts = productPrices?.map((product) =>
      mapPlatformProductToPricingProduct(product, interval),
    );

    if (!mappedProducts?.length) {
      return null;
    }

    return (
      <div className={'w-full'}>
        <If condition={platformIntervals.length > 1}>
          <div className={'mx-auto mb-8 flex justify-center gap-2'}>
            <PriceIntervalSwitcher
              intervals={platformIntervals as Interval[]}
              interval={interval}
              setInterval={setInterval}
            />
          </div>
        </If>

        <div
          className={
            'flex flex-col items-center justify-center gap-6 md:flex-row md:items-stretch md:gap-4'
          }
        >
          {mappedProducts.map((product) => {
            if (!product.prices?.length) {
              return null;
            }

            return product.prices.map((price) => {
              const primaryLineItem = !price.lineItems.length
                ? undefined
                : price.lineItems[0];

              if (!price.lineItems.length && !primaryLineItem) {
                return null;
              }

              return (
                <PricingItem
                  key={price.id}
                  selectable
                  paths={paths}
                  redirectToCheckout={redirectToCheckout}
                  price={price}
                  product={product}
                  primaryLineItem={primaryLineItem}
                  displayPriceDetails={displayPriceDetails}
                  alwaysDisplayMonthlyPrice={alwaysDisplayMonthlyPrice}
                  CheckoutButton={CheckoutButtonRenderer}
                />
              );
            });
          })}
        </div>
      </div>
    );
  }

  return null;
}

PricingTable.displayName = 'PricingTable';

// Re-export components for external use
export * from './pricing-item';
export * from './checkout-button';
export * from './features-list';
export * from './line-item-details';
export * from './line-item-price';
export * from './price';
export * from './price-interval-switcher';
export * from './pricing-utils';
