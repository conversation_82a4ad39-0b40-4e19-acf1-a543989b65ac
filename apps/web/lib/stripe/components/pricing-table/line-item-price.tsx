'use client';

import React from 'react';

import { If } from '@kit/ui/if';
import { Trans } from '@kit/ui/trans';
import { cn } from '@kit/ui/utils';

import {
  PricingItemCommonProps,
  PricingLineItem,
} from '../../types/platform-billing';

type LineItemPriceProps = {
  interval: string;
  alwaysDisplayMonthlyPrice?: boolean;
  product: PricingItemCommonProps['product'];
  price: PricingItemCommonProps['price'];
  lineItem: PricingLineItem | undefined;
};

export function LineItemPrice(props: LineItemPriceProps) {
  const oneTime = !props.price.interval;
  const currency = props.product.currency;
  const locale =
    typeof navigator !== 'undefined' ? navigator.language : 'en-US';

  const shouldShowMonthlyPrice =
    !oneTime && props.alwaysDisplayMonthlyPrice
      ? 'month'
      : props.price.interval;

  const formattedPrice = getFormattedPrice(
    shouldShowMonthlyPrice ?? '',
    props.lineItem,
    currency,
    locale,
  );

  if (!formattedPrice) {
    return null;
  }

  if (!props.lineItem) {
    return <ContactSales />;
  }

  if (props.price.label && props.lineItem.type === 'flat' && formattedPrice) {
    return (
      <React.Fragment>
        <div className={'text-foreground flex items-baseline text-4xl'}>
          <Trans i18nKey={props.price.label} defaults={props.price.label} />
        </div>
      </React.Fragment>
    );
  }

  if (props.lineItem.type === 'perSeat') {
    return (
      <React.Fragment>
        <div
          className={cn(
            `text-foreground animate-in slide-in-from-left-4 fade-in text-4xl font-semibold`,
          )}
        >
          {formattedPrice}
          <If condition={props.alwaysDisplayMonthlyPrice}>
            <span className={'ml-1 text-sm'}>
              <Trans i18nKey={'billing:perMonth'} />
            </span>
          </If>
        </div>
      </React.Fragment>
    );
  }

  return (
    <React.Fragment>
      <div
        className={cn(
          `text-foreground animate-in slide-in-from-left-4 fade-in text-4xl font-semibold`,
        )}
      >
        {formattedPrice}
        <If condition={props.alwaysDisplayMonthlyPrice}>
          <span className={'ml-1 text-sm'}>
            <Trans i18nKey={'billing:perMonth'} />
          </span>
        </If>
      </div>
    </React.Fragment>
  );
}

function ContactSales() {
  return (
    <div className={'flex flex-col'}>
      <div className={'text-foreground flex items-baseline text-3xl'}>
        <Trans i18nKey={'billing:contactSales'} />
      </div>
    </div>
  );
}

function getFormattedPrice(
  interval: string,
  lineItem: LineItemPriceProps['lineItem'],
  currency: string,
  locale: string,
) {
  if (!lineItem) {
    return '';
  }

  const cost = lineItem.cost;

  // TODO: support metered line items and show ranges in pricing table
  // For the beta, we only have flat and fixed amounts, so we show a simple number
  try {
    // format to currency string
    const formatter = Intl.NumberFormat(locale, {
      style: 'currency',
      currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    });

    // Yearly prices get divided by 12 if we're displaying monthly pricing
    const isYearly = interval === 'year';

    if (isYearly && !Number.isNaN(cost)) {
      // Monthly price = Yearly price / 12
      return formatter.format(cost / 12);
    }

    return formatter.format(cost);
  } catch {
    // If the current locale is not supported, fallback to defaults
    return `${currency} ${cost}`;
  }
}

LineItemPrice.displayName = 'LineItemPrice';
getFormattedPrice.displayName = 'getFormattedPrice';
ContactSales.displayName = 'ContactSales';
