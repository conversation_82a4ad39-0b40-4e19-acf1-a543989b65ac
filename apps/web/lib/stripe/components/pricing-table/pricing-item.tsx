'use client';

import React from 'react';

import { Badge } from '@kit/ui/badge';
import { If } from '@kit/ui/if';
import { Separator } from '@kit/ui/separator';
import { Trans } from '@kit/ui/trans';
import { cn } from '@kit/ui/utils';

import {
  PricingItemCommonProps,
  PricingItemInterval,
  PricingLineItem,
} from '../../types/platform-billing';
import { CheckoutButton } from './checkout-button';
import { FeaturesList } from './features-list';
import { LineItemDetails } from './line-item-details';
import { LineItemPrice } from './line-item-price';
import { Price } from './price';

type Paths = {
  signUp: string;
  return: string;
};

export type PricingItemProps = PricingItemCommonProps & {
  className?: string;
  displayPriceDetails: boolean;
  paths: Paths;
  selectable: boolean;
  primaryLineItem: PricingLineItem | undefined;
  redirectToCheckout?: boolean;
  alwaysDisplayMonthlyPrice?: boolean;

  CheckoutButton?: React.ComponentType<{
    priceId: string;
    productId: string;
    highlighted?: boolean;
  }>;
};

export function PricingItem(props: React.PropsWithChildren<PricingItemProps>) {
  const highlighted = props.product.highlighted ?? false;

  const lineItem = props.primaryLineItem;

  // we exclude flat line items from the details since
  // it doesn't need further explanation
  const lineItemsToDisplay = props.price.lineItems.filter((item) => {
    return item.type !== 'flat';
  });

  const interval = props.price.interval as PricingItemInterval;

  return (
    <div
      data-cy={'subscription-price'}
      className={cn(
        props.className,
        `s-full relative flex flex-1 grow flex-col items-stretch justify-between self-stretch rounded-xl border p-8 lg:w-4/12 xl:max-w-[20rem]`,
        {
          ['border-primary']: highlighted,
          ['border-border']: !highlighted,
        },
      )}
    >
      <If condition={props.product.badge}>
        <div className={'absolute -top-2.5 left-0 flex w-full justify-center'}>
          <Badge
            className={highlighted ? '' : 'bg-background'}
            variant={highlighted ? 'default' : 'outline'}
          >
            <span>
              <Trans
                i18nKey={props.product.badge}
                defaults={props.product.badge}
              />
            </span>
          </Badge>
        </div>
      </If>

      <div className={'flex flex-col space-y-6'}>
        <div className={'flex flex-col space-y-2.5'}>
          <div className={'flex items-center space-x-6'}>
            <b
              className={
                'text-current-foreground font-heading font-semibold tracking-tight uppercase'
              }
            >
              <Trans
                i18nKey={props.product.name}
                defaults={props.product.name}
              />
            </b>
          </div>

          <span className={cn(`text-muted-foreground h-6 text-sm`)}>
            <Trans
              i18nKey={props.product.description}
              defaults={props.product.description}
            />
          </span>
        </div>

        <Separator />

        <div className={'flex flex-col space-y-2'}>
          <Price isMonthlyPrice={props.alwaysDisplayMonthlyPrice}>
            <LineItemPrice
              price={props.price}
              product={props.product}
              interval={interval}
              lineItem={lineItem}
              alwaysDisplayMonthlyPrice={props.alwaysDisplayMonthlyPrice}
            />
          </Price>

          <If condition={props.price.name}>
            <span
              className={cn(
                `animate-in slide-in-from-left-4 fade-in text-muted-foreground flex items-center space-x-0.5 text-sm capitalize`,
              )}
            >
              <span>
                <If
                  condition={props.price.interval}
                  fallback={<Trans i18nKey={'billing:lifetime'} />}
                >
                  {(interval) => (
                    <Trans i18nKey={`billing:billingInterval.${interval}`} />
                  )}
                </If>
              </span>

              <If condition={lineItem && lineItem?.type !== 'flat'}>
                <span>/</span>

                <span
                  className={'flex items-center space-x-0.5 text-xs'}
                  data-cy="primary-line-item-unit"
                >
                  <If
                    condition={!lineItem?.unit}
                    fallback={
                      <span>
                        <Trans
                          i18nKey={lineItem?.unit ?? ''}
                          defaults={lineItem?.unit}
                        />
                      </span>
                    }
                  >
                    <Trans i18nKey={'billing:count'} />
                  </If>
                </span>
              </If>
            </span>
          </If>
        </div>

        <If condition={lineItemsToDisplay.length && props.displayPriceDetails}>
          <div>
            <div
              className={cn(`text-muted-foreground flex flex-col space-y-1`)}
            >
              <LineItemDetails
                lineItems={lineItemsToDisplay}
                currency={props.product.currency}
                selectedInterval={interval}
              />
            </div>
          </div>
        </If>

        <If condition={props.product.features.length > 0}>
          <div className={'py-2.5'}>
            <FeaturesList features={props.product.features} />
          </div>
        </If>
      </div>

      <div className={'mt-5'}>
        <CheckoutButton
          CheckoutButtonComponent={props.CheckoutButton}
          priceId={props.price.id}
          productId={props.product.id}
          paths={props.paths}
          highlighted={highlighted}
          redirectToCheckout={props.redirectToCheckout}
        />
      </div>
    </div>
  );
}

PricingItem.displayName = 'PricingItem';
