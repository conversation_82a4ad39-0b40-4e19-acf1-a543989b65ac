'use client';

import React from 'react';

import { Button } from '@kit/ui/button';
import { Trans } from '@kit/ui/trans';
import { cn } from '@kit/ui/utils';

type Interval = 'month' | 'year';

type PriceIntervalSwitcherProps = {
  intervals: Interval[];
  interval: string;
  setInterval: (interval: Interval) => void;
};

export function PriceIntervalSwitcher({
  intervals,
  interval,
  setInterval,
}: React.PropsWithChildren<PriceIntervalSwitcherProps>) {
  return (
    <div className={'flex items-center'}>
      <div className={'flex rounded-full border p-1'}>
        {intervals.map((value) => {
          const isActive = value === interval;
          const className = isActive
            ? `bg-primary hover:bg-primary hover:text-muted`
            : `bg-transparent hover:bg-muted hover:text-foreground`;

          return (
            <Button
              key={value}
              size={'sm'}
              variant={'ghost'}
              data-cy={`billing-period-${value}`}
              className={cn(
                `text-muted-foreground rounded-full px-2.5 text-xs capitalize`,
                className,
              )}
              onClick={() => setInterval(value)}
            >
              <Trans i18nKey={`billing:billingInterval.${value}`} />
            </Button>
          );
        })}
      </div>
    </div>
  );
}

PriceIntervalSwitcher.displayName = 'PriceIntervalSwitcher';
