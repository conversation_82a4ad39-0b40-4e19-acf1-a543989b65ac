'use client';

import React from 'react';

import { cn } from '@kit/ui/utils';

export type PriceProps = {
  isMonthlyPrice?: boolean;
};

export function Price({
  children,
  isMonthlyPrice = true,
}: React.PropsWithChildren<PriceProps>) {
  return (
    <div
      className={cn(
        'font-heading flex flex-col',
        isMonthlyPrice
          ? 'flex-row items-start gap-x-1'
          : 'flex-col items-baseline',
      )}
    >
      {children}
    </div>
  );
}

Price.displayName = 'Price';
