'use client';

import React from 'react';

import { Trans } from '@kit/ui/trans';

type FeaturesListProps = {
  features: string[];
};

export function FeaturesList({
  features,
}: React.PropsWithChildren<FeaturesListProps>) {
  return (
    <ul className={'space-y-1.5'} data-cy={'features-list'}>
      {features.map((feature) => {
        return (
          <li key={feature} className={'flex'}>
            <div className={'mt-1 mr-2'}>
              <CheckIcon className={'h-4 w-4'} />
            </div>

            <span className={'text-sm'}>
              <Trans i18nKey={feature} defaults={feature} />
            </span>
          </li>
        );
      })}
    </ul>
  );
}

// CheckIcon component
function CheckIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <polyline points="20 6 9 17 4 12" />
    </svg>
  );
}

// Export the CheckIcon separately for reuse
export { CheckIcon };

FeaturesList.displayName = 'FeaturesList';
CheckIcon.displayName = 'CheckIcon';
