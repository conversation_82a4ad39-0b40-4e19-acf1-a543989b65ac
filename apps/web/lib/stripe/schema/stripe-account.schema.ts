import { z } from 'zod';

/**
 * Schema for creating a stripe account FOR A USER
 */
export const StripeAccountParamsSchema = z.object({
  /**
   * The ID of the user creating the stripe account
   */
  userId: z.string().uuid(),

  /**
   * The stripe account ID (optional for creation, required for updates)
   */
  stripeAccountId: z
    .string()
    .regex(/^acct_[a-zA-Z0-9]+$/)
    .optional(),

  /**
   * The country where the stripe account is based
   */
  countryISOCode1: z.string().length(2),

  /**
   * The email of the account owner (user)
   */
  email: z.string().email(),

  /**
   * The type of account to create
   */
  accountType: z.enum(['express', 'standard', 'custom']),

  /**
   * Optional business type
   */
  businessType: z.enum(['individual', 'company']).optional(),

  /**
   * Optional capabilities
   */
  capabilities: z.array(z.enum(['card_payments', 'transfers'])).optional(),

  /**
   * Optional business profile
   */
  businessProfile: z
    .object({
      /**
       * The merchant category code for the business
       */
      mcc: z.string().optional(),

      /**
       * The URL of the business's website
       */
      url: z.string().url().optional(),

      /**
       * A description of the business
       */
      name: z.string().optional(),

      /**
       * A short description of the product or service being sold
       */
      productDescription: z.string().optional(),
    })
    .optional(),

  /**
   * Options for the newly created account
   */
  settings: z
    .object({
      /**
       * Settings for payouts
       */
      payouts: z
        .object({
          /**
           * The schedule for payouts
           */
          schedule: z
            .object({
              /**
               * The interval for payouts
               */
              interval: z
                .enum(['manual', 'daily', 'weekly', 'monthly'])
                .default('weekly'),

              /**
               * The day of the week for weekly payouts (1 = Monday, 7 = Sunday)
               */
              weeklyAnchor: z
                .enum([
                  'sunday',
                  'monday',
                  'tuesday',
                  'wednesday',
                  'thursday',
                  'friday',
                  'saturday',
                ])
                .optional(),

              /**
               * The day of the month for monthly payouts
               */
              monthlyAnchor: z.number().min(1).max(31).optional(),
            })
            .optional(),
        })
        .optional(),
    })
    .optional(),
});

/**
 * Schema for retrieving a stripe account
 */
export const RetrieveStripeAccountParamsSchema = z.object({
  /**
   * The Stripe Account ID of the stripe account
   */
  stripeAccountId: z.string().regex(/^acct_[a-zA-Z0-9]+$/),
});

/**
 * Schema for retrieving supported countries for a stripe account
 */
export const RetrieveSupportedCountriesParamsSchema = z.object({
  /**
   * The stripe account ID of the stripe account
   */
  stripeAccountId: z
    .string()
    .regex(/^acct_[a-zA-Z0-9]+$/)
    .optional(),
});

/**
 * Schema for creating a login link for a stripe account
 */
export const CreateLoginLinkParamsSchema = z.object({
  /**
   * The stripe account ID of the stripe account
   */
  stripeAccountId: z.string().regex(/^acct_[a-zA-Z0-9]+$/),

  /**
   * The URL to redirect to on success
   */
  refreshUrl: z.string().url(),

  /**
   * The URL to redirect to on completion
   */
  returnUrl: z.string().url(),
});

/**
 * Schema for resetting a stripe account
 */
export const ResetStripeAccountParamsSchema = z.object({
  /**
   * The stripe account ID of the stripe account
   */
  stripeAccountId: z.string().regex(/^acct_[a-zA-Z0-9]+$/),
});

/**
 * Schema for creating an account link for onboarding
 */
export const RecipientAccountLinkParamsSchema = z.object({
  /**
   * The stripe account ID of the stripe account
   */
  stripeAccountId: z.string().regex(/^acct_[a-zA-Z0-9]+$/),

  /**
   * The type of account link to create
   */
  type: z.enum(['account_onboarding', 'account_update']),

  /**
   * The URL to redirect to on success
   */
  refreshUrl: z.string().url(),

  /**
   * The URL to redirect to on completion
   */
  returnUrl: z.string().url(),

  /**
   * Optionally specify whether to collect 'currently_due' info during onboarding/update
   */
  collect: z.enum(['currently_due']).optional(),
});

/**
 * Response schema for account link
 */
export const AccountLinkResponseSchema = z.object({
  /**
   * The URL to redirect to for onboarding
   */
  url: z.string().url(),

  /**
   * When the account link expires
   */
  expiresAt: z.number(),

  /**
   * Timestamp when the link was created
   */
  createdAt: z.number(),
});

/**
 * Schema for retrieving a stripe account's supported countries
 */
export const RetrieveStripeAccountCountriesParamsSchema = z.object({
  /**
   * The stripe account ID of the stripe account
   */

  // acct_ followed by letters & digits, per Stripe docs
  stripeAccountId: z
    .string()
    .regex(/^acct_[a-zA-Z0-9]+$/)
    .optional(),
});

/**
 * Response schema for stripe account
 */
export const StripeAccountResponseSchema = z.object({
  /**
   * The ID of the stripe account in the database
   */
  id: z.string(),

  /**
   * The stripe account ID of the stripe account
   */
  stripeAccountId: z.string().regex(/^acct_[a-zA-Z0-9]+$/),

  /**
   * The internal DB ID of the stripe account
   */
  userId: z.string().uuid().optional(),

  /**
   * The ISO 3166-1 alpha-2 code of the country where the stripe account is based
   */
  countryISOCode1: z.string().length(2).optional(),

  /**
   * The email of the account owner
   */
  email: z.string().email().optional(),

  /**
   * The type of account to create
   */
  accountType: z.enum(['express', 'standard', 'custom']).optional(),

  /**
   * The capabilities enabled for the stripe account
   */
  capabilities: z.record(z.string(), z.string()).optional(),

  /**
   * Whether charges are enabled for the stripe account
   */
  chargesEnabled: z.boolean().optional(),

  /**
   * Whether payouts are enabled for the stripe account
   */
  payoutsEnabled: z.boolean().optional(),

  /**
   * Whether the account details have been submitted
   */
  detailsSubmitted: z.boolean().optional(),

  /**
   * Requirements that need to be completed for the account
   */
  requirements: z
    .object({
      /**
       * Currently due requirements
       */
      currentlyDue: z.array(z.string()).optional(),

      /**
       * Eventually due requirements
       */
      eventuallyDue: z.array(z.string()).optional(),

      /**
       * Pending verification requirements
       */
      pendingVerification: z.array(z.string()).optional(),

      /**
       * When requirements will be disabled
       */
      disabledReason: z.string().nullable().optional(),

      /**
       * Requirements that are currently disabled
       */
      currentDeadline: z.number().nullable().optional(),
    })
    .optional(),
});
