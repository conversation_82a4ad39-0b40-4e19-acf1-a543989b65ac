import { z } from 'zod';

// Create an enum object with the same values as the LineItemType type
export const LineItemTypeEnum = {
  Flat: 'flat',
  PerSeat: 'perSeat',
  Metered: 'metered',
} as const;

const BillingIntervalSchema = z.enum(['month', 'year']);
export const LineItemTypeSchema = z.enum(['flat', 'perSeat', 'metered']);

export const PaymentTypeSchema = z.enum(['one_time', 'recurring']);

export const LineItemSchema = z
  .object({
    id: z
      .string({
        description:
          'Unique identifier for the line item. Defined by the Provider.',
      })
      .min(1),
    name: z
      .string({
        description: 'Name of the line item. Displayed to the user.',
      })
      .min(1),
    description: z
      .string({
        description:
          'Description of the line item. Displayed to the user and will replace the auto-generated description inferred' +
          ' from the line item. This is useful if you want to provide a more detailed description to the user.',
      })
      .optional(),
    cost: z
      .number({
        description: 'Cost of the line item. Displayed to the user.',
      })
      .min(0),
    type: LineItemTypeSchema,
    unit: z
      .string({
        description:
          'Unit of the line item. Displayed to the user. Example "seat" or "GB"',
      })
      .optional(),
    setupFee: z
      .number({
        description: `Lemon Squeezy only: If true, in addition to the cost, a setup fee will be charged.`,
      })
      .positive()
      .optional(),
    tiers: z
      .array(
        z.object({
          cost: z.number().min(0),
          upTo: z.union([z.number().min(0), z.literal('unlimited')]),
        }),
      )
      .optional(),
  })
  .refine(
    (data) =>
      data.type !== LineItemTypeEnum.Metered ||
      (data.unit && data.tiers !== undefined),
    {
      message: 'Metered line items must have a unit and tiers',
      path: ['type', 'unit', 'tiers'],
    },
  )
  .refine(
    (data) => {
      if (data.type === LineItemTypeEnum.Metered) {
        return data.cost === 0;
      }

      return true;
    },
    {
      message:
        'Metered line items must have a cost of 0. Please add a different line item type for a flat fee (Stripe)',
      path: ['type', 'cost'],
    },
  );

export const PriceSchema = z
  .object({
    id: z
      .string({
        description: 'Unique identifier for the price. Defined by yourself.',
      })
      .min(1),
    name: z
      .string({
        description: 'Name of the price. Displayed to the user.',
      })
      .min(1),
    interval: BillingIntervalSchema.optional(),
    custom: z.boolean().default(false).optional(),
    label: z.string().min(1).optional(),
    buttonLabel: z.string().min(1).optional(),
    href: z.string().min(1).optional(),
    lineItems: z.array(LineItemSchema).refine(
      (schema) => {
        const types = schema.map((item) => item.type);

        const perSeat = types.filter(
          (type) => type === LineItemTypeEnum.PerSeat,
        ).length;

        const flat = types.filter(
          (type) => type === LineItemTypeEnum.Flat,
        ).length;

        return perSeat <= 1 && flat <= 1;
      },
      {
        message: 'Prices can only have one per-seat and one flat line item',
        path: ['lineItems'],
      },
    ),
    trialDays: z
      .number({
        description:
          'Number of days for the trial period. Leave empty for no trial.',
      })
      .positive()
      .optional(),
    paymentType: PaymentTypeSchema,
  })
  .refine(
    (data) => {
      if (data.custom) {
        return data.lineItems.length === 0;
      }

      return data.lineItems.length > 0;
    },
    {
      message: 'Non-Custom Prices must have at least one line item',
      path: ['lineItems'],
    },
  )
  .refine(
    (data) => {
      if (data.custom) {
        return data.lineItems.length === 0;
      }

      return data.lineItems.length > 0;
    },
    {
      message: 'Custom Prices must have 0 line items',
      path: ['lineItems'],
    },
  )
  .refine(
    (data) => data.paymentType !== 'one_time' || data.interval === undefined,
    {
      message: 'One-time prices must not have an interval',
      path: ['paymentType', 'interval'],
    },
  )
  .refine(
    (data) => data.paymentType !== 'recurring' || data.interval !== undefined,
    {
      message: 'Recurring prices must have an interval',
      path: ['paymentType', 'interval'],
    },
  )
  .refine(
    (item) => {
      // metered line items can be shared across prices
      const lineItems = item.lineItems.filter(
        (item) => item.type !== LineItemTypeEnum.Metered,
      );

      const ids = lineItems.map((item) => item.id);

      return ids.length === new Set(ids).size;
    },
    {
      message: 'Line item IDs must be unique',
      path: ['lineItems'],
    },
  )
  .refine(
    (data) => {
      if (data.paymentType === 'one_time') {
        const nonFlatLineItems = data.lineItems.filter(
          (item) => item.type !== LineItemTypeEnum.Flat,
        );

        return nonFlatLineItems.length === 0;
      }

      return true;
    },
    {
      message: 'One-time prices must not have non-flat line items',
      path: ['paymentType', 'lineItems'],
    },
  );

const ProductSchema = z
  .object({
    id: z
      .string({
        description:
          'Unique identifier for the product. Defined by th Provider.',
      })
      .min(1),
    name: z
      .string({
        description: 'Name of the product. Displayed to the user.',
      })
      .min(1),
    description: z
      .string({
        description: 'Description of the product. Displayed to the user.',
      })
      .min(1),
    currency: z
      .string({
        description: 'Currency code for the product. Displayed to the user.',
      })
      .min(3)
      .max(3),
    badge: z
      .string({
        description:
          'Badge for the product. Displayed to the user. Example: "Popular"',
      })
      .optional(),
    features: z
      .array(
        z.string({
          description: 'Features of the product. Displayed to the user.',
        }),
      )
      .nonempty(),
    enableDiscountField: z
      .boolean({
        description: 'Enable discount field for the product in the checkout.',
      })
      .optional(),
    highlighted: z
      .boolean({
        description: 'Highlight this product. Displayed to the user.',
      })
      .optional(),
    prices: z.array(PriceSchema),
  })
  .refine((data) => data.prices.length > 0, {
    message: 'Products must have at least one price',
    path: ['prices'],
  })
  .refine(
    (item) => {
      const priceIds = item.prices.map((price) => price.id);

      return priceIds.length === new Set(priceIds).size;
    },
    {
      message: 'Price IDs must be unique',
      path: ['prices'],
    },
  );

const ProductsSchema = z
  .object({
    products: z.array(ProductSchema).nonempty(),
  })
  .refine(
    (data) => {
      const ids = data.products.flatMap((product) =>
        product.prices.flatMap((price) =>
          price.lineItems.map((item) => item.id),
        ),
      );

      return ids.length === new Set(ids).size;
    },
    {
      message: 'Line item IDs must be unique',
      path: ['products'],
    },
  );

export function createProductsSchema(config: z.infer<typeof ProductsSchema>) {
  return ProductsSchema.parse(config);
}

export type PlatformProducts = z.infer<typeof ProductsSchema>;
export type ProductSchema = z.infer<typeof ProductSchema>;

export function getPriceIntervals(config: z.infer<typeof ProductsSchema>) {
  const intervals = config.products
    .flatMap((product) => product.prices.map((price) => price.interval))
    .filter(Boolean);

  return Array.from(new Set(intervals));
}

/**
 * @name getPrimaryLineItem
 * @description Get the primary line item for a price
 * By default, the primary line item is the first flat line item in the price. If there are no flat line items,
 * the first line item is returned.
 *
 * @param config
 * @param priceId
 */
export function getPrimaryLineItem(
  config: z.infer<typeof ProductsSchema>,
  priceId: string,
) {
  for (const product of config.products) {
    for (const price of product.prices) {
      if (price.id === priceId) {
        const flatLineItem = price.lineItems.find(
          (item) => item.type === LineItemTypeEnum.Flat,
        );

        if (flatLineItem) {
          return flatLineItem;
        }

        return price.lineItems[0];
      }
    }
  }

  throw new Error('Base line item not found');
}

export function getProductPricePair(
  config: z.infer<typeof ProductsSchema>,
  priceId: string,
) {
  for (const product of config.products) {
    for (const price of product.prices) {
      if (price.id === priceId) {
        return { product, price };
      }
    }
  }

  throw new Error('Price not found');
}

export function getProductPricePairByVariantId(
  config: z.infer<typeof ProductsSchema>,
  priceId: string,
) {
  for (const product of config.products) {
    for (const price of product.prices) {
      for (const lineItem of price.lineItems) {
        if (lineItem.id === priceId) {
          return { product, price };
        }
      }
    }
  }

  throw new Error('Price not found');
}

export function getLineItemTypeById(
  config: z.infer<typeof ProductsSchema>,
  id: string,
) {
  for (const product of config.products) {
    for (const price of product.prices) {
      for (const lineItem of price.lineItems) {
        if (lineItem.id === id) {
          return lineItem.type;
        }
      }
    }
  }

  throw new Error(`Line Item with ID ${id} not found`);
}
