import { z } from 'zod';

import { PriceSchema } from './products-schema';

export const CreateBillingCheckoutSchema = z.object({
  returnUrl: z.string().url(),
  userId: z.string().uuid(),
  communityId: z.string().uuid(),
  price: PriceSchema,
  stripeAccountId: z.string().optional(),
  customerEmail: z.string().email().optional(),
  enableDiscountField: z.boolean().optional(),
  metadata: z.record(z.string()).optional(),
});
