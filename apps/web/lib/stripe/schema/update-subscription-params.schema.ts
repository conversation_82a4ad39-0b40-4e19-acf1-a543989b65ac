import { z } from 'zod';

/**
 * Schema for validating parameters to update a Stripe subscription
 */
export const UpdateSubscriptionParamsSchema = z.object({
  /**
   * The ID of the subscription to update
   */
  subscriptionId: z.string(),

  /**
   * The ID of the subscription item to update
   */
  subscriptionItemId: z.string(),

  /**
   * The new quantity for the subscription
   */
  quantity: z.number().int().positive(),
});
