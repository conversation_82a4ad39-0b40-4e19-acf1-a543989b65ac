import { z } from 'zod';

export const StripeServerEnvSchema = z
  .object({
    secretKey: z
      .string({
        required_error: `Please provide the variable STRIPE_SECRET_KEY`,
      })
      .min(1),
    webhooksSecret: z
      .string({
        required_error: `Please provide the variable STRIPE_WEBHOOK_SECRET`,
      })
      .min(1),
    // API version environment variables - at least one should be provided
    apiVersionV1: z.string().min(1).optional(),
    apiVersionV1Beta: z.string().min(1).optional(),
    apiVersionV2: z.string().min(1).optional(),
  })
  .refine(
    (schema) => {
      const key = schema.secretKey;
      const secretKeyPrefix = 'sk_';
      const restrictKeyPrefix = 'rk_';

      return (
        key.startsWith(secretKeyPrefix) || key.startsWith(restrictKeyPrefix)
      );
    },
    {
      path: ['STRIPE_SECRET_KEY'],
      message: `Stripe secret key must start with 'sk_' or 'rk_'`,
    },
  )
  .refine(
    (schema) => {
      return schema.webhooksSecret.startsWith('whsec_');
    },
    {
      path: ['STRIPE_WEBHOOK_SECRET'],
      message: `Stripe webhook secret must start with 'whsec_'`,
    },
  )
  .refine(
    (schema) => {
      // At least one API version should be provided
      return (
        schema.apiVersionV1 || schema.apiVersionV1Beta || schema.apiVersionV2
      );
    },
    {
      message: `At least one Stripe API version must be provided (STRIPE_API_VERSION_V1, STRIPE_API_VERSION_V1_BETA, or STRIPE_API_VERSION_V2)`,
    },
  );
