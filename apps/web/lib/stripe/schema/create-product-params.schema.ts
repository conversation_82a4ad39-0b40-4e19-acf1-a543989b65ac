import { z } from 'zod';

/**
 * Schema for creating a product with a Stripe account
 */
export const CreateProductParamsSchema = z.object({
  /**
   * The name of the product
   */
  name: z.string(),

  /**
   * Optional description of the product
   */
  description: z.string().optional(),

  /**
   * Optional metadata for the product
   */
  metadata: z.record(z.string()).optional(),

  /**
   * The community ID to create the product for
   */
  communityId: z.string().uuid().optional(),

  /**
   * The seller of the product
   */
  seller: z.enum(['community', 'user', 'platform']),

  /**
   * Optional trial days for the product
   */
  trialDays: z.number().min(0).max(90).optional(),
});
