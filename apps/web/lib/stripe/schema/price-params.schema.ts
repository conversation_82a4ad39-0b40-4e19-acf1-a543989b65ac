import { z } from 'zod';

export const CreatePriceParamsSchema = z
  .object({
    productId: z.string(),
    nickname: z.string().optional(),
    unitAmount: z.number().int().positive(),
    currency: z.string(),
    type: z.enum(['one_time', 'recurring']),
    interval: z.enum(['month', 'year', 'week', 'day', 'one_time']).optional(),
    metadata: z.record(z.string()).optional(),
  })
  .refine(
    (data) => {
      // Require interval only for recurring prices
      if (data.type === 'recurring' && !data.interval) {
        return false;
      }
      return true;
    },
    {
      message: 'Interval is required for recurring prices',
      path: ['interval'],
    },
  );

export const UpdatePriceParamsSchema = z.object({
  priceId: z.string(),
  nickname: z.string().optional(),
  active: z.boolean().optional(),
  metadata: z.record(z.string()).optional(),
});

export const DeletePriceParamsSchema = z.object({
  priceId: z.string(),
});

export const TogglePriceActiveParamsSchema = z.object({
  priceId: z.string(),
  active: z.boolean(),
});

export const PriceResponseSchema = z.object({
  id: z.string(),
  nickname: z.string().optional(),
  unitAmount: z.number().optional(),
  currency: z.string(),
  active: z.boolean(),
});

export const DeletePriceResponseSchema = z.object({
  id: z.string(),
});

export const TogglePriceActiveResponseSchema = z.object({
  id: z.string(),
  active: z.boolean(),
});
