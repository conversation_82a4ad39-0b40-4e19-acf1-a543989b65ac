import { z } from 'zod';

/**
 * Schema for updating a product with a Stripe account
 */
export const UpdateProductParamsSchema = z.object({
  /**
   * The ID of the product to update
   */
  productId: z.string(),

  /**
   * Optional new name for the product
   */
  name: z.string().optional(),

  /**
   * Optional new description for the product
   */
  description: z.string().optional(),

  /**
   * Optional trial days for the product
   */
  trialDays: z.number().optional(),
  /**
   * Optional new metadata for the product
   */
  metadata: z.record(z.string()).optional(),

  /**
   * Whether the product is active
   */
  active: z.boolean().optional(),

  /**
   * Optional array of image URLs
   */
  images: z.array(z.string()).optional(),
});
