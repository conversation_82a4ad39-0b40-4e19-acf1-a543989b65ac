import { z } from 'zod';

/**
 * Response schema for product operations
 */
export const ProductResponseSchema = z.object({
  /**
   * The ID of the product
   */
  id: z.string().uuid(),

  /**
   * The name of the product
   */
  name: z.string(),

  /**
   * Optional description of the product
   */
  description: z.string().nullable(),

  /**
   * Whether the product is active
   */
  active: z.boolean(),

  /**
   * Optional trial days for the product
   */
  trialDays: z.number().nullable(),

  /**
   * Metadata associated with the product
   */
  metadata: z.record(z.string(), z.string()).optional(),

  /**
   * Images associated with the product
   */
  images: z.array(z.string()).optional(),

  /**
   * The Stripe account ID the product belongs to
   */
  stripeAccountId: z.string().optional(),
});
