import { z } from 'zod';

/**
 * Schema for validating parameters to query billing usage
 */
export const QueryBillingUsageSchema = z.object({
  /**
   * The ID of the subscription item to query usage for
   */
  id: z.string(),

  /**
   * The timestamp to start the query from (optional)
   */
  from: z.number().optional(),

  /**
   * The timestamp to end the query at (optional)
   */
  to: z.number().optional(),
});
