import 'server-only';

import type <PERSON><PERSON> from 'stripe';
import { z } from 'zod';

import { getLogger } from '@kit/shared/logger';

import { UpdateSubscriptionParamsSchema } from '../../../schema';

/**
 * Updates a subscription item
 */
export async function updateSubscriptionItemImpl(
  stripe: Stripe,
  params: z.infer<typeof UpdateSubscriptionParamsSchema>,
): Promise<{ success: boolean }> {
  const logger = await getLogger();
  const ctx = {
    name: 'updateSubscriptionItem',
    subscriptionItemId: params.subscriptionItemId,
  };
  logger.info(ctx, 'Updating subscription item');
  // Update the subscription item in Stripe
  await stripe.subscriptionItems.update(params.subscriptionItemId, {
    quantity: params.quantity,
  });

  return {
    success: true,
  };
}
