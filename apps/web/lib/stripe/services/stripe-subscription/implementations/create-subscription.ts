import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import type Stripe from 'stripe';

import { getLogger } from '@kit/shared/logger';

import { PurchaseTypeEnum } from '../../../types';
import { getStripeCustomerService } from '../../stripe-factory.service';

/**
 * Creates a subscription
 */
export async function createSubscriptionImpl(
  stripe: Stripe,
  client: SupabaseClient,
  {
    userId,
    paymentMethodId,
    priceId,
    trialDays,
    purchaseType,
    metadata = {},
  }: {
    userId: string;
    paymentMethodId: string;
    priceId: string;
    trialDays?: number;
    purchaseType: PurchaseTypeEnum;
    metadata?: Record<string, string>;
  },
): Promise<{
  success?: boolean;
  requiresAction?: boolean;
  clientSecret?: string;
  subscriptionId?: string;
  stripeAccountId?: string;
  isSetupIntent?: boolean;
}> {
  const logger = await getLogger();
  const ctx = {
    name: 'createSubscription',
    userId,
    paymentMethodId,
    priceId,
    trialDays,
  };
  logger.info(ctx, 'Creating subscription');
  try {
    if (!userId) throw new Error('userId is required');
    if (!paymentMethodId) throw new Error('paymentMethodId is required');
    if (!priceId || priceId.trim() === '') {
      throw new Error('Valid priceId is required');
    }

    const stripeCustomerService = getStripeCustomerService(client);

    // 1. Get or create customer
    const stripeAccountId =
      await stripeCustomerService.getOrCreateStripeAccount({
        userId,
      });

    // 2. Attach payment method
    await stripeCustomerService.attachPaymentMethodToCustomer(
      stripeAccountId,
      paymentMethodId,
    );

    // 3. Create subscription
    const subscription = await stripe.subscriptions.create({
      customer_account: stripeAccountId,
      items: [{ price: priceId }],
      default_payment_method: paymentMethodId,
      payment_behavior: 'default_incomplete',
      trial_period_days: trialDays ?? 0,
      payment_settings: {
        payment_method_types: ['card'],
        save_default_payment_method: 'on_subscription',
        payment_method_options: {
          card: {
            request_three_d_secure: 'automatic',
          },
        },
      },
      collection_method: 'charge_automatically',
      expand: ['latest_invoice.confirmation_secret', 'items.data'],
      metadata: {
        user_id: userId,
        purchase_type: purchaseType,
        ...metadata,
      },
      off_session: false,
    });

    logger.info(
      `Created Stripe subscription ${subscription.id} for user ${userId} with customer ${stripeAccountId}`,
      ctx,
    );

    // 4. Create subscription in database
    const { error: subscriptionError } = await client.rpc(
      'create_subscription',
      {
        p_target_user_id: userId,
        p_target_community_id: metadata.community_id,
        p_target_subscription_id: subscription.id,
        p_purchase_type: purchaseType,
        p_status: subscription.status,
        p_active: subscription.status === 'active',
        p_payment_method_id: paymentMethodId,
        p_cancel_at_period_end: subscription.cancel_at_period_end,
        p_currency: subscription.items.data[0]?.price.currency,
        p_current_period_start: subscription.items.data[0]?.current_period_start
          ? new Date(
              subscription.items.data[0].current_period_start * 1000,
            ).toISOString()
          : null,
        p_current_period_end: subscription.items.data[0]?.current_period_end
          ? new Date(
              subscription.items.data[0].current_period_end * 1000,
            ).toISOString()
          : null,
        p_trial_starts_at: subscription.trial_start
          ? new Date(subscription.trial_start * 1000).toISOString()
          : null,
        p_trial_ends_at: subscription.trial_end
          ? new Date(subscription.trial_end * 1000).toISOString()
          : null,
      },
    );

    if (subscriptionError) {
      logger.error(
        `Failed to create subscription in database for Stripe subscription ${subscription.id}`,
        {
          error: subscriptionError,
          message: subscriptionError.message,
          details: subscriptionError.details,
          hint: subscriptionError.hint,
          code: subscriptionError.code,
          userId,
          stripeAccountId,
          subscriptionId: subscription.id,
        },
        ctx,
      );
      throw new Error(
        `Failed to create subscription in database: ${subscriptionError.message || subscriptionError.details || JSON.stringify(subscriptionError)}`,
      );
    }

    // 5. Check if additional authentication is needed
    const latestInvoice = subscription.data
      ?.latest_invoice as Stripe.Invoice | null;

    // Check for setup_intent
    if (subscription.pending_setup_intent) {
      logger.info(
        `Subscription ${subscription.id} requires additional authentication via setup_intent`,
      );
      const setupIntent = (
        await stripe.setupIntents.retrieve(
          subscription.pending_setup_intent as string,
        )
      ).data as Stripe.SetupIntent;
      if (setupIntent.client_secret) {
        return {
          requiresAction: true,
          clientSecret: setupIntent.client_secret,
          isSetupIntent: true,
        };
      }
    }

    // Check if latest_invoice and confirmation_secret exist and are objects
    if (
      latestInvoice &&
      typeof latestInvoice === 'object' &&
      latestInvoice.confirmation_secret &&
      typeof latestInvoice.confirmation_secret === 'object'
    ) {
      const confirmationSecret = latestInvoice.confirmation_secret as {
        client_secret?: string | null;
      };

      // Use confirmation_secret.client_secret to check if action is required
      if (latestInvoice.status === 'open' && confirmationSecret.client_secret) {
        logger.info(
          `Subscription ${subscription.id} requires additional authentication via invoice confirmation`,
        );
        return {
          requiresAction: true,
          clientSecret: confirmationSecret.client_secret,
        };
      }
    } else {
      // Handle cases where invoice or confirmation secret might not be available as expected
      // This could happen if the invoice wasn't expanded correctly or if the structure changed
      logger.warn(
        `Could not find latest_invoice or confirmation_secret for subscription ${subscription.id}. Assuming no action needed.`,
        {
          latestInvoiceStatus: latestInvoice?.status,
          hasConfirmationSecret: !!(
            latestInvoice &&
            typeof latestInvoice === 'object' &&
            latestInvoice.confirmation_secret
          ),
        },
      );
    }

    // 6. If payment succeeded or trial started
    logger.info(
      `Subscription ${subscription.id} setup completed successfully`,
      ctx,
    );
    return {
      success: true,
      subscriptionId: subscription.id,
      stripeAccountId,
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    const errorStack = error instanceof Error ? error.stack : undefined;

    logger.error('Error setting up subscription:', {
      error,
      message: errorMessage,
      stack: errorStack,
      userId,
      paymentMethodId,
      priceId,
    });
    throw error;
  }
}
