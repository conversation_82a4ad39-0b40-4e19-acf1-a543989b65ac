import 'server-only';

import type <PERSON><PERSON> from 'stripe';

import { PurchaseTypeEnum, UpsertSubscriptionParams } from '../../../types';
import { buildSubscriptionPayloadFromStripe } from '../../../utils/subscription-builder';

/**
 * Gets a subscription
 */
export async function getSubscriptionImpl(
  stripe: Stripe,
  subscriptionId: string,
): Promise<UpsertSubscriptionParams> {
  try {
    // Retrieve subscription data from Stripe with expanded line items
    const subscription = await stripe.subscriptions.retrieve(subscriptionId, {
      expand: ['items.data.price', 'items.data.price.product'],
    });

    // Extract customer and account IDs from subscription
    const communityId = subscription.metadata?.community_id || '';
    const userId = subscription.metadata?.user_id || '';

    // Build and return subscription payload with the new utility
    return buildSubscriptionPayloadFromStripe(subscription, {
      communityId,
      userId,
      paymentMethodId: subscription.default_payment_method as string | null,
      purchaseType: subscription.metadata?.purchase_type as PurchaseTypeEnum,
    });
  } catch (error) {
    console.error(`Failed to retrieve subscription: ${subscriptionId}`, error);
    throw new Error(`Failed to retrieve subscription: ${subscriptionId}`);
  }
}
