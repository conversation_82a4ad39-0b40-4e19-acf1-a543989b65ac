import 'server-only';

import type <PERSON><PERSON> from 'stripe';
import { z } from 'zod';

import { getLogger } from '@kit/shared/logger';

import { CancelSubscriptionParamsSchema } from '../../../schema';

/**
 * Cancels a subscription
 */
export async function cancelSubscriptionImpl(
  stripe: Stripe,
  params: z.infer<typeof CancelSubscriptionParamsSchema>,
): Promise<{ success: boolean }> {
  const logger = await getLogger();
  const ctx = {
    name: 'cancelSubscription',
    subscriptionId: params.subscriptionId,
  };
  logger.info(ctx, 'Cancelling subscription');
  // Cancel the subscription in Stripe
  await stripe.subscriptions.cancel(params.subscriptionId, {
    invoice_now: params.invoiceNow,
  });

  return {
    success: true,
  };
}
