import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import type <PERSON>e from 'stripe';
import { z } from 'zod';

import {
  CancelSubscriptionParamsSchema,
  UpdateSubscriptionParamsSchema,
} from '../../schema';
import { PurchaseTypeEnum, UpsertSubscriptionParams } from '../../types';
import { cancelSubscriptionImpl } from './implementations/cancel-subscription';
import { createSubscriptionImpl } from './implementations/create-subscription';
import { getSubscriptionImpl } from './implementations/get-subscription';
import { updateSubscriptionItemImpl } from './implementations/update-subscription-item';

/**
 * Stripe subscription service for managing subscriptions and checkout sessions
 */
export class StripeSubscriptionService {
  constructor(
    private readonly stripe: Stripe,
    private readonly client: SupabaseClient,
  ) {}

  /**
   * Creates a subscription
   * @param params.userId - The ID of the user
   * @param params.paymentMethodId - The ID of the payment method
   * @param params.priceId - The ID of the price
   * @param params.trialDays - The number of trial days
   * @param params.purchaseType - The type of the subscription
   * @param params.metadata - The metadata
   * @returns The created subscription
   */
  async createSubscription({
    userId,
    paymentMethodId,
    priceId,
    trialDays,
    purchaseType,
    metadata = {},
  }: {
    userId: string;
    paymentMethodId: string;
    priceId: string;
    trialDays?: number;
    purchaseType: PurchaseTypeEnum;
    metadata?: Record<string, string>;
  }): Promise<{
    success?: boolean;
    requiresAction?: boolean;
    clientSecret?: string;
    subscriptionId?: string;
    stripeAccountId?: string;
    isSetupIntent?: boolean;
  }> {
    return createSubscriptionImpl(this.stripe, this.client, {
      userId,
      paymentMethodId,
      priceId,
      trialDays,
      purchaseType,
      metadata,
    });
  }

  /**
   * Gets a subscription
   * @param subscriptionId - The ID of the subscription
   * @returns The subscription
   */
  async getSubscription(
    subscriptionId: string,
  ): Promise<UpsertSubscriptionParams> {
    return getSubscriptionImpl(this.stripe, subscriptionId);
  }

  /**
   * Updates a subscription item
   * @param params.subscriptionId - The ID of the subscription
   * @param params.priceId - The ID of the price
   * @param params.trialDays - The number of trial days
   * @param params.purchaseType - The type of the subscription
   * @param params.metadata - The metadata
   * @returns The updated subscription
   */
  async updateSubscriptionItem(
    params: z.infer<typeof UpdateSubscriptionParamsSchema>,
  ): Promise<{ success: boolean }> {
    return updateSubscriptionItemImpl(this.stripe, params);
  }

  /**
   * Cancels a subscription
   * @param params.subscriptionId - The ID of the subscription
   * @returns The cancellation result
   */
  async cancelSubscription(
    params: z.infer<typeof CancelSubscriptionParamsSchema>,
  ): Promise<{ success: boolean }> {
    return cancelSubscriptionImpl(this.stripe, params);
  }
}
