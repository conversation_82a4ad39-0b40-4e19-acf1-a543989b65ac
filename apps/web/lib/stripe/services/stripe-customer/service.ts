import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import type Strip<PERSON> from 'stripe';

import { attachPaymentMethodToCustomerImpl } from './implementations/attach-payment-method-to-customer';
import { detachPaymentMethodImpl } from './implementations/detach-payment-method';
import { getOrCreateStripeAccountImpl } from './implementations/get-or-create-stripe-account';
import { getStripeAccountBillingImpl } from './implementations/get-stripe-account-billing';
import { listPaymentMethodsImpl } from './implementations/list-payment-methods';

/**
 * Stripe customer service for managing customers and payment methods
 */
export class StripeCustomerService {
  constructor(
    private readonly stripe: Stripe,
    private readonly client: SupabaseClient,
  ) {}

  /**
   * Lists payment methods for a customer
   * @param stripeCustomerId The ID of the customer to list payment methods for
   * @returns The payment methods for the customer
   */
  async listPaymentMethods(stripeCustomerId: string): Promise<{
    data: Array<{
      id: string;
      card: {
        brand: string;
        last4: string;
        expMonth: number;
        expYear: number;
      };
    }>;
  }> {
    return listPaymentMethodsImpl(this.stripe, stripeCustomerId);
  }

  /**
   * Gets customer billing data including stripe_account_id and default_payment_method
   * @param userId User ID to get billing data for
   * @returns Customer billing data or null if not found
   */
  async getStripeAccountBilling(userId: string): Promise<{
    stripeAccountId: string;
    defaultPaymentMethodId: string | null;
  } | null> {
    return getStripeAccountBillingImpl(this.client, userId);
  }

  /**
   * Gets or creates a Stripe account for a user
   * Checks both our database and Stripe for existing accounts
   * @param params.userId The ID of the user to get or create the account for
   * @returns The ID of the account
   */
  async getOrCreateStripeAccount({
    userId,
  }: {
    userId: string;
  }): Promise<string> {
    return getOrCreateStripeAccountImpl(this.stripe, this.client, { userId });
  }

  /**
   * Attaches a payment method to a stripe account and sets it as the default
   * @param stripeAccountId The ID of the stripe account to attach the payment method to
   * @param paymentMethodId The ID of the payment method to attach to the stripe account
   * @returns The void
   */
  async attachPaymentMethodToCustomer(
    stripeAccountId: string,
    paymentMethodId: string,
  ): Promise<void> {
    return attachPaymentMethodToCustomerImpl(
      this.stripe,
      stripeAccountId,
      paymentMethodId,
    );
  }

  /**
   * Detaches a payment method from a customer
   * @param paymentMethodId The payment method ID to detach
   * @returns The detached payment method object
   */
  async detachPaymentMethod(paymentMethodId: string): Promise<{
    id: string;
    card?: {
      brand: string;
      last4: string;
      expMonth: number;
      expYear: number;
    };
  }> {
    return detachPaymentMethodImpl(this.stripe, paymentMethodId);
  }
}
