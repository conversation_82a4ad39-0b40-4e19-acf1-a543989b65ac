import 'server-only';

import type <PERSON><PERSON> from 'stripe';

import { getLogger } from '@kit/shared/logger';

/**
 * Attaches a payment method to a stripe account and sets it as the default
 * @param stripe Stripe client
 * @param stripeAccountId The ID of the stripe account to attach the payment method to
 * @param paymentMethodId The ID of the payment method to attach to the stripe account
 */
export async function attachPaymentMethodToCustomerImpl(
  stripe: Stripe,
  stripeAccountId: string,
  paymentMethodId: string,
): Promise<void> {
  const logger = await getLogger();

  const ctx = {
    name: 'attachPaymentMethodToCustomer',
    stripeAccountId,
    paymentMethodId,
  };

  logger.info(ctx, 'Attaching payment method to customer');

  try {
    // Attach the payment method to the customer
    await stripe.paymentMethods.attach(paymentMethodId, {
      customer_account: stripeAccountId,
    });

    // Set as default payment method
    await stripe.customers.update(stripeAccountId, {
      invoice_settings: {
        default_payment_method: paymentMethodId,
      },
    });
  } catch (error) {
    logger.error(ctx, 'Failed to attach payment method to customer', {
      error,
      stripeAccountId,
      paymentMethodId,
    });
    throw new Error(
      `Failed to attach payment method: ${
        error instanceof Error ? error.message : String(error)
      }`,
    );
  }
}
