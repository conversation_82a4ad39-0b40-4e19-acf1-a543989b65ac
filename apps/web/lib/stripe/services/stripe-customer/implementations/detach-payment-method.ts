import 'server-only';

import type <PERSON><PERSON> from 'stripe';

import { getLogger } from '@kit/shared/logger';

/**
 * Detaches a payment method from a customer
 * @param stripe Stripe client
 * @param paymentMethodId The payment method ID to detach
 * @returns The detached payment method object
 */
export async function detachPaymentMethodImpl(
  stripe: Stripe,
  paymentMethodId: string,
): Promise<{
  id: string;
  card?: {
    brand: string;
    last4: string;
    expMonth: number;
    expYear: number;
  };
}> {
  const logger = await getLogger();

  const ctx = {
    name: 'detachPaymentMethod',
    paymentMethodId,
  };

  logger.info(ctx, 'Detaching payment method');

  try {
    const paymentMethod = await stripe.paymentMethods.detach(paymentMethodId);

    return {
      id: paymentMethod.id,
      card: paymentMethod.card
        ? {
            brand: paymentMethod.card.brand,
            last4: paymentMethod.card.last4,
            expMonth: paymentMethod.card.exp_month,
            expYear: paymentMethod.card.exp_year,
          }
        : undefined,
    };
  } catch (error) {
    logger.error(ctx, 'Failed to detach payment method', {
      error,
      paymentMethodId,
    });
    throw new Error(
      `Failed to detach payment method: ${
        error instanceof Error ? error.message : String(error)
      }`,
    );
  }
}
