import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import type Strip<PERSON> from 'stripe';

import { getLogger } from '@kit/shared/logger';

import { getStripeAccountBillingImpl } from './get-stripe-account-billing';
import { saveStripeAccountToDatabaseImpl } from './save-stripe-account-to-database';

/**
 * Gets or creates a Stripe account for a user
 * Checks both our database and Stripe for existing accounts
 * @param stripe Stripe client
 * @param client Supabase client
 * @param params.userId The ID of the user to get or create the account for
 * @returns The ID of the account
 */
export async function getOrCreateStripeAccountImpl(
  stripe: Stripe,
  client: SupabaseClient,
  { userId }: { userId: string },
): Promise<string> {
  const logger = await getLogger();
  const ctx = {
    name: 'getOrCreateStripeAccount',
    userId,
  };

  logger.info(ctx, 'Getting or creating stripe account');

  // 1. Check if account exists in our database using getStripeAccountBillingImpl
  const existingStripeAccount = await getStripeAccountBillingImpl(
    client,
    userId,
  );
  if (existingStripeAccount?.stripeAccountId) {
    logger.info(
      { ...ctx, stripeAccountId: existingStripeAccount.stripeAccountId },
      'Stripe account found in database returning it',
    );
    return existingStripeAccount.stripeAccountId;
  }

  // 2. Get user data for creating/searching
  logger.info(ctx, 'Getting user data for creating/searching');
  const { data: userData } = await client
    .from('users')
    .select('email, first_name, last_name')
    .eq('id', userId)
    .single();

  if (!userData?.email) {
    throw new Error(`User ${userId} has no email address`);
  }

  // 3. Check if customer exists in Stripe by email
  try {
    // Looks like we can't search accounts by email so we need to search for customers
    // by email then get the customer_account field to lookup the account
    logger.info(ctx, 'Searching for customer in Stripe');
    const customerSearchResult = await stripe.customers.search({
      query: `email:'${userData.email}'`,
      limit: 1,
    });
    logger.info({ ctx, customerSearchResult }, 'Customer search result');
    // @ts-expect-error - Stripe v2 is not fully typed yet
    if (customerSearchResult.data?.[0]?.customer_account) {
      // Customer has stripe account so lets return it
      logger.info(
        // @ts-expect-error - Stripe v2 is not fully typed yet
        { ctx, stripeAccountId: customerSearchResult.data[0].customer_account },
        'Customer has stripe account so lets return it',
      );
      // @ts-expect-error - Stripe v2 is not fully typed yet
      return customerSearchResult.data[0].customer_account as string;
    }
  } catch (error) {
    logger.error(ctx, 'Error searching for customer', {
      error,
    });
    // Continue with creating a new customer
  }

  // 4. Create new customer
  const fullName =
    `${userData.first_name || ''} ${userData.last_name || ''}`.trim();

  logger.info(ctx, 'Creating new Stripe account');
  const newStripeAccount = await stripe.v2.core.accounts.create({
    configuration: {
      customer: {
        // Basic customer configuration - this enables the account to act as a customer
      },
    },
    contact_email: userData.email,
    display_name: fullName || undefined,
    metadata: { user_id: userId },
  });

  logger.info(ctx, 'Update customer metadata with user_id');
  await stripe.customers.update(newStripeAccount.id, {
    metadata: { user_id: userId },
  });

  logger.info(
    { ctx, stripeAccountId: newStripeAccount.id },
    'New Stripe account created',
  );

  // Save to our database
  await saveStripeAccountToDatabaseImpl(client, userId, newStripeAccount.id);
  return newStripeAccount.id;
}
