import 'server-only';

import type <PERSON><PERSON> from 'stripe';

import { getLogger } from '@kit/shared/logger';

/**
 * Lists payment methods for a customer
 * @param stripe Stripe client
 * @param stripeCustomerId The ID of the customer to list payment methods for
 * @returns The payment methods for the customer
 */
export async function listPaymentMethodsImpl(
  stripe: Stripe,
  stripeCustomerId: string,
): Promise<{
  data: Array<{
    id: string;
    card: {
      brand: string;
      last4: string;
      expMonth: number;
      expYear: number;
    };
  }>;
}> {
  const logger = await getLogger();
  const ctx = {
    name: 'listPaymentMethods',
    stripeCustomerId,
  };

  logger.info(ctx, 'Listing payment methods');
  const result = await stripe.customers.listPaymentMethods(
    stripeCustomerId,
    {
      type: 'card',
    },
    {
      // @ts-expect-error - customer_account is not yet in the types
      customer_account: stripeCustomerId,
    },
  );

  logger.info(ctx, 'Payment methods listed');

  return {
    data: result.data.map((method) => ({
      id: method.id,
      card: {
        brand: method.card?.brand ?? '',
        last4: method.card?.last4 ?? '',
        expMonth: method.card?.exp_month ?? 0,
        expYear: method.card?.exp_year ?? 0,
      },
    })),
  };
}
