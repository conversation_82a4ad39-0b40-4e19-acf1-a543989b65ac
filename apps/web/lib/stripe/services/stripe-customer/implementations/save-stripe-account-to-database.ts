import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

/**
 * Saves a Stripe account to our database
 * @param client Supabase client
 * @param userId The ID of the user to save the stripe account for
 * @param stripeAccountId The ID of the stripe account to save
 */
export async function saveStripeAccountToDatabaseImpl(
  client: SupabaseClient,
  userId: string,
  stripeAccountId: string,
): Promise<void> {
  const logger = await getLogger();

  const ctx = {
    name: 'saveStripeAccountToDatabase',
    userId,
    stripeAccountId,
  };

  // check if stripe_accounts table has a row with user_id = userId
  const { data: stripeAccount, error: stripeAccountError } = await client
    .from('stripe_accounts')
    .select('*')
    .eq('user_id', userId)
    .maybeSingle();

  if (stripeAccountError) {
    logger.error(
      { ctx, error: stripeAccountError },
      'Failed to get stripe account from database',
    );
    throw new Error(
      `Failed to get stripe account from database: ${stripeAccountError}`,
    );
  }

  if (stripeAccount) {
    logger.info(ctx, 'Saving Stripe account to database');
    const { error } = await client
      .from('users')
      .update({
        stripe_account_id: stripeAccountId,
      })
      .eq('id', userId);

    if (error) {
      logger.error(
        { ...ctx, error },
        'Failed to save Stripe account to database',
      );
      throw new Error(`Failed to save Stripe account to database: ${error}`);
    }
  } else {
    // if stripe_accounts table does not have a row with user_id = userId, create a new row
    const { error: stripeAccountError } = await client
      .from('stripe_accounts')
      .insert({
        id: stripeAccountId,
        user_id: userId,
        applied_configurations: ['customer'],
      });

    if (stripeAccountError) {
      logger.error(
        { ctx, error: stripeAccountError },
        'Failed to save Stripe account to database',
      );
      throw new Error(
        `Failed to save Stripe account to database: ${stripeAccountError}`,
      );
    }

    logger.info(ctx, 'Saving Stripe account to database');
    const { error: userError } = await client
      .from('users')
      .update({
        stripe_account_id: stripeAccountId,
      })
      .eq('id', userId);

    if (userError) {
      logger.error(
        { ctx, error: userError },
        'Failed to save Stripe account to database',
      );
      throw new Error(
        `Failed to save Stripe account to database: ${userError}`,
      );
    }

    logger.info(
      ctx,
      'Successfully created new Stripe account and updated user',
    );
  }
}
