import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

/**
 * Gets user stripe account data including stripe_account_id and default_payment_method
 * @param client Supabase client
 * @param userId User ID to get billing data for
 * @returns User stripe account data or null if not found
 */
export async function getStripeAccountBillingImpl(
  client: SupabaseClient,
  userId: string,
): Promise<{
  stripeAccountId: string;
  defaultPaymentMethodId: string | null;
} | null> {
  const logger = await getLogger();

  const ctx = {
    name: 'getStripeAccountBilling',
    userId,
  };

  logger.info(ctx, 'Getting stripe account billing data');

  try {
    const { data } = await client
      .from('users')
      .select('stripe_account_id, default_payment_method_id')
      .eq('id', userId)
      .single();

    if (!data) {
      return null;
    }

    return {
      stripeAccountId: data.stripe_account_id,
      defaultPaymentMethodId: data.default_payment_method_id,
    };
  } catch (error) {
    logger.error(ctx, 'Failed to load stripe account billing data', {
      error,
      userId,
    });
    return null;
  }
}
