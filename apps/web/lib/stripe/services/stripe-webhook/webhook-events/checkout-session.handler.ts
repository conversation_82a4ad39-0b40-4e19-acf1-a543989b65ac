import 'server-only';

import { UUID } from 'crypto';
import type <PERSON><PERSON> from 'stripe';

import { getLogger } from '@kit/shared/logger';

import { syncStripeInvoiceToDb } from '~/lib/stripe/_lib/stripe-sync-utils/sync-stripe-invoice-to-db';
import { PurchaseTypeEnum } from '~/lib/stripe/types';

import {
  handleCommunityMembershipSubscription,
  handleCommunityOwnershipSubscription,
} from '../../../_lib/common-handlers';
import { syncStripeSubscriptionToDb } from '../../../_lib/stripe-sync-utils/sync-stripe-subscription-to-db';
import type { BaseWebhookHandler, WebhookHandlerContext } from './types';

export class CheckoutSessionHandler implements BaseWebhookHandler {
  constructor(private readonly context: WebhookHandlerContext) {}

  async handle(
    event:
      | Stripe.CheckoutSessionCompletedEvent
      | Stripe.CheckoutSessionExpiredEvent,
  ): Promise<unknown> {
    const { client } = this.context;

    const logger = await getLogger();
    const session = event.data.object as Stripe.Checkout.Session;
    const stripeAccountId = session.customer_account as string;

    const ctx = {
      name: 'CheckoutSessionHandler',
      eventType: event.type,
      eventId: event.id,
      sessionId: session.id,
      stripeAccountId,
    };

    logger.info(ctx, 'Processing checkout session event');

    // Get the userId from client_reference_id or metadata
    const userId = session.metadata?.user_id as UUID;
    const communityId = session.metadata?.community_id as UUID;

    if (!userId) {
      logger.error(ctx, 'No user ID found in metadata');
      throw new Error('No user ID found for checkout session');
    }

    if (!communityId) {
      logger.error(ctx, 'No community ID found in metadata');
      throw new Error('No community ID found for checkout session');
    }

    // Check if session is expired
    if (event.type === 'checkout.session.expired') {
      logger.info(ctx, 'Checkout session expired');

      // Remove the checkout session from the database
      await client.from('checkout_sessions').delete().eq('id', session.id);

      return null;
    }

    // Check if it's a subscription or one-time payment
    if (session.mode === 'subscription' && session.subscription) {
      return this.handleSubscriptionCheckout(
        event.type,
        session,
        userId,
        communityId,
      );
    } else {
      return this.handleOneTimePaymentCheckout(
        event.type,
        session,
        userId,
        communityId,
      );
    }
  }

  private async handleSubscriptionCheckout(
    eventType: string,
    session: Stripe.Checkout.Session,
    userId: UUID,
    communityId: UUID,
  ): Promise<unknown> {
    console.log(`🚀 ~ handleSubscriptionCheckout session:`, session);
    const { client, stripe } = this.context;
    const logger = await getLogger();
    const subscriptionId = session.subscription as string;

    const stripeAccountId = session.customer_account as string;

    const ctx = {
      name: 'handleSubscriptionCheckout',
      eventType,
      subscriptionId,
      stripeAccountId,
      userId,
      communityId,
    };

    logger.info(ctx, 'Processing subscription checkout completion');

    try {
      const { stripeSubscription } = await syncStripeSubscriptionToDb(
        client,
        stripe,
        subscriptionId,
      );

      // Sync the invoice to database before processing membership
      if (stripeSubscription.latest_invoice) {
        logger.info(ctx, 'Syncing invoice to database');
        await syncStripeInvoiceToDb(
          client,
          stripe,
          stripeSubscription.latest_invoice as string,
        );
        logger.info(ctx, 'Successfully synced invoice to database');
      }

      const purchaseType = stripeSubscription.metadata
        ?.purchase_type as PurchaseTypeEnum;

      // Handle community ownership subscription
      if (purchaseType === 'community_ownership') {
        logger.info(ctx, 'Processing community ownership subscription');
        await handleCommunityOwnershipSubscription(stripeSubscription, client);
      } else if (purchaseType === 'community_membership') {
        // Handle community membership subscription
        const status = ['active', 'trialing'].includes(
          stripeSubscription.status,
        )
          ? 'active'
          : 'inactive';

        const userId = stripeSubscription.metadata?.user_id as UUID;
        const communityId = stripeSubscription.metadata?.community_id as UUID;

        const priceId = stripeSubscription.items.data[0]?.price.id;

        if (
          purchaseType === 'community_membership' &&
          status === 'active' &&
          priceId
        ) {
          logger.info(ctx, 'Processing community membership subscription');
          await handleCommunityMembershipSubscription(
            client,
            communityId,
            userId,
            purchaseType,
            'subscription',
            priceId,
            status,
            stripeSubscription.latest_invoice as string,
            stripeSubscription.id,
          );
        }
      }
    } catch (error) {
      logger.error({ ...ctx, error }, 'Error handling subscription checkout');
      throw error;
    }
    return null;
  }

  private async handleOneTimePaymentCheckout(
    eventType: string,
    session: Stripe.Checkout.Session,
    userId: string,
    communityId: UUID,
  ): Promise<string> {
    const { client, stripe } = this.context;
    const logger = await getLogger();
    const sessionId = session.id;
    console.log(`🚀 ~ session:`, session);

    const stripeAccountId = session.customer_account as string;

    const ctx = {
      name: 'handleOneTimePaymentCheckout',
      sessionId,
      stripeAccountId,
      userId,
    };

    logger.info(ctx, 'Processing one-time payment checkout completion');

    try {
      // Retrieve the session with line items expanded
      const checkoutSession = await stripe.checkout.sessions.retrieve(
        sessionId,
        {
          expand: ['line_items', 'invoice'],
        },
      );
      console.log(`🚀 ~ checkoutSession:`, checkoutSession);

      // TODO: Replace this with syncStripeInvoiceToDb

      // Access the actual session data from the response
      const sessionData = checkoutSession.data || checkoutSession;
      const lineItems = sessionData.line_items?.data ?? [];
      console.log(`🚀 ~ lineItems:`, lineItems);
      const purchaseType = session.metadata?.purchase_type as PurchaseTypeEnum;
      const userId = session.metadata?.user_id as UUID;
      const priceId = lineItems[0]?.price.id;
      console.log(`🚀 ~ priceId:`, priceId);

      // Add the invoice to the database
      logger.info(ctx, 'Adding invoice to database');

      const invoice = await syncStripeInvoiceToDb(
        client,
        stripe,
        sessionData.invoice as string,
      );

      logger.info(ctx, 'Successfully added invoice to database');
      // Handle community membership subscription
      if (purchaseType === 'community_membership') {
        // Handle community membership subscription
        const status = ['paid'].includes(sessionData.payment_status)
          ? 'active'
          : 'inactive';

        if (
          purchaseType === 'community_membership' &&
          status === 'active' &&
          priceId
        ) {
          logger.info(ctx, 'Processing community membership subscription');
          await handleCommunityMembershipSubscription(
            client,
            communityId,
            userId,
            purchaseType,
            'one-time',
            priceId,
            status,
            invoice.id,
          );
        }
      }

      return session.id;
    } catch (error) {
      logger.error(
        { ...ctx, error },
        'Error handling one-time payment checkout',
      );
      throw error;
    }
  }
}
