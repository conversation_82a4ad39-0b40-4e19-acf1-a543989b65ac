import 'server-only';

import { UUID } from 'crypto';
import type <PERSON><PERSON> from 'stripe';

import { getLogger } from '@kit/shared/logger';

import { handleCommunityMembershipSubscription } from '../../../_lib/common-handlers/community-membership.handler';
import { handleCommunityOwnershipSubscription } from '../../../_lib/common-handlers/community-ownership.handler';
import { handlePaymentMethodUpdate } from '../../../_lib/common-handlers/payment-method-update.handler';
import { syncStripeSubscriptionToDb } from '../../../_lib/stripe-sync-utils/sync-stripe-subscription-to-db';
import { PurchaseTypeEnum } from '../../../types';
import type { BaseWebhookHandler, WebhookHandlerContext } from './types';

export class CustomerSubscriptionHandler implements BaseWebhookHandler {
  constructor(private readonly context: WebhookHandlerContext) {}

  async handle(event: Stripe.Event): Promise<unknown> {
    const { client, stripe } = this.context;
    const logger = await getLogger();

    const ctx = {
      name: 'CustomerSubscriptionHandler',
      eventType: event.type,
      eventId: event.id,
    };

    logger.info(ctx, `Processing subscription event: ${event.type}`);

    const eventSubscription = event.data.object as Stripe.Subscription;

    if (!eventSubscription) {
      logger.error(ctx, 'No subscription found in event');
      return null;
    }

    // Sync the subscription to the database before handling the event
    const { stripeSubscription } = await syncStripeSubscriptionToDb(
      client,
      stripe,
      eventSubscription.id,
    );

    switch (event.type) {
      case 'customer.subscription.created':
      case 'customer.subscription.deleted':
      case 'customer.subscription.updated':
        return this.handleSubscriptionEvent(event, stripeSubscription);

      default:
        logger.info(ctx, `Unhandled subscription event type: ${event.type}`);
        return null;
    }
  }

  private async handleSubscriptionEvent(
    event:
      | Stripe.CustomerSubscriptionCreatedEvent
      | Stripe.CustomerSubscriptionDeletedEvent
      | Stripe.CustomerSubscriptionUpdatedEvent,
    stripeSubscription: Stripe.Subscription,
  ): Promise<unknown> {
    const { client } = this.context;
    const logger = await getLogger();

    const purchaseType = stripeSubscription.metadata
      ?.purchase_type as PurchaseTypeEnum;

    const ctx = {
      name: 'handleSubscriptionEvent',
      eventType: event.type,
      subscriptionId: stripeSubscription.id,
    };

    logger.info(ctx, 'Processing subscription event');

    // Handle community ownership subscription
    if (purchaseType === 'community_ownership') {
      logger.info(ctx, 'Processing community ownership subscription');
      await handleCommunityOwnershipSubscription(stripeSubscription, client);
    }

    // Handle community membership subscription
    if (
      purchaseType === 'community_membership' &&
      stripeSubscription.status === 'active'
    ) {
      // Get the userId from client_reference_id or metadata
      const userId = stripeSubscription.metadata?.user_id as UUID;
      const communityId = stripeSubscription.metadata?.community_id as UUID;
      const priceId = stripeSubscription.items.data[0].price.id;
      const status = stripeSubscription.status;

      if (!userId) {
        logger.error(ctx, 'No user ID found in metadata');
        throw new Error('No user ID found for checkout session');
      }

      if (!communityId) {
        logger.error(ctx, 'No community ID found in metadata');
        throw new Error('No community ID found for checkout session');
      }

      logger.info(ctx, 'Processing community membership subscription');
      await handleCommunityMembershipSubscription(
        client,
        communityId,
        userId,
        purchaseType,
        'subscription',
        priceId,
        status,
        stripeSubscription.id,
        stripeSubscription.latest_invoice as string,
      );
    }

    // Track payment method for the subscription
    await handlePaymentMethodUpdate(stripeSubscription, client);

    return null;
  }
}
