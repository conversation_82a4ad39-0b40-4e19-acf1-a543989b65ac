import 'server-only';

import type Stripe from 'stripe';

import { getLogger } from '@kit/shared/logger';

import { syncStripeInvoiceToDb } from '../../../_lib/stripe-sync-utils/sync-stripe-invoice-to-db';
import type { BaseWebhookHandler, WebhookHandlerContext } from './types';

export class InvoiceHandler implements BaseWebhookHandler {
  constructor(private readonly context: WebhookHandlerContext) {}

  async handle(event: Stripe.Event): Promise<unknown> {
    const logger = await getLogger();
    const ctx = {
      name: 'InvoiceHandler',
      eventType: event.type,
      eventId: event.id,
    };

    logger.info(ctx, 'Processing invoice event');

    const invoice = event.data.object as Stripe.Invoice;

    const { client, stripe } = this.context;
    return await syncStripeInvoiceToDb(client, stripe, invoice.id!);
  }
}
