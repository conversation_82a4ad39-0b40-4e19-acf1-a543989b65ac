import 'server-only';

import type <PERSON><PERSON> from 'stripe';

import { getLogger } from '@kit/shared/logger';

import { getStripeProductsService } from '../../stripe-factory.service';
import type { BaseWebhookHandler, WebhookHandlerContext } from './types';

export class ProductPriceHandler implements BaseWebhookHandler {
  constructor(private readonly context: WebhookHandlerContext) {}

  async handle(event: Stripe.Event): Promise<unknown> {
    const logger = await getLogger();

    const ctx = {
      name: 'ProductPriceHandler',
      eventType: event.type,
      eventId: event.id,
    };

    switch (event.type) {
      case 'product.created':
        return this.handleProductCreated(event);
      case 'product.updated':
        return this.handleProductUpdated(event);
      case 'price.created':
        return this.handlePriceCreated(event);
      case 'price.updated':
        return this.handlePriceUpdated(event);
      case 'plan.created':
      case 'plan.updated':
      case 'plan.deleted':
        logger.info(
          ctx,
          `Received ${event.type} event (note: Plans API is deprecated in favor of Prices API)`,
        );
        return null;
      default:
        logger.info(ctx, `Unhandled product/price event type: ${event.type}`);
        return null;
    }
  }

  private async handleProductCreated(
    event: Stripe.ProductCreatedEvent,
  ): Promise<unknown> {
    const { client } = this.context;
    const logger = await getLogger();
    const product = event.data.object as Stripe.Product;

    const ctx = {
      name: 'handleProductCreated',
      eventType: event.type,
      productId: product.id,
      communityId: product.metadata?.community_id,
      event: event,
    };

    logger.info(ctx, 'Processing product.created event');

    try {
      // Use the product service to upsert the product
      const stripeProductService = getStripeProductsService(client);
      const dbProduct = await stripeProductService.upsertProduct(product);

      logger.info(
        { ...ctx, platformProductId: dbProduct.id },
        'Successfully synced product to database',
      );

      return dbProduct;
    } catch (error) {
      logger.error({ ...ctx, error }, 'Error handling product.created event');
      throw error;
    }
  }

  private async handleProductUpdated(
    event: Stripe.ProductUpdatedEvent,
  ): Promise<unknown> {
    const { client } = this.context;
    const logger = await getLogger();
    const product = event.data.object as Stripe.Product;

    const ctx = {
      name: 'handleProductUpdated',
      eventType: event.type,
      productId: product.id,
    };

    logger.info(ctx, 'Processing product.updated event');

    try {
      // Use the product service to upsert the product
      const stripeProductService = getStripeProductsService(client);
      const dbProduct = await stripeProductService.upsertProduct(product);

      logger.info(
        { ...ctx, platformProductId: dbProduct.id },
        'Successfully synced product to database',
      );

      return dbProduct;
    } catch (error) {
      logger.error({ ...ctx, error }, 'Error handling product.updated event');
      throw error;
    }
  }

  private async handlePriceCreated(
    event: Stripe.PriceCreatedEvent,
  ): Promise<unknown> {
    const { client } = this.context;
    const logger = await getLogger();
    const price = event.data.object as Stripe.Price;
    const productId = price.product as string;

    const ctx = {
      name: 'handlePriceCreated',
      eventType: event.type,
      priceId: price.id,
      productId: productId,
    };

    logger.info(ctx, 'Processing price.created event');

    try {
      // First, look up the internal TEXT for the product using id
      const { data: productData, error: productError } = await client
        .from('products')
        .select('id')
        .eq('id', productId)
        .single();

      if (productError) {
        logger.error(
          { ...ctx, error: productError },
          'Error looking up product TEXT from id',
        );
        throw new Error(`Error looking up product: ${productError.message}`);
      }

      if (!productData) {
        logger.error(
          { ...ctx },
          'Product not found in database. Make sure the product exists before creating prices',
        );
        throw new Error(`Product with id=${productId} not found in database`);
      }

      // Use the product service to upsert the price
      const stripeProductService = getStripeProductsService(client);
      const dbPrice = await stripeProductService.upsertPrice(
        price,
        productData.id,
      );

      logger.info(
        { ...ctx, platformPriceId: dbPrice.id },
        'Successfully synced price to database',
      );

      return dbPrice;
    } catch (error) {
      logger.error({ ...ctx, error }, 'Error handling price.created event');
      throw error;
    }
  }

  private async handlePriceUpdated(
    event: Stripe.PriceUpdatedEvent,
  ): Promise<unknown> {
    const { client } = this.context;
    const logger = await getLogger();
    const price = event.data.object as Stripe.Price;
    const productId = price.product as string;

    const ctx = {
      name: 'handlePriceUpdated',
      eventType: event.type,
      priceId: price.id,
      productId: productId,
    };

    logger.info(ctx, 'Processing price.updated event');

    try {
      // First, look up the internal TEXT for the product using id
      const { data: productData, error: productError } = await client
        .from('products')
        .select('id')
        .eq('id', productId)
        .single();

      if (productError) {
        logger.error(
          { ...ctx, error: productError },
          'Error looking up product TEXT from id',
        );
        throw new Error(`Error looking up product: ${productError.message}`);
      }

      if (!productData) {
        logger.error(
          { ...ctx },
          'Product not found in database. Make sure the product exists before updating prices',
        );
        throw new Error(`Product with id=${productId} not found in database`);
      }

      // Use the product service to upsert the price
      const stripeProductService = getStripeProductsService(client);
      const dbPrice = await stripeProductService.upsertPrice(
        price,
        productData.id,
      );

      logger.info(
        { ...ctx, platformPriceId: dbPrice.id },
        'Successfully synced price to database',
      );

      return dbPrice;
    } catch (error) {
      logger.error({ ...ctx, error }, 'Error handling price.updated event');
      throw error;
    }
  }
}
