import 'server-only';

import type <PERSON><PERSON> from 'stripe';

import { getLogger } from '@kit/shared/logger';

import type { BaseWebhookHandler, WebhookHandlerContext } from './types';

export class CustomerHandler implements BaseWebhookHandler {
  constructor(private readonly context: WebhookHandlerContext) {}

  async handle(event: Stripe.Event): Promise<unknown> {
    const logger = await getLogger();

    const ctx = {
      name: 'CustomerHandler',
      eventType: event.type,
      eventId: event.id,
    };

    logger.info(ctx, 'Processing customer event');

    switch (event.type) {
      case 'customer.created':
        return this.handleCustomerCreated(event);
      default:
        logger.warn(ctx, `Unhandled customer event type: ${event.type}`);
        return null;
    }
  }

  /**
   * Handles customer.created webhook events
   * Ensures the customer exists in our database
   */
  private async handleCustomerCreated(
    event: Stripe.CustomerCreatedEvent,
  ): Promise<void> {
    const { client } = this.context;
    const logger = await getLogger();
    const customer = event.data.object as Stripe.Customer;
    const userId = customer.metadata?.user_id;
    const ctx = {
      name: 'handleCustomerCreated',
      eventType: event.type,
      eventId: event.id,
      customerId: customer.id,
      userId,
    };

    // Only handle customers that have a userId in metadata
    if (!userId) {
      logger.info(ctx, 'Customer created without userId in metadata, skipping');
      return;
    }

    try {
      // Check if we already have this customer associated with this user

      const { data: existingCustomer } = await client
        .from('users')
        .select('id')
        .eq('id', userId)
        .eq('stripe_account_id', customer.customer_account)
        .single();

      if (existingCustomer) {
        logger.info(ctx, 'Customer already exists in database');
        return;
      }

      // Add customer id to user
      const { error } = await client
        .from('users')
        .update({
          stripe_account_id: customer.customer_account,
        })
        .eq('id', userId);

      if (error) {
        throw error;
      }

      logger.info(ctx, 'Successfully saved Stripe customer to database');
    } catch (error) {
      logger.error(
        { ...ctx, error },
        'Failed to save Stripe customer to database',
      );
    }
  }
}
