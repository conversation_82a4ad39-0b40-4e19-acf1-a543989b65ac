import 'server-only';

import type <PERSON><PERSON> from 'stripe';

import { getLogger } from '@kit/shared/logger';

import type { BaseWebhookHandler, WebhookHandlerContext } from './types';

export class PaymentHandler implements BaseWebhookHandler {
  constructor(private readonly context: WebhookHandlerContext) {}

  async handle(event: Stripe.Event): Promise<unknown> {
    const logger = await getLogger();
    const paymentIntent = event.data.object as Stripe.PaymentIntent;

    const ctx = {
      name: 'PaymentHandler',
      eventType: event.type,
      eventId: event.id,
      paymentIntentId: paymentIntent.id,
      customerId: paymentIntent.customer as string,
    };

    logger.info(ctx, 'Processing payment event');

    switch (event.type) {
      case 'payment_intent.succeeded':
        return this.handlePaymentSucceeded(event, ctx);
      case 'payment_intent.payment_failed':
        return this.handlePaymentFailed(event, ctx);
      default:
        logger.warn(ctx, `Unhandled payment event type: ${event.type}`);
        return null;
    }
  }

  private async handlePaymentSucceeded(
    event: Stripe.PaymentIntentSucceededEvent,
    ctx: Record<string, unknown>,
  ): Promise<void> {
    const { client } = this.context;
    const logger = await getLogger();

    try {
      // Update payment status in database
      await client
        .from('payments')
        .update({ status: 'succeeded' })
        .eq('payment_intent_id', event.data.object.id);

      logger.info(ctx, 'Payment status updated to succeeded');
    } catch (error) {
      logger.error(
        { ...ctx, error },
        'Failed to update payment status to succeeded',
      );
      throw error;
    }
  }

  private async handlePaymentFailed(
    event: Stripe.PaymentIntentPaymentFailedEvent,
    ctx: Record<string, unknown>,
  ): Promise<void> {
    const { client } = this.context;
    const logger = await getLogger();

    try {
      // Update payment status in database
      await client
        .from('payments')
        .update({ status: 'failed' })
        .eq('payment_intent_id', event.data.object.id);

      logger.info(ctx, 'Payment status updated to failed');
    } catch (error) {
      logger.error(
        { ...ctx, error },
        'Failed to update payment status to failed',
      );
      throw error;
    }
  }
}
