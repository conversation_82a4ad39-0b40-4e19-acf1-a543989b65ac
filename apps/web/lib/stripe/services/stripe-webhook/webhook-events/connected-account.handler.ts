import 'server-only';

import type Stripe from 'stripe';

import { getLogger } from '@kit/shared/logger';

import { syncStripeAccountV2ToDb } from '~/lib/stripe/_lib/stripe-sync-utils/sync-stripe-account-v2-to-db';

import { ConnectAccountData } from '../../../types';
import type { BaseWebhookHandler, WebhookHandlerContext } from './types';

export class ConnectedAccountHandler implements BaseWebhookHandler {
  constructor(private readonly context: WebhookHandlerContext) {}

  async handle(event: Stripe.Event): Promise<unknown> {
    const logger = await getLogger();

    const ctx = {
      name: 'ConnectedAccountHandler',
      eventType: event.type,
      account: event.account,
      eventObject: event.data.object,
    };

    logger.info(ctx, 'ℹ️ ConnectedAccountHandler handle called with event');

    switch (event.type) {
      case 'account.updated':
      case 'account.application.authorized':
      case 'capability.updated':
      case 'account.external_account.created':
      case 'account.application.deauthorized':
      case 'account.external_account.updated':
      case 'account.external_account.deleted':
      case 'financial_connections.account.created':
        return this.handleAccountUpdated(event);
      default:
        logger.info(ctx, `Unhandled account event type: ${event.type}`);
        return null;
    }
  }

  private async handleAccountUpdated(
    event: Stripe.Event,
  ): Promise<ConnectAccountData | null> {
    const { client, stripe } = this.context;
    const logger = await getLogger();

    const ctx = {
      name: 'handleAccountUpdated',
      stripeAccountId: event.account,
    };

    logger.info(ctx, 'Processing account.updated event');

    const stripeAccountId = event.account as string;

    try {
      await syncStripeAccountV2ToDb(client, stripe, stripeAccountId);
    } catch (error) {
      logger.error(
        { ...ctx, error },
        'Error syncing stripe account to database',
      );
      throw error;
    }
    return null;
  }
}
