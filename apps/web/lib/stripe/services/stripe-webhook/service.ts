import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import type Stripe from 'stripe';

import type { Database } from '@kit/supabase/database';

import { handleEventImpl } from './implementations/handle-event';
import { verifyWebhookSignatureImpl } from './implementations/verify-webhook-signature';

/**
 * Type for the Stripe webhook handler service
 * @param clientProvider - The client provider
 * @param stripe - The Stripe client
 * @param webhookSecret - The webhook secret
 */
export type StripeWebhookHandler = {
  verifyWebhookSignature(request: Request): Promise<Stripe.Event>;
  handleEvent(event: Stripe.Event): Promise<unknown>;
};

/**
 * Service for handling Stripe webhook events
 * @param clientProvider - The client provider
 * @param stripe - The Stripe client
 * @param webhookSecret - The webhook secret
 */
export class StripeWebhookService implements StripeWebhookHandler {
  constructor(
    private readonly clientProvider: () => Promise<SupabaseClient<Database>>,
    private readonly stripe: Stripe,
    private readonly webhookSecret: string,
  ) {}

  /**
   * Handles webhook events
   * @param event - The Stripe event
   * @returns The result of the handler
   */
  async handleEvent(event: Stripe.Event): Promise<unknown> {
    return handleEventImpl(this.clientProvider, this.stripe, event);
  }

  /**
   * Verifies the webhook signature from a request and returns the event
   * @param request - The request
   * @returns The Stripe event
   */
  async verifyWebhookSignature(request: Request): Promise<Stripe.Event> {
    return verifyWebhookSignatureImpl(this.stripe, request, this.webhookSecret);
  }
}
