import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import type Strip<PERSON> from 'stripe';

import { getLogger } from '@kit/shared/logger';
import type { Database } from '@kit/supabase/database';

import { CheckoutSessionHandler } from '../webhook-events/checkout-session.handler';
import { ConnectedAccountHandler } from '../webhook-events/connected-account.handler';
import { CustomerSubscriptionHandler } from '../webhook-events/customer-subscription.handler';
import { CustomerHandler } from '../webhook-events/customer.handler';
import { InvoiceHandler } from '../webhook-events/invoice.handler';
import { PaymentHandler } from '../webhook-events/payment.handler';
import { ProductPriceHandler } from '../webhook-events/product-price.handler';

/**
 * Handles Stripe webhook events
 * @param clientProvider - The client provider
 * @param stripe - The Stripe client
 * @param event - The Stripe event
 * @returns The result of the handler
 */
export async function handleEventImpl(
  clientProvider: () => Promise<SupabaseClient<Database>>,
  stripe: Stripe,
  event: Stripe.Event,
): Promise<unknown> {
  const logger = await getLogger();
  const client = await clientProvider();
  const ctx = {
    name: 'stripe-webhook-handler',
    eventType: event.type,
    eventId: event.id,
  };

  logger.info(ctx, 'Processing webhook event');

  const handlerContext = {
    client,
    stripe,
    namespace: 'stripe-webhook-handler',
  };

  switch (event.type) {
    case 'account.updated':
    case 'account.application.authorized':
    case 'account.application.deauthorized':
    case 'account.external_account.created':
    case 'account.external_account.updated':
    case 'account.external_account.deleted':
    case 'capability.updated':
      return new ConnectedAccountHandler(handlerContext).handle(event);
    case 'checkout.session.completed':
    case 'checkout.session.expired':
      return new CheckoutSessionHandler(handlerContext).handle(event);
    case 'customer.created':
      return new CustomerHandler(handlerContext).handle(event);
    case 'customer.subscription.created':
    case 'customer.subscription.updated':
    case 'customer.subscription.deleted':
    case 'customer.subscription.paused':
    case 'customer.subscription.resumed':
    case 'customer.subscription.pending_update_applied':
    case 'customer.subscription.pending_update_expired':
    case 'customer.subscription.trial_will_end':
      return new CustomerSubscriptionHandler(handlerContext).handle(event);
    case 'invoice.paid':
    case 'invoice.payment_failed':
    case 'invoice.payment_action_required':
    case 'invoice.upcoming':
    case 'invoice.marked_uncollectible':
    case 'invoice.payment_succeeded':
      return new InvoiceHandler(handlerContext).handle(event);
    case 'payment_intent.succeeded':
    case 'payment_intent.payment_failed':
    case 'payment_intent.canceled':
      return new PaymentHandler(handlerContext).handle(event);
    case 'product.created':
    case 'product.updated':
    case 'price.created':
    case 'price.updated':
      return new ProductPriceHandler(handlerContext).handle(event);
    default:
      await logger.warn(ctx, `Unhandled webhook event type: ${event.type}`);
      return null;
  }
}
