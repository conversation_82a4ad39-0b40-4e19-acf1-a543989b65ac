import 'server-only';

import type <PERSON><PERSON> from 'stripe';

import { getLogger } from '@kit/shared/logger';

/**
 * Verifies the webhook signature from a request and returns the event
 * @param stripe - The Stripe client
 * @param request - The request
 * @param webhookSecret - The webhook secret
 * @returns The Stripe event
 */
export async function verifyWebhookSignatureImpl(
  stripe: Stripe,
  request: Request,
  webhookSecret: string,
): Promise<Stripe.Event> {
  const logger = await getLogger();
  const payload = await request.text();
  const signature = request.headers.get('stripe-signature');

  if (!signature) {
    logger.error('No Stripe signature found in request');
    throw new Error('No Stripe signature found in request');
  }

  try {
    return stripe.webhooks.constructEvent(payload, signature, webhookSecret);
  } catch (error) {
    logger.error({ error }, 'Invalid webhook signature');
    throw new Error('Invalid webhook signature');
  }
}
