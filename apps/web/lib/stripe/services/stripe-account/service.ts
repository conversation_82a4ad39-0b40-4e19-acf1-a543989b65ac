import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import type Stripe from 'stripe';
import { z } from 'zod';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

import {
  AccountLinkResponseSchema,
  RecipientAccountLinkParamsSchema,
  ResetStripeAccountParamsSchema,
  RetrieveStripeAccountParamsSchema,
  RetrieveSupportedCountriesParamsSchema,
  StripeAccountParamsSchema,
  StripeAccountResponseSchema,
} from '../../schema/stripe-account.schema';
import {
  CommunityStripeAccountStatus,
  DbStripeAccount,
} from '../../types/stripe-account.types';
import { createStripeSdk } from '../../utils/stripe-sdk';
import { addCountryToDbStripeAccountImpl } from './implementations/add-country-db-stripe-account';
import { createOnboardingOrLoginLinkImpl } from './implementations/create-onboard-or-login-link';
import { createRecipientAccountLinkImpl } from './implementations/create-recipient-account-link';
import { createRecipientStripeAccountImpl } from './implementations/create-recipient-stripe-account';
import { getCommunityStripeAccountStatusImpl } from './implementations/get-community-stripe-account-status';
import { getDbStripeAccountByIdImpl } from './implementations/get-stripe-account-by-id';
import { getDbStripeAccountByUserIdImpl } from './implementations/get-stripe-account-by-user-id';
import { linkCommunityToAccountImpl } from './implementations/link-community-to-account';
import { resetStripeAccountMerchantImpl } from './implementations/reset-stripe-account-merchant';
import { retrieveStripeAccountImpl } from './implementations/retrieve-stripe-account';
import { retrieveSupportedCountriesForAccountImpl } from './implementations/retrieve-supported-countries-for-account';
import { updateStripeAccountImpl } from './implementations/update-stripe-account';

/**
 * Service for managing Stripe accounts
 */
export class StripeAccountService {
  private stripe: Stripe;
  private client: SupabaseClient;

  /**
   * Creates a new StripeAccountService instance
   *
   * This allows the service to be created via the factory pattern,
   * consistent with other Stripe services
   *
   * @param stripe - Optional Stripe SDK instance
   * @param client - Optional Supabase client
   */
  constructor(stripe?: Stripe, client?: SupabaseClient) {
    this.stripe = stripe || createStripeSdk();
    this.client = client || getSupabaseServerClient();
  }

  async addCountryToDbStripeAccount(
    stripeAccountId: string,
    countryISOCode1: string,
  ): Promise<DbStripeAccount> {
    return addCountryToDbStripeAccountImpl(
      this.client,
      stripeAccountId,
      countryISOCode1,
    );
  }

  /**
   * Creates an onboarding or login link for Stripe Connect
   * @param stripeAccountId The ID of the Stripe account to create the login link for
   * @returns The onboarding or login link
   */
  async createOnboardingOrLoginLink(
    stripeAccountId: string,
    refreshUrl: string,
    returnUrl: string,
  ): Promise<{ url: string; linkType: 'login' | 'onboarding' | 'setup' }> {
    return createOnboardingOrLoginLinkImpl(
      this.stripe,
      stripeAccountId,
      refreshUrl,
      returnUrl,
    );
  }

  /**
   * Creates a new Stripe Connect account FOR A USER.
   * @param params The parameters for creating a new Stripe Connect account
   * @returns The created stripe account
   */
  async createRecipientStripeAccount(
    params: z.infer<typeof StripeAccountParamsSchema>,
  ): Promise<z.infer<typeof StripeAccountResponseSchema>> {
    return createRecipientStripeAccountImpl(this.stripe, this.client, params);
  }

  /**
   * Get Db Stripe Account by Stripe Account ID
   * @param stripeAccountId The ID of the stripe account to get
   * @returns The db stripe account object
   */
  async getDbStripeAccountById(
    stripeAccountId: string,
  ): Promise<DbStripeAccount | null> {
    return getDbStripeAccountByIdImpl(this.client, stripeAccountId);
  }

  /**
   * Get Db Stripe Account by User ID
   * @param userId The ID of the user to get the stripe account for
   * @returns The db stripe account object
   */
  async getDbStripeAccountByUserId(
    userId: string,
  ): Promise<DbStripeAccount | null> {
    return getDbStripeAccountByUserIdImpl(this.client, userId);
  }

  /**
   * Get the stripe account status for the given community
   * @param communityId The ID of the community to get the stripe account status for
   * @returns The stripe account status for the given community
   */
  async getCommunityStripeAccountStatus(
    communityId: string,
  ): Promise<CommunityStripeAccountStatus | null> {
    return getCommunityStripeAccountStatusImpl(this.client, communityId);
  }

  /**
   * Updates a stripe account record in the database via RPC
   * @param stripeAccount The Stripe account object to update
   * @returns The updated stripe account object
   */
  async updateStripeAccount(
    stripeAccount: Stripe.Account,
    email: string,
    countryISOCode1: string,
  ): Promise<unknown> {
    return updateStripeAccountImpl(
      this.stripe,
      stripeAccount,
      email,
      countryISOCode1,
    );
  }

  /**
   * Links an existing user-owned stripe account to a specific community
   * @param communityId The ID of the community to link the stripe account to
   * @param userId The ID of the user to link the stripe account to
   * @returns The void
   */
  async linkCommunityToAccount(
    communityId: string,
    userId: string,
  ): Promise<void> {
    return linkCommunityToAccountImpl(this.client, communityId, userId);
  }

  /**
   * Retrieves a stripe account from Stripe API
   * @param params The parameters for retrieving a stripe account
   * @returns The stripe account object
   */
  async retrieveStripeAccount(
    params: z.infer<typeof RetrieveStripeAccountParamsSchema>,
  ): Promise<Stripe.V2.Core.Account> {
    return retrieveStripeAccountImpl(this.stripe, params);
  }

  /**
   * Retrieves supported countries for a stripe account
   * @param params The parameters for retrieving supported countries for a stripe account
   * @returns The supported countries for the stripe account
   */
  async retrieveSupportedCountriesForAccount(
    params: z.infer<typeof RetrieveSupportedCountriesParamsSchema>,
  ): Promise<string[]> {
    return retrieveSupportedCountriesForAccountImpl(this.stripe, params);
  }

  /**
   * Resets a Stripe account in the database by clearing merchant data
   * @param params The parameters for resetting a stripe account
   * @returns The success status of the reset operation
   */
  async resetStripeAccount(
    params: z.infer<typeof ResetStripeAccountParamsSchema>,
  ): Promise<{ success: boolean }> {
    return resetStripeAccountMerchantImpl(this.stripe, this.client, params);
  }

  /**
   * Creates a recipient account link for Stripe Connect onboarding or updates
   * @param params The parameters for creating an account link
   * @returns The account link object
   */
  async createRecipientAccountLink(
    params: z.infer<typeof RecipientAccountLinkParamsSchema>,
  ): Promise<z.infer<typeof AccountLinkResponseSchema>> {
    return createRecipientAccountLinkImpl(this.stripe, params);
  }
}
