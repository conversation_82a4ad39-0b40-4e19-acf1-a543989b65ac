import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import type <PERSON><PERSON> from 'stripe';
import { z } from 'zod';

import { getLogger } from '@kit/shared/logger';

import { ResetStripeAccountParamsSchema } from '../../../schema/stripe-account.schema';

/**
 * Resets a Stripe account in the database by clearing merchant data
 * @param stripe Stripe client
 * @param client Supabase client
 * @param params.stripeAccountId The ID of the Stripe account to reset
 * @returns The success status of the reset operation
 */
export async function resetStripeAccountMerchantImpl(
  stripe: Stripe,
  client: SupabaseClient,
  params: z.infer<typeof ResetStripeAccountParamsSchema>,
): Promise<{ success: boolean }> {
  const logger = await getLogger();
  const ctx = {
    name: 'resetStripeAccount',
    stripeAccountId: params.stripeAccountId,
  };

  logger.info(ctx, 'Resetting Stripe account');

  try {
    const { error } = await client
      .from('stripe_accounts')
      .update({
        country_id: null,
        capabilities: {},
        configuration: {},
        identity: {},
        requirements: {},
      })
      .eq('id', params.stripeAccountId);

    if (error) {
      logger.error(
        { ...ctx, error },
        'Error resetting Stripe account in database',
      );
      throw error;
    }

    logger.info(ctx, 'Successfully reset Stripe account in database');
  } catch (error) {
    logger.error({ ...ctx, error }, 'Error resetting Stripe account');
    throw error;
  }
  return {
    success: true,
  };
}
