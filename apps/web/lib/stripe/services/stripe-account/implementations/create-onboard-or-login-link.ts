import 'server-only';

import type <PERSON><PERSON> from 'stripe';

import { getLogger } from '@kit/shared/logger';

import type { StripeV2Account } from '../../../types/stripe-account-v2.types';

/**
 * Creates an onboarding or login link for Stripe Connect
 * This function handles all three stages:
 * 1. Setup: Account needs recipient configuration applied
 * 2. Onboarding: Account has recipient config but needs to complete onboarding
 * 3. Login: Account is fully set up and user wants to access dashboard
 *
 * @param stripe Stripe client
 * @param stripeAccountId The ID of the Stripe account to create the link for
 * @param refreshUrl The URL to redirect the customer to if the link is expired
 * @param returnUrl The URL to redirect the customer to after completing onboarding
 * @returns The link URL and type
 */
export async function createOnboardingOrLoginLinkImpl(
  stripe: Stripe,
  stripeAccountId: string,
  refreshUrl: string,
  returnUrl: string,
): Promise<{ url: string; linkType: 'login' | 'onboarding' | 'setup' }> {
  const logger = await getLogger();

  const ctx = {
    name: 'createOnboardingOrLoginLinkImpl',
    stripeAccountId,
  };

  try {
    logger.info(ctx, 'Retrieving Stripe account for link creation');

    // Retrieve account using v2 API to check configurations
    const stripeAccount = await stripe.v2.core.accounts.retrieve(
      stripeAccountId,
      {
        include: [
          'configuration.customer',
          'configuration.merchant',
          'configuration.recipient',
          'identity',
          'requirements',
        ],
      },
    );

    if (!stripeAccount?.id) {
      logger.error(
        { ctx, stripeAccount },
        'Account retrieval failed - no account returned',
      );
      throw new Error(`Account ${stripeAccountId} not found`);
    }

    logger.info(
      {
        ctx,
        accountId: stripeAccount.id,
        appliedConfigurations: (stripeAccount as unknown as StripeV2Account)
          .applied_configurations,
      },
      'Account retrieved successfully',
    );

    // Check if account has recipient configuration applied
    const hasRecipientConfiguration = (
      stripeAccount as unknown as Stripe.V2.Core.Account
    ).applied_configurations?.includes('recipient');

    if (!hasRecipientConfiguration) {
      logger.info(
        ctx,
        'Account missing recipient configuration, returning setup link type',
      );
      return {
        url: '',
        linkType: 'setup',
      };
    }

    logger.info(
      ctx,
      'Account has recipient configuration, creating account link (onboarding or login)',
    );

    const hasActiveRecipientConfiguration =
      (stripeAccount as unknown as Stripe.V2.Core.Account).configuration
        ?.merchant?.capabilities?.card_payments?.status === 'active' &&
      (stripeAccount as unknown as Stripe.V2.Core.Account).configuration
        ?.recipient?.capabilities?.stripe_balance?.stripe_transfers?.status ===
        'active';

    logger.info(
      { ctx, hasActiveRecipientConfiguration },
      'hasActiveRecipientConfiguration',
    );

    const linkType = hasActiveRecipientConfiguration ? 'login' : 'onboarding';

    let accountLinkResponse:
      | Stripe.Response<Stripe.AccountLink>
      | Stripe.Response<Stripe.LoginLink>;
    if (hasActiveRecipientConfiguration) {
      logger.info(
        ctx,
        'Account has active recipient configuration, creating login link',
      );
      accountLinkResponse =
        await stripe.accounts.createLoginLink(stripeAccountId);
    } else {
      logger.info(
        ctx,
        'Account does not have active recipient configuration, creating onboarding link',
      );
      accountLinkResponse = await stripe.accountLinks.create({
        account: stripeAccountId,
        type: 'account_onboarding',
        refresh_url: refreshUrl,
        return_url: returnUrl,
      });
    }

    // Access the data from the response wrapper
    const accountLink = accountLinkResponse.data || accountLinkResponse;

    logger.info(
      { ctx, url: accountLink.url },
      'Account link created successfully using v1 API',
    );

    return {
      url: accountLink.url,
      linkType: linkType as 'login' | 'onboarding' | 'setup',
    };
  } catch (error) {
    logger.error({ ...ctx, error }, 'Failed to create account link');
    throw error;
  }
}
