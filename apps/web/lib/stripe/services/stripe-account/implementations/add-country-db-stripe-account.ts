import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import { DbStripeAccount } from '../../../types/stripe-account.types';

/**
 * Add a country to a Stripe account in the database
 * @param client Supabase client
 * @param stripeAccountId The ID of the Stripe account to update
 * @param countryISOCode1 The ISO code of the country to add
 * @returns The updated Stripe account object
 */
export async function addCountryToDbStripeAccountImpl(
  client: SupabaseClient,
  stripeAccountId: string,
  countryISOCode1: string,
): Promise<DbStripeAccount> {
  const logger = await getLogger();
  const ctx = {
    name: 'addCountryToDbStripeAccount',
    stripeAccountId,
    countryISOCode1,
  };

  logger.info(ctx, 'Getting country_id by ISO code');
  const { data: country, error: countryError } = await client
    .from('countries')
    .select('id')
    .eq('iso_code_1', countryISOCode1.toUpperCase())
    .single();

  if (!country) {
    logger.error(ctx, 'Country not found');
    throw new Error('Country not found');
  }
  if (countryError) {
    logger.error(ctx, 'Error getting country_id by ISO code', {
      countryISOCode1,
      error: countryError,
    });
    throw countryError;
  }

  logger.info(ctx, 'Adding country to db stripe account');
  const { data, error } = await client
    .from('stripe_accounts')
    .update({
      country_id: country.id,
    })
    .eq('id', stripeAccountId)
    .select('*')
    .single();

  if (error) {
    logger.error(ctx, 'Error adding country to db stripe account', {
      error,
    });
    throw error;
  }

  logger.info(ctx, 'Country added to db stripe account', {
    stripeAccountId,
  });
  return objectToCamel(data) as DbStripeAccount;
}
