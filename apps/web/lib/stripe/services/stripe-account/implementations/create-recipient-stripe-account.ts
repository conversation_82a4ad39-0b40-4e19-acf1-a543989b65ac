import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import type Strip<PERSON> from 'stripe';
import { z } from 'zod';

import { getLogger } from '@kit/shared/logger';

import {
  StripeAccountParamsSchema,
  StripeAccountResponseSchema,
} from '../../../schema/stripe-account.schema';
import type { StripeV2Account } from '../../../types/stripe-account-v2.types';

/**
 * Updates an existing Stripe account to add recipient configuration
 * @param stripe Stripe client
 * @param client Supabase client
 * @param params.userId The ID of the user
 * @param params.stripeAccountId The ID of the existing stripe account to update
 * @param params.countryISOCode1 The country code for the account
 * @param params.email The email for the account
 * @returns The updated stripe account
 */
export async function createRecipientStripeAccountImpl(
  stripe: Stripe,
  client: SupabaseClient,
  params: z.infer<typeof StripeAccountParamsSchema> & { email: string },
): Promise<z.infer<typeof StripeAccountResponseSchema>> {
  const logger = await getLogger();
  const ctx = { name: createRecipientStripeAccountImpl.name };

  logger.info(ctx, 'Updating Stripe account with recipient configuration', {
    userId: params.userId,
    stripeAccountId: params.stripeAccountId,
    countryISOCode1: params.countryISOCode1,
  });

  if (!params.stripeAccountId) {
    throw new Error(
      'stripeAccountId is required to update account with recipient configuration',
    );
  }

  try {
    // Update the existing account to add recipient configuration
    const updatedAccount = await stripe.v2.core.accounts.update(
      params.stripeAccountId,
      {
        dashboard: 'express',
        configuration: {
          merchant: {
            capabilities: {
              card_payments: {
                requested: true,
              },
            },
          },
          recipient: {
            capabilities: {
              stripe_balance: {
                stripe_transfers: {
                  requested: true,
                },
              },
            },
          },
        },
        identity: {
          country:
            params.countryISOCode1.toLowerCase() as Stripe.V2.Core.Account.Identity.Country,
        },
        defaults: {
          responsibilities: {
            fees_collector: 'application',
            losses_collector: 'application',
          },
        },
        metadata: {
          user_id: params.userId,
        },
      },
    );

    // Type assertion for v2 response
    const accountData = updatedAccount as unknown as StripeV2Account;

    logger.info(
      ctx,
      'Successfully updated Stripe account with recipient configuration',
      {
        accountId: accountData.id,
        appliedConfigurations: accountData.applied_configurations,
      },
    );

    logger.info(ctx, 'Getting country_id by ISO code', {
      countryISOCode1: params.countryISOCode1,
    });
    const { data: country, error: countryError } = await client
      .from('countries')
      .select('id')
      .eq('iso_code_1', params.countryISOCode1.toUpperCase())
      .single();

    if (!country) {
      logger.error(ctx, 'Country not found', {
        countryISOCode1: params.countryISOCode1,
      });
      throw new Error('Country not found');
    }
    if (countryError) {
      logger.error(ctx, 'Error getting country_id by ISO code', {
        countryISOCode1: params.countryISOCode1,
        error: countryError,
      });
      throw countryError;
    }

    // Update the database record
    const { error: updateError } = await client
      .from('stripe_accounts')
      .update({
        country_id: country.id,
        updated_at: new Date().toISOString(),
      })
      .eq('user_id', params.userId)
      .eq('stripe_account_id', params.stripeAccountId);

    if (updateError) {
      logger.error(ctx, 'Failed to update database record', {
        error: updateError,
      });
      throw new Error(`Failed to update database: ${updateError.message}`);
    }

    return {
      id: accountData.id,
      stripeAccountId: accountData.id,
      countryISOCode1: params.countryISOCode1,
      requirements: accountData.requirements as
        | {
            currentlyDue?: string[];
            eventuallyDue?: string[];
            pendingVerification?: string[];
            disabledReason?: string | null;
            currentDeadline?: number | null;
          }
        | undefined,
    };
  } catch (error) {
    logger.error(
      ctx,
      'Failed to update Stripe account with recipient configuration',
      {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: params.userId,
        stripeAccountId: params.stripeAccountId,
      },
    );

    throw error;
  }
}
