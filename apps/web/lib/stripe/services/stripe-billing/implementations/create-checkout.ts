import 'server-only';

import type Stripe from 'stripe';
import { z } from 'zod';

import { CreateBillingCheckoutSchema } from '../../../schema';

/**
 * Implementation for creating a Stripe checkout session
 * @param stripe Stripe client
 * @param params.returnUrl The URL to redirect the user to after the session is created
 * @param params.userId The ID of the user to create the checkout session for
 * @param params.communityId The ID of the community to create the checkout session for
 * @param params.price The price to create the checkout session for
 * @param params.stripeAccountId The ID of the stripe account to create the checkout session for
 * @param params.customerEmail The email of the user to create the checkout session for
 * @param params.enableDiscountField Whether to enable the discount field
 * @param params.metadata The metadata to attach to the checkout session
 * @returns The checkout session token
 */

export async function createCheckoutSessionImpl(
  stripe: Stripe,
  params: z.infer<typeof CreateBillingCheckoutSchema>,
): Promise<{ checkoutToken: string }> {
  const clientReferenceId = params.communityId;
  const stripeAccountId = params.stripeAccountId ?? undefined;

  // docs: https://stripe.com/docs/billing/subscriptions/build-subscription
  const mode: 'subscription' | 'payment' =
    params.price.paymentType === 'recurring' ? 'subscription' : 'payment';

  const isSubscription = mode === 'subscription';

  const trialSettings = params.price.trialDays
    ? {
        trial_settings: {
          end_behavior: {
            missing_payment_method: 'cancel' as const,
          },
        },
      }
    : {};

  const subscriptionData = isSubscription
    ? {
        trial_period_days: params.price.trialDays,
        metadata: {
          community_id: params.communityId,
          user_id: params.userId,
          ...(params.metadata ?? {}),
        },
        ...trialSettings,
      }
    : undefined;

  const metadata = {
    ...(params.metadata ?? {}),
    community_id: params.communityId,
    user_id: params.userId,
  };

  const returnUrl = `${params.returnUrl}&session_id={CHECKOUT_SESSION_ID}`;

  // Use the embedded mode, so the user does not leave the page
  const uiMode = 'embedded';

  const customerData = stripeAccountId
    ? {
        customer_account: stripeAccountId,
      }
    : {
        customer_email: params.customerEmail,
      };

  const customerCreation =
    isSubscription || stripeAccountId
      ? ({} as Record<string, string>)
      : { customer_creation: 'always' };

  // All line items are now flat-rate only
  const lineItems = params.price.lineItems.map((item) => {
    // Use default quantity
    const quantity = 1;

    return {
      price: item.id,
      quantity,
    };
  });

  const paymentCollectionMethod = params.price.trialDays
    ? {
        payment_method_collection: 'if_required' as const,
      }
    : {};

  const session = await stripe.checkout.sessions.create({
    mode,
    allow_promotion_codes: params.enableDiscountField,
    ui_mode: uiMode,
    line_items: lineItems,
    client_reference_id: clientReferenceId,
    subscription_data: subscriptionData,
    return_url: returnUrl,
    ...customerCreation,
    ...customerData,
    ...paymentCollectionMethod,
    metadata: metadata,
  });

  return {
    checkoutToken: session.client_secret || '',
  };
}
