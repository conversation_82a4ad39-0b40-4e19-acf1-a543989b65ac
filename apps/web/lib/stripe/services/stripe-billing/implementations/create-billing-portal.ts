import 'server-only';

import type <PERSON><PERSON> from 'stripe';
import { z } from 'zod';

import { CreateBillingPortalSessionSchema } from '../../../schema';

/**
 * Implementation for creating a Stripe billing portal session
 * @param stripe Stripe client
 * @param params.stripeAccountId The ID of the stripe account to create the billing portal session for
 * @param params.returnUrl The URL to redirect the stripe account to after the session is created
 * @returns The billing portal session URL
 */

export async function createBillingPortalSessionImpl(
  stripe: Stripe,
  params: z.infer<typeof CreateBillingPortalSessionSchema>,
): Promise<{ url: string }> {
  const session = await stripe.billingPortal.sessions.create({
    customer_account: params.stripeAccountId,
    return_url: params.returnUrl,
  });

  return {
    url: session.data?.url as string,
  };
}
