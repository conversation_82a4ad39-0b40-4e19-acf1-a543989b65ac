import 'server-only';

import type <PERSON><PERSON> from 'stripe';
import { z } from 'zod';

import { RetrieveCheckoutSessionSchema } from '../../../schema';

/**
 * Implementation for retrieving a Stripe checkout session
 * @param stripe Stripe client
 * @param params.sessionId The ID of the checkout session to retrieve
 * @returns The checkout session token
 */
export async function retrieveCheckoutSessionImpl(
  stripe: Stripe,
  params: z.infer<typeof RetrieveCheckoutSessionSchema>,
): Promise<{
  checkoutToken: string | null;
  status: 'complete' | 'expired' | 'open';
  isSessionOpen: boolean;
  customer: {
    email: string | null;
  };
}> {
  const session = await stripe.checkout.sessions.retrieve(params.sessionId);

  return {
    checkoutToken: session.client_secret,
    status: session.status as 'complete' | 'expired' | 'open',
    isSessionOpen: session.status === 'open',
    customer: {
      email: session.customer_email,
    },
  };
}
