import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import type <PERSON><PERSON> from 'stripe';
import { z } from 'zod';

import {
  CreateBillingCheckoutSchema,
  CreateBillingPortalSessionSchema,
  RetrieveCheckoutSessionSchema,
} from '../../schema';
// Import implementations
import { createBillingPortalSessionImpl } from './implementations/create-billing-portal';
import { createCheckoutSessionImpl } from './implementations/create-checkout';
import { retrieveCheckoutSessionImpl } from './implementations/retrieve-checkout';

/**
 * Stripe billing service for managing subscriptions and checkout sessions
 */
export class StripeBillingService {
  constructor(
    private readonly stripe: Stripe,
    private readonly client: SupabaseClient,
  ) {}

  /**
   * Creates a billing portal session for managing subscriptions
   * @param params The parameters for creating a billing portal session
   * @returns The URL of the billing portal session
   */
  async createBillingPortalSession(
    params: z.infer<typeof CreateBillingPortalSessionSchema>,
  ): Promise<{ url: string }> {
    return createBillingPortalSessionImpl(this.stripe, params);
  }

  /**
   * Creates a checkout session
   * @param params The parameters for creating a checkout session
   * @returns The checkout session token
   */
  async createCheckoutSession(
    params: z.infer<typeof CreateBillingCheckoutSchema>,
  ): Promise<{ checkoutToken: string }> {
    return createCheckoutSessionImpl(this.stripe, params);
  }

  /**
   * Retrieves a checkout session
   * @param params The parameters for retrieving a checkout session
   * @returns The checkout session object
   */
  async retrieveCheckoutSession(
    params: z.infer<typeof RetrieveCheckoutSessionSchema>,
  ): Promise<{
    checkoutToken: string | null;
    status: 'complete' | 'expired' | 'open';
    isSessionOpen: boolean;
    customer: {
      email: string | null;
    };
  }> {
    return retrieveCheckoutSessionImpl(this.stripe, params);
  }
}
