import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import type Stripe from 'stripe';
import { z } from 'zod';

import { getLogger } from '@kit/shared/logger';

import { CreateProductParamsSchema } from '../../../schema/create-product-params.schema';
import { ProductResponseSchema } from '../../../schema/product-response.schema';

/**
 * Creates a product in a Stripe account
 *
 * @param stripe - Stripe SDK instance
 * @param client - Supabase client
 * @param params.name - The name of the product
 * @param params.description - The description of the product
 * @param params.metadata - The metadata of the product
 * @param params.communityId - The ID of the community to create the product for
 * @param params.seller - The seller of the product
 * @param params.trialDays - Number of trial days for the product (for recurring subscriptions)
 * @returns The created product data
 */
export async function createProductImpl(
  stripe: Stripe,
  client: SupabaseClient,
  params: z.infer<typeof CreateProductParamsSchema>,
): Promise<z.infer<typeof ProductResponseSchema>> {
  const logger = await getLogger();
  let stripeProduct: Stripe.Product | undefined;

  const ctx = {
    name: 'createProduct',
    productName: params.name,
    communityId: params.communityId,
    metadata: params.metadata,
    trialDays: params.trialDays,
  };

  logger.info(ctx, 'Creating Stripe product');

  try {
    const { data: stripeProductResponse } = await stripe.products.create({
      name: params.name,
      description: params.description,
      metadata: params.metadata,
      active: true,
    });

    // Insert into our database and return the generated ID
    const { data: platformProduct, error: productInsertError } = await client
      .from('products')
      .insert({
        id: stripeProductResponse.id,
        name: stripeProductResponse.name,
        description: stripeProductResponse.description,
        community_id: params.communityId,
        seller: params.seller,
        trial_days: params.trialDays ?? 0,
      })
      .select('id')
      .single();

    if (productInsertError || !platformProduct) {
      // Cleanup the Stripe product if DB insert fails
      await stripe.products.del(stripeProductResponse.id);
      logger.error(
        'Error creating Stripe product, cleaned up Stripe resource',
        {
          error: productInsertError,
          stripeProductId: stripeProductResponse.id,
        },
      );
      throw productInsertError;
    }

    return {
      id: platformProduct.id,
      name: stripeProductResponse.name,
      description: stripeProductResponse.description,
      active: stripeProductResponse.active,
      trialDays: params.trialDays || 0,
    };
  } catch (error) {
    // If Stripe product was created but something else failed, clean it up
    if (stripeProduct?.id) {
      try {
        await stripe.products.del(stripeProduct.id);
      } catch (cleanupError) {
        logger.error('Failed to cleanup Stripe product after error', {
          error: cleanupError,
          stripeProductId: stripeProduct.id,
        });
      }
    }
    logger.error('Error creating Stripe product', {
      error,
      name: params.name,
    });
    throw error;
  }
}
