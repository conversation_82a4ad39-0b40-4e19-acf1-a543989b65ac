import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import type Strip<PERSON> from 'stripe';
import { z } from 'zod';

import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';

import { PriceResponseSchema } from '../../../schema/price-params.schema';
import { ProductResponseSchema } from '../../../schema/product-response.schema';

/**
 * Creates a default product and $0 price for a community
 * @param stripe - Stripe SDK instance
 * @param client - Supabase client
 * @param communityId - The ID of the community
 * @returns The created product and price
 */
export async function createCommunityDefaultProductImpl(
  stripe: Stripe,
  client: SupabaseClient,
  communityId: string,
): Promise<{
  product: z.infer<typeof ProductResponseSchema>;
  price: z.infer<typeof PriceResponseSchema>;
}> {
  const logger = await getLogger();
  const adminClient = getSupabaseServerAdminClient();

  const ctx = {
    name: 'createCommunityDefaultProduct',
    communityId,
  };

  logger.info(ctx, 'Creating community default product');

  try {
    // Check if community already has any products
    const { data: products, error: productsError } = await client
      .from('products')
      .select('*')
      .eq('community_id', communityId)
      .eq('seller', 'community')
      .order('active', { ascending: false });

    if (productsError) {
      logger.error(
        { error: productsError },
        'Error checking existing community products',
      );
      throw productsError;
    }

    if (products.length > 0) {
      const firstProduct = products[0];
      if (!firstProduct) {
        throw new Error('Product not found');
      }

      logger.info(
        { communityId },
        'Community already has products, skipping default product creation',
      );

      const existingProduct: z.infer<typeof ProductResponseSchema> = {
        id: firstProduct.id,
        name: firstProduct.name || 'Unnamed Product',
        description: firstProduct.description || null,
        active: firstProduct.active || false,
        trialDays: null,
      };

      // Get the prices for this product
      const { data: prices, error: pricesError } = await client
        .from('product_prices')
        .select('*')
        .eq('product_id', firstProduct.id);

      if (pricesError) {
        logger.error(
          { error: pricesError },
          'Error fetching prices for existing product',
        );
        throw pricesError;
      }

      if (!prices.length) {
        throw new Error('No prices found for existing product');
      }

      const firstPrice = prices[0];
      if (!firstPrice) {
        throw new Error('Price not found');
      }

      const existingPrice: z.infer<typeof PriceResponseSchema> = {
        id: firstPrice.id,
        active: firstPrice.active || false,
        currency: firstPrice.currency || 'usd',
        nickname: firstPrice.nickname || undefined,
        unitAmount:
          firstPrice.unit_amount === null ? undefined : firstPrice.unit_amount,
      };

      return {
        product: existingProduct,
        price: existingPrice,
      };
    }

    // 1. Create the default product in Stripe
    const stripeProduct = await stripe.products.create({
      name: 'communities:defaultProductName',
      description: 'communities:defaultProductDescription',
      metadata: {
        community_id: communityId,
      },
      active: true,
    });

    // 2. Create the product record in our database
    const { data: dbProduct, error: productInsertError } = await client
      .from('products')
      .insert({
        id: stripeProduct.id,
        name: stripeProduct.name,
        description: stripeProduct.description,
        community_id: communityId,
        seller: 'community',
        active: true,
      })
      .select('id')
      .single();

    if (productInsertError || !dbProduct) {
      // Cleanup the Stripe product if DB insert fails
      await stripe.products.del(stripeProduct.id);
      logger.error(
        'Error creating community default product, cleaned up Stripe resource',
        {
          error: productInsertError,
          stripeProductId: stripeProduct.id,
        },
      );
      throw productInsertError || new Error('Failed to insert product');
    }

    // 3. Create a $0 price for the product in Stripe
    const stripePrice = await stripe.prices.create({
      product: dbProduct.id,
      nickname: 'Free Plan',
      unit_amount: 0,
      currency: 'usd',
      active: true,
      recurring: {
        interval: 'month',
      },
    });

    // 4. Create the price record in our database
    const { data: dbPrice, error: priceInsertError } = await client
      .from('product_prices')
      .insert({
        id: stripePrice.id,
        product_id: dbProduct.id,
        nickname: 'Free Plan',
        unit_amount: 0,
        currency: 'usd',
        type: 'recurring',
        active: true,
      })
      .select('id')
      .single();

    if (priceInsertError || !dbPrice) {
      // Cleanup the Stripe price and product if DB insert fails
      await stripe.prices.update(stripePrice.id, { active: false });
      await stripe.products.update(stripeProduct.id, { active: false });
      logger.error(
        'Error creating price for community default product, cleaned up Stripe resources',
        {
          error: priceInsertError,
          stripeProductId: stripeProduct.id,
          stripePriceId: stripePrice.id,
        },
      );
      throw priceInsertError || new Error('Failed to insert price');
    }

    // 5. Update the community's default_product_id
    const { error: updateError } = await adminClient
      .from('communities')
      .update({ default_product_id: dbProduct.id })
      .eq('id', communityId);

    if (updateError) {
      logger.error(
        { error: updateError, communityId },
        'Error updating community default_product_id',
      );
      throw updateError;
    }

    const product: z.infer<typeof ProductResponseSchema> = {
      id: dbProduct.id,
      name: stripeProduct.name,
      description: stripeProduct.description,
      active: stripeProduct.active,
      trialDays: null,
    };

    const price: z.infer<typeof PriceResponseSchema> = {
      id: dbPrice.id,
      nickname: stripePrice.nickname || undefined,
      unitAmount:
        stripePrice.unit_amount === null ? undefined : stripePrice.unit_amount,
      currency: stripePrice.currency,
      active: stripePrice.active,
    };

    return { product, price };
  } catch (error) {
    logger.error(
      { error, communityId },
      'Error creating community default product',
    );
    throw error;
  }
}
