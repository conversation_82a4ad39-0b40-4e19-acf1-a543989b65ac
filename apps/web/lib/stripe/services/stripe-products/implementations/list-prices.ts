import 'server-only';

import type <PERSON>e from 'stripe';

import { getLogger } from '@kit/shared/logger';

/**
 * List prices from Stripe
 * @param stripe - Stripe SDK instance
 * @param params.limit - The maximum number of prices to return
 * @param params.product - The ID of the product to list prices for
 * @param params.active - Whether to filter by active prices
 * @param params.currency - The currency to filter by
 * @param params.type - The type of prices to filter by
 * @param params.startingAfter - The starting price to filter by
 * @param params.endingBefore - The ending price to filter by
 * @returns An array of Stripe prices
 */
export async function listPricesImpl(
  stripe: Stripe,
  {
    limit = 100,
    product,
    active,
    currency,
    type,
    startingAfter,
    endingBefore,
  }: {
    limit?: number;
    product?: string;
    active?: boolean;
    currency?: string;
    type?: 'one_time' | 'recurring';
    startingAfter?: string;
    endingBefore?: string;
  } = {},
) {
  const logger = await getLogger();

  const ctx = {
    name: 'listPrices',
    limit,
    product,
    active,
    currency,
    type,
    startingAfter,
    endingBefore,
  };

  logger.info(ctx, 'Listing prices from Stripe');

  try {
    const params: Stripe.PriceListParams = {
      limit,
      expand: ['data.product'],
    };

    if (product) {
      params.product = product;
    }

    if (active !== undefined) {
      params.active = active;
    }

    if (currency) {
      params.currency = currency;
    }

    if (type) {
      params.type = type;
    }

    if (startingAfter) {
      params.starting_after = startingAfter;
    }

    if (endingBefore) {
      params.ending_before = endingBefore;
    }

    const result = await stripe.prices.list(params);

    logger.info(ctx, 'Prices listed from Stripe', {
      data: result.data,
    });

    return result.data;
  } catch (error) {
    logger.error({ ...ctx, error }, 'Error listing prices from Stripe');
    throw new Error('Failed to list prices from Stripe');
  }
}
