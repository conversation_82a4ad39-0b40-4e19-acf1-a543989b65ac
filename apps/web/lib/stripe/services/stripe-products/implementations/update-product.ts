import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import type Strip<PERSON> from 'stripe';
import { z } from 'zod';

import { getLogger } from '@kit/shared/logger';

import { ProductResponseSchema } from '../../../schema/product-response.schema';
import { UpdateProductParamsSchema } from '../../../schema/update-product-params.schema';

/**
 * Updates a product in a Stripe account
 *
 * @param stripe - Stripe SDK instance
 * @param client - Supabase client
 * @param params.productId - The ID of the product to update
 * @param params.name - The name of the product
 * @param params.description - The description of the product
 * @param params.active - Whether the product is active
 * @param params.trialDays - The number of trial days for the product
 * @param params.metadata - The metadata of the product
 * @param params.images - The images of the product
 * @returns The updated product data
 */
export async function updateProductImpl(
  stripe: Stripe,
  client: SupabaseClient,
  params: z.infer<typeof UpdateProductParamsSchema>,
): Promise<z.infer<typeof ProductResponseSchema>> {
  const logger = await getLogger();
  const ctx = {
    name: 'updateProduct',
    productId: params.productId,
  };
  logger.info(ctx, 'Updating Stripe product');

  try {
    // Update product in Stripe using the Stripe product ID
    const product = await stripe.products.update(params.productId, {
      name: params.name,
      description: params.description,
      metadata: params.metadata,
      active: params.active,
      ...(params.images && { images: params.images }),
    });

    // Update the product in our database
    const { error: updateError } = await client
      .from('products')
      .update({
        name: params.name,
        description: params.description,
        active: params.active,
        trial_days: params.trialDays,
      })
      .eq('id', params.productId);

    if (updateError) {
      logger.error('Error updating platform product', {
        error: updateError,
        productId: params.productId,
      });
      throw updateError;
    }

    return {
      id: params.productId,
      name: product.name,
      description: product.description,
      active: product.active,
      trialDays: params.trialDays ?? null,
    };
  } catch (error) {
    logger.error('Error updating Stripe product', {
      error,
      id: params.productId,
    });

    throw error;
  }
}
