import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import type Stripe from 'stripe';
import { z } from 'zod';

import { getLogger } from '@kit/shared/logger';

import {
  TogglePriceActiveParamsSchema,
  TogglePriceActiveResponseSchema,
} from '../../../schema/price-params.schema';

/**
 * Toggle a price's active status
 * @param stripe - Stripe SDK instance
 * @param client - Supabase client
 * @param params.priceId - The ID of the price to toggle active
 * @param params.active - Whether to toggle the price active
 * @returns The toggled price
 */
export async function togglePriceActiveImpl(
  stripe: Stripe,
  client: SupabaseClient,
  params: z.infer<typeof TogglePriceActiveParamsSchema>,
): Promise<z.infer<typeof TogglePriceActiveResponseSchema>> {
  const logger = await getLogger();

  const ctx = {
    name: 'togglePriceActive',
    priceId: params.priceId,
    active: params.active,
  };

  logger.info(ctx, 'Toggling Stripe price active status');

  try {
    // Update active status in Stripe
    const price = await stripe.prices.update(params.priceId, {
      active: params.active,
    });

    // Update active status in our database
    const { error: priceUpdateError } = await client
      .from('product_prices')
      .update({ active: params.active })
      .eq('id', params.priceId);

    if (priceUpdateError) {
      logger.error('Error toggling platform price active status', {
        error: priceUpdateError,
        priceId: price.id,
      });
      throw priceUpdateError;
    }

    return {
      id: price.id,
      active: price.active,
    };
  } catch (error) {
    logger.error('Error toggling Stripe price active status', {
      error,
      priceId: params.priceId,
    });
    throw error;
  }
}
