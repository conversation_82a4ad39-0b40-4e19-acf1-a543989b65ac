import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import type <PERSON><PERSON> from 'stripe';

import { getLogger } from '@kit/shared/logger';

import { listProductsImpl } from './list-products';
import { syncPricesFromStripeImpl } from './sync-prices-from-stripe';
import { upsertProductImpl } from './upsert-product';

/**
 * Sync products from Stripe to the database
 * @param stripe - Stripe SDK instance
 * @param client - Supabase client
 * @param limit - Maximum number of products to fetch
 * @returns Operation result with success status, message and optional errors
 */
export async function syncProductsFromStripeImpl(
  stripe: Stripe,
  client: SupabaseClient,
  limit = 100,
) {
  const logger = await getLogger();

  const ctx = {
    name: 'syncProductsFromStripe',
    limit,
  };

  logger.info(ctx, 'Syncing products from Stripe');

  const errors: {
    message: string;
    details?: { productId?: string; priceId?: string; code?: string };
  }[] = [];

  try {
    logger.info(ctx, 'Syncing products from Stripe...');

    // Fetch products from Stripe
    const products = await listProductsImpl(stripe, { limit });

    if (products.length === 0) {
      const message = 'No products found in Stripe';
      logger.warn(message);
      errors.push({ message });
    }

    // Process each product
    for (const product of products) {
      logger.info({ productId: product.id }, 'Processing product');

      try {
        // Insert or update product in DB using the existing upsertProduct method
        const dbProduct = await upsertProductImpl(client, product);

        // Sync prices for this product
        const priceErrors = await syncPricesFromStripeImpl(
          stripe,
          client,
          product.id,
          dbProduct.id,
          limit,
        );

        if (priceErrors.length > 0) {
          errors.push(...priceErrors);
        }
      } catch (error) {
        const message = `Error processing product ${product.id}: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`;
        logger.error(ctx, message, {
          error,
          productId: product.id,
        });
        errors.push({
          message,
          details: {
            productId: product.id,
          },
        });
      }
    }

    return {
      success: true,
      message: `Successfully synced ${products.length} products from Stripe`,
      count: products.length,
      errors: errors.length > 0 ? errors : undefined,
    };
  } catch (error) {
    logger.error(ctx, 'Unexpected error in syncProductsFromStripe', {
      error,
    });
    return {
      success: false,
      error: 'An unexpected error occurred during sync',
      count: 0,
      errors,
    };
  }
}
