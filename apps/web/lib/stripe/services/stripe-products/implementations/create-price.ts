import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import type Stripe from 'stripe';
import { z } from 'zod';

import { getLogger } from '@kit/shared/logger';

import {
  CreatePriceParamsSchema,
  PriceResponseSchema,
} from '../../../schema/price-params.schema';

/**
 * Create a price for a product
 * @param stripe - Stripe SDK instance
 * @param client - Supabase client
 * @param params.productId - The ID of the product to create the price for
 * @param params.nickname - The nickname of the price
 * @param params.unitAmount - The unit amount of the price
 * @param params.currency - The currency of the price
 * @param params.type - The type of the price
 * @param params.interval - The interval of the price
 * @returns The created price
 */
export async function createPriceImpl(
  stripe: Stripe,
  client: SupabaseClient,
  params: z.infer<typeof CreatePriceParamsSchema>,
): Promise<z.infer<typeof PriceResponseSchema>> {
  const logger = await getLogger();
  let stripePrice: Stripe.Price | undefined;

  const ctx = {
    name: 'createPrice',
    productId: params.productId,
  };

  logger.info('Creating Stripe price', ctx);

  try {
    // Create price in Stripe using the Stripe product ID
    const { data: stripePriceResponse } = await stripe.prices.create({
      product: params.productId,
      nickname: params.nickname,
      unit_amount: params.unitAmount,
      currency: params.currency,
      recurring:
        params.type === 'recurring' &&
        params.interval &&
        params.interval !== 'one_time'
          ? {
              interval: params.interval as Stripe.Price.Recurring.Interval,
            }
          : undefined,
      metadata: params.metadata,
      active: true,
    });

    // Insert price into our database
    const { error: priceInsertError } = await client
      .from('product_prices')
      .insert({
        id: stripePriceResponse.id,
        product_id: params.productId,
        nickname: params.nickname,
        unit_amount: params.unitAmount,
        currency: params.currency,
        type: params.type,
        interval: params.interval,
        active: true,
      });

    if (priceInsertError) {
      // Cleanup the Stripe price if DB insert fails
      await stripe.prices.update(stripePriceResponse.id, { active: false });
      logger.error(
        'Error creating platform price, cleaned up Stripe resource',
        {
          error: priceInsertError,
          stripePriceId: stripePriceResponse.id,
        },
      );
      throw priceInsertError;
    }

    return {
      id: stripePriceResponse.id,
      nickname: stripePriceResponse.nickname || undefined,
      unitAmount: stripePriceResponse.unit_amount || undefined,
      currency: stripePriceResponse.currency,
      active: stripePriceResponse.active,
    };
  } catch (error) {
    // If Stripe price was created but something else failed, clean it up
    if (stripePrice?.id) {
      try {
        await stripe.prices.update(stripePrice.id, { active: false });
      } catch (cleanupError) {
        logger.error('Failed to cleanup Stripe price after error', {
          error: cleanupError,
          stripePriceId: stripePrice.id,
        });
      }
    }
    logger.error('Error creating Stripe price', {
      error,
      productId: params.productId,
    });
    throw error;
  }
}
