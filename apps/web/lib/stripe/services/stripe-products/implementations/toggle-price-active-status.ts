import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import type Stripe from 'stripe';

import { getLogger } from '@kit/shared/logger';

/**
 * Toggle a price's active status in Stripe and database
 * @param stripe - Stripe SDK instance
 * @param client - Supabase client
 * @param priceId - The internal price ID
 * @returns The operation result
 */
export async function togglePriceActiveStatusImpl(
  stripe: Stripe,
  client: SupabaseClient,
  priceId: string,
) {
  const logger = await getLogger();

  const ctx = {
    name: 'togglePriceActiveStatus',
    id: priceId,
  };

  logger.info(ctx, 'Toggling price active status');

  try {
    // First, get the price to retrieve its Stripe ID and current active status
    const { data: price, error: fetchError } = await client
      .from('product_prices')
      .select('id, active')
      .eq('id', priceId)
      .single();

    if (fetchError) {
      logger.error(ctx, 'Error fetching price', {
        error: fetchError,
        id: priceId,
      });
      return { success: false, error: 'Price not found' };
    }

    // Toggle the active status
    const active = !price.active;

    // Update price in Stripe
    try {
      logger.info(ctx, 'Updating price active status in Stripe', {
        priceId: price.id,
        active,
      });
      await stripe.prices.update(price.id, { active });
      logger.info(ctx, 'Updated price status in Stripe', {
        id: priceId,
        active,
      });
    } catch (stripeError) {
      logger.error(
        {
          error: stripeError,
          id: priceId,
        },
        'Error updating price in Stripe',
      );
      // Continue with database update even if Stripe fails
      // The sync process can reconcile later
    }

    // Update price active status in database
    const { error } = await client
      .from('product_prices')
      .update({
        active,
        updated_at: new Date().toISOString(),
      })
      .eq('id', priceId);

    if (error) {
      logger.error(ctx, 'Error updating price active status', {
        error,
        id: priceId,
      });
      return { success: false, error: error.message };
    }

    return {
      success: true,
      message: `Price ${active ? 'enabled' : 'disabled'} successfully`,
    };
  } catch (error) {
    logger.error(ctx, 'Unexpected error in togglePriceActiveStatus', {
      error,
    });
    return { success: false, error: 'An unexpected error occurred' };
  }
}
