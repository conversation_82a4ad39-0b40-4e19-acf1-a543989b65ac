import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import { PlatformProduct, ProductPrice } from '../../../types/platform-billing';
import { GetProductsParams } from '../service';

/**
 * Get billing products with prices and line items from the database.
 *
 * @param client - Supabase client
 * @param params.productId - The ID of the product to get
 * @param params.activeOnly - Whether to filter by active products
 * @param params.withPrices - Whether to include prices in the response
 * @param params.seller - The seller of the product
 * @returns Array of platform products with their prices and line items
 */
export async function getProductsImpl(
  client: SupabaseClient,
  params: GetProductsParams = {},
): Promise<(PlatformProduct & { productPrices?: ProductPrice[] })[]> {
  const { productId, activeOnly = true, withPrices = true, seller } = params;

  const logger = await getLogger();

  const ctx = {
    name: 'getProducts',
    productId,
    activeOnly,
    withPrices,
    seller,
  };

  logger.info(ctx, 'Getting products');

  if (!seller) {
    logger.error(ctx, 'Seller is required');
    throw new Error('Seller is required');
  }

  try {
    // 1. Build the query for products
    let query = client
      .from('products')
      .select(
        withPrices ? '*,product_prices!fk_product_prices_product_id(*)' : '*',
      );

    // Platform only products
    query = query.eq('seller', seller as 'community' | 'user' | 'platform');

    // Apply filters
    if (activeOnly) {
      query = query.eq('active', true);
    }

    if (productId) {
      query = query.eq('id', productId);
    }

    // 2. Execute the query
    const { data, error } = await query;

    if (!data) {
      return [];
    }

    const products = objectToCamel(data);

    if (error) {
      logger.error({ error }, 'Error loading platform products');
      return [];
    }

    if (!products || products.length === 0) {
      return [];
    }

    // 3. Load line items for each price if needed
    if (withPrices) {
      const productsWithPrices = await Promise.all(
        (
          products as unknown as (PlatformProduct & {
            productPrices?: ProductPrice[];
          })[]
        ).map(
          async (
            product: PlatformProduct & { productPrices?: ProductPrice[] },
          ) => {
            if (!product.productPrices || product.productPrices.length === 0) {
              return product;
            }

            // Load line items for each price
            const updatedPrices = await Promise.all(
              product.productPrices.map(async (price) => {
                return {
                  ...price,
                };
              }),
            );

            return { ...product, productPrices: updatedPrices };
          },
        ),
      );

      return productsWithPrices;
    }

    return objectToCamel(
      products as unknown as (PlatformProduct & {
        productPrices?: ProductPrice[];
      })[],
    );
  } catch (error) {
    logger.error({ error }, 'Unexpected error loading billing products');
    return [];
  }
}
