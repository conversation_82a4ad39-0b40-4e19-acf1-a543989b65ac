import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import type Strip<PERSON> from 'stripe';

import { getLogger } from '@kit/shared/logger';

/**
 * Toggle a product's active status in Stripe and database
 * @param stripe - Stripe SDK instance
 * @param client - Supabase client
 * @param productId - The internal product ID
 * @returns The operation result
 */
export async function toggleProductActiveStatusImpl(
  stripe: Stripe,
  client: SupabaseClient,
  productId: string,
) {
  const logger = await getLogger();

  const ctx = {
    name: 'toggleProductActiveStatus',
    id: productId,
  };

  logger.info(ctx, 'Toggling product active status');

  try {
    // First, get the product to retrieve its Stripe ID and current active status
    const { data: product, error: fetchError } = await client
      .from('products')
      .select('id, active')
      .eq('id', productId)
      .single();

    if (fetchError) {
      logger.error(
        { error: fetchError, id: productId },
        'Error fetching product',
      );
      return { success: false, error: 'Product not found' };
    }

    // Toggle the active status
    const active = !product.active;

    // Update product in Stripe
    try {
      logger.info(
        { productId: product.id, active },
        'Updating product active status in Stripe',
      );
      await stripe.products.update(product.id, {
        active,
      });
      logger.info(
        {
          id: productId,
          active,
        },
        'Updated product status in Stripe',
      );
    } catch (stripeError) {
      logger.error(
        {
          error: stripeError,
          id: productId,
        },
        'Error updating product in Stripe',
      );
      // Continue with database update even if Stripe fails
      // The sync process can reconcile later
    }

    // Update product active status in database
    const { error } = await client
      .from('products')
      .update({
        active,
        updated_at: new Date().toISOString(),
      })
      .eq('id', productId);

    if (error) {
      logger.error(
        { error, id: productId },
        'Error updating product active status',
      );
      return { success: false, error: error.message };
    }

    return {
      success: true,
      message: `Product ${active ? 'enabled' : 'disabled'} successfully`,
    };
  } catch (error) {
    logger.error({ error }, 'Unexpected error in toggleProductActiveStatus');
    return { success: false, error: 'An unexpected error occurred' };
  }
}
