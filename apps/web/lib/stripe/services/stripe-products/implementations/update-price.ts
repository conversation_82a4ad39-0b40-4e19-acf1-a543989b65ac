import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import type Stripe from 'stripe';
import { z } from 'zod';

import { getLogger } from '@kit/shared/logger';

import {
  PriceResponseSchema,
  UpdatePriceParamsSchema,
} from '../../../schema/price-params.schema';

/**
 * Update a price for a product
 * @param stripe - Stripe SDK instance
 * @param client - Supabase client
 * @param params.priceId - The ID of the price to update
 * @param params.nickname - The nickname of the price
 * @param params.active - Whether the price is active
 * @param params.metadata - The metadata of the price
 * @returns The updated price
 */
export async function updatePriceImpl(
  stripe: Stripe,
  client: SupabaseClient,
  params: z.infer<typeof UpdatePriceParamsSchema>,
): Promise<z.infer<typeof PriceResponseSchema>> {
  const logger = await getLogger();

  const ctx = {
    name: 'updatePrice',
    priceId: params.priceId,
  };

  logger.info(ctx, 'Updating Stripe price');

  try {
    // Update price in Stripe using the Stripe price ID
    const price = await stripe.prices.update(params.priceId, {
      nickname: params.nickname,
      active: params.active,
      metadata: params.metadata,
    });

    // Update price in our database
    const { error: priceUpdateError } = await client
      .from('product_prices')
      .update({
        nickname: params.nickname,
        active: params.active,
      })
      .eq('id', params.priceId);

    if (priceUpdateError) {
      logger.error('Error updating platform price', {
        error: priceUpdateError,
        priceId: price.id,
      });
      throw priceUpdateError;
    }

    return {
      id: price.id,
      nickname: price.nickname || undefined,
      unitAmount: price.unit_amount || undefined,
      currency: price.currency,
      active: price.active,
    };
  } catch (error) {
    logger.error('Error updating Stripe price', {
      error,
      priceId: params.priceId,
    });
    throw error;
  }
}
