import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import type <PERSON><PERSON> from 'stripe';
import { z } from 'zod';

import { getLogger } from '@kit/shared/logger';

import { ProductResponseSchema } from '../../../schema/product-response.schema';
import { toggleProductActiveImpl } from './toggle-product-active';

/**
 * Activates a specific product while deactivating all other products for the community.
 * Also updates the community's default_product_id.
 *
 * @param stripe - Stripe SDK instance
 * @param client - Supabase client
 * @param params.productId - The ID of the product to toggle active
 * @returns The activated product
 */
export async function toggleCurrentProductActiveImpl(
  stripe: Stripe,
  client: SupabaseClient,
  params: {
    productId: string;
  },
): Promise<z.infer<typeof ProductResponseSchema>> {
  const logger = await getLogger();

  const ctx = {
    name: 'toggleCurrentProductActive',
    productId: params.productId,
  };

  logger.info(ctx, 'Starting product activation process');

  try {
    // 1. Get the community ID for this product
    const { data: product, error: productError } = await client
      .from('products')
      .select('community_id')
      .eq('id', params.productId)
      .single();

    if (productError || !product) {
      logger.error({ ...ctx, error: productError }, 'Product not found');
      throw new Error('Product not found');
    }

    // 2. Get all active products for this community
    const { data: activeProducts, error: activeProductsError } = await client
      .from('products')
      .select('id')
      .eq('community_id', product.community_id)
      .eq('active', true)
      .neq('id', params.productId); // Exclude the current product

    if (activeProductsError) {
      logger.error(
        { ...ctx, error: activeProductsError },
        'Error fetching active products',
      );
      throw activeProductsError;
    }

    // 3. Deactivate all other active products
    for (const activeProduct of activeProducts || []) {
      logger.info(
        { ...ctx, deactivatingProductId: activeProduct.id },
        'Deactivating other product',
      );
      await toggleProductActiveImpl(stripe, client, {
        productId: activeProduct.id,
        active: false,
      });
    }

    // 4. Activate the current product
    logger.info(ctx, 'Activating current product');
    const activatedProduct = await toggleProductActiveImpl(stripe, client, {
      productId: params.productId,
      active: true,
    });

    // 5. Update the community's default_product_id
    logger.info(
      { ...ctx, communityId: product.community_id },
      'Updating community default product',
    );
    const { error: updateError } = await client
      .from('communities')
      .update({ default_product_id: params.productId })
      .eq('id', product.community_id);

    if (updateError) {
      logger.error(
        { ...ctx, error: updateError },
        'Error updating community default product',
      );
      throw updateError;
    }

    return activatedProduct;
  } catch (error) {
    logger.error({ ...ctx, error }, 'Error in product activation process');
    throw error;
  }
}
