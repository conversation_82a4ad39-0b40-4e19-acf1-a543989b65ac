import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import type <PERSON><PERSON> from 'stripe';
import { z } from 'zod';

import { getLogger } from '@kit/shared/logger';

import { ProductResponseSchema } from '../../../schema/product-response.schema';

/**
 * Toggle a product's active status in both Stripe and our database
 * @param stripe - Stripe SDK instance
 * @param client - Supabase client
 * @param params.productId - The ID of the product to toggle active
 * @param params.active - Whether to toggle the product active
 * @returns The toggled product
 */
export async function toggleProductActiveImpl(
  stripe: Stripe,
  client: SupabaseClient,
  params: {
    productId: string;
    active: boolean;
  },
): Promise<z.infer<typeof ProductResponseSchema>> {
  const logger = await getLogger();
  const ctx = {
    name: 'toggleProductActive',
    productId: params.productId,
    active: params.active,
  };
  logger.info(ctx, 'Toggling product active status');

  try {
    const product = await stripe.products.update(params.productId, {
      active: params.active,
    });

    // Update the product in our database
    const { error: updateError } = await client
      .from('products')
      .update({ active: params.active })
      .eq('id', params.productId);

    if (updateError) {
      logger.error('Error updating platform product', {
        error: updateError,
        productId: params.productId,
      });
      throw updateError;
    }

    return {
      id: params.productId,
      name: product.name,
      description: product.description,
      active: product.active,
    };
  } catch (error) {
    logger.error('Error toggling product active status', {
      error,
      productId: params.productId,
    });
    throw error;
  }
}
