import 'server-only';

import type Stripe from 'stripe';

import { getLogger } from '@kit/shared/logger';

/**
 * Get a single price from Stripe
 * @param stripe - Stripe SDK instance
 * @param priceId - The Stripe price ID
 * @returns A Stripe price
 */
export async function getPriceImpl(stripe: Stripe, priceId: string) {
  const logger = await getLogger();
  const ctx = {
    name: 'getPrice',
    priceId,
  };

  logger.info(ctx, 'Getting price from Stripe');

  try {
    const price = await stripe.prices.retrieve(priceId, {
      expand: ['product'],
    });

    logger.info(ctx, 'Price retrieved from Stripe', {
      price,
    });

    return price;
  } catch (error) {
    logger.error({ ...ctx, error }, 'Error getting price from Stripe');
    throw new Error(`Failed to get price from Stripe: ${priceId}`);
  }
}
