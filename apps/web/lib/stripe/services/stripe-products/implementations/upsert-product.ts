import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import type Strip<PERSON> from 'stripe';

import { getLogger } from '@kit/shared/logger';

/**
 * Upsert a Stripe product into the database
 * Returns the internal product ID
 * @param client - Supabase client
 * @param product - The Stripe product to upsert
 * @returns The internal product ID
 */
export async function upsertProductImpl(
  client: SupabaseClient,
  product: Stripe.Product,
): Promise<{ id: string }> {
  const logger = await getLogger();
  const ctx = {
    name: 'upsertProduct',
    productId: product.id,
  };
  logger.info(ctx, 'Upserting Stripe product');

  const communityId = product.metadata?.community_id;

  let seller = 'platform';

  if (communityId) {
    seller = 'community';
  }

  try {
    const { data: dbProduct, error: productError } = await client
      .from('products')
      .upsert(
        {
          id: product.id,
          name: product.name,
          description: product.description,
          active: product.active,
          community_id: communityId,
          seller,
        },
        {
          onConflict: 'id',
          ignoreDuplicates: false,
        },
      )
      .select('id')
      .single();

    if (productError) {
      logger.error(
        { error: productError, productId: product.id },
        'Error upserting product',
      );
      throw new Error(`Failed to upsert product: ${productError.message}`);
    }

    return dbProduct;
  } catch (error) {
    logger.error({ error, productId: product.id }, 'Error in upsertProduct');
    throw error;
  }
}
