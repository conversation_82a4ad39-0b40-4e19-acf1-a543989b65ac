import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import type Stripe from 'stripe';
import { z } from 'zod';

import { getLogger } from '@kit/shared/logger';

import {
  DeletePriceParamsSchema,
  DeletePriceResponseSchema,
} from '../../../schema/price-params.schema';

/**
 * Delete a price for a product
 * @param stripe - Stripe SDK instance
 * @param client - Supabase client
 * @param params.priceId - The ID of the price to delete
 * @returns The deleted price
 */
export async function deletePriceImpl(
  stripe: Stripe,
  client: SupabaseClient,
  params: z.infer<typeof DeletePriceParamsSchema>,
): Promise<z.infer<typeof DeletePriceResponseSchema>> {
  const logger = await getLogger();

  const ctx = {
    name: 'deletePrice',
    priceId: params.priceId,
  };

  logger.info(ctx, 'Deleting Stripe price');

  try {
    // Delete price in Stripe (actually deactivates it) using the Stripe price ID
    const price = await stripe.prices.update(params.priceId, {
      active: false,
    });

    // Delete price from our database
    const { error: priceDeleteError } = await client
      .from('product_prices')
      .delete()
      .eq('id', params.priceId);

    if (priceDeleteError) {
      logger.error('Error deleting platform price', {
        error: priceDeleteError,
        priceId: price.id,
      });
      throw priceDeleteError;
    }

    return {
      id: price.id,
    };
  } catch (error) {
    logger.error('Error deleting Stripe price', {
      error,
      priceId: params.priceId,
    });
    throw error;
  }
}
