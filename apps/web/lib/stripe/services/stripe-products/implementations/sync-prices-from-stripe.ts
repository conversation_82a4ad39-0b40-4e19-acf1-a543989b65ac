import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import type Strip<PERSON> from 'stripe';

import { getLogger } from '@kit/shared/logger';

import { listPricesImpl } from './list-prices';
import { upsertPriceImpl } from './upsert-price';

/**
 * Sync prices for a product from Stripe to the database
 * @param stripe - Stripe SDK instance
 * @param client - Supabase client
 * @param stripeProductId - The Stripe product ID
 * @param dbProductId - The internal database product ID
 * @param limit - Maximum number of prices to fetch
 * @returns Array of errors if any occurred
 */
export async function syncPricesFromStripeImpl(
  stripe: Stripe,
  client: SupabaseClient,
  stripeProductId: string,
  dbProductId: string,
  limit: number,
) {
  const logger = await getLogger();
  const ctx = {
    name: 'syncPricesFromStripe',
    stripeProductId,
    dbProductId,
    limit,
  };

  logger.info(ctx, 'Syncing prices from <PERSON><PERSON>');

  const errors: {
    message: string;
    details?: { productId?: string; priceId?: string; code?: string };
  }[] = [];

  try {
    // Fetch prices for this product
    const prices = await listPricesImpl(stripe, {
      product: stripeProductId,
      limit,
    });

    logger.info(ctx, 'Prices listed from Stripe', {
      productId: stripeProductId,
      pricesCount: prices.length,
    });

    for (const price of prices) {
      logger.info(
        { priceId: price.id, productId: stripeProductId },
        'Processing price',
      );

      try {
        // Insert or update price in DB using the existing upsertPrice method
        await upsertPriceImpl(client, price, dbProductId);

        logger.info(ctx, 'Price upserted in DB', {
          priceId: price.id,
          productId: stripeProductId,
        });
      } catch (error) {
        const message = `Error upserting price ${price.id}: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`;
        logger.error(
          { error, priceId: price.id, productId: stripeProductId },
          message,
        );
        errors.push({
          message,
          details: {
            productId: stripeProductId,
            priceId: price.id,
          },
        });
      }
    }

    return errors;
  } catch (error) {
    const message = `Error syncing prices for product ${stripeProductId}: ${
      error instanceof Error ? error.message : 'Unknown error'
    }`;
    logger.error({ error, productId: stripeProductId }, message);
    errors.push({
      message,
      details: {
        productId: stripeProductId,
      },
    });
    return errors;
  }
}
