import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import type Stripe from 'stripe';

import { getLogger } from '@kit/shared/logger';

/**
 * Upsert a Stripe price into the database
 * @param client - Supabase client
 * @param price - The Stripe price to upsert
 * @param productId - The internal product ID
 * @returns The internal price ID
 */
export async function upsertPriceImpl(
  client: SupabaseClient,
  price: Stripe.Price,
  productId: string,
): Promise<{ id: string }> {
  const logger = await getLogger();
  const ctx = {
    name: 'upsertPrice',
    priceId: price.id,
    productId,
  };
  logger.info(ctx, 'Upserting Stripe price');

  try {
    const { data: dbPrice, error: priceError } = await client
      .from('product_prices')
      .upsert(
        {
          id: price.id,
          nickname: price.nickname,
          product_id: productId,
          unit_amount: price.unit_amount,
          currency: price.currency,
          active: price.active,
          type: price.type,
          interval: price.recurring?.interval,
        },
        {
          onConflict: 'id',
          ignoreDuplicates: false,
        },
      )
      .select('id')
      .single();

    if (priceError) {
      logger.error(
        { error: priceError, priceId: price.id },
        'Error upserting price',
      );
      throw new Error(`Failed to upsert price: ${priceError.message}`);
    }

    return dbPrice;
  } catch (error) {
    logger.error({ error, priceId: price.id }, 'Error in upsertPrice');
    throw error;
  }
}
