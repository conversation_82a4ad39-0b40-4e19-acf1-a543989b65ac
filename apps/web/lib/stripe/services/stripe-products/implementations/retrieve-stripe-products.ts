import 'server-only';

import type <PERSON><PERSON> from 'stripe';
import { z } from 'zod';

import { getLogger } from '@kit/shared/logger';

import { ProductResponseSchema } from '../../../schema/product-response.schema';
import { RetrieveStripeProductsParamsSchema } from '../../../schema/retrieve-products-params.schema';

/**
 * Retrieves a list of products for a community
 *
 * @param stripe - Stripe SDK instance
 * @param params.communityId - The ID of the community to retrieve products for
 * @returns An array of products
 */
export async function retrieveStripeProductsImpl(
  stripe: Stripe,

  params: z.infer<typeof RetrieveStripeProductsParamsSchema>,
): Promise<z.infer<typeof ProductResponseSchema>[]> {
  const logger = await getLogger();
  const ctx = {
    name: 'retrieveStripeProducts',
    communityId: params.communityId,
  };

  logger.info(ctx, 'Retrieving Stripe products');

  try {
    const { data: products } = await stripe.products.search({
      query: `metadata["community_id"]:\"${params.communityId}\"`,
      limit: 100,
    });

    return products.map((product: Stripe.Product) => ({
      id: product.id,
      name: product.name,
      description: product.description,
      active: product.active,
      images: product.images || [],
    }));
  } catch (error) {
    logger.error(ctx, 'Error retrieving Stripe products', {
      error,
      communityId: params.communityId,
    });
    return [];
  }
}
