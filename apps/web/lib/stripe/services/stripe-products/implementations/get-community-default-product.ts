import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type {
  PlatformProduct,
  ProductPrice,
} from '../../../types/platform-billing';

/**
 * Get the default product and *all* its associated prices for a community
 *
 * @param client - Supabase client
 * @param communityId - The ID of the community
 * @returns The default product and an array of its prices
 */
export async function getCommunityDefaultProductImpl(
  client: SupabaseClient,
  communityId: string,
): Promise<{
  product: PlatformProduct | null;
  prices: ProductPrice[];
}> {
  const logger = await getLogger();

  const ctx = {
    name: 'getCommunityDefaultProduct',
    communityId,
  };

  try {
    logger.info(ctx, 'Getting community default product id');
    const { data: community, error: communityError } = await client

      .from('communities')
      .select('default_product_id')
      .eq('id', communityId)
      .single();

    if (communityError) {
      logger.error({ error: communityError }, 'Error getting community');
      throw communityError;
    }

    // Throw error if community has no default product id set
    if (!community?.default_product_id) {
      logger.warn(
        { ...ctx },
        'Community does not have a default_product_id set. Returning null product and empty prices.',
      );
      return { product: null, prices: [] };
    }

    // Get the default community product with its prices and line items
    logger.info(ctx, 'Getting community default product');
    // Simplify select - fetch all product columns and nested prices
    const { data: productDataSnake, error: productError } = await client
      .from('products')
      .select(
        `
        *,
        product_prices!fk_product_prices_product_id(
          *
        )
      `,
      )
      .eq('id', community.default_product_id)
      .eq('seller', 'community') // Ensure it's a community product
      .eq('active', true) // Ensure the default product is active
      .single();

    if (productError) {
      logger.error(
        {
          error: productError,
          defaultProductId: community.default_product_id,
        },
        'Error getting community default product',
      );
      return { product: null, prices: [] };
    }

    // Check if the default product was found
    if (!productDataSnake) {
      logger.warn(
        {
          ...ctx,
          defaultProductId: community.default_product_id,
        },
        'Community default product not found or is inactive in database. Returning null product and empty prices.',
      );
      return { product: null, prices: [] };
    }

    // Define the expected structure after camelCase conversion
    type CamelCasedProductWithPrices = PlatformProduct & {
      // Ensure productPrices key matches the output of objectToCamel
      productPrices?: ProductPrice[];
    };

    // Convert snake_case keys from DB to camelCase
    const convertedData = objectToCamel(productDataSnake);

    // Check if the conversion result is a valid object before asserting type
    if (!convertedData || typeof convertedData !== 'object') {
      logger.error(
        { ...ctx, rawData: productDataSnake },
        'Failed to convert product data to camelCase object',
      );
      return { product: null, prices: [] };
    }

    // Explicitly type the constant AFTER verifying it's a valid object
    const productData = convertedData as CamelCasedProductWithPrices;

    // Extract prices - type should now be inferred correctly
    const prices = productData.productPrices || [];

    if (prices.length === 0) {
      logger.warn(
        { ...ctx, productId: productData.id },
        'No prices found for the community default product. Returning product with empty prices.',
      );
      // Return the product but with empty prices if none found
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { productPrices: _prices, ...cleanProduct } = productData;
      return { product: cleanProduct as PlatformProduct, prices: [] };
    }

    // Find the first active price - type of p should be inferred
    const defaultPrice = prices.find((p) => p.active);

    if (!defaultPrice) {
      logger.warn(
        { ...ctx, productId: productData.id },
        'No *active* prices found for the community default product. Returning product with all (inactive) prices.',
      );
      // Return the product but with all prices (even if inactive)
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { productPrices: _prices, ...cleanProduct } = productData;
      return { product: cleanProduct as PlatformProduct, prices: prices };
    }

    // Remove the prices array from product object
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { productPrices: _unusedPrices, ...cleanProduct } = productData;

    return {
      product: cleanProduct as PlatformProduct,
      prices: prices as ProductPrice[],
    };
  } catch (error) {
    logger.error(
      { error, communityId },
      'Error getting community default product',
    );
    return { product: null, prices: [] };
  }
}
