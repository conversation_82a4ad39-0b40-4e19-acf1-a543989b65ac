import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type {
  PlatformProduct,
  ProductPrice,
} from '../../../types/platform-billing';

/**
 * Get all community products and their associated prices
 *
 * @param client - Supabase client
 * @param communityId - The ID of the community
 * @returns Array of community products and their prices
 */
export async function getCommunityProductsAndPricesImpl(
  client: SupabaseClient,
  communityId: string,
): Promise<{
  products: PlatformProduct[];
  prices: Record<string, ProductPrice[]>;
}> {
  const logger = await getLogger();

  const ctx = {
    name: 'getCommunityProductsAndPrices',
    communityId,
  };

  logger.info(ctx, 'Getting community products and prices');

  try {
    // Get all community products with their prices and line items in a single query
    const { data: products, error: productError } = await client
      .from('products')
      .select(
        `
        *,
        product_prices!fk_product_prices_product_id(*)
      `,
      )
      .eq('community_id', communityId)
      .eq('seller', 'community')
      .order('active', { ascending: false })
      .order('name', { ascending: true });

    if (productError) {
      logger.error({ error: productError }, 'Error getting community products');
      throw productError;
    }

    if (!products || products.length === 0) {
      return { products: [], prices: {} };
    }

    // Process the nested data into the expected format
    const pricesMap: Record<string, ProductPrice[]> = {};
    const cleanProducts = products.map((product) => {
      // Extract prices before removing them from product object
      const prices = product.productPrices || [];
      pricesMap[product.id] = prices;

      // Remove the prices array from product object to match PlatformProduct type
      const { ...cleanProduct } = product;
      return cleanProduct;
    });

    return {
      products: objectToCamel(cleanProducts) as PlatformProduct[],
      prices: objectToCamel(pricesMap) as Record<string, ProductPrice[]>,
    };
  } catch (error) {
    logger.error(
      { error, communityId },
      'Error getting community products and prices',
    );
    throw error;
  }
}
