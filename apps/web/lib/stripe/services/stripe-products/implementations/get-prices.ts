import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { ProductPrice } from '../../../types/platform-billing';
import { GetPricesParams } from '../service';

/**
 * Get prices for a specific product
 * @param client - Supabase client
 * @param params.productId - The ID of the product to get prices for
 * @param params.active - Whether to filter by active prices
 * @returns Array of platform prices
 */
export async function getPricesImpl(
  client: SupabaseClient,
  params: GetPricesParams = {},
): Promise<ProductPrice[]> {
  const logger = await getLogger();
  const ctx = {
    name: 'getPrices',
    productId: params.productId,
    active: params.active,
  };

  logger.info(ctx, 'Getting prices');

  try {
    // Build the query for prices
    let query = client.from('product_prices').select<'*', ProductPrice>('*');

    // Apply filters
    if (params.productId) {
      query = query.eq('product_id', params.productId);
    }

    if (params.active !== undefined) {
      query = query.eq('active', params.active);
    }

    // Execute the query
    const { data: prices, error } = await query;

    if (error) {
      logger.error({ error }, 'Error loading platform prices');
      return [];
    }

    return objectToCamel(prices) || [];
  } catch (error) {
    logger.error({ error }, 'Unexpected error loading prices');
    return [];
  }
}
