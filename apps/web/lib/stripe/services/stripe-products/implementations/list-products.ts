import 'server-only';

import type <PERSON><PERSON> from 'stripe';

import { getLogger } from '@kit/shared/logger';

/**
 * List products from Stripe
 * @param stripe - Stripe SDK instance
 * @param params.limit - The maximum number of products to return
 * @param params.active - Whether to filter by active products
 * @param params.startingAfter - The starting product to filter by
 * @param params.endingBefore - The ending product to filter by
 * @returns An array of Stripe products
 */
export async function listProductsImpl(
  stripe: Stripe,
  {
    limit = 100,
    active,
    startingAfter,
    endingBefore,
  }: {
    limit?: number;
    active?: boolean;
    startingAfter?: string;
    endingBefore?: string;
  } = {},
) {
  const logger = await getLogger();

  const ctx = {
    name: 'listProducts',
    limit,
    active,
    startingAfter,
    endingBefore,
  };

  logger.info(ctx, 'Listing products from Stripe');

  try {
    const params: Stripe.ProductListParams = {
      limit,
      expand: ['data.default_price'],
    };

    if (active !== undefined) {
      params.active = active;
    }

    if (startingAfter) {
      params.starting_after = startingAfter;
    }

    if (endingBefore) {
      params.ending_before = endingBefore;
    }

    const result = await stripe.products.list(params);

    logger.info(ctx, 'Products listed from Stripe', {
      data: result.data,
    });

    return result.data;
  } catch (error) {
    logger.error({ ...ctx, error }, 'Error listing products from Stripe');
    throw new Error('Failed to list products from Stripe');
  }
}
