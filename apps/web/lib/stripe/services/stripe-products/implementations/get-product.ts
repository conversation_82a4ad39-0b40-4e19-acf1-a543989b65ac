import 'server-only';

import type Strip<PERSON> from 'stripe';

import { getLogger } from '@kit/shared/logger';

/**
 * Get a single product from Stripe
 * @param stripe - Stripe SDK instance
 * @param productId - The Stripe product ID
 * @returns A Stripe product
 */
export async function getProductImpl(stripe: Stripe, productId: string) {
  const logger = await getLogger();

  const ctx = {
    name: 'getProduct',
    productId,
  };

  logger.info(ctx, 'Getting product from Stripe');

  try {
    const product = await stripe.products.retrieve(productId, {
      expand: ['default_price'],
    });

    logger.info(ctx, 'Product retrieved from Stripe', {
      product,
    });

    return product;
  } catch (error) {
    logger.error({ ...ctx, error }, 'Error getting product from Stripe');
    throw new Error(`Failed to get product from Stripe: ${productId}`);
  }
}
