import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import type <PERSON><PERSON> from 'stripe';
import { z } from 'zod';

import { getLogger } from '@kit/shared/logger';

import {
  DeleteProductParamsSchema,
  DeleteProductResponseSchema,
} from '../../../schema/delete-product-params.schema';

/**
 * Deletes a product in a Stripe account
 *
 * @param stripe - Stripe SDK instance
 * @param client - Supabase client
 * @param params.productId - The ID of the product to delete
 * @returns The deleted product data
 */
export async function deleteProductImpl(
  stripe: Stripe,
  client: SupabaseClient,
  params: z.infer<typeof DeleteProductParamsSchema>,
): Promise<z.infer<typeof DeleteProductResponseSchema>> {
  const logger = await getLogger();
  const ctx = {
    name: 'deleteProduct',
    productId: params.productId,
  };

  logger.info(ctx, 'Starting product archival process');

  try {
    logger.info(ctx, 'Querying database for product details');
    // Get the Stripe product ID and associated prices from our database
    const query = client
      .from('products')
      .select(
        `
        id,
        product_prices!fk_product_prices_product_id (
          id,
          product_id
        )
      `,
      )
      .eq('id', params.productId)
      .single();

    logger.info(
      {
        ...ctx,
        query: query.toString(),
      },
      'Executing database query',
    );

    const { data: platformProduct, error: productError } = await query;

    if (productError) {
      logger.error(
        {
          ...ctx,
          error: productError,
          errorMessage: productError.message,
          errorCode: productError.code,
          details: productError.details,
        },
        'Database error when fetching product',
      );
      throw new Error(`Database error: ${productError.message}`);
    }

    if (!platformProduct) {
      logger.error(
        {
          ...ctx,
          query: query.toString(),
        },
        'Product not found in database - query returned no results',
      );
      throw new Error('Product not found in database');
    }

    logger.info(
      {
        ...ctx,
        productDetails: {
          id: platformProduct.id,
          priceCount: platformProduct.product_prices?.length || 0,
        },
      },
      'Found product in database',
    );

    // First archive all associated prices in Stripe
    if (platformProduct.product_prices?.length > 0) {
      logger.info(
        {
          ...ctx,
          priceCount: platformProduct.product_prices.length,
        },
        'Starting price archival process',
      );

      // Archive all prices in Stripe
      for (const price of platformProduct.product_prices) {
        try {
          logger.info(
            {
              ...ctx,
              priceId: price.id,
            },
            'Archiving price in Stripe',
          );

          await stripe.prices.update(price.id, {
            active: false,
          });

          // Update price status in our database
          await client
            .from('product_prices')
            .update({ active: false })
            .eq('id', price.id);

          logger.info(
            {
              ...ctx,
              priceId: price.id,
            },
            'Successfully archived price',
          );
        } catch (error) {
          logger.error(
            {
              ...ctx,
              error,
              priceId: price.id,
              errorMessage:
                error instanceof Error ? error.message : 'Unknown error',
            },
            'Error archiving price',
          );
          throw error;
        }
      }
    }

    // Now archive the product in Stripe
    logger.info(
      {
        ...ctx,
        platformProductId: platformProduct.id,
      },
      'Archiving product in Stripe',
    );

    try {
      await stripe.products.update(platformProduct.id, {
        active: false,
      });

      // Update product status in our database
      await client
        .from('products')
        .update({ active: false })
        .eq('id', platformProduct.id);

      logger.info(
        {
          ...ctx,
          platformProductId: platformProduct.id,
        },
        'Successfully archived product',
      );
    } catch (error) {
      logger.error(
        {
          ...ctx,
          error,
          platformProductId: platformProduct.id,
          errorMessage:
            error instanceof Error ? error.message : 'Unknown error',
        },
        'Error archiving product',
      );
      throw error;
    }

    logger.info(ctx, 'Successfully completed product archival process');

    return {
      id: platformProduct.id,
    };
  } catch (error) {
    logger.error(
      {
        ...ctx,
        error,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
      },
      'Error in product archival process',
    );

    throw error;
  }
}
