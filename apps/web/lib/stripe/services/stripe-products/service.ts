import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import type Stripe from 'stripe';
import { z } from 'zod';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { CreateProductParamsSchema } from '../../schema/create-product-params.schema';
import {
  DeleteProductParamsSchema,
  DeleteProductResponseSchema,
} from '../../schema/delete-product-params.schema';
import {
  CreatePriceParamsSchema,
  DeletePriceParamsSchema,
  DeletePriceResponseSchema,
  PriceResponseSchema,
  TogglePriceActiveParamsSchema,
  TogglePriceActiveResponseSchema,
  UpdatePriceParamsSchema,
} from '../../schema/price-params.schema';
import { ProductResponseSchema } from '../../schema/product-response.schema';
import { RetrieveStripeProductsParamsSchema } from '../../schema/retrieve-products-params.schema';
import { UpdateProductParamsSchema } from '../../schema/update-product-params.schema';
import { PlatformProduct, ProductPrice } from '../../types/platform-billing';
import { createStripeSdk } from '../../utils/stripe-sdk';
import { createCommunityDefaultProductImpl } from './implementations/create-community-default-product';
import { createPriceImpl } from './implementations/create-price';
import { createProductImpl } from './implementations/create-product';
import { deletePriceImpl } from './implementations/delete-price';
import { deleteProductImpl } from './implementations/delete-product';
import { getCommunityDefaultProductImpl } from './implementations/get-community-default-product';
import { getCommunityProductsAndPricesImpl } from './implementations/get-community-products-and-prices';
import { getPriceImpl } from './implementations/get-price';
import { getPricesImpl } from './implementations/get-prices';
import { getProductImpl } from './implementations/get-product';
import { getProductsImpl } from './implementations/get-products';
import { listPricesImpl } from './implementations/list-prices';
import { listProductsImpl } from './implementations/list-products';
import { retrieveStripeProductsImpl } from './implementations/retrieve-stripe-products';
import { syncPricesFromStripeImpl } from './implementations/sync-prices-from-stripe';
import { syncProductsFromStripeImpl } from './implementations/sync-products-from-stripe';
import { toggleCurrentProductActiveImpl } from './implementations/toggle-current-product-active';
import { togglePriceActiveImpl } from './implementations/toggle-price-active';
import { togglePriceActiveStatusImpl } from './implementations/toggle-price-active-status';
import { toggleProductActiveImpl } from './implementations/toggle-product-active';
import { toggleProductActiveStatusImpl } from './implementations/toggle-product-active-status';
import { updatePriceImpl } from './implementations/update-price';
import { updateProductImpl } from './implementations/update-product';
import { upsertPriceImpl } from './implementations/upsert-price';
import { upsertProductImpl } from './implementations/upsert-product';

// Extended types for internal use
type ProductWithPrices = PlatformProduct & {
  productPrices?: ProductPrice[];
};

export type GetPricesParams = {
  id?: string;
  active?: boolean;
  productId?: string;
  withProduct?: boolean;
};

export type GetProductsParams = {
  productId?: string;
  activeOnly?: boolean;
  withPrices?: boolean;
  seller?: 'community' | 'user' | 'platform';
};

/**
 * Service for managing Stripe Products and Platform Products
 */
export class StripeProductsService {
  private namespace = 'stripe-products.service';
  private stripe: Stripe;
  private client: SupabaseClient;

  /**
   * Creates a new StripeProductsService instance
   *
   * This allows the service to be created via the factory pattern,
   * consistent with other Stripe services
   *
   * @param stripe - Optional Stripe SDK instance
   * @param client - Optional Supabase client
   */
  constructor(stripe?: Stripe, client?: SupabaseClient) {
    this.stripe = stripe || createStripeSdk();
    this.client = client || getSupabaseServerClient();
  }

  /**
   * Creates a product in a Stripe account
   * @param params.name - The name of the product
   * @param params.description - The description of the product
   * @param params.active - Whether the product is active
   * @param params.metadata - The metadata of the product
   * @param params.images - The images of the product
   * @returns The created product data
   */
  async createProduct(
    params: z.infer<typeof CreateProductParamsSchema>,
  ): Promise<z.infer<typeof ProductResponseSchema>> {
    return createProductImpl(this.stripe, this.client, params);
  }

  /**
   * Updates a product in a Stripe account
   * @param params.productId - The ID of the product to update
   * @param params.name - The name of the product
   * @param params.description - The description of the product
   * @param params.active - Whether the product is active
   * @param params.metadata - The metadata of the product
   * @param params.images - The images of the product
   * @param params.trialDays - The number of trial days for the product
   * @returns The updated product data
   */
  async updateProduct(
    params: z.infer<typeof UpdateProductParamsSchema>,
  ): Promise<z.infer<typeof ProductResponseSchema>> {
    return updateProductImpl(this.stripe, this.client, params);
  }

  /**
   * Deletes a product in a Stripe account
   * @param params.productId - The ID of the product to delete
   * @returns The deleted product data
   */
  async deleteProduct(
    params: z.infer<typeof DeleteProductParamsSchema>,
  ): Promise<z.infer<typeof DeleteProductResponseSchema>> {
    return deleteProductImpl(this.stripe, this.client, params);
  }

  /**
   * Toggle a product's active status in both Stripe and our database
   * @param params.productId - The ID of the product to toggle active
   * @param params.active - Whether the product is active
   * @returns The toggled product
   */
  async toggleProductActive(params: {
    productId: string;
    active: boolean;
  }): Promise<z.infer<typeof ProductResponseSchema>> {
    return toggleProductActiveImpl(this.stripe, this.client, params);
  }

  /**
   * Retrieves a list of products for a community
   * @param params.communityId - The ID of the community
   * @returns An array of products
   */
  async retrieveStripeProducts(
    params: z.infer<typeof RetrieveStripeProductsParamsSchema>,
  ): Promise<z.infer<typeof ProductResponseSchema>[]> {
    return retrieveStripeProductsImpl(this.stripe, params);
  }

  /**
   * Get billing products with prices and line items from the database.
   * @param params.communityId - The ID of the community
   * @returns Array of platform products with their prices and line items
   */
  async getProducts(
    params: GetProductsParams = {},
  ): Promise<ProductWithPrices[]> {
    return getProductsImpl(this.client, params);
  }

  /**
   * Create a price for a product
   * @param params.productId - The ID of the product to create the price for
   * @param params.nickname - The nickname of the price
   * @param params.unitAmount - The unit amount of the price
   * @param params.currency - The currency of the price
   * @param params.type - The type of the price
   * @param params.interval - The interval of the price
   * @returns The created price
   */
  async createPrice(
    params: z.infer<typeof CreatePriceParamsSchema>,
  ): Promise<z.infer<typeof PriceResponseSchema>> {
    return createPriceImpl(this.stripe, this.client, params);
  }

  /**
   * Update a price for a product
   * @param params.priceId - The ID of the price to update
   * @param params.nickname - The nickname of the price
   * @param params.active - Whether the price is active
   * @returns The updated price
   */
  async updatePrice(
    params: z.infer<typeof UpdatePriceParamsSchema>,
  ): Promise<z.infer<typeof PriceResponseSchema>> {
    return updatePriceImpl(this.stripe, this.client, params);
  }

  /**
   * Delete a price for a product
   * @param params.priceId - The ID of the price to delete
   * @returns The deleted price
   */
  async deletePrice(
    params: z.infer<typeof DeletePriceParamsSchema>,
  ): Promise<z.infer<typeof DeletePriceResponseSchema>> {
    return deletePriceImpl(this.stripe, this.client, params);
  }

  /**
   * Toggle a price's active status
   * @param params.priceId - The ID of the price to toggle active
   * @returns The toggled price
   */
  async togglePriceActive(
    params: z.infer<typeof TogglePriceActiveParamsSchema>,
  ): Promise<z.infer<typeof TogglePriceActiveResponseSchema>> {
    return togglePriceActiveImpl(this.stripe, this.client, params);
  }

  /**
   * Get all community products and their associated prices
   * @param communityId - The ID of the community
   * @returns Array of community products and their prices
   */
  async getCommunityProductsAndPrices(communityId: string): Promise<{
    products: PlatformProduct[];
    prices: Record<string, ProductPrice[]>;
  }> {
    return getCommunityProductsAndPricesImpl(this.client, communityId);
  }

  /**
   * Get prices for a specific product
   * @param client - Supabase client
   * @param params.productId - The ID of the product
   * @param params.active - Whether the prices are active
   * @returns Array of platform prices
   */
  async getPrices(params: GetPricesParams = {}): Promise<ProductPrice[]> {
    return getPricesImpl(this.client, params);
  }

  /**
   * Creates a default product and $0 price for a community
   * @param communityId - The ID of the community
   * @returns The created product and price
   */
  async createCommunityDefaultProduct(communityId: string): Promise<{
    product: z.infer<typeof ProductResponseSchema>;
    price: z.infer<typeof PriceResponseSchema>;
  }> {
    return createCommunityDefaultProductImpl(
      this.stripe,
      this.client,
      communityId,
    );
  }

  /**
   * Activates a specific product while deactivating all other products for the community.
   * Also updates the community's default_product_id.
   *
   * @param params.productId - The ID of the product to toggle active
   * @returns The activated product
   */
  async toggleCurrentProductActive(params: {
    productId: string;
  }): Promise<z.infer<typeof ProductResponseSchema>> {
    return toggleCurrentProductActiveImpl(this.stripe, this.client, params);
  }

  /**
   * Get the default product and *all* its associated prices for a community
   *
   * @param communityId - The ID of the community
   * @returns The default product and an array of its prices
   */
  async getCommunityDefaultProduct(communityId: string): Promise<{
    product: PlatformProduct | null;
    prices: ProductPrice[];
  }> {
    return getCommunityDefaultProductImpl(this.client, communityId);
  }

  /**
   * List products from Stripe
   * @param params.limit - The maximum number of products to return
   * @param params.active - Whether the products are active
   * @param params.startingAfter - The product to start the list after
   * @param params.endingBefore - The product to end the list before
   * @returns An array of Stripe products
   */
  async listProducts({
    limit = 100,
    active,
    startingAfter,
    endingBefore,
  }: {
    limit?: number;
    active?: boolean;
    startingAfter?: string;
    endingBefore?: string;
  } = {}) {
    return listProductsImpl(this.stripe, {
      limit,
      active,
      startingAfter,
      endingBefore,
    });
  }

  /**
   * Get a single product from Stripe
   * @param productId - The Stripe product ID
   * @returns A Stripe product
   */
  async getProduct(productId: string) {
    return getProductImpl(this.stripe, productId);
  }

  /**
   * Toggle a product's active status in Stripe and database
   * @param productId - The internal product ID
   * @returns The operation result
   */
  async toggleProductActiveStatus(productId: string) {
    return toggleProductActiveStatusImpl(this.stripe, this.client, productId);
  }

  /**
   * Toggle a price's active status in Stripe and database
   * @param priceId - The internal price ID
   * @returns The operation result
   */
  async togglePriceActiveStatus(priceId: string) {
    return togglePriceActiveStatusImpl(this.stripe, this.client, priceId);
  }

  /**
   * List prices from Stripe
   * @param options - Options for listing prices
   * @returns An array of Stripe prices
   */
  async listPrices({
    limit = 100,
    product,
    active,
    currency,
    type,
    startingAfter,
    endingBefore,
  }: {
    limit?: number;
    product?: string;
    active?: boolean;
    currency?: string;
    type?: 'one_time' | 'recurring';
    startingAfter?: string;
    endingBefore?: string;
  } = {}) {
    return listPricesImpl(this.stripe, {
      limit,
      product,
      active,
      currency,
      type,
      startingAfter,
      endingBefore,
    });
  }

  /**
   * Get a single price from Stripe
   * @param priceId - The Stripe price ID
   * @returns A Stripe price
   */
  async getPrice(priceId: string) {
    return getPriceImpl(this.stripe, priceId);
  }

  /**
   * Upsert a Stripe product into the database
   * Returns the internal product ID
   * @param product - The Stripe product to upsert
   * @returns The internal product ID
   */
  async upsertProduct(product: Stripe.Product): Promise<{ id: string }> {
    return upsertProductImpl(this.client, product);
  }

  /**
   * Upsert a Stripe price into the database
   * @param price - The Stripe price to upsert
   * @param productId - The internal product ID
   * @returns The internal price ID
   */
  async upsertPrice(
    price: Stripe.Price,
    productId: string,
  ): Promise<{ id: string }> {
    return upsertPriceImpl(this.client, price, productId);
  }

  /**
   * Sync products from Stripe to the database
   * @param limit - Maximum number of products to fetch
   * @returns Operation result with success status, message and optional errors
   */
  async syncProductsFromStripe(limit = 100) {
    return syncProductsFromStripeImpl(this.stripe, this.client, limit);
  }

  /**
   * Sync prices for a product from Stripe to the database
   * @param stripeProductId - The Stripe product ID
   * @param dbProductId - The internal database product ID
   * @param limit - Maximum number of prices to fetch
   * @returns Array of errors if any occurred
   */
  async syncPricesFromStripe(
    stripeProductId: string,
    dbProductId: string,
    limit: number,
  ) {
    return syncPricesFromStripeImpl(
      this.stripe,
      this.client,
      stripeProductId,
      dbProductId,
      limit,
    );
  }
}
