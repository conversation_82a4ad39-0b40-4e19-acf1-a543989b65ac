import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import type Stripe from 'stripe';

import { createStripeSdk } from '../utils/stripe-sdk';
import { StripeAccountService } from './stripe-account';
import { StripeBillingService } from './stripe-billing';
import { StripeCustomerService } from './stripe-customer';
import { StripeProductsService } from './stripe-products';
import { StripeSubscriptionService } from './stripe-subscription';
import { StripeWebhookService } from './stripe-webhook';

/**
 * Returns an instance of StripeBillingService
 *
 * @param client - The Supabase client
 * @param stripe - Optional Stripe SDK instance
 * @returns StripeBillingService instance
 */
export function getStripeBillingService(
  client: SupabaseClient,
  stripe?: Stripe,
): StripeBillingService {
  const stripeSdk = stripe || createStripeSdk();

  return new StripeBillingService(stripeSdk, client);
}

/**
 * Returns an instance of StripeAccountService
 *
 * @param client - The Supabase client
 * @param stripe - Optional Stripe SDK instance
 * @returns getStripeAccountService instance
 */
export function getStripeAccountService(
  client: SupabaseClient,
  stripe?: Stripe,
): StripeAccountService {
  const stripeSdk = stripe || createStripeSdk('V2');

  return new StripeAccountService(stripeSdk, client);
}

/**
 * Returns an instance of StripeCustomerService
 *
 * @param client - The Supabase client
 * @param stripe - Optional Stripe SDK instance
 * @returns StripeCustomerService instance
 */
export function getStripeCustomerService(
  client: SupabaseClient,
  stripe?: Stripe,
): StripeCustomerService {
  const stripeSdk = stripe || createStripeSdk('V2');
  return new StripeCustomerService(stripeSdk, client);
}

/**
 * Returns an instance of StripeProductsService
 *
 * @param client - The Supabase client
 * @param stripe - Optional Stripe SDK instance
 * @returns StripeProductsService instance
 */
export function getStripeProductsService(
  client: SupabaseClient,
  stripe?: Stripe,
): StripeProductsService {
  const stripeSdk = stripe || createStripeSdk();

  return new StripeProductsService(stripeSdk, client);
}

/**
 * Returns an instance of StripeSubscriptionService
 *
 * @param client - The Supabase client
 * @param stripe - Optional Stripe SDK instance
 * @returns StripeSubscriptionService instance
 */
export function getStripeSubscriptionService(
  client: SupabaseClient,
  stripe?: Stripe,
): StripeSubscriptionService {
  const stripeSdk = stripe || createStripeSdk('V2');
  return new StripeSubscriptionService(stripeSdk, client);
}

/**
 * Returns an instance of StripeWebhookService
 *
 * @param clientProvider - Function that returns a Supabase client
 * @param stripe - Optional Stripe SDK instance
 * @returns StripeWebhookService instance
 */
export function getStripeWebhookHandler(
  clientProvider: () => Promise<SupabaseClient>,
  stripe?: Stripe,
): StripeWebhookService {
  const stripeSdk = stripe || createStripeSdk('V2');
  const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

  if (!webhookSecret) {
    throw new Error('STRIPE_WEBHOOK_SECRET is required for webhook handling');
  }

  return new StripeWebhookService(clientProvider, stripeSdk, webhookSecret);
}
