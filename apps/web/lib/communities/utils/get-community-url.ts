import 'server-only';

import { getLogger } from '@kit/shared/logger';

import appConfig from '~/config/app.config';

/**
 * Gets the community URL for a specific path and slug
 * @param path - The path to the community page
 * @param slug - The community slug
 * @returns The full community URL
 */
export async function getCommunityUrl(
  path: string,
  slug: string,
): Promise<string> {
  const logger = await getLogger();

  logger.info(
    {
      path,
      slug,
    },
    `Getting community URL...`,
  );
  return new URL(path, appConfig.url).toString().replace('[community]', slug);
}
