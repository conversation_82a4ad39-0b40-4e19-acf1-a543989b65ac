import { SupabaseClient } from '@supabase/supabase-js';

import type { JSONContent } from '@kit/ui/dojo/organisms/tiptap-editor';

import type { CreateDraftForumPost } from '~/[community]/forums/_lib/schema/forum-post.schema';
import type { Database } from '~/lib/database.types';

import * as implementations from './implementations';
import { ReactionType } from './reaction.types';

export function createCommunityForumsService(client: SupabaseClient<Database>) {
  return new CommunityForumsService(client);
}

class CommunityForumsService {
  private readonly namespace = 'forums.service';

  constructor(private readonly client: SupabaseClient<Database>) {}

  async getForumCategories(params: { communityId: string }) {
    return implementations.getForumCategories(this.client, params);
  }

  async getPaginatedForumPosts(params: {
    communityId: string;
    page: number;
    limit?: number;
    categoryId?: string;
    status?: 'draft' | 'published';
  }) {
    return implementations.getPaginatedForumPosts(this.client, params);
  }

  async getForumPostComments(params: {
    postId: string;
    cursorTimestamp?: string;
    limit?: number;
  }) {
    return implementations.getForumPostComments(this.client, params);
  }

  async getPostDetails(params: { postId?: string; postSlug?: string }) {
    return implementations.getPostDetails(this.client, params);
  }

  /**
   * Creates a new draft forum post
   */
  async createDraftPost(params: CreateDraftForumPost) {
    return implementations.createDraftPost(this.client, params);
  }

  /**
   * Updates a draft forum post including content, title, and category
   */
  async updateDraftPost(params: {
    postId: string;
    draftTitle?: string;
    draftContent: JSONContent;
    draftCategoryId?: string;
    draftCharacterCount?: number;
    draftWordCount?: number;
  }) {
    return implementations.updateDraftPost(this.client, params);
  }

  /**
   * Publishes a draft post
   */
  async publishDraftPost(params: { postId: string }) {
    return implementations.publishDraftPost(this.client, params);
  }

  /**
   * Discards draft content of a post
   */
  async discardDraftContent(params: { postId: string }) {
    return implementations.discardDraftContent(this.client, params);
  }

  /**
   * Gets all draft posts for a user in an account
   */
  async getDraftPosts(params: { communityId: string; categoryId?: string }) {
    return implementations.getDraftPosts(this.client, params);
  }

  /**
   * Deletes a forum post
   */
  async deleteForumPost(params: { postId: string }) {
    return implementations.deleteForumPost(this.client, params);
  }

  /**
   * Deletes a forum post comment
   */
  async deleteForumPostComment(params: { commentId: string }) {
    return implementations.deleteForumPostComment(this.client, params);
  }

  // Add createForumPost method
  async createForumPost(params: {
    title: string;
    content: JSONContent; // Expect parsed content
    categoryId: string;
  }) {
    return implementations.createForumPost(this.client, params);
  }

  // Add togglePinnedPost method
  async togglePinnedPost(params: { postId: string }) {
    return implementations.togglePinnedPost(this.client, params);
  }

  // Add toggleReaction method
  async toggleReaction(params: {
    postId: string | null;
    commentId: string | null;
    reactionType: ReactionType;
  }) {
    return implementations.toggleReaction(this.client, params);
  }

  // Add createComment method
  async createComment(params: {
    postId: string;
    content: JSONContent;
    parentId?: string;
    characterCount: number;
    wordCount: number;
  }) {
    return implementations.createComment(this.client, params);
  }

  // Add getReactionCounts method
  async getReactionCounts(params: { postId?: string; commentId?: string }) {
    return implementations.getReactionCounts(this.client, params);
  }
}
