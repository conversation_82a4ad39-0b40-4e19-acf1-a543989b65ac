import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'forums';

/**
 * Deletes a forum post
 * @param client - The Supabase client
 * @param params - Post parameters
 * @returns Success status
 */
export async function deleteForumPost(
  client: SupabaseClient<Database>,
  params: { postId: string },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    postId: params.postId,
  };

  logger.info(ctx, 'Deleting forum post in database...');

  const { data: success, error } = await client.rpc(
    'delete_community_forum_post',
    {
      p_post_id: params.postId,
    },
  );

  if (error || !success) {
    logger.error({ ...ctx, error }, 'Failed to delete forum post in database');
    throw new Error('Failed to delete forum post in database');
  }

  logger.info(ctx, 'Successfully deleted forum post in database');
  return true;
}
