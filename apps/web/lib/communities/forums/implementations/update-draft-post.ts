import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import type { JSONContent } from '@kit/ui/dojo/organisms/tiptap-editor';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'forums';

/**
 * Updates a draft forum post
 * @param client - The Supabase client
 * @param params - Draft update parameters
 * @returns Update result
 */
export async function updateDraftPost(
  client: SupabaseClient<Database>,
  params: {
    postId: string;
    draftTitle?: string;
    draftContent: JSONContent;
    draftCategoryId?: string;
    draftCharacterCount?: number;
    draftWordCount?: number;
  },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    postId: params.postId,
  };

  logger.info(ctx, 'Updating draft post...');

  const { data, error } = await client.rpc(
    'update_draft_community_forum_post',
    {
      p_post_id: params.postId,
      p_draft_content: params.draftContent ? params.draftContent : {},
      p_draft_title: params.draftTitle,
      p_draft_category_id: params.draftCategoryId,
      p_draft_character_count: params.draftCharacterCount,
      p_draft_word_count: params.draftWordCount,
    },
  );

  if (error) {
    logger.error(
      {
        ...ctx,
        error,
      },
      'Failed to update draft post',
    );

    throw new Error('Failed to update draft post');
  }

  logger.info(ctx, 'Successfully updated draft post');
  return data;
}
