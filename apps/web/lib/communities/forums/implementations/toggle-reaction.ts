import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import type { Database } from '~/lib/database.types';

import { ReactionType } from '../reaction.types';

const NAMESPACE = 'forums';

/**
 * Toggles a reaction on a post or comment
 * @param client - The Supabase client
 * @param params - Reaction parameters
 * @returns Success status
 */
export async function toggleReaction(
  client: SupabaseClient<Database>,
  params: {
    postId: string | null;
    commentId: string | null;
    reactionType: ReactionType;
  },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    postId: params.postId,
    commentId: params.commentId,
    reactionType: params.reactionType,
  };

  logger.info(ctx, 'Toggling reaction in database...');

  const { data, error } = await client
    .rpc('community_forum_toggle_reaction', {
      p_post_id: params.postId ?? undefined,
      p_comment_id: params.commentId ?? undefined,
      p_reaction_type: params.reactionType,
    })
    .single<boolean>(); // Assuming RPC returns boolean indicating if added/updated

  if (error) {
    logger.error({ ...ctx, error }, 'Failed to toggle reaction in database');
    throw new Error(error.message || 'Failed to toggle reaction');
  }

  logger.info(ctx, 'Successfully toggled reaction in database');
  return data; // Return boolean status
}
