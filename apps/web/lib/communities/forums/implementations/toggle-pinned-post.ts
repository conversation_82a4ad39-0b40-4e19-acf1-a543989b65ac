import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'forums';

/**
 * Toggles a forum post's pinned status
 * @param client - The Supabase client
 * @param params - Post parameters
 * @returns New pinned status
 */
export async function togglePinnedPost(
  client: SupabaseClient<Database>,
  params: { postId: string },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    postId: params.postId,
  };

  logger.info(ctx, 'Toggling pinned post status in database...');

  const { data, error } = await client
    .rpc('toggle_community_forum_post_pinned', {
      p_post_id: params.postId,
    })
    .single<boolean>(); // Assuming the RPC returns a boolean indicating if pinned

  if (error) {
    logger.error(
      { ...ctx, error },
      'Failed to toggle pinned post status in database',
    );
    throw new Error(error.message || 'Failed to toggle pinned post status');
  }

  logger.info(ctx, 'Successfully toggled pinned post status in database');
  return data; // Return boolean status
}
