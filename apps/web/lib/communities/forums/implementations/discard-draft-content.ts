import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'forums';

/**
 * Discards draft content of a post
 * @param client - The Supabase client
 * @param params - Post parameters
 * @returns Success status
 */
export async function discardDraftContent(
  client: SupabaseClient<Database>,
  params: { postId: string },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    postId: params.postId,
  };

  logger.info(ctx, 'Discarding draft content...');

  const { data: success, error } = await client.rpc(
    'discard_community_forum_post_draft',
    {
      p_post_id: params.postId,
    },
  );

  if (error || !success) {
    logger.error({ ...ctx, error }, 'Failed to discard draft content');
    throw new Error('Failed to discard draft content');
  }

  logger.info(ctx, 'Successfully discarded draft content');
  return true;
}
