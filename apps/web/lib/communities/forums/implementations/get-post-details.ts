import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'forums';

/**
 * Retrieves details of a forum post
 * @param client - The Supabase client
 * @param params - Query parameters
 * @returns Forum post details
 */
export async function getPostDetails(
  client: SupabaseClient<Database>,
  params: { postId?: string; postSlug?: string },
) {
  const logger = await getLogger();

  const ctx = {
    namespace: NAMESPACE,
    ...params,
  };

  logger.info(ctx, 'Fetching post details...');

  const { data, error } = await client.rpc('get_community_forum_post_details', {
    p_post_id: params.postId ?? undefined,
    p_post_slug: params.postSlug ?? undefined,
  });

  if (error) {
    logger.error(
      {
        ...ctx,
        error,
      },
      `Failed to fetch post details`,
    );

    throw error;
  }

  logger.info(ctx, 'Successfully fetched post details');

  return objectToCamel(data);
}
