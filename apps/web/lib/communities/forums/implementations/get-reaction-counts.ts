import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import type { Database } from '~/lib/database.types';

import { ReactionType } from '../reaction.types';

const NAMESPACE = 'forums';

/**
 * Gets reaction counts for a post or comment
 * @param client - The Supabase client
 * @param params - Query parameters
 * @returns Reaction counts map
 */
export async function getReactionCounts(
  client: SupabaseClient<Database>,
  params: { postId?: string; commentId?: string },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    postId: params.postId,
    commentId: params.commentId,
  };

  logger.info(ctx, 'Fetching reaction counts from database...');

  const { data, error } = await client
    .rpc('community_forum_get_reaction_counts', {
      p_post_id: params.postId ?? undefined,
      p_comment_id: params.commentId ?? undefined,
    })
    .returns<
      Array<{
        reaction: ReactionType;
        count: number;
        user_reacted: boolean;
      }>
    >();

  if (error) {
    logger.error({ ...ctx, error }, 'Failed to fetch reaction counts');
    throw new Error(error.message || 'Failed to fetch reaction counts');
  }

  logger.info(ctx, 'Successfully fetched reaction counts');

  // Process data into the desired map format
  const map: Record<string, { count: number; user_reacted: boolean }> = {};
  (data ?? []).forEach(({ reaction, count, user_reacted }) => {
    map[reaction] = { count, user_reacted };
  });

  return map;
}
