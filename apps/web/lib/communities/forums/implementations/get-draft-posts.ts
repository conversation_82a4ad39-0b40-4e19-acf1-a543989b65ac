import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'forums';

/**
 * Gets all draft posts for a user in a community
 * @param client - The Supabase client
 * @param params - Query parameters
 * @returns Draft posts
 */
export async function getDraftPosts(
  client: SupabaseClient<Database>,
  params: { communityId: string; categoryId?: string },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    communityId: params.communityId,
  };

  logger.info(ctx, 'Fetching draft posts...');

  const { data, error } = await client
    .from('community_forum_posts')
    .select(
      `
      id,
      title,
      content,
      draft_content,
      category_id,
      status,
      last_edited_at,
      auto_saved_at,
      created_at,
      updated_at,
      created_by_user_id,
      forum_categories (
        name
      )
    `,
    )
    .eq('community_id', params.communityId)
    .eq('status', 'draft');

  if (error) {
    logger.error({ ...ctx, error }, 'Failed to fetch draft posts');
    throw new Error('Failed to fetch draft posts');
  }

  logger.info(ctx, 'Successfully fetched draft posts');
  return objectToCamel(data);
}
