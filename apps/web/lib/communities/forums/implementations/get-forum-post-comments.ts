import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'forums';

/**
 * Retrieves comments for a forum post
 * @param client - The Supabase client
 * @param params - Query parameters
 * @returns Forum post comments
 */
export async function getForumPostComments(
  client: SupabaseClient<Database>,
  params: {
    postId: string;
    cursorTimestamp?: string;
    limit?: number;
  },
) {
  const logger = await getLogger();

  const ctx = {
    namespace: NAMESPACE,
    ...params,
  };

  logger.info(ctx, 'Fetching forum post comments...');

  const { data, error } = await client.rpc(
    'get_community_forum_post_comments',
    {
      p_post_id: params.postId,
      p_cursor_timestamp: params.cursorTimestamp,
      limit_rows: params.limit,
    },
  );

  if (error) {
    logger.error(
      {
        ...ctx,
        error,
      },
      `Failed to fetch forum post comments`,
    );

    throw error;
  }

  logger.info(ctx, 'Successfully fetched forum post comments');

  return objectToCamel(data);
}
