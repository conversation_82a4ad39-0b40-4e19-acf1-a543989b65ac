import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'forums';

/**
 * Retrieves forum categories for a community
 * @param client - The Supabase client
 * @param params - Query parameters
 * @returns Forum categories
 */
export async function getForumCategories(
  client: SupabaseClient<Database>,
  params: { communityId: string },
) {
  const logger = await getLogger();

  const ctx = {
    namespace: NAMESPACE,
    ...params,
  };

  logger.info(ctx, 'Fetching forum categories...');

  const { data, error } = await client.rpc('get_community_forum_categories', {
    p_community_id: params.communityId,
  });

  if (error) {
    logger.error(
      {
        ...ctx,
        error,
      },
      `Failed to fetch forum categories`,
    );

    throw error;
  }

  logger.info(ctx, 'Successfully fetched forum categories');

  return {
    data: objectToCamel(data) ?? [],
  };
}
