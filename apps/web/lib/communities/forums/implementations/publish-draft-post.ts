import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'forums';

/**
 * Publishes a draft forum post
 * @param client - The Supabase client
 * @param params - Draft post parameters
 * @returns Success status
 */
export async function publishDraftPost(
  client: SupabaseClient<Database>,
  params: { postId: string },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    postId: params.postId,
  };

  logger.info(ctx, 'Publishing draft post...');

  const { data: success, error } = await client.rpc(
    'publish_community_forum_post_draft',
    {
      p_post_id: params.postId,
    },
  );

  if (error || !success) {
    logger.error({ ...ctx, error }, 'Failed to publish draft post');
    throw new Error('Failed to publish draft post');
  }

  logger.info(ctx, 'Successfully published draft post');
  return true;
}
