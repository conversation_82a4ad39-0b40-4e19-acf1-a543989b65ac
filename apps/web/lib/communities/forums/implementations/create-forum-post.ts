import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import type { JSONContent } from '@kit/ui/dojo/organisms/tiptap-editor';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'forums';

/**
 * Creates a new forum post
 * @param client - The Supabase client
 * @param params - Post parameters
 * @returns Created post ID
 */
export async function createForumPost(
  client: SupabaseClient<Database>,
  params: {
    title: string;
    content: JSONContent;
    categoryId: string;
  },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    categoryId: params.categoryId,
  };

  logger.info(ctx, 'Creating forum post in database...');

  const { data, error } = await client
    .rpc('create_community_forum_post', {
      p_title: params.title,
      p_content: params.content,
      p_category_id: params.categoryId,
    })
    .single<string>(); // Assuming RPC returns the new post ID

  if (error) {
    logger.error({ ...ctx, error }, 'Failed to create forum post in database');
    throw new Error(error.message || 'Failed to create forum post');
  }

  logger.info(ctx, 'Successfully created forum post in database');
  return data; // Return the new post ID
}
