import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';
import type { JSONContent } from '@kit/ui/dojo/organisms/tiptap-editor';

import type { Database } from '~/lib/database.types';
import { computePagination } from '~/lib/utils/compute-pagination';

const NAMESPACE = 'forums';

/**
 * Retrieves paginated forum posts for a community
 * @param client - The Supabase client
 * @param params - Query parameters
 * @returns Paginated forum posts
 */
export async function getPaginatedForumPosts(
  client: SupabaseClient<Database>,
  params: {
    communityId: string;
    page: number;
    limit?: number;
    categoryId?: string;
    status?: 'draft' | 'published';
  },
) {
  const logger = await getLogger();

  const ctx = {
    namespace: NAMESPACE,
    ...params,
  };

  logger.info(ctx, 'Fetching paginated forum posts...');

  const { resolvedLimit: limit, offset } = computePagination(
    params.page,
    params.limit,
  );

  // Use different RPC function based on status
  logger.info(
    ctx,
    `Fetching paginated forum posts with status: ${params.status} categoryId: ${params.categoryId}`,
  );

  const { data, error } =
    params.status === 'draft'
      ? await client.rpc('get_paginated_community_forum_drafts', {
          p_community_id: params.communityId,
          p_category_id: params.categoryId ? params.categoryId : undefined,
          limit_rows: limit,
          offset_rows: offset,
        })
      : await client.rpc('get_paginated_community_forum_posts', {
          p_community_id: params.communityId,
          p_category_id: params.categoryId ? params.categoryId : undefined,
          p_pinned_limit: 3,
          p_post_status: params.status,
          limit_rows: limit,
          offset_rows: offset,
        });
  if (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(
      {
        ...ctx,
        error: errorMessage,
      },
      `Failed to fetch paginated forum posts`,
    );
    throw new Error('Failed to fetch paginated forum posts');
  }

  logger.info(ctx, `Successfully fetched ${data?.length ?? 0} forum posts`);

  const totalCount = data?.[0]?.total_count ?? 0;

  logger.info(ctx, 'Successfully fetched paginated forum posts');

  // Transform posts to include is_pinned and parse content as JSONContent
  const transformedPosts = (data ?? []).map((post) => ({
    ...post,
    is_pinned:
      (post as { status: 'draft' | 'published'; is_pinned?: boolean })
        .status === 'published'
        ? ((post as { is_pinned?: boolean }).is_pinned ?? false)
        : false,
    content:
      typeof post.content === 'string'
        ? (JSON.parse(post.content) as JSONContent)
        : (post.content as JSONContent),
  }));

  return {
    data: objectToCamel(transformedPosts),
    count: totalCount,
    pageSize: limit,
    page: params.page,
    pageCount: Math.ceil(totalCount / limit),
  };
}
