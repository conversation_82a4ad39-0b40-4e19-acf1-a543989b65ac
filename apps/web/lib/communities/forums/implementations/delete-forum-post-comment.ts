import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'forums';

/**
 * Deletes a forum post comment
 * @param client - The Supabase client
 * @param params - Comment parameters
 * @returns Success status
 */
export async function deleteForumPostComment(
  client: SupabaseClient<Database>,
  params: { commentId: string },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    commentId: params.commentId,
  };

  logger.info(ctx, 'Deleting forum post comment...');

  const { data: success, error } = await client.rpc(
    'delete_community_forum_comment',
    {
      p_comment_id: params.commentId,
    },
  );

  if (error || !success) {
    logger.error({ ...ctx, error }, 'Failed to delete forum post comment');
    throw new Error('Failed to delete forum post comment');
  }

  logger.info(ctx, 'Successfully deleted forum post comment');
  return true;
}
