import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import type { CreateDraftForumPost } from '~/[community]/forums/_lib/schema/forum-post.schema';
import type { Database } from '~/lib/database.types';

const NAMESPACE = 'forums';

/**
 * Creates a new draft forum post
 * @param client - The Supabase client
 * @param params - Draft post parameters
 * @returns Created post ID
 */
export async function createDraftPost(
  client: SupabaseClient<Database>,
  params: CreateDraftForumPost,
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
  };

  logger.info(ctx, 'Creating draft forum post...');

  const { data, error } = await client.rpc(
    'create_draft_community_forum_post',
    {
      p_draft_title: params.draftTitle,
      p_draft_content: params.draftContent,
      p_draft_category_id: params.draftCategoryId,
      p_draft_character_count: params.draftCharacterCount,
      p_draft_word_count: params.draftWordCount,
    },
  );

  if (error) {
    logger.error({ ...ctx, error }, 'Failed to create draft forum post');
    throw new Error('Failed to create draft forum post');
  }

  logger.info(ctx, 'Successfully created draft forum post');
  return { id: data };
}
