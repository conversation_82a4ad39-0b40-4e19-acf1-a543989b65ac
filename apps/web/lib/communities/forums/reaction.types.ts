import type { Database } from '~/lib/database.types';

export const forumReactionValues = [
  { value: '+1' as const, icon: '+1' },
  { value: 'heart' as const, icon: 'heart' },
  { value: 'fire' as const, icon: 'fire' },
  { value: '100' as const, icon: '100' },
  { value: 'pray' as const, icon: 'pray' },
  { value: 'joy' as const, icon: 'joy' },
  { value: 'tada' as const, icon: 'tada' },
  { value: 'cry' as const, icon: 'cry' },
  { value: 'clap' as const, icon: 'clap' },
  { value: '-1' as const, icon: '-1', hideFromList: true },
] as const;

export type ReactionType =
  Database['public']['Enums']['community_forum_reaction_type'];
