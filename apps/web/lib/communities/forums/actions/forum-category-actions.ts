'use server';

import { revalidatePath } from 'next/cache';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { CreateForumCategorySchema } from '~/lib/communities/forums/schema/create-forum-category.schema';
import { DeleteForumCategorySchema } from '~/lib/communities/forums/schema/delete-forum-category.schema';
import { UpdateForumCategorySchema } from '~/lib/communities/forums/schema/update-forum-category.schema';

export const createForumCategoryAction = enhanceAction(
  async (
    params,
  ): Promise<{
    error?: boolean;
    success?: boolean;
    message?: string;
    data?: {
      id: string;
      name: string;
      description: string;
      icon: string;
    };
  }> => {
    const client = getSupabaseServerClient();
    const logger = await getLogger();
    const { communityId, name, description, icon } = params;

    const ctx = {
      name: 'forums_category.create',
      communityName: name,
    };

    logger.info(ctx, `Creating forum category...`);

    const { error, data } = await client
      .rpc('community_create_forum_category', {
        p_community_id: communityId,
        p_name: name,
        p_description: description ?? undefined,
        p_icon: icon ?? undefined,
      })
      .single();

    if (error) {
      logger.error({ ...ctx, error }, `Failed to create forum category`);
      logger.error({ ctx, supabaseError: error }, 'Additional error context');
      return { error: true, message: 'Category already exists' };
    }

    logger.info(ctx, `Forum category created`);
    revalidatePath(`/[community]/forums/settings`, 'page');

    return {
      success: true,
      data: {
        id: data,
        name,
        description: description ?? '',
        icon: icon ?? '',
      },
    };
  },
  {
    schema: CreateForumCategorySchema,
  },
);

export const updateForumCategoryAction = enhanceAction(
  async (
    params,
  ): Promise<{
    error?: boolean;
    success?: boolean;
    data?: {
      id: string;
      name: string;
      description: string;
      icon: string;
    };
  }> => {
    const client = getSupabaseServerClient();
    const logger = await getLogger();
    const { categoryId, name, description, icon } = params;

    // Validate categoryId is a valid UUID
    if (!categoryId || typeof categoryId !== 'string') {
      return { error: true };
    }

    const ctx = {
      name: 'forums_category.update',
      communityName: name,
    };

    logger.info(ctx, `Updating forum category details...`);

    const { error } = await client
      .rpc('community_update_forum_category', {
        p_category_id: categoryId,
        p_new_name: name,
        p_new_description: description ?? '',
        p_new_icon: icon ?? '',
      })
      .single();

    if (error) {
      logger.error(
        { ...ctx, error },
        `Failed to update forum category details`,
      );
      return { error: true };
    }

    logger.info(ctx, `Forum category details updated`);
    revalidatePath(`/[community]/forums/settings`, 'page');

    return {
      success: true,
      data: {
        id: categoryId,
        name,
        description: description ?? '',
        icon: icon ?? '',
      },
    };
  },
  {
    schema: UpdateForumCategorySchema,
  },
);

export const deleteForumCategoryAction = enhanceAction(
  async (params: { categoryId: string }) => {
    const client = getSupabaseServerClient();
    const logger = await getLogger();

    const ctx = {
      name: 'forums_category.delete',
      categoryId: params.categoryId,
    };

    logger.info(ctx, `Deleting forum category...`);

    const { error } = await client
      .rpc('community_delete_forum_category', {
        p_category_id: params.categoryId,
      })
      .single();

    if (error) {
      logger.error({ ...ctx, error }, `Failed to delete forum category`);
      logger.error({ ctx, supabaseError: error }, 'Additional error context');
      return { error: true, message: 'Failed to delete forum category' };
    }

    logger.info(ctx, `Forum category deleted`);

    // Revalidate both the settings page and the forums page
    revalidatePath(`/[community]/forums/settings`, 'page');
    revalidatePath(`/[community]/forums`, 'page');

    return {
      success: true,
    };
  },
  {
    schema: DeleteForumCategorySchema,
    auth: true,
  },
);
