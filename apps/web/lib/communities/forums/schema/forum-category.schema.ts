import { object, string } from 'zod';

export const ForumCategorySchema = object({
  id: string().uuid(),
  name: string().min(3).max(50).trim(),
  description: string().min(10).max(500).trim().nullable(),
  icon: string()
    .regex(/^[a-z-]+$/, 'Icon must contain only lowercase letters and hyphens')
    .nullable(),
  slug: string()
    .min(3)
    .max(50)
    .regex(
      /^[a-z0-9-]+$/,
      'Slug must contain only lowercase letters, numbers, and hyphens',
    ),
});
