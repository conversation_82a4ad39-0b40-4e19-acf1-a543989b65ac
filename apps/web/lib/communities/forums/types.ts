import { ObjectToCamel } from '@kit/shared/utils';
import type { JSONContent } from '@kit/ui/dojo/organisms/tiptap-editor';

import type { Database } from '~/lib/database.types';
import { ISODateString } from '~/lib/global-types';

/**
 * Represents a forum post with its complete metadata and content
 * @property {string} postId - Unique identifier for the post
 * @property {string} title - Title of the post
 * @property {JSONContent} content - Rich text content using TipTap
 * @property {string} slug - URL-friendly identifier
 * @property {ISODateString} createdAt - ISO timestamp of creation
 * @property {ISODateString} updatedAt - ISO timestamp of last update
 * @property {string} createdByUserId - Post author's identifier
 * @property {string} authorFirstName - Post author's first name
 * @property {string} authorLastName - Post author's last name
 * @property {string} authorPictureUrl - URL to the post author's profile picture
 * @property {string} categoryId - Category identifier
 * @property {string} categoryName - Category classification
 * @property {number} commentCount - Number of comments
 * @property {boolean} isPinned - Whether post is pinned
 * @property {number} totalCount - Total engagement count
 * @property {'draft' | 'published'} status - Current post status
 * @property {ISODateString} lastEditedAt - ISO timestamp of last edit
 * @property {ISODateString} autoSavedAt - ISO timestamp of last auto-save
 */

export type ForumPost = {
  id: string;
  title: string;
  content: JSONContent;
  categoryId: string;
  slug: string;
  createdAt: ISODateString;
  updatedAt: ISODateString;
  createdByUserId: string;
  authorFirstName: string;
  authorLastName: string;
  authorPictureUrl: string;
  categoryName: string;
  commentCount: number;
  isPinned: boolean;
  status: ForumPostStatus;
  lastEditedAt?: ISODateString | null;
  autoSavedAt?: ISODateString | null;
  totalCount: number;
};

export type ForumPostStatus =
  Database['public']['Enums']['community_forum_post_status'];

type ForumPostReturn =
  Database['public']['Functions']['get_paginated_community_forum_posts']['Returns'][number];

export type ForumPostPaginated = ObjectToCamel<ForumPostReturn>;

type ForumDraftPostReturn =
  Database['public']['Functions']['get_paginated_community_forum_drafts']['Returns'][number];

export type ForumDraftPostPaginated = ObjectToCamel<ForumDraftPostReturn>;

export type ForumReactionType =
  Database['public']['Enums']['community_forum_reaction_type'];

// Comments
type CommentReturn =
  Database['public']['Functions']['create_community_forum_comment']['Returns'][0];

export type Comment = ObjectToCamel<CommentReturn>;

// Forum Categories
type ForumCategoryReturn =
  Database['public']['Functions']['get_community_forum_categories']['Returns'][number];

export type ForumCategory = ObjectToCamel<ForumCategoryReturn>;
