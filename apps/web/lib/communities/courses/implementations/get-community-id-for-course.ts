import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'courses';

/**
 * Retrieves the community ID for a course
 * @param client - The Supabase client
 * @param params - Query parameters
 * @returns Community ID and slug
 */
export async function getCommunityIdForCourse(
  client: SupabaseClient<Database>,
  params: { courseId: string },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    courseId: params.courseId,
  };

  logger.info(ctx, 'Fetching community ID for course...');

  const { data, error } = await client
    .from('community_courses')
    .select('community:communities(slug), community_id')
    .eq('id', params.courseId)
    .single();

  if (error || !data) {
    logger.error({ ...ctx, error }, 'Failed to fetch community ID for course');
    throw new Error(
      error?.message || 'Failed to fetch community ID for course',
    );
  }

  logger.info(ctx, 'Successfully fetched community ID for course');
  return {
    communityId: data.community_id,
    communitySlug: data.community?.slug,
  };
}
