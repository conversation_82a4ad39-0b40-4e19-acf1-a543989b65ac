import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'courses';

/**
 * Updates a chapter's details
 * @param client - The Supabase client
 * @param params - Chapter update parameters
 * @returns Success status
 */
export async function updateChapter(
  client: SupabaseClient<Database>,
  params: {
    chapterId: string;
    title: string;
    description?: string;
    icon?: string;
  },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    chapterId: params.chapterId,
  };

  logger.info(ctx, 'Updating chapter in database...');

  const { error } = await client
    .rpc('update_community_course_chapter', {
      p_chapter_id: params.chapterId,
      p_title: params.title,
      p_description: params.description ?? '',
      p_icon: params.icon ?? '',
    })
    .single();

  if (error) {
    logger.error({ ...ctx, error }, 'Failed to update chapter in database');
    throw new Error(error?.message || 'Failed to update chapter');
  }

  logger.info(ctx, 'Successfully updated chapter in database');
  return { success: true };
}
