import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

import type { CourseAccessEnum } from '../types';

const NAMESPACE = 'courses';

type CourseRecord = {
  id: string;
  slug: string;
  title: string;
  description: string;
  cover_url: string | null;
  prerequisite_course_ids: string[];
  access: CourseAccessEnum;
};

/**
 * Updates an existing course
 * @param client - The Supabase client
 * @param params - Course update parameters
 * @returns The updated course
 */
export async function updateCourse(
  client: SupabaseClient<Database>,
  params: {
    courseId: string;
    title: string;
    description: string;
    coverUrl?: string;
    prerequisiteCourseIds?: string[];
    access: CourseAccessEnum;
  },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    courseId: params.courseId,
    title: params.title,
  };

  logger.info(ctx, 'Updating course in database...');

  const { error, data } = await client
    .rpc('update_community_course_with_prerequisites', {
      p_course_id: params.courseId,
      p_title: params.title,
      p_description: params.description,
      p_cover_url: params.coverUrl,
      p_prerequisite_ids: params.prerequisiteCourseIds,
      p_access: params.access,
    })
    .single<CourseRecord>();

  if (error || !data) {
    logger.error({ ...ctx, error }, 'Failed to update course in database');
    throw new Error(error?.message || 'Failed to update course');
  }

  logger.info(ctx, 'Successfully updated course in database');
  return objectToCamel(data);
}
