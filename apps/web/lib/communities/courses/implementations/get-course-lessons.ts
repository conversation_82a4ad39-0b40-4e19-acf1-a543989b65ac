import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'courses';

/**
 * Gets the lessons for a specific course
 * @param client - The Supabase client
 * @param params - Query parameters
 * @returns Course lessons
 */
export async function getCourseLessons(
  client: SupabaseClient<Database>,
  params: { courseId?: string },
) {
  const logger = await getLogger();

  const ctx = {
    namespace: NAMESPACE,
    ...params,
  };

  logger.info(ctx, 'Fetching course lessons...');

  if (!params.courseId) {
    throw new Error('Course ID is required');
  }

  const { data, error } = await client.rpc('get_community_course_lessons', {
    p_course_id: params.courseId,
  });

  if (error) {
    logger.error(
      {
        ...ctx,
        error,
      },
      `Failed to fetch course lessons`,
    );

    throw error;
  }

  logger.info(ctx, 'Successfully fetched course lessons');

  return objectToCamel(data);
}
