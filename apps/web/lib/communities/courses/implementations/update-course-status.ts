import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import type { Database } from '~/lib/database.types';

import type { CourseStatusEnum } from '../types';

const NAMESPACE = 'courses';

/**
 * Updates the status of a course
 * @param client - The Supabase client
 * @param params - Course status parameters
 * @returns The new course status
 */
export async function updateCourseStatus(
  client: SupabaseClient<Database>,
  params: {
    courseSlug: string;
    targetStatus: CourseStatusEnum;
  },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    courseSlug: params.courseSlug,
    targetStatus: params.targetStatus,
  };

  logger.info(ctx, 'Updating course status in database...');

  const { data, error } = await client
    .rpc('update_community_course_status', {
      p_course_slug: params.courseSlug,
      p_target_status: params.targetStatus,
    })
    .single<CourseStatusEnum>();

  if (error) {
    logger.error(
      { ...ctx, error },
      'Failed to update course status in database',
    );
    throw new Error(error?.message || 'Failed to update course status');
  }

  logger.info(ctx, 'Successfully updated course status in database');
  return data;
}
