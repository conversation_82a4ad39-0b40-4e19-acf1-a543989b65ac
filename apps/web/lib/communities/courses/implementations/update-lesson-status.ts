import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import type { Database } from '~/lib/database.types';

import type { CourseStatusEnum } from '../types';

const NAMESPACE = 'courses';

/**
 * Updates a lesson's status
 * @param client - The Supabase client
 * @param params - Lesson status parameters
 * @returns Success status
 */
export async function updateLessonStatus(
  client: SupabaseClient<Database>,
  params: {
    lessonId: string;
    targetStatus: CourseStatusEnum;
  },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    lessonId: params.lessonId,
    targetStatus: params.targetStatus,
  };

  logger.info(ctx, 'Updating lesson status in database...');

  const { error } = await client
    .rpc('update_community_course_lesson_status', {
      p_lesson_id: params.lessonId,
      p_target_status: params.targetStatus,
    })
    .single();

  if (error) {
    logger.error(
      { ...ctx, error },
      'Failed to update lesson status in database',
    );
    throw new Error(error?.message || 'Failed to update lesson status');
  }

  logger.info(ctx, 'Successfully updated lesson status in database');
  return { success: true };
}
