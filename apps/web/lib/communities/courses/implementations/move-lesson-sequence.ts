import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'courses';

/**
 * Changes a lesson's sequence order
 * @param client - The Supabase client
 * @param params - Lesson sequence parameters
 * @returns Success status
 */
export async function moveLessonSequence(
  client: SupabaseClient<Database>,
  params: {
    lessonId: string;
    targetSequence: number;
    direction: 'up' | 'down';
  },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    lessonId: params.lessonId,
    targetSequence: params.targetSequence,
    direction: params.direction,
  };

  logger.info(ctx, 'Moving lesson sequence in database...');

  const { error } = await client
    .rpc('move_community_course_lesson_sequence_order', {
      p_lesson_id: params.lessonId,
      p_target_sequence: params.targetSequence,
      p_direction: params.direction,
    })
    .single();

  if (error) {
    logger.error(
      { ...ctx, error },
      'Failed to move lesson sequence in database',
    );
    throw new Error(error?.message || 'Failed to move lesson sequence');
  }

  logger.info(ctx, 'Successfully moved lesson sequence in database');
  return { success: true };
}
