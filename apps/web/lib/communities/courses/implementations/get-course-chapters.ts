import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'courses';

/**
 * Gets the chapters for a specific course
 * @param client - The Supabase client
 * @param params - Query parameters
 * @returns Course chapters
 */
export async function getCourseChapters(
  client: SupabaseClient<Database>,
  params: { courseId?: string },
) {
  const logger = await getLogger();

  const ctx = {
    namespace: NAMESPACE,
    ...params,
  };

  logger.info(ctx, 'Fetching course chapters...');

  if (!params.courseId) {
    throw new Error('Course ID is required');
  }

  const { data, error } = await client.rpc('get_community_course_chapters', {
    p_course_id: params.courseId,
  });

  if (error) {
    logger.error(
      {
        ...ctx,
        error,
      },
      `Failed to fetch course chapters`,
    );

    throw error;
  }

  logger.info(ctx, 'Successfully fetched course chapters');

  return objectToCamel(data);
}
