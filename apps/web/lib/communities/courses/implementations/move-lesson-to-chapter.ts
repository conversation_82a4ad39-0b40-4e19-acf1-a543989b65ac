import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'courses';

/**
 * Moves a lesson to a different chapter
 * @param client - The Supabase client
 * @param params - Lesson movement parameters
 * @returns Success status
 */
export async function moveLessonToChapter(
  client: SupabaseClient<Database>,
  params: {
    lessonId: string;
    newChapterId: string | null;
  },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    lessonId: params.lessonId,
    newChapterId: params.newChapterId,
  };

  logger.info(ctx, 'Moving lesson to chapter in database...');

  if (params.newChapterId === null) {
    const { error } = await client
      .from('community_course_lessons')
      .update({ chapter_id: null })
      .eq('id', params.lessonId)
      .select()
      .single();

    if (error) {
      logger.error(
        { ...ctx, error },
        'Failed to remove lesson from chapter in database',
      );
      throw new Error(error?.message || 'Failed to remove lesson from chapter');
    }
  } else {
    const { error } = await client
      .rpc('move_community_course_lesson_to_chapter', {
        p_lesson_id: params.lessonId,
        p_new_chapter_id: params.newChapterId,
      })
      .single();

    if (error) {
      logger.error(
        { ...ctx, error },
        'Failed to move lesson to chapter in database',
      );
      throw new Error(error?.message || 'Failed to move lesson to chapter');
    }
  }

  logger.info(ctx, 'Successfully moved lesson to chapter in database');
  return { success: true };
}
