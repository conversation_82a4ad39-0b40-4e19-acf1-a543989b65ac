import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'courses';

/**
 * Gets detailed information about a course
 * @param client - The Supabase client
 * @param params - Query parameters
 * @returns Course details
 */
export async function getCourseDetails(
  client: SupabaseClient<Database>,
  params: { courseId?: string; courseSlug?: string },
) {
  const logger = await getLogger();

  const ctx = {
    namespace: NAMESPACE,
    ...params,
  };

  logger.info(ctx, 'Fetching course details...');

  if (!params.courseId && !params.courseSlug) {
    throw new Error('Either courseId or courseSlug must be provided');
  }

  const { data, error } = await client.rpc('get_community_course_details', {
    course_id: params.courseId ?? undefined,
    course_slug: params.courseSlug ?? undefined,
  });

  if (error) {
    logger.error(
      {
        ...ctx,
        error,
      },
      `Failed to fetch course details`,
    );

    throw error;
  }

  logger.info(ctx, 'Successfully fetched course details');

  return objectToCamel(data);
}
