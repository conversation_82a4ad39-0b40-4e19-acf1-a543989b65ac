import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'courses';

/**
 * Moves a chapter's position in a course
 * @param client - The Supabase client
 * @param params - Chapter movement parameters
 * @returns Success status
 */
export async function moveChapter(
  client: SupabaseClient<Database>,
  params: { chapterId: string; direction: 'up' | 'down' },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    chapterId: params.chapterId,
    direction: params.direction,
  };

  logger.info(ctx, 'Moving chapter in database...');

  const { error } = await client
    .rpc('move_community_course_chapter_sequence_order', {
      p_chapter_id: params.chapterId,
      p_direction: params.direction,
    })
    .single();

  if (error) {
    logger.error({ ...ctx, error }, 'Failed to move chapter in database');
    throw new Error(error?.message || 'Failed to move chapter');
  }

  logger.info(ctx, 'Successfully moved chapter in database');
  return { success: true };
}
