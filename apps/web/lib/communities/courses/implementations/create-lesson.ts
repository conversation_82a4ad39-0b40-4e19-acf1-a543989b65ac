import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'courses';

/**
 * Creates a lesson in a course chapter
 * @param client - The Supabase client
 * @param params - Lesson parameters
 * @returns Lesson ID
 */
export async function createLesson(
  client: SupabaseClient<Database>,
  params: {
    courseSlug: string;
    title: string;
    chapterId: string;
  },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    courseSlug: params.courseSlug,
    chapterId: params.chapterId,
    title: params.title,
  };

  logger.info(ctx, 'Creating lesson in database...');

  const { data, error } = await client
    .rpc('create_community_course_lesson', {
      p_course_slug: params.courseSlug,
      p_title: params.title,
      p_chapter_id: params.chapterId,
    })
    .single<string>();

  if (error) {
    logger.error({ ...ctx, error }, 'Failed to create lesson in database');
    throw new Error(error?.message || 'Failed to create lesson');
  }

  logger.info(ctx, 'Successfully created lesson in database');
  return data;
}
