import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

import type { CourseAccessEnum } from '../types';

const NAMESPACE = 'courses';

type CourseRecord = {
  id: string;
  slug: string;
  title: string;
  description: string;
  cover_url: string | null;
  prerequisite_course_ids: string[];
  access: CourseAccessEnum;
};

/**
 * Creates a new course
 * @param client - The Supabase client
 * @param params - Course parameters
 * @returns The created course
 */
export async function createCourse(
  client: SupabaseClient<Database>,
  params: {
    communityId: string;
    title: string;
    description: string;
    coverUrl?: string;
    prerequisiteCourseIds?: string[];
    access: CourseAccessEnum;
  },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    communityId: params.communityId,
    title: params.title,
  };

  logger.info(ctx, 'Creating course in database...');

  const { error, data } = await client
    .rpc('create_community_course', {
      p_community_id: params.communityId,
      p_title: params.title,
      p_description: params.description,
      p_cover_url: params.coverUrl,
      p_prerequisite_course_ids: params.prerequisiteCourseIds,
      p_access: params.access,
    })
    .single<CourseRecord>();

  if (error || !data) {
    logger.error({ ...ctx, error }, 'Failed to create course in database');
    throw new Error(error?.message || 'Failed to create course');
  }

  logger.info(ctx, 'Successfully created course in database');
  return objectToCamel(data);
}
