import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'courses';

/**
 * Toggles the published status of a course
 * @param client - The Supabase client
 * @param params - Course parameters
 * @returns The new published status
 */
export async function toggleCoursePublished(
  client: SupabaseClient<Database>,
  params: { courseSlug: string },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    courseSlug: params.courseSlug,
  };

  logger.info(ctx, 'Toggling course published status in database...');

  const { data, error } = await client
    .rpc('toggle_community_course_published', {
      course_slug: params.courseSlug,
    })
    .single<boolean>();

  if (error) {
    logger.error(
      { ...ctx, error },
      'Failed to toggle course published status in database',
    );
    throw new Error(
      error?.message || 'Failed to toggle course published status',
    );
  }

  logger.info(ctx, 'Successfully toggled course published status in database');
  return data;
}
