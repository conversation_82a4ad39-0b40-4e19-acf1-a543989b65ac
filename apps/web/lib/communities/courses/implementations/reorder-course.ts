import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'courses';

/**
 * Reorders a course position relative to other courses
 * @param client - The Supabase client
 * @param params - Reordering parameters
 * @returns Operation result
 */
export async function reorderCourse(
  client: SupabaseClient<Database>,
  params: {
    courseId: string;
    direction: 'left' | 'right';
  },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    courseId: params.courseId,
    direction: params.direction,
  };

  logger.info(ctx, 'Reordering course in database...');

  const { data, error } = await client.rpc('reorder_community_course', {
    p_course_id: params.courseId,
    p_direction: params.direction,
  });

  if (error) {
    logger.error({ ...ctx, error }, 'Failed to reorder course in database');
    throw new Error(error?.message || 'Failed to reorder course');
  }

  logger.info(ctx, 'Successfully reordered course in database');
  return objectToCamel(data);
}
