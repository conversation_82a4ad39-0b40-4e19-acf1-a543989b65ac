import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'courses';

/**
 * Creates a chapter in a course
 * @param client - The Supabase client
 * @param params - Chapter parameters
 * @returns Chapter ID
 */
export async function createChapter(
  client: SupabaseClient<Database>,
  params: {
    courseSlug: string;
    title: string;
    description?: string;
    icon?: string;
  },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    courseSlug: params.courseSlug,
    title: params.title,
  };

  logger.info(ctx, 'Creating chapter in database...');

  const { data, error } = await client
    .rpc('create_community_course_chapter', {
      p_course_slug: params.courseSlug,
      p_title: params.title,
      p_description: params.description,
      p_icon: params.icon,
    })
    .single<string>();

  if (error) {
    logger.error({ ...ctx, error }, 'Failed to create chapter in database');
    throw new Error(error?.message || 'Failed to create chapter');
  }

  logger.info(ctx, 'Successfully created chapter in database');
  return data;
}
