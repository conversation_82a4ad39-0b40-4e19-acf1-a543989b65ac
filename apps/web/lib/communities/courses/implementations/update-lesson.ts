import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import type { Database } from '~/lib/database.types';
import type { Json } from '~/lib/database.types';

import type { LessonContentTypeEnum } from '../types';

const NAMESPACE = 'courses';

/**
 * Updates a lesson's content and details
 * @param client - The Supabase client
 * @param params - Lesson update parameters
 * @returns Success status
 */
export async function updateLesson(
  client: SupabaseClient<Database>,
  params: {
    lessonId: string;
    title: string;
    icon?: string;
    lessonText?: Json;
    contentData?: Json;
    contentType?: LessonContentTypeEnum;
    characterCount?: number;
    wordCount?: number;
  },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    lessonId: params.lessonId,
  };

  logger.info(ctx, 'Updating lesson in database...');

  const { error } = await client
    .rpc('update_community_course_lesson', {
      p_lesson_id: params.lessonId,
      p_new_title: params.title,
      p_new_icon: params.icon ?? '',
      p_new_lesson_text: params.lessonText ?? null,
      p_new_content_data: params.contentData ?? null,
      p_new_content_type: params.contentType ?? 'none',
      p_new_character_count: params.characterCount ?? 0,
      p_new_word_count: params.wordCount ?? 0,
    })
    .single();

  if (error) {
    logger.error({ ...ctx, error }, 'Failed to update lesson in database');
    throw new Error(error?.message || 'Failed to update lesson');
  }

  logger.info(ctx, 'Successfully updated lesson in database');
  return { success: true };
}
