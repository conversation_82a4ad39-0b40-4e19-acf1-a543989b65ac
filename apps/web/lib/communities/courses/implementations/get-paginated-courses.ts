import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';
import { computePagination } from '~/lib/utils/compute-pagination';

import type { CourseStatusEnum } from '../types';

const NAMESPACE = 'courses';

/**
 * Retrieves a paginated list of courses for a community
 * @param client - The Supabase client
 * @param params - Query parameters
 * @returns Paginated course list
 */
export async function getPaginatedCourses(
  client: SupabaseClient<Database>,
  params: {
    communityId: string;
    page: number;
    limit?: number;
    status?: CourseStatusEnum;
  },
) {
  const logger = await getLogger();

  const ctx = {
    namespace: NAMESPACE,
    ...params,
  };

  logger.info(ctx, 'Fetching paginated courses...');

  if (!params.communityId) {
    throw new Error('Community ID is required');
  }

  const { resolvedLimit: limit, offset } = computePagination(
    params.page,
    params.limit,
  );

  const { data, error } = await client.rpc('get_paginated_community_courses', {
    p_community_id: params.communityId,
    p_status_filter: params.status ?? 'published',
    limit_rows: limit,
    offset_rows: offset,
  });

  if (error) {
    throw error;
  }

  const totalCount = data?.[0]?.total_count ?? 0;

  const pageCount = Math.ceil(totalCount / limit);

  logger.info(
    {
      ...ctx,
      dataLength: data?.length,
      totalCount,
      pageCount,
      offset,
      limit,
    },
    'Successfully fetched paginated courses',
  );

  return {
    data: objectToCamel(data) ?? [],
    count: totalCount,
    pageSize: limit,
    page: params.page,
    pageCount,
  };
}
