import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'courses';

/**
 * Deletes a chapter from a course
 * @param client - The Supabase client
 * @param params - Chapter parameters
 * @returns Success status
 */
export async function deleteChapter(
  client: SupabaseClient<Database>,
  params: { chapterId: string },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    chapterId: params.chapterId,
  };

  logger.info(ctx, 'Deleting chapter in database...');

  const { error } = await client
    .rpc('delete_community_course_chapter', {
      p_chapter_id: params.chapterId,
    })
    .single();

  if (error) {
    logger.error({ ...ctx, error }, 'Failed to delete chapter in database');
    throw new Error(error?.message || 'Failed to delete chapter');
  }

  logger.info(ctx, 'Successfully deleted chapter in database');
  return { success: true };
}
