import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'courses';

/**
 * Deletes a lesson from a course
 * @param client - The Supabase client
 * @param params - Lesson parameters
 * @returns Success status
 */
export async function deleteLesson(
  client: SupabaseClient<Database>,
  params: { lessonId: string },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    lessonId: params.lessonId,
  };

  logger.info(ctx, 'Deleting lesson in database...');

  const { error } = await client
    .rpc('delete_community_course_lesson', {
      p_lesson_id: params.lessonId,
    })
    .single();

  if (error) {
    logger.error({ ...ctx, error }, 'Failed to delete lesson in database');
    throw new Error(error?.message || 'Failed to delete lesson');
  }

  logger.info(ctx, 'Successfully deleted lesson in database');
  return { success: true };
}
