import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'courses';

/**
 * Deletes a course
 * @param client - The Supabase client
 * @param params - Course parameters
 * @returns Success status
 */
export async function deleteCourse(
  client: SupabaseClient<Database>,
  params: { courseSlug: string },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    courseSlug: params.courseSlug,
  };

  logger.info(ctx, 'Deleting course in database...');

  const { error } = await client
    .rpc('delete_community_course', {
      course_slug: params.courseSlug,
    })
    .single();

  if (error) {
    logger.error({ ...ctx, error }, 'Failed to delete course in database');
    throw new Error(error?.message || 'Failed to delete course');
  }

  logger.info(ctx, 'Successfully deleted course in database');
  return { success: true };
}
