import { object, string, z } from 'zod';

export const CourseSchema = object({
  title: string().min(1).max(255),
  description: string().min(1).max(255),
  coverUrl: z.union([string().min(1).max(1000), z.null()]),
  prerequisiteCourseIds: z.union([z.array(string().uuid()), z.null()]),
});

export const CreateCourseFormSchema = CourseSchema.merge(
  object({
    userId: string().uuid(),
  }),
);

export const courseSchema = z.object({
  id: z.string().optional(),
  title: z
    .string()
    .min(1, 'Title is required')
    .max(60, 'Title must be 60 characters or less'),
  description: z
    .string()
    .max(160, 'Description must be 160 characters or less')
    .optional(),
  coverImage: z
    .custom<File>((value) => {
      if (!(value instanceof File)) return false;
      // Validate file size (e.g., 5MB limit)
      if (value.size > 5 * 1024 * 1024) return false;
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
      return allowedTypes.includes(value.type);
    })
    .or(
      z
        .string()
        .url()
        .regex(/\.(jpg|jpeg|png|webp)$/i, 'URL must point to an image file'),
    )
    .nullable()
    .optional(),
  prerequisites: z.array(z.string()).nullable(),
  status: z.enum(['draft', 'published', 'archived']),
});

export type CourseFormData = z.infer<typeof courseSchema>;
