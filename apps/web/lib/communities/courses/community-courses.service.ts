import { SupabaseClient } from '@supabase/supabase-js';

import type { Database } from '~/lib/database.types';
import type { Json } from '~/lib/database.types';

import * as implementations from './implementations';
import type {
  CourseAccessEnum,
  CourseStatusEnum,
  LessonContentTypeEnum,
} from './types';

export function createCommunityCoursesService(
  client: SupabaseClient<Database>,
) {
  return new CommunityCoursesService(client);
}

export class CommunityCoursesService {
  private readonly namespace = 'courses';

  constructor(private readonly client: SupabaseClient<Database>) {}

  async getPaginatedCourses(params: {
    communityId: string;
    page: number;
    limit?: number;
    status?: CourseStatusEnum;
  }) {
    return implementations.getPaginatedCourses(this.client, params);
  }

  async getCourseDetails(params: { courseId?: string; courseSlug?: string }) {
    return implementations.getCourseDetails(this.client, params);
  }

  async getCourseChapters(params: { courseId?: string }) {
    return implementations.getCourseChapters(this.client, params);
  }

  async getCourseLessons(params: { courseId?: string }) {
    return implementations.getCourseLessons(this.client, params);
  }

  async createCourse(params: {
    communityId: string;
    title: string;
    description: string;
    coverUrl?: string;
    prerequisiteCourseIds?: string[];
    access: CourseAccessEnum;
  }) {
    return implementations.createCourse(this.client, params);
  }

  async updateCourse(params: {
    courseId: string;
    title: string;
    description: string;
    coverUrl?: string;
    prerequisiteCourseIds?: string[];
    access: CourseAccessEnum;
  }) {
    return implementations.updateCourse(this.client, params);
  }

  async reorderCourse(params: {
    courseId: string;
    direction: 'left' | 'right';
  }) {
    return implementations.reorderCourse(this.client, params);
  }

  async getCommunityIdForCourse(params: { courseId: string }) {
    return implementations.getCommunityIdForCourse(this.client, params);
  }

  async toggleCoursePublished(params: { courseSlug: string }) {
    return implementations.toggleCoursePublished(this.client, params);
  }

  async updateCourseStatus(params: {
    courseSlug: string;
    targetStatus: CourseStatusEnum;
  }) {
    return implementations.updateCourseStatus(this.client, params);
  }

  async deleteCourse(params: { courseSlug: string }) {
    return implementations.deleteCourse(this.client, params);
  }

  async createChapter(params: {
    courseSlug: string;
    title: string;
    description?: string;
    icon?: string;
  }) {
    return implementations.createChapter(this.client, params);
  }

  async deleteChapter(params: { chapterId: string }) {
    return implementations.deleteChapter(this.client, params);
  }

  async moveChapter(params: { chapterId: string; direction: 'up' | 'down' }) {
    return implementations.moveChapter(this.client, params);
  }

  async updateChapter(params: {
    chapterId: string;
    title: string;
    description?: string;
    icon?: string;
  }) {
    return implementations.updateChapter(this.client, params);
  }

  async createLesson(params: {
    courseSlug: string;
    title: string;
    chapterId: string;
  }) {
    return implementations.createLesson(this.client, params);
  }

  async deleteLesson(params: { lessonId: string }) {
    return implementations.deleteLesson(this.client, params);
  }

  async moveLessonToChapter(params: {
    lessonId: string;
    newChapterId: string | null;
  }) {
    return implementations.moveLessonToChapter(this.client, params);
  }

  async moveLessonSequence(params: {
    lessonId: string;
    targetSequence: number;
    direction: 'up' | 'down';
  }) {
    return implementations.moveLessonSequence(this.client, params);
  }

  async updateLessonStatus(params: {
    lessonId: string;
    targetStatus: CourseStatusEnum;
  }) {
    return implementations.updateLessonStatus(this.client, params);
  }

  async updateLesson(params: {
    lessonId: string;
    title: string;
    icon?: string;
    lessonText?: Json;
    contentData?: Json;
    contentType?: LessonContentTypeEnum;
    characterCount?: number;
    wordCount?: number;
  }) {
    return implementations.updateLesson(this.client, params);
  }
}
