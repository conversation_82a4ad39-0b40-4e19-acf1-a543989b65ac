import { ObjectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

export type CourseStatusEnum =
  Database['public']['Enums']['community_content_status_enum'];

type CommunityCourseRow =
  Database['public']['Tables']['community_courses']['Row'];

export type CommunityCourse = ObjectToCamel<CommunityCourseRow>;

export type CoursePaginated = ObjectToCamel<
  Database['public']['Functions']['get_paginated_community_courses']['Returns'][number]
>;

/** Course access types:
 * @property {'standard'} - Regular course access
 * @property {'time'} - Time-limited access (e.g., 30-day access)
 * @property {'private'} - Restricted/invitation-only access
 * @property {'level'} - Progressive/level-based access
 * @property {'paid'} - Premium/paid course access
 */
export type CourseAccessEnum =
  Database['public']['Enums']['community_course_access_enum'];

export type ContentTypeEnum =
  Database['public']['Enums']['community_course_content_type_enum'];
/**
 * Represents the status of a student in a course
 * @property {'not_enrolled'} - The student is not enrolled in the course
 * @property {'enrolled'} - The student is enrolled in the course
 * @property {'completed'} - The student has completed the course
 */
export type StudentStatus = 'not_enrolled' | 'enrolled' | 'completed';

/**
 * Represents a course that must be completed before accessing another course
 * @property {string} id - Unique identifier of the prerequisite course
 * @property {string} title - Name of the prerequisite course
 * @property {boolean} completed - Whether the student has completed this prerequisite
 */
export type PrerequisiteCourse = {
  id: string;
  title: string;
  completed: boolean;
};

/**
 * String ID representing a prerequisite course.
 * This type is used in the database schema and some API operations.
 */
export type PrerequisiteId = string;

export type CourseData = {
  id: string;
  slug: string;
  title: string;
  description: string;
  coverUrl?: string;
  prerequisiteCourseIds?: string[];
  access: CourseAccessEnum;
};

export type CourseDetails = ObjectToCamel<
  Database['public']['Functions']['get_community_course_details']['Returns'][number]
>;

// Chapter types

export type CourseChapters = ObjectToCamel<
  Database['public']['Functions']['get_community_course_chapters']['Returns'][number]
>;

export type CourseChapter = ObjectToCamel<
  Database['public']['Tables']['community_course_chapters']['Row']
>;

// Lesson types

export type CourseLesson = ObjectToCamel<
  Database['public']['Tables']['community_course_lessons']['Row']
>;

export type LessonStatusEnum =
  Database['public']['Enums']['community_content_status_enum'];

export type LessonContentTypeEnum =
  Database['public']['Enums']['community_course_content_type_enum'];

export type LessonContentData = {
  actionItems?: string[];
  note?: string;
  videoUrl?: string;
  questions?: Question[];
  exercise?: ExerciseContent;
};

export type Question = {
  id: string;
  question: string;
  options: string[];
  correctAnswer: string;
};

export type ExerciseContent = {
  instructions: string;
  starter_code: string;
  solution: string;
};
