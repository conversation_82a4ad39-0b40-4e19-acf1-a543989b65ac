import { type ObjectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

type CommunityReturn =
  Database['public']['Functions']['community_workspace']['Returns'][0];

export type CommunityDb = ObjectToCamel<CommunityReturn>;

export type PermissionsEnum =
  Database['public']['Enums']['community_permissions'];

export type SubscriptionStatusEnum = ObjectToCamel<{
  status: Database['public']['Enums']['subscription_status'];
}>;

// Community type with permissions as string array
export type Community = CommunityDb & {
  permissions: PermissionsEnum;
  subscriptionStatus: SubscriptionStatusEnum;
};

type TeamMembersReturn =
  Database['public']['Functions']['get_community_team_members']['Returns'];

export type TeamMembers = ObjectToCamel<TeamMembersReturn>;

type MembersOnlyReturn =
  Database['public']['Functions']['get_community_members_only']['Returns'];

export type MembersOnly = ObjectToCamel<MembersOnlyReturn>;

type MembersWithEmailReturn =
  Database['public']['Functions']['get_community_members']['Returns'][number];

export type MembersWithEmail = ObjectToCamel<MembersWithEmailReturn>;

type CommunityMembershipRow =
  Database['public']['Tables']['community_memberships']['Row'];

export type CommunityMembership = ObjectToCamel<CommunityMembershipRow>;

type UserCreatedCommunityRow =
  Database['public']['Views']['user_created_communities']['Row'];

export type UserCreatedCommunity = ObjectToCamel<UserCreatedCommunityRow>;

type UserMemberCommunityRow =
  Database['public']['Functions']['get_user_member_communities']['Returns'][0];

export type UserMemberCommunity = ObjectToCamel<UserMemberCommunityRow>;

export type CommunityMembershipStatusEnum =
  Database['public']['Enums']['community_membership_status'];
