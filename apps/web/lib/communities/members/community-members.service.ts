import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import { z } from 'zod';

import type { RemoveMemberSchema } from '~/lib/communities/members/schema/remove-member.schema';
import type { TransferOwnershipConfirmationSchema } from '~/lib/communities/members/schema/transfer-ownership-confirmation.schema';
import type { UpdateMemberRoleSchema } from '~/lib/communities/members/schema/update-member-role.schema';
import type {
  UserCreatedCommunity,
  UserMemberCommunity,
} from '~/lib/communities/members/types';
import type { Database } from '~/lib/database.types';

import * as implementations from './implementations';

/**
 * Creates a new instance of the CommunityMembersService
 * @param client The Supabase client
 * @returns A new CommunityMembersService instance
 */
export function createCommunityMembersService(
  client: SupabaseClient<Database>,
) {
  return new CommunityMembersService(client);
}

/**
 * Service for managing community members in the application
 */
class CommunityMembersService {
  constructor(private readonly client: SupabaseClient<Database>) {}

  /**
   * @name getUserStatus
   * @description Get user's authentication status, membership status, and community privacy status
   * @param communitySlug The slug of the community
   */
  async getUserStatus(communitySlug: string) {
    return implementations.getUserStatus(this.client, communitySlug);
  }

  /**
   * @name removeMemberFromCommunity
   * @description Removes a member from a community.
   * @param params
   */
  async removeMemberFromCommunity(params: z.infer<typeof RemoveMemberSchema>) {
    return implementations.removeMemberFromCommunity(this.client, params);
  }

  /**
   * @name updateMemberRole
   * @description Updates the role of a member in a community.
   * @param params
   * @param adminClient
   */
  async updateMemberRole(
    params: z.infer<typeof UpdateMemberRoleSchema>,
    adminClient: SupabaseClient<Database>,
  ) {
    return implementations.updateMemberRole(this.client, params, adminClient);
  }

  /**
   * @name transferOwnership
   * @description Transfers ownership of an community to another user.
   * @param params
   * @param adminClient
   */
  async transferOwnership(
    params: z.infer<typeof TransferOwnershipConfirmationSchema>,
    adminClient: SupabaseClient<Database>,
  ) {
    return implementations.transferOwnership(this.client, params, adminClient);
  }

  /**
   * @name getCommunityMembersOnly
   * @description Gets all members of a community excluding the team members.
   * @param communityId
   */
  async getCommunityMembersOnly(communityId: string) {
    return implementations.getCommunityMembersOnly(this.client, communityId);
  }

  /**
   * @name getCommunityTeamMembers
   * @description Gets all members of a community including the team members.
   * @param communityId
   */
  async getCommunityTeamMembers(communityId: string) {
    return implementations.getCommunityTeamMembers(this.client, communityId);
  }

  /**
   * @name getCommunityAllMembers
   * @description Gets all members of a community including the team members.
   * @param communityId
   */
  async getCommunityAllMembers(communityId: string) {
    return implementations.getCommunityAllMembers(this.client, communityId);
  }

  /**
   * @name getMembersCountById
   * @description Get the number of members in the community.
   * @param communityId
   */
  async getMembersCountById(communityId: string) {
    return implementations.getMembersCountById(this.client, communityId);
  }

  /**
   * @name getUserCreatedCommunities
   * @description Gets all communities created by a specific user.
   * @param userId The ID of the user
   */
  async getUserCreatedCommunities(userId: string): Promise<{
    communities: UserCreatedCommunity[];
    total: number;
  }> {
    return implementations.getUserCreatedCommunities(this.client, userId);
  }

  /**
   * @name getUserMemberCommunities
   * @description Get communities a user is a member of
   * @param userId The user ID to get communities for
   */
  async getUserMemberCommunities(userId: string | null): Promise<{
    communities: UserMemberCommunity[];
    total: number;
  }> {
    return implementations.getUserMemberCommunities(this.client, userId);
  }

  /**
   * @name leaveCommunity
   * @description Allows a user to leave a community
   * @param params
   */
  async leaveCommunity(params: {
    communityId: string;
    userId: string;
  }): Promise<void> {
    return implementations.leaveCommunity(this.client, params);
  }

  /**
   * @name joinCommunity
   * @description Allows a user to join a community (free or paid)
   * @param params
   */
  async joinCommunity(params: {
    communityId: string;
    userId: string;
    orderType: 'subscription' | 'one-time';
    priceId: string;
    latestInvoiceId: string;
    subscriptionId?: string;
  }): Promise<void> {
    return implementations.joinCommunity(this.client, params);
  }
}
