import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { z } from 'zod';

import { getLogger } from '@kit/shared/logger';

import type { UpdateMemberRoleSchema } from '~/lib/communities/members/schema/update-member-role.schema';
import type { Database } from '~/lib/database.types';

const NAMESPACE = 'community-members';

/**
 * Updates the role of a member in a community
 * @param client The regular Supabase client
 * @param params The update parameters
 * @param adminClient The admin Supabase client with elevated permissions
 * @returns The result of the update operation
 */
export async function updateMemberRole(
  client: SupabaseClient<Database>,
  params: z.infer<typeof UpdateMemberRoleSchema>,
  adminClient: SupabaseClient<Database>,
) {
  const logger = await getLogger();

  const ctx = {
    namespace: NAMESPACE,
    function: 'updateMemberRole',
    ...params,
  };

  logger.info(ctx, `Validating permissions to update member role...`);

  const { data: canActionAccountMember, error: accountError } =
    await client.rpc('can_action_community_member', {
      p_target_user_id: params.userId,
      p_target_community_id: params.communityId,
    });

  if (accountError ?? !canActionAccountMember) {
    logger.error(
      {
        ...ctx,
        accountError,
      },
      `Failed to validate permissions to update member role`,
    );

    throw new Error(`Failed to validate permissions to update member role`);
  }

  logger.info(ctx, `Permissions validated. Updating member role...`);

  // we use the Admin client to update the role
  // since we do not set any RLS policies on the community_memberships table
  // for updating community_memberships. Instead, we use the can_action_community_member
  // RPC to validate permissions to update the role
  const { data, error } = await adminClient
    .from('community_memberships')
    .update({
      community_role: params.role,
    })
    .match({
      community_id: params.communityId,
      user_id: params.userId,
    });

  if (error) {
    logger.error(
      {
        ...ctx,
        error,
      },
      `Failed to update member role`,
    );

    throw error;
  }

  logger.info(ctx, `Successfully updated member role`);

  return data;
}
