import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { UserCreatedCommunity } from '~/lib/communities/members/types';
import type { Database } from '~/lib/database.types';

const NAMESPACE = 'community-members';

/**
 * Gets all communities created by a specific user
 * @param client The Supabase client
 * @param userId The ID of the user
 * @returns Object containing communities and total count
 */
export async function getUserCreatedCommunities(
  client: SupabaseClient<Database>,
  userId: string,
): Promise<{
  communities: UserCreatedCommunity[];
  total: number;
}> {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    function: 'getUserCreatedCommunities',
    userId,
  };

  if (!userId) {
    logger.info(ctx, 'No user ID provided, returning empty results');
    return { communities: [], total: 0 };
  }

  logger.info(ctx, 'Fetching user created communities');

  const {
    data: communities,
    error,
    count,
  } = await client
    .from('user_created_communities')
    .select('*', { count: 'exact' })
    .eq('primary_owner_user_id', userId)
    .order('created_at', { ascending: false });

  if (error) {
    logger.error({ ...ctx, error }, 'Error fetching user created communities');
    return { communities: [], total: 0 };
  }

  logger.info(
    { ...ctx, count },
    'Successfully fetched user created communities',
  );

  try {
    return {
      communities: objectToCamel(communities),
      total: count || 0,
    };
  } catch (validationError) {
    logger.error(
      {
        ...ctx,
        error: validationError,
        rawData: communities ? JSON.stringify(communities[0]) : 'No data',
        communityCount: communities ? communities.length : 0,
      },
      'Data validation error for created communities',
    );
    return { communities: [], total: 0 };
  }
}
