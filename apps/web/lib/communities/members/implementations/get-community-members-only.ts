import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'community-members';

/**
 * Gets all members of a community excluding the team members
 * @param client The Supabase client
 * @param communityId The ID of the community
 * @returns The members of the community (excluding team members)
 */
export async function getCommunityMembersOnly(
  client: SupabaseClient<Database>,
  communityId: string,
) {
  const logger = await getLogger();

  const ctx = {
    namespace: NAMESPACE,
    function: 'getCommunityMembersOnly',
    communityId,
  };

  logger.info(ctx, 'Getting community members only...');

  const { data, error } = await client.rpc('get_community_members_only', {
    p_community_id: communityId,
  });

  if (error) {
    logger.error({ ...ctx, error }, 'Failed to get community members only');

    throw error;
  }

  logger.info(ctx, 'Community members only successfully retrieved');

  return objectToCamel(data);
}
