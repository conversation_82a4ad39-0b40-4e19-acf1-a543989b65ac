import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { z } from 'zod';

import { getLogger } from '@kit/shared/logger';

import type { TransferOwnershipConfirmationSchema } from '~/lib/communities/members/schema/transfer-ownership-confirmation.schema';
import type { Database } from '~/lib/database.types';

const NAMESPACE = 'community-members';

/**
 * Transfers ownership of a community to another user
 * @param client The regular Supabase client
 * @param params The transfer parameters
 * @param adminClient The admin Supabase client with elevated permissions
 * @returns The result of the transfer operation
 */
export async function transferOwnership(
  client: SupabaseClient<Database>,
  params: z.infer<typeof TransferOwnershipConfirmationSchema>,
  adminClient: SupabaseClient<Database>,
) {
  const logger = await getLogger();

  const ctx = {
    namespace: NAMESPACE,
    function: 'transferOwnership',
    ...params,
  };

  logger.info(ctx, `Transferring ownership of community...`);

  const { data, error } = await adminClient.rpc(
    'transfer_community_ownership',
    {
      target_community_id: params.communityId,
      new_owner_id: params.userId,
    },
  );

  if (error) {
    logger.error(
      { ...ctx, error },
      `Failed to transfer ownership of community`,
    );

    throw error;
  }

  logger.info(ctx, `Successfully transferred ownership of community`);

  return data;
}
