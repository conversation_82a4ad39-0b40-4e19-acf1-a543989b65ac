import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'community-members';

/**
 * Allows a user to join a community
 * @param client The Supabase client
 * @param params The join parameters
 */
export async function joinCommunity(
  client: SupabaseClient<Database>,
  params: {
    communityId: string;
    userId: string;
    priceId: string;
    orderType: 'subscription' | 'one-time';
    latestInvoiceId: string;
    subscriptionId?: string;
  },
): Promise<void> {
  const logger = await getLogger();

  const ctx = {
    namespace: NAMESPACE,
    function: 'joinCommunity',
    ...params,
  };

  logger.info(ctx, 'Joining community...');

  const { error } = await client.rpc('add_user_to_community', {
    p_community_id: params.communityId,
    p_user_id: params.userId,
    p_order_type: params.orderType,
    p_price_id: params.priceId,
    p_latest_invoice_id: params.latestInvoiceId,
    p_subscription_id: params.subscriptionId ?? null,
  });

  if (error) {
    logger.error({ ...ctx, error }, 'Failed to join community');
    throw new Error('Failed to join community');
  }

  logger.info(ctx, 'Successfully joined community');
}
