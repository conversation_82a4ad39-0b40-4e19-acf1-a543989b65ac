import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { z } from 'zod';

import { getLogger } from '@kit/shared/logger';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'community-members';

const LeaveCommunitySchema = z.object({
  communityId: z.string().uuid(),
  userId: z.string().uuid(),
});

/**
 * Allows a user to leave a community
 * @param client The Supabase client
 * @param params The leave parameters
 */
export async function leaveCommunity(
  client: SupabaseClient<Database>,
  params: z.infer<typeof LeaveCommunitySchema>,
): Promise<void> {
  const logger = await getLogger();

  const ctx = {
    namespace: NAMESPACE,
    function: 'leaveCommunity',
    ...params,
  };

  logger.info(ctx, 'Leaving community...');

  const { communityId, userId } = LeaveCommunitySchema.parse(params);

  const { error } = await client.from('community_memberships').delete().match({
    community_id: communityId,
    user_id: userId,
  });

  if (error) {
    logger.error({ ...ctx, error }, 'Failed to leave community');

    throw new Error('Failed to leave community');
  }

  logger.info(ctx, 'Successfully left community');
}
