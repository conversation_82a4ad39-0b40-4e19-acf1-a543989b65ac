import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'community-members';

/**
 * Get user's authentication status, membership status, and community privacy status
 * @param client The Supabase client
 * @param communitySlug The slug of the community
 * @returns Object with isAuthenticated, isMember, isPrivate and userId
 */
export async function getUserStatus(
  client: SupabaseClient<Database>,
  communitySlug: string,
) {
  const logger = await getLogger();

  const ctx = {
    namespace: NAMESPACE,
    function: 'getUserStatus',
    communitySlug,
  };

  logger.info(ctx, 'Getting user status for community...');

  // Default values
  let isAuthenticated = false;
  let isMember = false;
  let isPrivate = false;
  let userId = undefined;

  try {
    // Check if user is authenticated
    const { data: authData } = await client.auth.getUser();
    userId = authData.user?.id;

    isAuthenticated = !!authData.user;

    // Get community privacy status
    const { data: communityData, error: communityError } = await client
      .from('communities')
      .select('is_private')
      .eq('slug', communitySlug)
      .single();

    if (communityError) {
      logger.error(
        {
          ...ctx,
          error: communityError,
        },
        'Failed to get community privacy status',
      );
    } else {
      isPrivate = !!communityData?.is_private;
    }

    // Check membership if user is authenticated
    if (isAuthenticated && userId) {
      const { data: memberships, error: membershipError } = await client
        .from('user_communities')
        .select('*')
        .eq('slug', communitySlug)
        .eq('user_id', userId)
        .maybeSingle();

      if (membershipError) {
        logger.error(
          {
            ...ctx,
            error: membershipError,
          },
          'Failed to check membership status',
        );
      } else {
        isMember = !!memberships;
      }
    }

    logger.info(
      {
        ...ctx,
        isAuthenticated,
        isMember,
        isPrivate,
      },
      'Successfully determined user status',
    );

    return { isAuthenticated, isMember, isPrivate, userId };
  } catch (error) {
    logger.error(
      {
        ...ctx,
        error,
      },
      'Error determining user status',
    );

    // Return default values if there's an error
    return { isAuthenticated, isMember, isPrivate, userId };
  }
}
