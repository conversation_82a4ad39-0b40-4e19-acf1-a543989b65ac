import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'community-members';

/**
 * Gets the count of members in a community
 * @param client The Supabase client
 * @param communityId The ID of the community
 * @returns The number of members in the community
 */
export async function getMembersCountById(
  client: SupabaseClient<Database>,
  communityId: string,
) {
  const logger = await getLogger();

  const ctx = {
    namespace: NAMESPACE,
    function: 'getMembersCountById',
    communityId,
  };

  logger.info(ctx, 'Getting community members count...');

  const { count, error } = await client
    .from('community_memberships')
    .select('*', {
      head: true,
      count: 'exact',
    })
    .eq('community_id', communityId);

  if (error) {
    logger.error({ ...ctx, error }, 'Failed to get community members count');
    throw error;
  }

  logger.info(ctx, `Community members count: ${count}`);

  return count;
}
