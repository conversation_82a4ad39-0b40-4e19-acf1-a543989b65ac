import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { UserMemberCommunity } from '~/lib/communities/members/types';
import type { Database } from '~/lib/database.types';

const NAMESPACE = 'community-members';

/**
 * Gets communities a user is a member of
 * @param client The Supabase client
 * @param userId The ID of the user
 * @returns Object containing communities and total count
 */
export async function getUserMemberCommunities(
  client: SupabaseClient<Database>,
  userId: string | null,
): Promise<{
  communities: UserMemberCommunity[];
  total: number;
}> {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    function: 'getUserMemberCommunities',
    userId,
  };

  // If userId is null, return empty results
  if (!userId) {
    logger.info(ctx, 'No user ID provided, returning empty results');
    return { communities: [], total: 0 };
  }

  logger.info(ctx, 'Fetching communities user is a member of');

  const { data, error } = await client.rpc('get_user_member_communities', {
    p_user_id: userId,
  });

  if (error) {
    logger.error(
      { ...ctx, error },
      'Error fetching communities user is a member of',
    );
    return { communities: [], total: 0 };
  }

  logger.info(
    { ...ctx, count: data?.length },
    'Successfully fetched communities user is a member of',
  );

  try {
    return {
      // Assuming the RPC returns an array, directly use it
      communities: objectToCamel(data),
      total: data?.length || 0,
    };
  } catch (validationError) {
    // Log the raw data structure if validation fails
    logger.error(
      {
        ...ctx,
        error: validationError,
        rawData: data ? JSON.stringify(data[0]) : 'No data',
        communityCount: data ? data.length : 0,
      },
      'Data validation error for member communities',
    );
    return { communities: [], total: 0 };
  }
}
