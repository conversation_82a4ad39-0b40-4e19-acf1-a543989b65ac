import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { z } from 'zod';

import { getLogger } from '@kit/shared/logger';

import type { RemoveMemberSchema } from '~/lib/communities/members/schema/remove-member.schema';
import type { Database } from '~/lib/database.types';

const NAMESPACE = 'community-members';

/**
 * Removes a member from a community
 * @param client The Supabase client
 * @param params The removal parameters
 * @returns The result of the removal operation
 */
export async function removeMemberFromCommunity(
  client: SupabaseClient<Database>,
  params: z.infer<typeof RemoveMemberSchema>,
) {
  const logger = await getLogger();

  const ctx = {
    namespace: NAMESPACE,
    function: 'removeMemberFromCommunity',
    ...params,
  };

  logger.info(ctx, `Removing member from community...`);

  // By removing a member if they have a subscription it will be cancelled via db webhook
  const { data, error } = await client
    .from('community_memberships')
    .delete()
    .match({
      community_id: params.communityId,
      user_id: params.userId,
    });

  if (error) {
    logger.error(
      {
        ...ctx,
        error,
      },
      `Failed to remove member from community`,
    );

    throw error;
  }

  logger.info(ctx, `Successfully removed member from community.`);

  return data;
}
