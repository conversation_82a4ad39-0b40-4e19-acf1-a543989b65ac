import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'community-members';

/**
 * Gets all members of a community including team members
 * @param client The Supabase client
 * @param communityId The ID of the community
 * @returns All members of the community
 */
export async function getCommunityAllMembers(
  client: SupabaseClient<Database>,
  communityId: string,
) {
  const logger = await getLogger();

  const ctx = {
    namespace: NAMESPACE,
    function: 'getCommunityAllMembers',
    communityId,
  };

  logger.info(ctx, 'Getting community members...');

  const { data, error } = await client.rpc('get_community_members', {
    p_community_id: communityId,
  });

  if (error) {
    logger.error({ ...ctx, error }, 'Failed to get community members');

    throw error;
  }

  logger.info(ctx, 'Community members successfully retrieved');

  return objectToCamel(data);
}
