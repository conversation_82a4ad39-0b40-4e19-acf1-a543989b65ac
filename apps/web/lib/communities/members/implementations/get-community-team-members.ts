import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'community-members';

/**
 * Gets all team members of a community
 * @param client The Supabase client
 * @param communityId The ID of the community
 * @returns The team members of the community
 */
export async function getCommunityTeamMembers(
  client: SupabaseClient<Database>,
  communityId: string,
) {
  const logger = await getLogger();

  const ctx = {
    namespace: NAMESPACE,
    function: 'getCommunityTeamMembers',
    communityId,
  };

  logger.info(ctx, 'Getting community team members...');

  const { data, error } = await client.rpc('get_community_team_members', {
    p_community_id: communityId,
  });

  if (error) {
    logger.error({ ...ctx, error }, 'Failed to get community team members');

    throw error;
  }

  logger.info(ctx, 'Community team members successfully retrieved');

  return objectToCamel(data);
}
