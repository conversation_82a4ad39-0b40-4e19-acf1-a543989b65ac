'use server';

import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';

import { enhanceAction } from '@kit/next/actions';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createCommunityMembersService } from '~/lib/communities/members/community-members.service';
import { LeaveCommunitySchema } from '~/lib/communities/members/schema/leave-community.schema';

export const leaveCommunityAccountAction = enhanceAction(
  async (data: { communityId: string }, user) => {
    const params = LeaveCommunitySchema.parse(data);

    const client = await getSupabaseServerClient();
    const membersService = createCommunityMembersService(client);

    await membersService.leaveCommunity({
      communityId: params.communityId,
      userId: user.id,
    });

    revalidatePath('/[community]', 'layout');

    return redirect('/home');
  },
  {},
);
