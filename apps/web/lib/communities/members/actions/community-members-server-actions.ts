'use server';

import { revalidatePath } from 'next/cache';

import { enhanceAction } from '@kit/next/actions';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { RemoveMemberSchema } from '~/lib/communities/members/schema/remove-member.schema';
import { TransferOwnershipConfirmationSchema } from '~/lib/communities/members/schema/transfer-ownership-confirmation.schema';
import { UpdateMemberRoleSchema } from '~/lib/communities/members/schema/update-member-role.schema';

import { createCommunityMembersService } from '../community-members.service';

/**
 * @name removeMemberFromCommunityAction
 * @description Removes a member from an community.
 */
export const removeMemberFromCommunityAction = enhanceAction(
  async ({ communityId, userId }) => {
    const client = getSupabaseServerClient();
    const service = createCommunityMembersService(client);

    await service.removeMemberFromCommunity({
      communityId,
      userId,
    });

    // revalidate all pages that depend on the community
    revalidatePath('/[community]', 'layout');

    return { success: true };
  },
  {
    schema: RemoveMemberSchema,
  },
);

/**
 * @name updateMemberRoleAction
 * @description Updates the role of a member in a community.
 */
export const updateMemberRoleAction = enhanceAction(
  async (data) => {
    const client = getSupabaseServerClient();
    const service = createCommunityMembersService(client);
    const adminClient = getSupabaseServerAdminClient();

    // update the role of the member
    await service.updateMemberRole(data, adminClient);

    // revalidate all pages that depend on the community
    revalidatePath('/[community]', 'layout');

    return { success: true };
  },
  {
    schema: UpdateMemberRoleSchema,
  },
);

/**
 * @name transferOwnershipAction
 * @description Transfers the ownership of an community to another member.
 */
export const transferOwnershipAction = enhanceAction(
  async (data) => {
    const client = getSupabaseServerClient();

    // assert that the user is the owner of the community
    const { data: isOwner, error } = await client.rpc('is_community_owner', {
      community_id: data.communityId,
    });

    if (error ?? !isOwner) {
      throw new Error(
        `You must be the owner of the community to transfer ownership`,
      );
    }

    const service = createCommunityMembersService(client);

    // at this point, the user is authenticated and is the owner of the community
    // so we proceed with the transfer of ownership with admin privileges
    const adminClient = getSupabaseServerAdminClient();

    // transfer the ownership of the community
    await service.transferOwnership(data, adminClient);

    // revalidate all pages that depend on the community
    revalidatePath('/[community]', 'layout');

    return {
      success: true,
    };
  },
  {
    schema: TransferOwnershipConfirmationSchema,
  },
);
