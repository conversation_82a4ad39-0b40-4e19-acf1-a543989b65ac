'use client';

import { useQuery } from '@tanstack/react-query';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';

export function useFetchTeamMembers(communityId: string) {
  const supabase = useSupabase();
  const queryKey = ['community_memberships', communityId];

  const queryFn = async () => {
    const { data, error } = await supabase.rpc('get_community_team_members', {
      p_community_id: communityId,
    });

    if (error) {
      throw error;
    }

    return data;
  };

  return useQuery({
    queryKey,
    queryFn,
  });
}
