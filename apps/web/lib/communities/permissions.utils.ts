import type { PermissionsEnum } from '~/lib/communities/members/types';

/**
 * Checks if a list of permissions includes a specific permission.
 *
 * @param permissions - The array of permissions the user has.
 * @param permissionToCheck - The specific permission to check for.
 * @returns True if the permission is included, false otherwise.
 */
export function hasCommunityPermission(
  permissions: PermissionsEnum[] | null | undefined,
  permissionToCheck: PermissionsEnum,
): boolean {
  if (!permissions) return false;
  const permissionSet = new Set(permissions);
  return permissionSet.has(permissionToCheck);
}
