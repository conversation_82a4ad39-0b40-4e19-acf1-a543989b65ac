import { ObjectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

// Base type for a community page row (snake_case)
type CommunityPageRow = Database['public']['Tables']['community_pages']['Row'];
// Base type for a community page media row (snake_case)
type CommunityPageMediaRow =
  Database['public']['Tables']['community_page_media']['Row'];

// Represents a single media item in camelCase
export type CommunityPageMedia = ObjectToCamel<CommunityPageMediaRow>;

// Represents a community page in camelCase (without media)
export type CommunityPage = ObjectToCamel<CommunityPageRow>;

// Represents a community page with its associated media, all in camelCase
export type CommunityPageWithMedia = CommunityPage & {
  communityPageMedia: CommunityPageMedia[];
};

export type CommunityPageMediaTypes =
  Database['public']['Enums']['community_page_media_type'];
