import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import { Database, Json } from '~/lib/database.types';
import { ImageStorageService } from '~/lib/images/services/image-storage.service';

import * as implementations from './implementations';
import type { CommunityPageMediaTypes, CommunityPageWithMedia } from './types';

/**
 * Creates a new instance of the CommunityPagesService
 * @param client Supabase client
 * @param imageService Optional image service for managing media files
 * @returns A new CommunityPagesService instance
 */
export function createCommunityPagesService(
  client: SupabaseClient<Database>,
  imageService?: ImageStorageService,
) {
  return new CommunityPagesService(client, imageService);
}

/**
 * Service for managing community pages and associated media
 */
class CommunityPagesService {
  constructor(
    private readonly client: SupabaseClient<Database>,
    private readonly imageService?: ImageStorageService,
  ) {}

  /**
   * Fetches pages for a community with pagination and optional search
   */
  async getPages(params: {
    communityId: string;
    page: number;
    limit?: number;
    query?: string;
  }): Promise<{
    data: CommunityPageWithMedia[];
    count: number;
    pageSize: number;
    page: number;
    pageCount: number;
  }> {
    return implementations.getPages(this.client, params);
  }

  /**
   * Fetches a community page by its ID
   */
  async getPageById(params: {
    pageId: string;
    communityId: string;
  }): Promise<CommunityPageWithMedia | null> {
    return implementations.getPageById(this.client, params);
  }

  /**
   * Fetches a community page by its slug
   */
  async getPageBySlug(params: {
    communityId: string;
    slug: string;
  }): Promise<CommunityPageWithMedia | null> {
    return implementations.getPageBySlug(this.client, params);
  }

  /**
   * Fetches a system page by its slug
   */
  async getSystemPage(params: {
    communityId: string;
    pageSlug: string;
  }): Promise<CommunityPageWithMedia | null> {
    return implementations.getSystemPage(this.client, params);
  }

  /**
   * Adds media to a community page
   */
  async addPageMedia(params: {
    pageId: string;
    mediaType: CommunityPageMediaTypes;
    url: string;
    altText?: string | null;
    caption?: string | null;
    displayOrder: number;
    dimensions?: Record<string, Json | undefined>;
    createdBy: string;
    thumbnailUrl?: string | null;
  }) {
    return implementations.addPageMedia(this.client, params);
  }

  /**
   * Gets all media associated with a specific page, ordered by display_order
   */
  async getPageMedia(params: { pageId: string }) {
    return implementations.getPageMedia(this.client, params);
  }

  /**
   * Deletes a media record by ID and optionally the associated file from storage
   */
  async deletePageMedia(params: { mediaId: string }) {
    return implementations.deletePageMedia(
      this.client,
      this.imageService,
      params,
    );
  }
}
