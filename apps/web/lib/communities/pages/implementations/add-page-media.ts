import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import type { CommunityPageMediaTypes } from '~/lib/communities/pages/types';
import { Database, Json } from '~/lib/database.types';

const NAMESPACE = 'pages.service';

/**
 * Adds media to a community page
 *
 * @param client Supabase client
 * @param params Media parameters
 * @returns Success status
 */
export async function addPageMedia(
  client: SupabaseClient<Database>,
  params: {
    pageId: string;
    mediaType: CommunityPageMediaTypes;
    url: string;
    altText?: string | null;
    caption?: string | null;
    displayOrder: number;
    dimensions?: Record<string, Json | undefined>;
    createdBy: string;
    thumbnailUrl?: string | null;
  },
): Promise<{ success: boolean }> {
  const logger = await getLogger();

  const ctx = {
    namespace: NAMESPACE,
    function: 'addPageMedia',
    ...params,
  };

  logger.info(ctx, 'Adding page media...');

  const { error } = await client.from('community_page_media').insert([
    {
      page_id: params.pageId,
      media_type: params.mediaType,
      url: params.url,
      alt_text: params.altText,
      caption: params.caption,
      display_order: params.displayOrder,
      dimensions: params.dimensions ?? {},
      created_by: params.createdBy,
      thumbnail_url: params.thumbnailUrl,
    },
  ]);

  if (error) {
    logger.error({ ...ctx, error }, 'Error adding page media:', { ...params });
    throw error;
  }

  return { success: true };
}
