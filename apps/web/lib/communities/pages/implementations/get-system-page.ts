import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { CommunityPageWithMedia } from '~/lib/communities/pages/types';
import { Database } from '~/lib/database.types';

const NAMESPACE = 'pages.service';

/**
 * Fetches a system page by its slug
 *
 * @param client Supabase client
 * @param params Query parameters
 * @returns System page with media or null if not found
 */
export async function getSystemPage(
  client: SupabaseClient<Database>,
  params: {
    communityId: string;
    pageSlug: string;
  },
): Promise<CommunityPageWithMedia | null> {
  const logger = await getLogger();

  const ctx = {
    namespace: NAMESPACE,
    function: 'getSystemPage',
    ...params,
  };

  logger.info(ctx, 'Getting system page...');

  const { data, error } = await client
    .from('community_pages')
    .select(
      `
      *,
      community_page_media(*)
    `,
    )
    .eq('community_id', params.communityId)
    .eq('page_type', 'system')
    .eq('slug', params.pageSlug)
    .single<CommunityPageWithMedia>();

  if (error) {
    if (error.code === 'PGRST116') {
      console.warn(
        `System page not found for slug "${params.pageSlug}" in community ${params.communityId}`,
      );
      return null;
    }
    logger.error(ctx, 'Error getting system page:', { error, ...params });
    throw error;
  }

  if (!data) {
    return null;
  }

  return objectToCamel(data) as CommunityPageWithMedia;
}
