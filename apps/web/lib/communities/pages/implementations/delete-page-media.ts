import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import { Database } from '~/lib/database.types';
import { ImageStorageService } from '~/lib/images/services/image-storage.service';

const NAMESPACE = 'pages.service';

/**
 * Deletes a media record by ID and optionally the associated file from storage
 *
 * @param client Supabase client
 * @param imageService Optional image storage service for deleting image files
 * @param params Delete parameters
 * @returns Success status and optional already deleted flag
 */
export async function deletePageMedia(
  client: SupabaseClient<Database>,
  imageService: ImageStorageService | undefined,
  params: { mediaId: string },
): Promise<{ success: boolean; alreadyDeleted?: boolean }> {
  const logger = await getLogger();

  const ctx = {
    namespace: NAMESPACE,
    function: 'deletePageMedia',
    ...params,
  };

  logger.info(ctx, 'Deleting page media...');

  // Fetch the media record first to get its type and URL
  const { data: mediaData, error: fetchError } = await client
    .from('community_page_media')
    .select('id, media_type, url::text') // Select only needed fields, cast url to text
    .eq('id', params.mediaId)
    .single();

  if (fetchError) {
    // If the error is that the row doesn't exist (PGRST116), it might have been deleted already.
    // In this case, we can consider it a success or log a warning.
    if (fetchError.code === 'PGRST116') {
      logger.warn(ctx, 'Media record not found, possibly already deleted:', {
        mediaId: params.mediaId,
      });
      return { success: true, alreadyDeleted: true };
    }
    logger.error(ctx, 'Error fetching media before delete:', {
      error: fetchError,
      mediaId: params.mediaId,
    });
    throw fetchError;
  }

  // Ensure mediaData is not null before proceeding
  if (!mediaData) {
    logger.warn(ctx, 'Media data was unexpectedly null after fetch:', {
      mediaId: params.mediaId,
    });
    // Consider this case as if it were already deleted
    return { success: true, alreadyDeleted: true };
  }

  // Attempt to delete from storage if it's an image with a URL
  if (mediaData.media_type === 'image' && mediaData.url) {
    const imageUrl = mediaData.url; // Assign to a new variable to satisfy TypeScript

    // Check if imageService was provided
    if (!imageService) {
      logger.error(
        ctx,
        'ImageStorageService was not provided to CommunityPagesService. Cannot delete image from storage.',
      );
    } else {
      // Proceed with deletion only if the service exists
      try {
        // Parse the URL to get the storage path and bucket
        const url = new URL(imageUrl);
        const pathSegments = url.pathname.split('/');

        // Ensure we have enough segments and identify the bucket and path
        const bucketIndex =
          pathSegments.findIndex((seg) => seg === 'public') + 1;
        if (bucketIndex > 0 && pathSegments.length > bucketIndex + 1) {
          const bucketName = pathSegments[bucketIndex]!; // Assert non-null
          const storagePath = pathSegments.slice(bucketIndex + 1).join('/');

          await imageService.deleteImage(storagePath, bucketName);
          logger.info(ctx, 'Deleted image from storage:', {
            path: storagePath,
            bucket: bucketName,
          });
        } else {
          logger.warn(ctx, 'Could not parse storage path from URL:', imageUrl);
        }
      } catch (storageError) {
        // Log storage deletion errors but don't block DB deletion
        logger.error(
          ctx,
          'Failed to delete image from storage (continuing with DB deletion):',
          {
            error: storageError,
            mediaId: params.mediaId,
            url: imageUrl,
          },
        );
      }
    }
  }

  // Delete the record from the database
  logger.info(ctx, 'Deleting media record from database...');

  const { error: deleteError } = await client
    .from('community_page_media')
    .delete()
    .eq('id', params.mediaId);

  if (deleteError) {
    logger.error(ctx, 'Error deleting media record from database:', {
      error: deleteError,
      mediaId: params.mediaId,
    });
    throw deleteError;
  }

  return { success: true };
}
