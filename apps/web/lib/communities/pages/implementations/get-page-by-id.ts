import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { CommunityPageWithMedia } from '~/lib/communities/pages/types';
import { Database } from '~/lib/database.types';

const NAMESPACE = 'pages.service';

/**
 * Fetches a community page by its ID
 *
 * @param client Supabase client
 * @param params Query parameters
 * @returns Community page with media or null if not found
 */
export async function getPageById(
  client: SupabaseClient<Database>,
  params: {
    pageId: string;
    communityId: string;
  },
): Promise<CommunityPageWithMedia | null> {
  const logger = await getLogger();

  const ctx = {
    namespace: NAMESPACE,
    function: 'getPageById',
    ...params,
  };

  logger.info(ctx, 'Getting page by id...');

  const { data, error } = await client
    .from('community_pages')
    .select(
      `
      *,
      community_page_media(*)
    `,
    )
    .eq('id', params.pageId)
    .eq('community_id', params.communityId)
    .neq('page_type', 'system')
    .single<CommunityPageWithMedia>();

  if (error) {
    if (error.code === 'PGRST116') {
      console.warn(
        `Page not found for id "${params.pageId}" in community ${params.communityId}`,
      );
      return null;
    }
    logger.error({ ...ctx, error }, 'Error getting page by id:', { ...params });
    throw error;
  }

  if (!data) {
    return null;
  }

  return objectToCamel(data) as CommunityPageWithMedia;
}
