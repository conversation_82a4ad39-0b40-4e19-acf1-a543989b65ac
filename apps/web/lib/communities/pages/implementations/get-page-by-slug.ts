import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { CommunityPageWithMedia } from '~/lib/communities/pages/types';
import { Database } from '~/lib/database.types';

const NAMESPACE = 'pages.service';

/**
 * Fetches a community page by its slug
 *
 * @param client Supabase client
 * @param params Query parameters
 * @returns Community page with media or null if not found
 */
export async function getPageBySlug(
  client: SupabaseClient<Database>,
  params: {
    communityId: string;
    slug: string;
  },
): Promise<CommunityPageWithMedia | null> {
  const logger = await getLogger();

  const ctx = {
    namespace: NAMESPACE,
    function: 'getPageBySlug',
    ...params,
  };

  logger.info(ctx, 'Getting page by slug...');

  const { data, error } = await client
    .from('community_pages')
    .select(
      `
      *,
      community_page_media(*)
    `,
    )
    .eq('community_id', params.communityId)
    .eq('slug', params.slug)
    .neq('page_type', 'system')
    // Use maybeSingle as a non-system page with a given slug might not exist
    .maybeSingle<CommunityPageWithMedia>();

  if (error) {
    logger.error(ctx, 'Error getting page by slug:', { error, ...params });
    throw error;
  }

  if (!data) {
    return null;
  }

  return objectToCamel(data) as CommunityPageWithMedia;
}
