import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import { Database } from '~/lib/database.types';

// Define a type for page media items
type PageMediaItem = {
  id: string;
  pageId: string;
  mediaType: string;
  url: string;
  altText?: string | null;
  caption?: string | null;
  displayOrder: number;
  dimensions: Record<string, unknown>;
  createdBy: string;
  createdAt: string;
  thumbnailUrl?: string | null;
};

const NAMESPACE = 'pages.service';

/**
 * Gets all media associated with a specific page, ordered by display_order
 *
 * @param client Supabase client
 * @param params Query parameters
 * @returns Array of page media objects
 */
export async function getPageMedia(
  client: SupabaseClient<Database>,
  params: { pageId: string },
): Promise<PageMediaItem[]> {
  const logger = await getLogger();

  const ctx = {
    namespace: NAMESPACE,
    function: 'getPageMedia',
    ...params,
  };

  logger.info(ctx, 'Getting page media...');

  const { data, error } = await client
    .from('community_page_media')
    .select('*')
    .eq('page_id', params.pageId)
    .order('display_order', { ascending: true });

  if (error) {
    logger.error(ctx, 'Error getting page media:', {
      error,
      pageId: params.pageId,
    });
    throw error;
  }

  return objectToCamel(data ?? []) as PageMediaItem[];
}
