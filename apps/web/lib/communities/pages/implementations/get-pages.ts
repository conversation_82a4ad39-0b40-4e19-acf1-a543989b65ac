import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { CommunityPageWithMedia } from '~/lib/communities/pages/types';
import { Database } from '~/lib/database.types';

const NAMESPACE = 'pages.service';

/**
 * Fetches pages for a community with pagination and optional search
 *
 * @param client Supabase client
 * @param params Query parameters
 * @returns Paginated list of community pages with media
 */
export async function getPages(
  client: SupabaseClient<Database>,
  params: {
    communityId: string;
    page: number;
    limit?: number;
    query?: string;
  },
): Promise<{
  data: CommunityPageWithMedia[];
  count: number;
  pageSize: number;
  page: number;
  pageCount: number;
}> {
  const logger = await getLogger();

  const ctx = {
    namespace: NAMESPACE,
    function: 'getPages',
    ...params,
  };

  logger.info(ctx, 'Fetching pages...');

  const limit = params.limit ?? 10;
  const startOffset = (params.page - 1) * limit;
  const endOffset = startOffset + limit - 1;

  let query = client
    .from('community_pages')
    .select(
      `
      *,
      community_page_media(*)
    `,
      {
        count: 'exact',
      },
    )
    .eq('community_id', params.communityId)
    .neq('page_type', 'system')
    .order('created_at', { ascending: false })
    .range(startOffset, endOffset);

  if (params.query) {
    query = query.textSearch('title', `"${params.query}"`);
  }

  const { data, error, count } = await query;

  if (error) {
    logger.error(ctx, 'Error getting pages:', { error, ...params });
    throw error;
  }

  const camelCaseData = (data ?? []).map((page) =>
    objectToCamel(page),
  ) as CommunityPageWithMedia[];

  return {
    data: camelCaseData,
    count: count ?? 0,
    pageSize: limit,
    page: params.page,
    pageCount: Math.ceil((count ?? 0) / limit),
  };
}
