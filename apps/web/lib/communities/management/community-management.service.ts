import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import type { Database } from '~/lib/database.types';

import * as implementations from './implementations';

export function createCommunityManagementService(
  client: SupabaseClient<Database>,
) {
  return new CommunityManagementService(client);
}

/**
 * @name CommunityManagementService
 * @description Service for managing communities including creation, deletion, and leaving.
 */
class CommunityManagementService {
  constructor(private readonly client: SupabaseClient<Database>) {}

  /**
   * Creates a new community with the given parameters
   */
  async createNewCommunity(params: {
    name: string;
    communityId: string;
    primaryOwnerUserId: string;
  }) {
    return implementations.createNewCommunity(this.client, params);
  }

  async updateCommunitySubscription(params: {
    id: string;
    data: {
      subscriptionId: string;
    };
  }) {
    return implementations.updateCommunitySubscription(this.client, params);
  }

  /**
   * Deletes a community. Permissions are not checked here, as they are
   * checked in the server action.
   *
   * USE WITH CAUTION. THE USER MUST HAVE THE NECESSARY PERMISSIONS.
   */
  // TODO: Add permissions check and remove Stripe billing, cancel any members subscriptions
  async deleteCommunity(params: { communityId: string; userId: string }) {
    return implementations.deleteCommunity(this.client, params);
  }
}
