import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'community-management';

/**
 * Updates a community's subscription
 * @param client - The Supabase client
 * @param params - Parameters for updating the subscription
 */
export async function updateCommunitySubscription(
  client: SupabaseClient<Database>,
  params: {
    id: string;
    data: {
      subscriptionId: string;
    };
  },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    function: 'updateCommunitySubscription',
    ...params,
  };

  logger.info(ctx, `Updating community subscription...`);

  const { error } = await client
    .from('communities')
    .update({
      subscription_id: params.data.subscriptionId,
    })
    .eq('id', params.id);

  if (error) {
    logger.error(
      {
        error,
        ...ctx,
      },
      `Error updating community subscription`,
    );

    throw new Error('Error updating community subscription');
  }

  logger.info(ctx, `Community subscription updated successfully`);
}
