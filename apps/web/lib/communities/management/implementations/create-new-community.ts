import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';
import { getStripeProductsService } from '~/lib/stripe/services';

const NAMESPACE = 'community-management';

/**
 * Creates a new community with the given parameters
 * @param client - The Supabase client
 * @param params - Parameters for creating the community
 * @returns The created community data
 */
export async function createNewCommunity(
  client: SupabaseClient<Database>,
  params: { name: string; communityId: string; primaryOwnerUserId: string },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    function: 'createNewCommunity',
    ...params,
  };

  logger.info(ctx, `Check if community exists...`);

  const { data: community, error: communityError } = await client
    .from('communities')
    .select('*')
    .eq('id', params.communityId)
    .single();

  if (community?.id) {
    logger.info(
      {
        error: communityError,
        ...ctx,
      },
      `Community already exists`,
    );
    return { data: objectToCamel(community), error: communityError };
  }

  logger.info(ctx, `Creating new community...`);

  const { error, data } = await client.rpc('create_community', {
    p_community_name: params.name,
    p_community_id: params.communityId,
    p_primary_owner_user_id: params.primaryOwnerUserId,
  });

  if (error) {
    logger.error(
      {
        error,
        ...ctx,
      },
      `Error creating community`,
    );

    throw new Error('Error creating community');
  }

  // Create a default product
  try {
    const stripeProductService = await getStripeProductsService(client);
    const { product, price } =
      await stripeProductService.createCommunityDefaultProduct(data.id);

    if (!product || !price) {
      logger.error(
        {
          ...ctx,
        },
        `Error creating default product`,
      );

      throw new Error('Error creating default product');
    }
    logger.info(ctx, `Successfully created default product`);
  } catch (error) {
    logger.error(
      {
        error,
        ...ctx,
      },
      `Error creating default product`,
    );

    throw new Error('Error creating default product');
  }

  logger.info(ctx, `Community created successfully`);

  return { data: objectToCamel(data), error };
}
