import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'community-management';

/**
 * Deletes a community. Permissions are not checked here, as they are
 * checked in the server action.
 *
 * USE WITH CAUTION. THE USER MUST HAVE THE NECESSARY PERMISSIONS.
 * @param client - The Supabase client
 * @param params - Parameters for deleting the community
 */
export async function deleteCommunity(
  client: SupabaseClient<Database>,
  params: { communityId: string; userId: string },
) {
  const logger = await getLogger();

  const ctx = {
    communityId: params.communityId,
    function: 'deleteCommunity',
    userId: params.userId,
    name: NAMESPACE,
  };

  logger.info(ctx, `Requested community deletion. Processing...`);

  /**
   * Note that after this will fire a db webhook that will cancel stripe subscriptions & delete the community
   */
  const { error } = await client
    .from('communities')
    .delete()
    .eq('id', params.communityId);

  if (error) {
    logger.error(
      {
        ...ctx,
        error,
      },
      'Failed to delete community',
    );

    throw new Error('Failed to delete community');
  }

  logger.info(ctx, 'Successfully deleted community');
}
