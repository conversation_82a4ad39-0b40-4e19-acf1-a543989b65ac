import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'billing.community';

/**
 * Get the billing stripe account ID for the given community by following the relationship:
 * community -> subscription -> user -> stripe_account_id
 * @param client - The Supabase client
 * @param communityId - The ID of the community
 * @returns The stripe account ID if found
 */
export async function getUserStripeAccountId(
  client: SupabaseClient<Database>,
  communityId: string,
): Promise<string | undefined> {
  const logger = await getLogger();

  logger.info(
    {
      communityId,
      name: NAMESPACE,
    },
    `Getting stripe account ID for community...`,
  );
  const { data, error } = await client
    .from('communities')
    .select(
      `
        subscription_id,
        subscriptions!communities_subscription_id_fkey (
          users!inner (
            stripe_account_id
          )
        )
      `,
    )
    .eq('id', communityId)
    .maybeSingle();

  if (error) {
    logger.error(
      {
        communityId,
        name: NAMESPACE,
        error,
      },
      `Failed to get stripe account ID for community`,
    );

    throw new Error('Failed to get stripe account ID');
  }

  // NB: Supabase returns nested objects here despite types expecting arrays
  // when using maybeSingle() and finding exactly one related record.

  const stripeAccountId = data?.subscriptions?.users?.stripe_account_id;

  return stripeAccountId ?? undefined;
}
