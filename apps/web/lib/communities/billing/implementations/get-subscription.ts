import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'billing.community';

/**
 * Get the subscription data and its items for the community, based on the following relationship:
 * community -> subscription -> subscription_items
 * @param client - The Supabase client
 * @param communityId - The ID of the community
 * @returns The subscription data or null if not found
 */
export async function getSubscription(
  client: SupabaseClient<Database>,
  communityId: string,
) {
  const logger = await getLogger();

  const ctx = {
    communityId,
    name: NAMESPACE,
  };

  logger.info(ctx, `Getting subscription for community...`);

  const { data, error } = await client
    .from('communities')
    .select(
      `
        subscription_id,
        subscriptions!communities_subscription_id_fkey (
          id,
          status,
          active,
          cancel_at_period_end,
          currency,
          current_period_start,
          current_period_end,
          trial_starts_at,
          trial_ends_at,
          items: subscription_items (
            id,
            subscription_id,
            product_id,
            variant_id,
            type,
            price_amount,
            quantity,
            interval,
            interval_count
          )
        )
      `,
    )
    .eq('id', communityId)
    .maybeSingle();

  if (error) {
    logger.error(
      {
        ...ctx,
        error,
      },
      `Failed to get subscription`,
    );
    throw new Error('Failed to get subscription');
  }
  if (!data || !data.subscriptions) {
    return null;
  }

  logger.info(
    {
      ...ctx,
      data: data.subscriptions,
    },
    `Subscription retrieved`,
  );
  return objectToCamel(data.subscriptions);
}
