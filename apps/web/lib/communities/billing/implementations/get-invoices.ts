import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'billing.community';

/**
 * Get the invoices data for the given community
 * @param client - The Supabase client
 * @param communityId - The ID of the community
 * @returns The invoice data or null if not found
 */
export async function getInvoices(
  client: SupabaseClient<Database>,
  communityId: string,
) {
  const logger = await getLogger();

  const ctx = {
    communityId,
    name: NAMESPACE,
  };

  logger.info(ctx, `Getting invoice for community...`);

  const response = await client
    .from('invoices')
    .select('*, items: invoice_items !inner (*)')
    .eq('community_id', communityId)
    .order('created_at', { ascending: false });

  if (response.error) {
    logger.error(
      {
        ...ctx,
        error: response.error,
      },
      `Failed to get invoice`,
    );
    throw new Error('Failed to get invoice');
  }

  if (!response.data) {
    return null;
  }

  logger.info(
    {
      ...ctx,
      data: response.data,
    },
    `Invoice retrieved`,
  );
  return objectToCamel(response.data);
}
