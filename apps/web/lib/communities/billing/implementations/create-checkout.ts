import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { z } from 'zod';

import { getLogger } from '@kit/shared/logger';
import { requireUser } from '@kit/supabase/require-user';

import pathsConfig from '~/config/paths.config';
import { createCommunityService } from '~/lib/communities/community/community.service';
import type { Database } from '~/lib/database.types';
import { getStripeBillingService } from '~/lib/stripe/services';

import { getCommunityUrl } from '../../utils/get-community-url';
import { CommunityCheckoutSchema } from '../schema/community-billing.schema';
import { getPlatformProducts } from './get-platform-products';
import { getUserStripeAccountId } from './get-stripe-account-id';

const NAMESPACE = 'billing.community';

/**
 * Creates a checkout session for a Community
 * @param client - The Supabase client
 * @param params - The checkout parameters
 * @returns The checkout token
 */
export async function createCheckout(
  client: SupabaseClient<Database>,
  params: z.infer<typeof CommunityCheckoutSchema>,
) {
  const { data: user } = await requireUser(client);

  if (!user) {
    throw new Error('Authentication required');
  }

  const userId = user.id;
  const communityId = params.communityId;
  const logger = await getLogger();

  const ctx = {
    userId,
    communityId,
    name: NAMESPACE,
  };

  logger.info(ctx, `Requested checkout session. Processing...`);

  const communityService = createCommunityService(client);

  // verify permissions to manage billing
  const hasPermission = await communityService.hasCommunityPermissionCheck({
    userId,
    communityId,
    permission: 'billing.manage',
  });

  // if the user does not have permission to manage billing for the community
  // then we should not proceed
  if (!hasPermission) {
    logger.warn(ctx, `User without permissions attempted to create checkout.`);

    throw new Error('Permission denied');
  }

  // Load billing products
  const products = await getPlatformProducts(client);

  // Find product with the given ID
  const product = products.find((item) => item.id === params.productId);
  if (!product) {
    throw new Error('Product not found');
  }

  // Find the price with the given ID
  let selectedPrice;
  if (product.productPrices) {
    selectedPrice = product.productPrices.find(
      (price) => price.id === params.priceId,
    );
  }

  if (!selectedPrice) {
    throw new Error('Price not found');
  }

  // Initialize the Stripe billing service
  // Use minimal config for backwards compatibility
  const stripeBillingService = await getStripeBillingService(client);

  // find the stripe account ID for the community if it exists
  // (eg. if the community has been billed before)
  const stripeAccountId = await getUserStripeAccountId(client, communityId);
  const customerEmail = user.email;

  // the return URL for the checkout session
  const returnUrl = await getCommunityUrl(
    pathsConfig.app.communityBillingReturn,
    params.slug,
  );

  logger.info(
    {
      ...ctx,
      priceId: selectedPrice.id,
    },
    `Creating checkout session...`,
  );

  try {
    // Create a single line item directly from the Stripe price ID
    const lineItems = [
      {
        id: selectedPrice.id, // This is the ID Stripe will recognize
        name: selectedPrice.nickname || 'Subscription',
        cost: (selectedPrice.unitAmount || 0) / 100, // Convert from cents to dollars, handle null case
        type: 'flat' as const,
      },
    ];

    // Create price data from the selected price with proper type handling
    const priceData = {
      id: selectedPrice.id,
      name: selectedPrice.nickname || selectedPrice.id,
      interval: selectedPrice.interval as 'month' | 'year' | undefined,
      paymentType:
        selectedPrice.type === 'recurring'
          ? ('recurring' as const)
          : ('one_time' as const),
      lineItems: lineItems, // Use our newly created line items
    };

    // call the payment gateway to create the checkout session
    const { checkoutToken } = await stripeBillingService.createCheckoutSession({
      userId,
      communityId,
      price: priceData,
      returnUrl,
      customerEmail,
      stripeAccountId,
    });

    // return the checkout token to the client
    // so we can call the payment gateway to complete the checkout
    return {
      checkoutToken,
    };
  } catch (error) {
    logger.error(
      {
        ...ctx,
        error,
      },
      `Error creating the checkout session`,
    );

    throw new Error(`Checkout not created`);
  }
}
