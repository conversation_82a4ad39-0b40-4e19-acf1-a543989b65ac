import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import type { Database } from '~/lib/database.types';
import { getStripeProductsService } from '~/lib/stripe/services';

const NAMESPACE = 'billing.community';

/**
 * Get billing products with prices and line items from the database
 * This is a direct replacement for the billingConfig approach
 *
 * @param client - The Supabase client
 * @param options - Filter options
 * @returns Array of platform products with their prices and line items
 */
export async function getPlatformProducts(
  client: SupabaseClient<Database>,
  options: {
    productId?: string;
    activeOnly?: boolean;
    withPrices?: boolean;
  } = {},
) {
  const logger = await getLogger();
  const productsService = getStripeProductsService(client);

  logger.info(
    {
      options,
      name: NAMESPACE,
    },
    `Getting platform products...`,
  );
  return productsService.getProducts({
    ...options,
    seller: 'platform',
  });
}
