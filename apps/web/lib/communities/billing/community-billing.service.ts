import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { z } from 'zod';

import type { Database } from '~/lib/database.types';

import * as implementations from './implementations';
import { CommunityCheckoutSchema } from './schema/community-billing.schema';

export function createCommunityBillingService(
  client: SupabaseClient<Database>,
) {
  return new CommunityBillingService(client);
}

/**
 * @name CommunityBillingService
 * @description Service for managing billing for communities.
 */
class CommunityBillingService {
  private readonly client: SupabaseClient<Database>;

  constructor(client: SupabaseClient<Database>) {
    this.client = client;
  }

  /**
   * @name createCheckout
   * @description Creates a checkout session for a Community
   */
  async createCheckout(params: z.infer<typeof CommunityCheckoutSchema>) {
    return implementations.createCheckout(this.client, params);
  }

  /**
   * @name createBillingPortalSession
   * @description Creates a new billing portal session for a community
   * @param communityId
   * @param slug
   */
  async createBillingPortalSession({
    communityId,
    slug,
  }: {
    communityId: string;
    slug: string;
  }) {
    return implementations.createBillingPortalSession({
      communityId,
      slug,
    });
  }

  /**
   * @name getUserStripeAccountId
   * @description Get the stripe account id for the given community by following the relationship:
   * community -> subscription -> user -> stripe_account_id
   * @param communityId
   */
  async getUserStripeAccountId(communityId: string) {
    return implementations.getUserStripeAccountId(this.client, communityId);
  }

  /**
   * Get billing products with prices and line items from the database
   * This is a direct replacement for the billingConfig approach
   *
   * @param options - Filter options
   * @returns Array of platform products with their prices and line items
   */
  async getPlatformProducts(
    options: {
      productId?: string;
      activeOnly?: boolean;
      withPrices?: boolean;
    } = {},
  ) {
    return implementations.getPlatformProducts(this.client, options);
  }

  /**
   * @name getSubscription
   * @description Get the subscription data and its items for the community, based on the following relationship:
   * community -> subscription -> subscription_items
   * @param communityId
   */
  async getSubscription(communityId: string) {
    return implementations.getSubscription(this.client, communityId);
  }

  /**
   * @name getInvoices
   * @description Get the invoice data for the given community.
   * @param communityId
   */
  async getInvoices(communityId: string) {
    return implementations.getInvoices(this.client, communityId);
  }
}
