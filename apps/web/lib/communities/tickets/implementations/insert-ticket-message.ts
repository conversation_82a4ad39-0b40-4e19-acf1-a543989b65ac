import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import { TicketAuthor } from '~/lib/communities/tickets/types';
import { Database } from '~/lib/database.types';

const NAMESPACE = 'tickets.service';

/**
 * Inserts a new message for a ticket
 *
 * @param client Supabase client
 * @param params Message parameters
 * @returns Created message
 */
export async function insertTicketMessage(
  client: SupabaseClient<Database>,
  params: {
    content: string;
    ticketId: string;
    userId: string;
    author: TicketAuthor;
  },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    function: 'insertTicketMessage',
    ...params,
  };

  logger.info(ctx, 'Inserting ticket message...');

  const response = await client
    .from('community_ticket_messages')
    .insert({
      content: params.content,
      ticket_id: params.ticketId,
      created_by_user_id: params.userId,
      author: params.author,
    })
    .select('*, user: created_by_user_id (email, picture_url, name)')
    .single();

  if (response.error) {
    logger.error(ctx, 'Error inserting ticket message:', {
      error: response.error,
    });
    throw new Error(response.error.message);
  }

  return objectToCamel(response.data);
}
