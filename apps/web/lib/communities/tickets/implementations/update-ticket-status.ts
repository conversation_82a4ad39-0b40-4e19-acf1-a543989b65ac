import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import { TicketStatus } from '~/lib/communities/tickets/types';
import { Database } from '~/lib/database.types';

const NAMESPACE = 'tickets.service';

/**
 * Updates the status of a ticket
 *
 * @param client Supabase client
 * @param params Update parameters
 * @returns Success status and ticket ID
 */
export async function updateTicketStatus(
  client: SupabaseClient<Database>,
  params: { ticketId: string; status: TicketStatus },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    function: 'updateTicketStatus',
    ...params,
  };

  logger.info(ctx, 'Updating ticket status...');

  const response = await client
    .from('community_tickets')
    .update({
      status: params.status,
    })
    .eq('id', params.ticketId)
    .select('id') // Select only ID to confirm update
    .single();

  if (response.error) {
    logger.error(ctx, 'Error updating ticket status:', {
      error: response.error,
    });
    throw new Error(response.error.message);
  }

  return { success: true, ticketId: response.data?.id };
}
