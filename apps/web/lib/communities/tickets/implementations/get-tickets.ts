import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import { Database } from '~/lib/database.types';

const NAMESPACE = 'tickets.service';

/**
 * Fetches tickets for a community with pagination and optional search
 *
 * @param client Supabase client
 * @param params Query parameters
 * @returns Paginated list of tickets
 */
export async function getTickets(
  client: SupabaseClient<Database>,
  params: {
    communitySlug: string;
    page: number;
    limit?: number;
    query?: string;
  },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    function: 'getTickets',
    ...params,
  };

  logger.info(ctx, 'Getting tickets...');

  const limit = params.limit ?? 10;
  const startOffset = (params.page - 1) * limit;
  const endOffset = startOffset + limit;

  let query = client
    .from('community_tickets')
    .select('*, community_id !inner (slug)', {
      count: 'exact',
    })
    .eq('community_id.slug', params.communitySlug)
    .order('created_at', { ascending: false })
    .range(startOffset, endOffset);

  if (params.query) {
    query = query.textSearch('title', `"${params.query}"`);
  }

  const { data, error, count } = await query;

  if (error) {
    logger.error({ ...ctx, error }, 'Error getting tickets:');
    throw error;
  }

  return {
    data: objectToCamel(data) ?? [],
    count: count ?? 0,
    pageSize: limit,
    page: params.page,
    pageCount: Math.ceil((count ?? 0) / limit),
  };
}
