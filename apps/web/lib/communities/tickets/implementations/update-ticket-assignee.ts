import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import { Database } from '~/lib/database.types';

const NAMESPACE = 'tickets.service';

/**
 * Updates the assignee of a ticket
 *
 * @param client Supabase client
 * @param params Update parameters
 * @returns Success status and ticket ID
 */
export async function updateTicketAssignee(
  client: SupabaseClient<Database>,
  params: { ticketId: string; assigneeId: string | null },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    function: 'updateTicketAssignee',
    ...params,
  };

  logger.info(ctx, 'Updating ticket assignee...');

  const response = await client
    .from('community_tickets')
    .update({
      assigned_to_user_id: params.assigneeId,
    })
    .eq('id', params.ticketId)
    .select('id') // Select only ID to confirm update
    .single();

  if (response.error) {
    logger.error(ctx, 'Error updating ticket assignee:', {
      error: response.error,
    });
    throw new Error(response.error.message);
  }

  return { success: true, ticketId: response.data?.id };
}
