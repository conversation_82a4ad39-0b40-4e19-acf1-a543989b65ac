import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import { Database, Tables } from '~/lib/database.types';

const NAMESPACE = 'tickets.service';

/**
 * Fetches a specific ticket by ID and community slug
 *
 * @param client Supabase client
 * @param params Query parameters
 * @returns Ticket details
 */
export async function getTicket(
  client: SupabaseClient<Database>,
  params: { ticketId: string; communitySlug: string },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    function: 'getTicket',
    ...params,
  };

  logger.info(ctx, 'Getting ticket...');

  const { data, error } = await client
    .from('community_tickets')
    .select<
      string,
      Tables<'community_tickets'> & {
        community_id: {
          id: string;
          slug: string;
        };
      }
    >('*, community_id !inner (slug, id)')
    .eq('id', params.ticketId)
    .eq('community_id.slug', params.communitySlug)
    .single();

  if (error) {
    logger.error({ ...ctx, error }, 'Error getting ticket:');
    throw error;
  }

  return objectToCamel(data);
}
