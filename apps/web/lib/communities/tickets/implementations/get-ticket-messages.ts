import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import { TicketMessages } from '~/lib/communities/tickets/types';
import { Database } from '~/lib/database.types';

const NAMESPACE = 'tickets.service';

/**
 * Fetches messages for a specific ticket with pagination
 *
 * @param client Supabase client
 * @param params Query parameters
 * @returns Paginated list of ticket messages
 */
export async function getTicketMessages(
  client: SupabaseClient<Database>,
  params: {
    ticketId: string;
    page: number;
    limit?: number;
  },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    function: 'getTicketMessages',
    ...params,
  };

  logger.info(ctx, 'Getting ticket messages...');

  const limit = params.limit ?? 25;
  const startOffset = (params.page - 1) * limit;
  const endOffset = startOffset + limit - 1; // range is inclusive

  const query = client
    .from('community_ticket_messages')
    .select<
      string,
      TicketMessages & {
        user: {
          email: string;
          first_name: string;
          last_name: string;
          picture_url: string;
        };
      }
    >(
      '*, user:created_by_user_id (email, first_name, last_name, picture_url)',
      {
        count: 'exact',
      },
    )
    .eq('ticket_id', params.ticketId)
    .order('created_at', { ascending: true })
    .range(startOffset, endOffset);

  const { data, error, count } = await query;

  if (error) {
    logger.error({ ...ctx, error }, 'Error getting ticket messages:');
    throw error;
  }

  return {
    // Ensure data is always an array, even if null/undefined is returned
    data: objectToCamel(data) ?? [],
    count: count ?? 0,
    pageSize: limit,
    page: params.page,
    pageCount: Math.ceil((count ?? 0) / limit),
  };
}
