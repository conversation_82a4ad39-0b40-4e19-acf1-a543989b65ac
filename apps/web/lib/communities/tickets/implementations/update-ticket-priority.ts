import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import { TicketPriority } from '~/lib/communities/tickets/types';
import { Database } from '~/lib/database.types';

const NAMESPACE = 'tickets.service';

/**
 * Updates the priority of a ticket
 *
 * @param client Supabase client
 * @param params Update parameters
 * @returns Success status and ticket ID
 */
export async function updateTicketPriority(
  client: SupabaseClient<Database>,
  params: { ticketId: string; priority: TicketPriority },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    function: 'updateTicketPriority',
    ...params,
  };

  logger.info(ctx, 'Updating ticket priority...');

  const response = await client
    .from('community_tickets')
    .update({
      priority: params.priority,
    })
    .eq('id', params.ticketId)
    .select('id') // Select only ID to confirm update
    .single();

  if (response.error) {
    logger.error(ctx, 'Error updating ticket priority:', {
      error: response.error,
    });
    throw new Error(response.error.message);
  }

  return { success: true, ticketId: response.data?.id };
}
