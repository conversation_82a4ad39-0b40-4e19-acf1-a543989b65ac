import { ObjectToCamel } from '@kit/shared/utils';

import { Database, Tables } from '~/lib/database.types';

type TicketReturn = Tables<'community_tickets'>;

export type Ticket = ObjectToCamel<TicketReturn>;

type TicketMessagesReturn = Tables<'community_ticket_messages'>;

export type TicketMessages = ObjectToCamel<TicketMessagesReturn>;

export type TicketAuthor =
  Database['public']['Enums']['community_message_author'];
export type TicketStatus =
  Database['public']['Enums']['community_ticket_status'];
export type TicketPriority =
  Database['public']['Enums']['community_ticket_priority'];
