import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import {
  TicketAuthor,
  TicketPriority,
  TicketStatus,
} from '~/lib/communities/tickets/types';
import { Database } from '~/lib/database.types';

import * as implementations from './implementations';

/**
 * Creates a new instance of the CommunityTicketsService
 * @param client Supabase client
 * @returns A new CommunityTicketsService instance
 */
export function createCommunityTicketsService(
  client: SupabaseClient<Database>,
) {
  return new CommunityTicketsService(client);
}

/**
 * Service for managing community support tickets and messages
 */
class CommunityTicketsService {
  constructor(private readonly client: SupabaseClient<Database>) {}

  /**
   * Fetches a specific ticket by ID and community slug
   */
  async getTicket(params: { ticketId: string; communitySlug: string }) {
    return implementations.getTicket(this.client, params);
  }

  /**
   * Fetches tickets for a community with pagination and optional search
   */
  async getTickets(params: {
    communitySlug: string;
    page: number;
    limit?: number;
    query?: string;
  }) {
    return implementations.getTickets(this.client, params);
  }

  /**
   * Fetches messages for a specific ticket with pagination
   */
  async getTicketMessages(params: {
    ticketId: string;
    page: number;
    limit?: number;
  }) {
    return implementations.getTicketMessages(this.client, params);
  }

  /**
   * Inserts a new message for a ticket
   */
  async insertTicketMessage(params: {
    content: string;
    ticketId: string;
    userId: string;
    author: TicketAuthor;
  }) {
    return implementations.insertTicketMessage(this.client, params);
  }

  /**
   * Updates the status of a ticket
   */
  async updateTicketStatus(params: { ticketId: string; status: TicketStatus }) {
    return implementations.updateTicketStatus(this.client, params);
  }

  /**
   * Updates the priority of a ticket
   */
  async updateTicketPriority(params: {
    ticketId: string;
    priority: TicketPriority;
  }) {
    return implementations.updateTicketPriority(this.client, params);
  }

  /**
   * Updates the assignee of a ticket
   */
  async updateTicketAssignee(params: {
    ticketId: string;
    assigneeId: string | null;
  }) {
    return implementations.updateTicketAssignee(this.client, params);
  }
}
