import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'community-invitations';

/**
 * Gets all invitations for a community.
 * @param client - The Supabase client
 * @param communityId - The ID of the community
 * @returns All invitations for the community
 */
export async function getInvitations(
  client: SupabaseClient<Database>,
  communityId: string,
) {
  const logger = await getLogger();

  const ctx = {
    communityId,
    namespace: NAMESPACE,
  };

  logger.info(ctx, 'Getting invitations...');

  const { data, error } = await client.rpc('get_community_invitations', {
    p_community_id: communityId,
  });

  if (error) {
    logger.error(
      {
        ...ctx,
        error,
      },
      'Failed to get invitations',
    );

    throw error;
  }

  logger.info(ctx, 'Invitations successfully retrieved');

  return objectToCamel(data);
}
