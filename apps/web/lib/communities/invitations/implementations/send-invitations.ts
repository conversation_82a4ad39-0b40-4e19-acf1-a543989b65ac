import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { z } from 'zod';

import { getLogger } from '@kit/shared/logger';

import type { InviteMembersSchema } from '~/lib/communities/invitations/schema/invite-members.schema';
import type { Database } from '~/lib/database.types';

import { validateInvitation } from './validate-invitation';

const NAMESPACE = 'community-invitations';

/**
 * Sends invitations to join a community.
 * @param client - The Supabase client
 * @param params - The parameters for sending invitations
 * @returns The response from adding invitations
 */
export async function sendInvitations(
  client: SupabaseClient<Database>,
  params: {
    invitations: z.infer<typeof InviteMembersSchema>['invitations'];
    communityId: string;
  },
) {
  const logger = await getLogger();

  const ctx = {
    communityId: params.communityId,
    namespace: NAMESPACE,
  };

  logger.info(ctx, 'Storing invitations...');

  try {
    await Promise.all(
      params.invitations.map((invitation) =>
        validateInvitation(client, invitation, params.communityId),
      ),
    );
  } catch (error) {
    logger.error(
      {
        ...ctx,
        error: (error as Error).message,
      },
      'Error validating invitations',
    );

    throw error;
  }

  const response = await client.rpc('add_invitations_to_community', {
    p_invitations: params.invitations,
    p_community_id: params.communityId,
  });

  if (response.error) {
    logger.error(
      {
        ...ctx,
        error: response.error,
      },
      `Failed to add invitations to community ${params.communityId}`,
    );

    throw response.error;
  }

  const responseInvitations = Array.isArray(response.data)
    ? response.data
    : [response.data];

  logger.info(
    {
      ...ctx,
      count: responseInvitations.length,
    },
    'Invitations added to community',
  );

  return responseInvitations;
}
