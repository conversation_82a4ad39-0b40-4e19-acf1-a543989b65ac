import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { addDays, formatISO } from 'date-fns';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'community-invitations';

/**
 * Renews an invitation to join a community by extending the expiration date by 7 days.
 * @param client - The Supabase client
 * @param invitationId - The ID of the invitation to renew
 * @returns The renewed invitation data or null if not found
 */
export async function renewInvitation(
  client: SupabaseClient<Database>,
  invitationId: string,
) {
  const logger = await getLogger();

  const ctx = {
    invitationId,
    namespace: NAMESPACE,
  };

  logger.info(ctx, 'Renewing invitation...');

  const sevenDaysFromNow = formatISO(addDays(new Date(), 7));

  const { data, error } = await client
    .from('community_invitations')
    .update({
      expires_at: sevenDaysFromNow,
    })
    .match({
      id: invitationId,
    });

  if (error) {
    logger.error(
      {
        ...ctx,
        error,
      },
      'Failed to renew invitation',
    );

    throw error;
  }

  logger.info(ctx, 'Invitation successfully renewed');

  return !data ? null : objectToCamel(data);
}
