import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'community-invitations';

/**
 * Get the invitation data from the invite token.
 * @param adminClient - The admin client instance. Since the user is not yet part of the community, we need to use an admin client to read the pending membership
 * @param token - The invitation token.
 * @returns The invitation data or null if not found
 */
export async function getInvitation(
  adminClient: SupabaseClient<Database>,
  token: string,
) {
  const logger = await getLogger();

  const ctx = {
    namespace: NAMESPACE,
  };

  logger.info(ctx, 'Getting invitation...');

  const { data: invitation, error } = await adminClient
    .from('community_invitations')
    .select<
      string,
      {
        id: string;
        community: {
          id: string;
          name: string;
          slug: string;
          picture_url: string;
        };
      }
    >(
      'id, expires_at, community: community_id !inner (id, name, slug, picture_url)',
    )
    .eq('invite_token', token)
    .gte('expires_at', new Date().toISOString())
    .single();

  if (error ?? !invitation) {
    return null;
  }

  return objectToCamel(invitation);
}
