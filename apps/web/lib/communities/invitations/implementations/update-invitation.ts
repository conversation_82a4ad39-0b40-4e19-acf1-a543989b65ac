import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { z } from 'zod';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { UpdateInvitationSchema } from '~/lib/communities/invitations/schema/update-invitation.schema';
import type { Database } from '~/lib/database.types';

const NAMESPACE = 'community-invitations';

/**
 * Updates an invitation in the database.
 * @param client - The Supabase client
 * @param params - The parameters for updating the invitation
 * @returns The updated invitation data or null if not found
 */
export async function updateInvitation(
  client: SupabaseClient<Database>,
  params: z.infer<typeof UpdateInvitationSchema>,
) {
  const logger = await getLogger();

  const ctx = {
    namespace: NAMESPACE,
    ...params,
  };

  logger.info(ctx, 'Updating invitation...');

  const { data, error } = await client
    .from('community_invitations')
    .update({
      role: params.role,
    })
    .match({
      id: params.invitationId,
    });

  if (error) {
    logger.error(
      {
        ...ctx,
        error,
      },
      'Failed to update invitation',
    );

    throw error;
  }

  logger.info(ctx, 'Invitation successfully updated');

  return !data ? null : objectToCamel(data);
}
