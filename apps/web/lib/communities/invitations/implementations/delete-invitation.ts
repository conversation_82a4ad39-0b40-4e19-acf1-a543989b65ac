import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { z } from 'zod';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { DeleteInvitationSchema } from '~/lib/communities/invitations/schema/delete-invitation.schema';
import type { Database } from '~/lib/database.types';

const NAMESPACE = 'community-invitations';

/**
 * Removes an invitation from the database.
 * @param client - The Supabase client
 * @param params - The parameters for deleting the invitation
 * @returns The deleted invitation data or null if not found
 */
export async function deleteInvitation(
  client: SupabaseClient<Database>,
  params: z.infer<typeof DeleteInvitationSchema>,
) {
  const logger = await getLogger();

  const ctx = {
    namespace: NAMESPACE,
    ...params,
  };

  logger.info(ctx, 'Removing invitation...');

  const { data, error } = await client
    .from('community_invitations')
    .delete()
    .match({
      id: params.invitationId,
    });

  if (error) {
    logger.error(ctx, `Failed to remove invitation`);

    throw error;
  }

  logger.info(ctx, 'Invitation successfully removed');

  return !data ? null : objectToCamel(data);
}
