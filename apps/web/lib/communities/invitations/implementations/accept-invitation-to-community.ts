import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'community-invitations';

/**
 * Accepts an invitation to join a community.
 * @param adminClient - The admin client instance
 * @param params - The parameters for accepting an invitation
 * @returns The response from accepting the invitation
 */
export async function acceptInvitationToCommunity(
  adminClient: SupabaseClient<Database>,
  params: {
    userId: string;
    inviteToken: string;
  },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    ...params,
  };

  logger.info(ctx, 'Accepting invitation to community');

  const { error, data } = await adminClient.rpc('accept_community_invitation', {
    p_token: params.inviteToken,
    p_user_id: params.userId,
  });

  if (error) {
    logger.error(
      {
        ...ctx,
        error,
      },
      'Failed to accept invitation to community',
    );

    throw error;
  }

  logger.info(ctx, 'Successfully accepted invitation to community');

  // If data is a string (like a confirmation message), return it directly
  if (typeof data === 'string') {
    return data;
  }

  return objectToCamel(data);
}
