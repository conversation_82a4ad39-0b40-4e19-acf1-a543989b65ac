import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { z } from 'zod';

import type { InviteMembersSchema } from '~/lib/communities/invitations/schema/invite-members.schema';
import type { Database } from '~/lib/database.types';

/**
 * Validates if an invitation can be sent to a user for a community
 * @param client - The Supabase client
 * @param invitation - The invitation to validate
 * @param communityId - The ID of the community
 * @throws Error if the user is already a member of the community
 */
export async function validateInvitation(
  client: SupabaseClient<Database>,
  invitation: z.infer<typeof InviteMembersSchema>['invitations'][number],
  communityId: string,
) {
  const { data: members, error } = await client.rpc('get_community_members', {
    p_community_id: communityId,
  });

  if (error) {
    throw error;
  }

  const isUserAlreadyMember = members.find((member) => {
    return member.email === invitation.email;
  });

  if (isUserAlreadyMember) {
    throw new Error('User already member of the community');
  }
}
