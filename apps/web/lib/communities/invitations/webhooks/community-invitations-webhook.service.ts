import { SupabaseClient } from '@supabase/supabase-js';

import { z } from 'zod';

import { getLogger } from '@kit/shared/logger';

import { Invitation } from '~/lib/communities/invitations/types';
import type { Database } from '~/lib/database.types';

// TODO: Move this to either its own package or the webhook package
const invitePath = '/join-community';
const siteURL = process.env.NEXT_PUBLIC_SITE_URL;
const productName = process.env.NEXT_PUBLIC_PRODUCT_NAME ?? '';
const emailSender = process.env.EMAIL_SENDER;

const env = z
  .object({
    invitePath: z.string().min(1),
    siteURL: z.string().min(1),
    productName: z.string(),
    emailSender: z.string().email(),
  })
  .parse({
    invitePath,
    siteURL,
    productName,
    emailSender,
  });

export function createCommunityInvitationsWebhookService(
  client: SupabaseClient<Database>,
) {
  return new CommunityInvitationsWebhookService(client);
}

class CommunityInvitationsWebhookService {
  private namespace = 'communities.invitations.webhook';

  constructor(private readonly adminClient: SupabaseClient<Database>) {}

  /**
   * @name handleInvitationWebhook
   * @description Handles the webhook event for invitations
   * @param invitation
   */
  async handleInvitationWebhook(invitation: Invitation) {
    return this.dispatchInvitationEmail(invitation);
  }

  private async dispatchInvitationEmail(invitation: Invitation) {
    const logger = await getLogger();

    logger.info(
      { invitation, name: this.namespace },
      'Handling invitation webhook event...',
    );

    const inviter = await this.adminClient
      .from('users')
      .select('email, first_name, last_name')
      .eq('id', invitation.createdByUserId)
      .single();

    if (inviter.error) {
      logger.error(
        {
          error: inviter.error,
          name: this.namespace,
        },
        'Failed to fetch inviter details',
      );

      throw inviter.error;
    }

    const community = await this.adminClient
      .from('communities')
      .select('name')
      .eq('id', invitation.communityId)
      .single();

    if (community.error) {
      logger.error(
        {
          error: community.error,
          name: this.namespace,
        },
        'Failed to fetch community details',
      );

      throw community.error;
    }

    const ctx = {
      invitationId: invitation.id,
      name: this.namespace,
    };

    logger.info(ctx, 'Invite retrieved. Sending invitation email...');

    try {
      const { renderInviteEmail } = await import('@kit/email-templates');
      const { getMailer } = await import('@kit/mailers');

      const mailer = await getMailer();
      const link = this.getInvitationLink(
        invitation.inviteToken,
        invitation.email,
      );

      const { html, subject } = await renderInviteEmail({
        link,
        invitedUserEmail: invitation.email,
        inviter: `${inviter.data.first_name} ${inviter.data.last_name}`,
        productName: env.productName,
        communityName: community.data.name,
      });

      await mailer
        .sendEmail({
          from: env.emailSender,
          to: invitation.email,
          subject,
          html,
        })
        .then(() => {
          logger.info(ctx, 'Invitation email successfully sent!');
        })
        .catch((error) => {
          logger.error({ error, ...ctx }, 'Failed to send invitation email');
        });

      return {
        success: true,
      };
    } catch (error) {
      logger.warn({ error, ...ctx }, 'Failed to invite user to community');

      return {
        error,
        success: false,
      };
    }
  }

  private getInvitationLink(token: string, email: string) {
    const searchParams = new URLSearchParams({
      invite_token: token,
      email,
    }).toString();

    const href = new URL(env.invitePath, env.siteURL).href;

    return `${href}?${searchParams}`;
  }
}
