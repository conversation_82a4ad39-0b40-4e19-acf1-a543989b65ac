import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import { z } from 'zod';

import type { DeleteInvitationSchema } from '~/lib/communities/invitations/schema/delete-invitation.schema';
import type { InviteMembersSchema } from '~/lib/communities/invitations/schema/invite-members.schema';
import type { UpdateInvitationSchema } from '~/lib/communities/invitations/schema/update-invitation.schema';
import type { Database } from '~/lib/database.types';

import * as implementations from './implementations';

export function createCommunityInvitationsService(
  client: SupabaseClient<Database>,
) {
  return new CommunityInvitationsService(client);
}

/**
 * @name CommunityInvitationsService
 * @description Service for managing community invitations.
 */
class CommunityInvitationsService {
  constructor(private readonly client: SupabaseClient<Database>) {}

  /**
   * @name getInvitation
   * @description Get the invitation data from the invite token.
   * @param adminClient - The admin client instance. Since the user is not yet part of the community, we need to use an admin client to read the pending membership
   * @param token - The invitation token.
   */
  async getInvitation(adminClient: SupabaseClient<Database>, token: string) {
    return implementations.getInvitation(adminClient, token);
  }

  /**
   * @name deleteInvitation
   * @description Removes an invitation from the database.
   * @param params
   */
  async deleteInvitation(params: z.infer<typeof DeleteInvitationSchema>) {
    return implementations.deleteInvitation(this.client, params);
  }

  /**
   * @name updateInvitation
   * @param params
   * @description Updates an invitation in the database.
   */
  async updateInvitation(params: z.infer<typeof UpdateInvitationSchema>) {
    return implementations.updateInvitation(this.client, params);
  }

  /**
   * Validates if an invitation can be sent to a user
   * @param invitation
   * @param communityId
   */
  async validateInvitation(
    invitation: z.infer<typeof InviteMembersSchema>['invitations'][number],
    communityId: string,
  ) {
    return implementations.validateInvitation(
      this.client,
      invitation,
      communityId,
    );
  }

  /**
   * @name sendInvitations
   * @description Sends invitations to join a community.
   * @param params - The parameters containing community ID and invitations
   */
  async sendInvitations(params: {
    invitations: z.infer<typeof InviteMembersSchema>['invitations'];
    communityId: string;
  }) {
    return implementations.sendInvitations(this.client, params);
  }

  /**
   * @name acceptInvitationToCommunity
   * @description Accepts an invitation to join a community.
   * @param adminClient - The admin client instance
   * @param params - The parameters for accepting the invitation
   */
  async acceptInvitationToCommunity(
    adminClient: SupabaseClient<Database>,
    params: {
      userId: string;
      inviteToken: string;
    },
  ) {
    return implementations.acceptInvitationToCommunity(adminClient, params);
  }

  /**
   * @name renewInvitation
   * @description Renews an invitation to join a community by extending the expiration date by 7 days.
   * @param invitationId - The ID of the invitation to renew
   */
  async renewInvitation(invitationId: string) {
    return implementations.renewInvitation(this.client, invitationId);
  }

  /**
   * @name getInvitations
   * @description Gets all invitations for a community.
   * @param communityId - The ID of the community
   */
  async getInvitations(communityId: string) {
    return implementations.getInvitations(this.client, communityId);
  }
}
