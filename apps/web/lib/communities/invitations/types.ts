import { ObjectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

type CommunityInvitationsReturn =
  Database['public']['Functions']['get_community_invitations']['Returns'];

export type Invitations = ObjectToCamel<CommunityInvitationsReturn>;

type CommunityInvitationRow =
  Database['public']['Tables']['community_invitations']['Row'];

export type Invitation = ObjectToCamel<CommunityInvitationRow>;
