'use server';

import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { AcceptInvitationSchema } from '~/lib/communities/invitations/schema/accept-invitation.schema';
import { DeleteInvitationSchema } from '~/lib/communities/invitations/schema/delete-invitation.schema';
import { InviteMembersSchema } from '~/lib/communities/invitations/schema/invite-members.schema';
import { RenewInvitationSchema } from '~/lib/communities/invitations/schema/renew-invitation.schema';
import { UpdateInvitationSchema } from '~/lib/communities/invitations/schema/update-invitation.schema';

import { createCommunityInvitationsService } from '../community-invitations.service';

/**
 * @name createInvitationsAction
 * @description Creates invitations for inviting members.
 */
export const createInvitationsAction = enhanceAction(
  async (params) => {
    const client = getSupabaseServerClient();

    // Create the service
    const service = createCommunityInvitationsService(client);

    // send invitations
    await service.sendInvitations(params);

    revalidateCommunityMemberPage();

    return {
      success: true,
    };
  },
  {
    schema: InviteMembersSchema.and(
      z.object({
        communityId: z.string().min(1),
      }),
    ),
  },
);

/**
 * @name deleteInvitationAction
 * @description Deletes an invitation specified by the invitation ID.
 */
export const deleteInvitationAction = enhanceAction(
  async (data) => {
    const client = getSupabaseServerClient();
    const service = createCommunityInvitationsService(client);

    // Delete the invitation
    await service.deleteInvitation(data);

    revalidateCommunityMemberPage();

    return {
      success: true,
    };
  },
  {
    schema: DeleteInvitationSchema,
  },
);

/**
 * @name updateInvitationAction
 * @description Updates an invitation.
 */
export const updateInvitationAction = enhanceAction(
  async (invitation) => {
    const client = getSupabaseServerClient();
    const service = createCommunityInvitationsService(client);

    await service.updateInvitation(invitation);

    revalidateCommunityMemberPage();

    return {
      success: true,
    };
  },
  {
    schema: UpdateInvitationSchema,
  },
);

/**
 * @name acceptInvitationAction
 * @description Accepts an invitation to join a community.
 */
export const acceptInvitationAction = enhanceAction(
  async (data: FormData, user) => {
    const client = getSupabaseServerClient();

    const { inviteToken, nextPath } = AcceptInvitationSchema.parse(
      Object.fromEntries(data),
    );

    const service = createCommunityInvitationsService(client);

    // use admin client to accept invitation
    const adminClient = getSupabaseServerAdminClient();

    // Accept the invitation
    const userId = await service.acceptInvitationToCommunity(adminClient, {
      inviteToken,
      userId: user.id,
    });

    // If the user ID is not present, throw an error
    if (!userId) {
      throw new Error('Failed to accept invitation');
    }

    return redirect(nextPath);
  },
  {},
);

/**
 * @name renewInvitationAction
 * @description Renews an invitation.
 */
export const renewInvitationAction = enhanceAction(
  async (params) => {
    const client = getSupabaseServerClient();
    const { invitationId } = RenewInvitationSchema.parse(params);

    const service = createCommunityInvitationsService(client);

    // Renew the invitation
    await service.renewInvitation(invitationId);

    revalidateCommunityMemberPage();

    return {
      success: true,
    };
  },
  {
    schema: RenewInvitationSchema,
  },
);

function revalidateCommunityMemberPage() {
  revalidatePath('/[community]/members', 'page');
}
