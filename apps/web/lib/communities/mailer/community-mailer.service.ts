import 'server-only';

import { Community } from '~/lib/communities/community/types';
import { User } from '~/lib/users/types';

import * as implementations from './implementations';

/**
 * Creates a new instance of the CommunityMailerService
 * @returns A new CommunityMailerService instance
 */
export function createCommunityMailerService() {
  return new CommunityMailerService();
}

/**
 * Service for handling email communications related to communities
 */
class CommunityMailerService {
  /**
   * Handles the webhook event for community deletion
   * @param community The deleted community
   * @param primaryOwnerUser The primary owner of the community
   */
  async handleCommunityDeletedWebhook(
    community: Community,
    primaryOwnerUser: User,
  ): Promise<void> {
    return implementations.handleCommunityDeletedWebhook(
      community,
      primaryOwnerUser,
    );
  }
}
