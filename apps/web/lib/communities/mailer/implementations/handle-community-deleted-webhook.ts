import 'server-only';

import { getLogger } from '@kit/shared/logger';

import { Community } from '~/lib/communities/community/types';
import { User } from '~/lib/users/types';

import { getEmailSettings } from './get-email-settings';
import { sendCommunityDeletionEmail } from './send-community-deletion-email';

const NAMESPACE = 'communities.webhooks';

/**
 * Handles the webhook event for community deletion
 * @param community The deleted community
 * @param primaryOwnerUser The primary owner of the community
 * @returns Promise resolving when the email has been sent
 */
export async function handleCommunityDeletedWebhook(
  community: Community,
  primaryOwnerUser: User,
): Promise<void> {
  const logger = await getLogger();

  const ctx = {
    communityId: community.id,
    namespace: NAMESPACE,
    function: 'handleCommunityDeletedWebhook',
  };

  logger.info(ctx, 'Received community deleted webhook. Processing...');

  const { fromEmail } = await getEmailSettings();

  await sendCommunityDeletionEmail({
    communityName: community.name,
    fromEmail,
    userEmail: primaryOwnerUser.email!,
    userDisplayName: primaryOwnerUser.firstName,
  });
}
