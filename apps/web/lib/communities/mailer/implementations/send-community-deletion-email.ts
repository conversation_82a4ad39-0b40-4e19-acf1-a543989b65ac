import 'server-only';

import { getLogger } from '@kit/shared/logger';

const NAMESPACE = 'communities.mailer';

/**
 * Sends an email notification about community deletion to the user
 * @param params Parameters for sending the email
 * @returns Promise resolving to the result of sending the email
 */
export async function sendCommunityDeletionEmail(params: {
  fromEmail: string;
  userEmail: string;
  userDisplayName: string;
  communityName: string;
}): Promise<void> {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    function: 'sendCommunityDeletionEmail',
  };

  logger.info(ctx, 'Sending community deletion email...');

  const { renderCommunityDeleteEmail } = await import('@kit/email-templates');
  const { getMailer } = await import('@kit/mailers');

  const mailer = await getMailer();

  const { html, subject } = await renderCommunityDeleteEmail({
    communityName: params.communityName,
  });

  await mailer.sendEmail({
    to: params.userEmail,
    from: params.fromEmail,
    subject,
    html,
  });
}
