import { z } from 'zod';

/**
 * Gets the email settings from environment variables
 * @returns Object containing productName and fromEmail
 */
export async function getEmailSettings() {
  const productName = process.env.NEXT_PUBLIC_PRODUCT_NAME;
  const fromEmail = process.env.EMAIL_SENDER;

  return z
    .object({
      productName: z.string(),
      fromEmail: z.string().email(),
    })
    .parse({
      productName,
      fromEmail,
    });
}
