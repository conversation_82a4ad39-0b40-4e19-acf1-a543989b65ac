'use client';

import { createContext } from 'react';

import { User } from '@supabase/supabase-js';

import {
  CommunityWorkspaceDb,
  UserCommunity,
} from '~/lib/communities/community/types';

type CommunityWorkspace = {
  community: CommunityWorkspaceDb;
  communities: UserCommunity[];
  user: User | null;
};

export const CommunityWorkspaceContext = createContext<CommunityWorkspace>(
  {} as CommunityWorkspace,
);

export function CommunityWorkspaceContextProvider(
  props: React.PropsWithChildren<{ value: CommunityWorkspace }>,
) {
  return (
    <CommunityWorkspaceContext.Provider value={props.value}>
      {props.children}
    </CommunityWorkspaceContext.Provider>
  );
}
