# Community Contexts

This directory contains React context providers that make community data available throughout the application.

## Context Types

### 1. PublicCommunityContext

**Purpose:** Provides basic community data available to all users (authenticated and unauthenticated).

**Data Structure:**

- `community`: Basic community information from the communities table
- `communityViewData`: Extended community data from public_communities_view
- `pageData`: Page-specific data like about page content

**Usage in Client Components:**

```tsx
'use client';

import { usePublicCommunity } from '~/lib/server/communities/context/public-community-context';

function MyClientComponent() {
  const { community, communityViewData } = usePublicCommunity();

  return <div>Community Name: {community.name}</div>;
}
```

**Usage in Server Components:**

```tsx
// Server components receive the data through props from the layout
// or by using the context value through custom hooks

async function MyServerComponent({ params }) {
  // Data is typically passed through props
  // or fetched in the server component itself
}
```

### 2. CommunityWorkspaceContext

**Purpose:** Provides authenticated user-specific workspace data, including permissions.

**Data Structure:**

- `communities`: List of communities the user belongs to
- `community`: Current community workspace with permissions
- `user`: Current authenticated user

**Usage in Client Components:**

```tsx
'use client';

import { useContext } from 'react';

import { CommunityWorkspaceContext } from '~/lib/server/communities/context/community-workspace-context';

function MyAuthenticatedComponent() {
  const { community, user } = useContext(CommunityWorkspaceContext);

  return (
    <div>
      <p>Welcome, {user.email}</p>
      <p>Your role: {community.permissions.join(', ')}</p>
    </div>
  );
}
```

**Usage in Server Components:**

```tsx
// Server components will use data passed through props from the layout
// This context is only available for authenticated users
```

## Implementation in Layouts

The contexts are applied in the community layout:

```tsx
// Simplified example
export default function CommunityLayout({ params, children }) {
  // Load public community data...
  const publicData = { community, communityViewData, pageData };

  // Check if user is authenticated
  const isAuthenticated = !!user;

  return (
    <PublicCommunityContextProvider value={publicData}>
      {isAuthenticated ? (
        <CommunityWorkspaceContextProvider value={workspace}>
          {/* Authenticated view */}
        </CommunityWorkspaceContextProvider>
      ) : (
        {
          /* Public view */
        }
      )}
    </PublicCommunityContextProvider>
  );
}
```

## Best Practices

1. **Data Fetching**: Fetch data once in the layout and pass it to the context
2. **Type Safety**: Use the defined types to ensure type safety
3. **Server Components**: Prefer passing data through props for server components
4. **Client Components**: Use the context hooks for client components

## When To Use Each Context

- Use `PublicCommunityContext` for data needed by all users (name, description, etc.)
- Use `CommunityWorkspaceContext` for authenticated-only data (permissions, user details)
