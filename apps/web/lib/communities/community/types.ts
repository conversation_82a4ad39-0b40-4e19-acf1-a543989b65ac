import { z } from 'zod';

import { ObjectToCamel } from '@kit/shared/utils';
import { Social } from '@kit/ui/dojo/organisms/socials-form';

import { CommunitySlimSchema } from '~/lib/communities/community/schema/community.schema';
import type { Database } from '~/lib/database.types';

type CommunityRow = Database['public']['Tables']['communities']['Row'];

export type Community = ObjectToCamel<CommunityRow>;

export type CommunitySlim = z.infer<typeof CommunitySlimSchema>;

export type CommunityExpanded = Community & {
  categoryName: string;
  languageName: string;
  description: string;
  primaryColor: string;
  socials: Social[];
};

export type CommunitiesSlim = Array<CommunitySlim>;

type PublicCommunityViewRow =
  Database['public']['Views']['public_communities_view']['Row'];

type ProductPriceRow = Database['public']['Tables']['product_prices']['Row'];

export type ProductPrice = ObjectToCamel<ProductPriceRow>;

// Raw product price type (without camelCase transformation)
export type RawProductPrice = {
  unit_amount: number;
  interval: string;
  id: string;
  product_id: string;
  currency: string;
  active: boolean;
  created_at: string;
  updated_at: string;
};

// Type for the raw community data from the database before transformation
export type RawCommunityWithProductPrices = PublicCommunityViewRow & {
  product_prices?: RawProductPrice[];
};

export type PublicCommunityView = ObjectToCamel<PublicCommunityViewRow> & {
  productPrices?: ProductPrice[];
};

export type CommunityCategory = {
  id: string;
  name: string;
  icon: string;
  slug: string;
  description: string;
};

export type Language = {
  id: string;
  name: string;
  icon: string;
};

// Replace the original UserCommunity definition which relied on the generated (nullable) view type.
// Define the stricter type based on the expected output of the user_communities view
// after applying INNER JOINs and COALESCE.
export type UserCommunity = {
  categoryIcon: string;
  categoryName: string;
  coverUrl: string; // From COALESCE('')
  description: string; // From COALESCE('')
  id: string; // From communities table (PK)
  isPrivate: boolean; // Assuming NOT NULL in communities table
  languageIcon: string;
  languageName: string;
  logoUrl: string; // From COALESCE('')
  memberCount: number; // From COUNT(*)
  memberId: string; // From memberships join
  name: string; // From communities table
  primaryColor: string; // From COALESCE('')
  role: string; // From memberships join
  slug: string; // From communities table
  userId: string; // From memberships join
  visibleOnProfile: boolean; // Controls whether this community appears on the user's public profile
};

type CommunityWorkspaceRow =
  Database['public']['Functions']['community_workspace']['Returns'][number];

export type CommunityWorkspaceDb = ObjectToCamel<CommunityWorkspaceRow>;

type CommunityConnectStatusRow =
  Database['public']['Functions']['get_community_connect_status']['Returns'][number];

export type CommunityConnectStatusData =
  ObjectToCamel<CommunityConnectStatusRow>;
