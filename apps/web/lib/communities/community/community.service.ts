import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import type { PermissionsEnum } from '~/lib/communities/members/types';
import type { Database } from '~/lib/database.types';

import * as implementations from './implementations';

/**
 * @name createCommunityService
 * @description Creates a new instance of the CommunityService
 * @param client The Supabase client instance
 * @returns A new CommunityService instance
 */
export function createCommunityService(client: SupabaseClient<Database>) {
  return new CommunityService(client);
}

/**
 * @name CommunityService
 * @description Service for managing communities in the application
 * @example
 * const client = getSupabaseClient();
 * const communityService = createCommunityService(client);
 */
export class CommunityService {
  constructor(private readonly client: SupabaseClient<Database>) {}

  /**
   * @name getCommunityBySlug
   * @description Get the community data for the given slug.
   * @param slug
   */
  async getCommunityBySlug(slug: string) {
    return implementations.getCommunityBySlug(this.client, slug);
  }

  /**
   * @name getCommunityById
   * @description Get the community data for the given id.
   * @param communityId
   */
  async getCommunityById(communityId: string) {
    return implementations.getCommunityById(this.client, communityId);
  }

  /**
   * @name getCommunityWorkspace
   * @description Get the community workspace data.
   * @param slug
   */
  async getCommunityWorkspace(slug: string) {
    return implementations.getCommunityWorkspace(this.client, slug);
  }

  /**
   * @name hasCommunityPermission
   * @description Check if the user has permission for the community.
   */
  async hasCommunityPermissionCheck(params: {
    communityId: string;
    userId: string;
    permission: PermissionsEnum;
  }) {
    return implementations.hasCommunityPermissionCheck(this.client, params);
  }

  /**
   * @name getCommunityCategories
   * @description Get the community categories for the given community.
   * @param params.sortByCount If true, sorts by number of communities in descending order. If false, sorts alphabetically by name (default: false)
   */
  async getCommunityCategories(params: { sortByCount?: boolean } = {}) {
    return implementations.getCommunityCategories(this.client, params);
  }

  /**
   * Gets a community's public data
   */
  async getCommunityPublicData(params: { communitySlug: string }) {
    return implementations.getCommunityPublicData(this.client, params);
  }

  /**
   * Gets a community's public data and product prices
   */
  async getAllCommunitiesWithProductPrices(
    params: {
      languageId?: string;
      categoryId?: string;
      isPrivate?: boolean;
      isPaid?: boolean;
      isListed?: boolean;
      page?: number;
      limit?: number;
    } = {},
  ) {
    return implementations.getAllCommunitiesWithProductPrices(
      this.client,
      params,
    );
  }
}
