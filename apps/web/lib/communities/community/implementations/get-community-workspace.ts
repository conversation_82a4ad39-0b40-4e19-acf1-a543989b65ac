import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'community.service';

/**
 * Get the community workspace data
 * @param client - The Supabase client
 * @param slug - The community slug
 * @returns The community workspace data
 */
export async function getCommunityWorkspace(
  client: SupabaseClient<Database>,
  slug: string,
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    function: 'getCommunityWorkspace',
    slug,
  };

  try {
    logger.info(ctx, 'Getting community workspace...');

    // Check that we have a valid slug
    if (!slug || typeof slug !== 'string' || slug.trim() === '') {
      logger.error({ ...ctx, slug }, 'Invalid community slug provided:');
      return {
        error: new Error('Invalid community slug'),
        data: null,
      };
    }

    logger.info(ctx, 'Getting community workspace...');
    // Get workspace data (community data with permissions)
    const communityPromise = client.rpc('community_workspace', {
      p_community_slug: slug,
    });

    // Get list of communities the user is a member of
    const communitiesPromise = client.from('user_communities').select('*');

    // Wait for both requests to complete
    const [communityResult, communitiesResult] = await Promise.all([
      communityPromise,
      communitiesPromise,
    ]);

    // Check for errors in community workspace data
    if (communityResult.error) {
      logger.error(
        { ...ctx, error: communityResult.error },
        `Failed to get community workspace for ${slug}`,
      );
      return {
        error: communityResult.error,
        data: null,
      };
    }

    // Check for errors in user communities data
    if (communitiesResult.error) {
      logger.error(
        { ...ctx, error: communitiesResult.error },
        'Failed to get user communities:',
      );
      return {
        error: communitiesResult.error,
        data: null,
      };
    }

    if (!communityResult.data || !communityResult.data[0]) {
      logger.error(ctx, `No community data found for slug: ${slug}`);
      return {
        error: new Error(`Community not found: ${slug}`),
        data: null,
      };
    }

    // Extract community data from result
    const communityData = objectToCamel(communityResult.data?.[0]);
    const communitiesData = objectToCamel(communitiesResult.data);

    // Check if we have community data
    if (!communityData) {
      logger.error(ctx, `No community data found for slug: ${slug}`);
      return {
        error: new Error(`Community not found: ${slug}`),
        data: null,
      };
    }

    // Return successful response with both community and communities data
    logger.info(ctx, `Successfully loaded workspace for community ${slug}`);
    return {
      data: {
        community: communityData,
        communities: communitiesData,
      },
      error: null,
    };
  } catch (error) {
    // Catch and log any unexpected errors
    logger.error(
      { ...ctx, error },
      `Unexpected error in getCommunityWorkspace for ${slug}`,
    );
    return {
      error:
        error instanceof Error
          ? error
          : new Error('Unknown error in getCommunityWorkspace'),
      data: null,
    };
  }
}
