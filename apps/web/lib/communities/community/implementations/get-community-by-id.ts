import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'community.service';

/**
 * Get the community data for the given id
 * @param client - The Supabase client
 * @param communityId - The community ID
 * @returns The community data
 */
export async function getCommunityById(
  client: SupabaseClient<Database>,
  communityId: string,
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    function: 'getCommunityById',
    communityId,
  };

  logger.info(ctx, 'Getting community data...');
  const { data, error } = await client
    .from('communities')
    .select('*')
    .eq('id', communityId)
    .single();

  if (error) {
    logger.error({ ...ctx, error }, 'Error getting community data:');
    throw error;
  }

  return objectToCamel(data);
}
