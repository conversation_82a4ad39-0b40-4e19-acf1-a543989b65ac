import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';

import type { PermissionsEnum } from '~/lib/communities/members/types';
import type { Database } from '~/lib/database.types';

const NAMESPACE = 'community.service';

/**
 * Check if the user has permission for the community
 * @param client - The Supabase client
 * @param params - Parameters containing the community ID, user ID, and permission name
 * @returns Whether the user has the specified permission
 */
export async function hasCommunityPermissionCheck(
  client: SupabaseClient<Database>,
  params: {
    communityId: string;
    userId: string;
    permission: PermissionsEnum;
  },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    function: 'hasCommunityPermissionCheck',
    ...params,
  };

  const { data, error } = await client.rpc('has_community_permission', {
    p_community_id: params.communityId,
    p_user_id: params.userId,
    p_permission_name: params.permission,
  });

  if (error) {
    logger.error({ ...ctx, error }, 'Error checking community permission:');
    throw error;
  }

  return data;
}
