import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'community.service';

/**
 * Get the community data for the given slug
 * @param client - The Supabase client
 * @param slug - The community slug
 * @returns The community data
 */
export async function getCommunityBySlug(
  client: SupabaseClient<Database>,
  slug: string,
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    function: 'getCommunityBySlug',
    slug,
  };

  logger.info(ctx, 'Getting community data...');

  const { data, error } = await client
    .from('communities')
    .select('*')
    .eq('slug', slug)
    .single();

  if (error) {
    logger.error({ ...ctx, error }, 'Error getting community data:');
    throw error;
  }

  logger.info(ctx, 'Community data retrieved successfully');
  return objectToCamel(data);
}
