import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'community.service';

/**
 * Gets all communities with optional filtering
 * @param client - The Supabase client
 * @param params - Optional filtering parameters
 * @returns List of communities
 */
export async function getAllCommunitiesWithProductPrices(
  client: SupabaseClient<Database>,
  params: {
    languageId?: string;
    categoryId?: string;
    isPrivate?: boolean;
    isPaid?: boolean;
    isListed?: boolean;
    page?: number;
    limit?: number;
  } = {},
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    function: 'getAllCommunities',
  };

  logger.info(ctx, `Getting all communities...`);

  let query = client
    .from('public_communities_view')
    .select('*')
    .order('created_at', { ascending: false });

  // Apply filters conditionally
  if (params.languageId) {
    query = query.eq('language_id', params.languageId);
  }

  if (params.categoryId) {
    query = query.eq('category_id', params.categoryId);
  }

  if (params.isPrivate !== undefined) {
    query = query.eq('is_private', params.isPrivate);
  }

  if (params.isListed !== undefined) {
    query = query.eq('is_listed', params.isListed);
  }

  // Note: isPaid filtering is handled after fetching product prices

  const page = params.page ?? 1;
  const limit = params.limit ?? 10;
  const start = (page - 1) * limit;
  const end = start + limit - 1;

  // Use range with correct pagination calculation
  query = query.range(start, end);

  const { error: communitiesDataError, data: communitiesData } = await query;

  if (communitiesDataError) {
    logger.error(
      {
        error: communitiesDataError,
        ...ctx,
      },
      `Error getting all communities`,
    );

    throw new Error('Error getting all communities');
  }

  if (!communitiesData) {
    logger.error(
      { ...ctx, error: 'Community not found' },
      'Community not found',
    );
    return {
      communities: null,
    };
  }

  if (!communitiesData.length) {
    return {
      communities: null,
    };
  }

  // For each community, fetch the product prices for its default product if it exists
  const communitiesWithPrices = await Promise.all(
    communitiesData.map(async (community) => {
      if (community.default_product_id) {
        const { data: productPrices, error: productPricesError } = await client
          .from('product_prices')
          .select('*')
          .eq('product_id', community.default_product_id);

        if (productPricesError) {
          logger.error(
            { ...ctx, error: productPricesError },
            'Error getting product prices for community',
          );
        }

        return {
          ...community,
          product_prices: productPrices || [],
        };
      }

      return {
        ...community,
        product_prices: [],
      };
    }),
  );

  // Filter by isPaid if specified
  let filteredCommunities = communitiesWithPrices;
  if (params.isPaid !== undefined) {
    filteredCommunities = communitiesWithPrices.filter((community) => {
      const hasPrices = community.product_prices?.some(
        (price) => price.unit_amount && price.unit_amount > 0,
      );
      return params.isPaid ? hasPrices : !hasPrices;
    });
  }

  logger.info(ctx, `All communities retrieved successfully`);

  return {
    communities: objectToCamel(filteredCommunities),
  };
}
