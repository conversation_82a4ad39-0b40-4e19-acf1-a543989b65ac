import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'community.service';

/**
 * Gets a community's public data
 * @param client - The Supabase client
 * @param params - Parameters containing the community slug
 * @returns The community public data
 */
export async function getCommunityPublicData(
  client: SupabaseClient<Database>,
  params: { communitySlug: string },
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    function: 'getCommunityPublicData',
    ...params,
  };

  logger.info(ctx, `Getting community public data...`);

  const { error: communityPublicDataError, data: communityPublicData } =
    await client
      .from('public_communities_view')
      .select('*')
      .eq('slug', params.communitySlug)
      .single();

  if (communityPublicDataError) {
    logger.error(
      {
        error: communityPublicDataError,
        ...ctx,
      },
      `Error getting community public data`,
    );

    throw new Error('Error getting community public data');
  }

  if (!communityPublicData) {
    logger.error(
      { ...ctx, error: 'Community not found' },
      'Community not found',
    );
    return {
      communityPublicData: null,
    };
  }

  if (!communityPublicData.id) {
    logger.error(ctx, 'Community ID not found');
    throw new Error('Community ID not found');
  }

  logger.info(ctx, `Community public data retrieved successfully`);

  return {
    communityPublicData: objectToCamel(communityPublicData),
  };
}
