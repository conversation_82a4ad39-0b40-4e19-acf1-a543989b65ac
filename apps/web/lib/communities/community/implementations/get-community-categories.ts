import 'server-only';

import type { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';

import type { Database } from '~/lib/database.types';

const NAMESPACE = 'community.service';

/**
 * Get the community categories
 * @param client - The Supabase client
 * @param params - Optional parameters for sorting
 * @returns The community categories
 */
export async function getCommunityCategories(
  client: SupabaseClient<Database>,
  params: { sortByCount?: boolean } = {},
) {
  const logger = await getLogger();
  const ctx = {
    namespace: NAMESPACE,
    function: 'getCommunityCategories',
    sortByCount: params.sortByCount,
  };

  logger.info(ctx, 'Getting community categories...');
  const { data, error } = await client.rpc('get_community_categories');

  if (error) {
    logger.error({ ...ctx, error }, 'Error getting community categories:');
    throw error;
  }

  // Convert to camelCase and ensure we have an array
  let categories = objectToCamel(data) ?? [];

  // Sort based on parameter
  if (params.sortByCount) {
    categories = categories.sort((a, b) => b.communityCount - a.communityCount);
  } else {
    // Already sorted alphabetically by the database function, but we'll make it explicit here
    categories = categories.sort((a, b) => a.name.localeCompare(b.name));
  }

  return {
    categories,
  };
}
