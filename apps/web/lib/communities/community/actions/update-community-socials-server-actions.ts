'use server';

import { revalidatePath } from 'next/cache';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

// Define the social media schema
const SocialSchema = z.object({
  name: z.string(),
  url: z.string(),
});

const UpdateCommunitySocialsSchema = z.object({
  communitySlug: z.string(),
  socials: z.array(SocialSchema),
});

export const updateCommunitySocialsAction = enhanceAction(
  async (params) => {
    const client = getSupabaseServerClient();
    const logger = await getLogger();
    const { communitySlug, socials } = params;

    const ctx = {
      name: 'communities.update-socials',
      communitySlug: communitySlug,
    };

    logger.info(ctx, `Begin updating community socials...`);

    // Update the account with the new socials
    const { error: updateError } = await client
      .from('communities')
      .update({
        socials: socials,
      })
      .eq('slug', communitySlug);

    if (updateError) {
      logger.error(
        { ...ctx, error: updateError },
        `Failed to update community socials`,
      );
      throw updateError;
    }

    logger.info(ctx, `Community socials updated successfully`);

    // Revalidate the community settings page
    revalidatePath(`/[community]/settings`, 'page');

    return { success: true };
  },
  {
    schema: UpdateCommunitySocialsSchema,
  },
);
