'use server';

import { redirect } from 'next/navigation';

import type { SupabaseClient } from '@supabase/supabase-js';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { DeleteCommunitySchema } from '~/lib/communities/community/schema/delete-community.schema';
import type { Database } from '~/lib/database.types';

import { createCommunityManagementService } from '../../management/community-management.service';

export const deleteCommunityAction = enhanceAction(
  async (formData: FormData, user) => {
    const logger = await getLogger();

    const params = DeleteCommunitySchema.parse(
      Object.fromEntries(formData.entries()),
    );

    const ctx = {
      name: 'communities.delete',
      userId: user.id,
      communityId: params.communityId,
    };

    logger.info(ctx, `Deleting community...`);

    await deleteCommunity({
      communityId: params.communityId,
      userId: user.id,
    });

    logger.info(ctx, `Community request successfully sent`);

    return redirect('/home');
  },
  {
    auth: true,
  },
);

async function deleteCommunity(params: {
  communityId: string;
  userId: string;
}) {
  const client = await getSupabaseServerClient();
  const communityManagementService = createCommunityManagementService(client);

  // verify that the user has the necessary permissions to delete the community
  await assertUserPermissionsToDeleteCommunity(client, params);

  // delete the community – and record any misfortune
  try {
    await communityManagementService.deleteCommunity(params);
  } catch (error) {
    const logger = await getLogger();
    logger.error(
      { ...params, error, name: 'communities.delete' },
      'Failed to delete community',
    );

    throw new Error('Unable to delete community at this time'); // avoid leaking internals
  }
}

async function assertUserPermissionsToDeleteCommunity(
  client: SupabaseClient<Database>,
  params: {
    communityId: string;
    userId: string;
  },
) {
  const { data, error } = await client
    .from('communities')
    .select('id')
    .eq('primary_owner_user_id', params.userId)
    .eq('id', params.communityId)
    .single();

  if (error ?? !data) {
    throw new Error('Community not found');
  }
}
