'use server';

import { revalidatePath } from 'next/cache';

import { URLSearchParams } from 'node:url';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { UpdateCommunityFormDetailsSchema } from '~/lib/communities/community/schema/update-community-details.schema';

export const updateCommunityDetailsAction = enhanceAction(
  async (params) => {
    const client = getSupabaseServerClient();
    const logger = await getLogger();
    const {
      name,
      path,
      slug,
      newSlug,
      description,
      primaryColor,
      category,
      language,
      isPrivate,
    } = params;

    const ctx = {
      name: 'communities.details.update',
      communitySlug: slug,
    };

    logger.info(ctx, `Updating community details...`);
    const { error } = await client
      .from('communities')
      .update({
        name,
        slug: newSlug,
        category_id: category ?? undefined,
        language_id: language ?? undefined,
        is_private: isPrivate,
        description: description,
        primary_color: primaryColor,
      })
      .match({
        slug,
      })
      .select('slug')
      .single();

    if (error) {
      logger.error({ ...ctx, error }, `Failed to update community details`);

      throw error;
    }

    logger.info(ctx, `Community details updated`);

    revalidatePath(`/[community]/settings`, 'page');

    const isNewSlug = newSlug !== slug;

    if (isNewSlug && newSlug) {
      // Construct the new path with the updated slug
      const basePath = path.replace('[community]', encodeURIComponent(newSlug));
      const searchParams = new URLSearchParams();
      searchParams.set('slugUpdate', 'success');
      const nextPath = `${basePath}?${searchParams.toString()}`;

      return {
        success: true,
        redirect: nextPath,
      };
    }

    return { success: true };
  },
  {
    schema: UpdateCommunityFormDetailsSchema,
  },
);
