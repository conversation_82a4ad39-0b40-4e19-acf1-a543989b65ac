'use client';

import { useContext } from 'react';

import { CommunityWorkspaceContext } from '../../context/community-workspace-context';

/**
 * @name useCommunityWorkspace
 * @description A hook to access the account workspace data.
 * @returns The account workspace data.
 */
export function useCommunityWorkspace() {
  const ctx = useContext(CommunityWorkspaceContext);

  if (!ctx) {
    throw new Error(
      'useCommunityWorkspace must be used within a CommunityWorkspaceContext.Provider. This is only provided within the community workspace [community]',
    );
  }

  return ctx;
}
