import { useState } from 'react';

import { useDebouncedCallback } from 'use-debounce';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';

type DebouncedCheckFunction = {
  (slug: string, currentCommunityId: string): Promise<void> | undefined;
  subscribe(callback: (result: boolean | null) => void): void;
  unsubscribe(callback: (result: boolean | null) => void): void;
};

export function useCheckCommunitySlugAvailability() {
  const client = useSupabase();
  const [subscribers] = useState<Set<(result: boolean | null) => void>>(
    new Set(),
  );

  const debouncedCheck = useDebouncedCallback(
    async (slug: string, currentCommunityId: string) => {
      if (!slug) {
        subscribers.forEach((cb) => cb(null));
        return;
      }

      try {
        const { data, error } = await client.rpc(
          'check_community_slug_availability',
          {
            p_slug_to_check: slug,
            p_current_community_id: currentCommunityId,
          },
        );

        if (error) {
          console.error('Error checking slug availability:', error);
          subscribers.forEach((cb) => cb(null));
          return;
        }

        subscribers.forEach((cb) => cb(data));
      } catch (error) {
        console.error('Error checking slug availability:', error);
        subscribers.forEach((cb) => cb(null));
      }
    },
    300,
  );

  const checkAvailability: DebouncedCheckFunction = Object.assign(
    debouncedCheck,
    {
      subscribe: (callback: (result: boolean | null) => void) => {
        subscribers.add(callback);
      },
      unsubscribe: (callback: (result: boolean | null) => void) => {
        subscribers.delete(callback);
      },
    },
  );

  return {
    checkAvailability,
  };
}
