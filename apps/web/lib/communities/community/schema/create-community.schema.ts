import { z } from 'zod';

/**
 * @name CommunityNameSchema
 * @description Schema for community name validation
 * Only allows letters (English and international), numbers, dashes, spaces, and ampersands
 */
const CommunityNameSchema = z
  .string({
    description: 'The name of the community',
  })
  .min(2)
  .max(50)
  .regex(
    /^[\p{L}\p{N}\-\s&]+$/u,
    'Community name can only contain letters, numbers, dashes, spaces, and ampersands',
  );

/**
 * @name CreateCommunitySchema
 * @description Schema for creating a community
 */
export const CreateCommunitySchema = z.object({
  name: CommunityNameSchema,
});

/**
 * @name CreateCommunityWithSubscriptionSchema
 * @description Schema for creating a community
 */
export const CreateCommunityWithSubscriptionSchema = z.object({
  name: CommunityNameSchema,
  subscriptionId: z.string(),
});

/**
 * @name CreateCommunityWithIdSchema
 * @description Schema for creating a community with an id
 */
export const CreateCommunityWithIdSchema = z.object({
  name: CommunityNameSchema,
  communityId: z.string().uuid(),
});
