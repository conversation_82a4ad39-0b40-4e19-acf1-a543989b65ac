import { z } from 'zod';

/**
 * @name CommunityNameSchema
 * @description Schema for community name validation
 * Only allows letters (English and international), numbers, dashes, spaces, and ampersands
 */
const CommunityNameSchema = z
  .string({
    description: 'The name of the community',
  })
  .min(2)
  .max(50)
  .regex(
    /^[\p{L}\p{N}\-\s&]+$/u,
    'Community name can only contain letters, numbers, dashes, spaces, and ampersands',
  );

export const CommunityDetailsFormSchema = z.object({
  name: CommunityNameSchema,
  slug: z.string().min(1).max(255),
  newSlug: z
    .string()
    .min(4)
    .max(30)
    .regex(
      /^[a-z0-9][a-z0-9-]*[a-z0-9]$/,
      'Must start and end with letter/number, and no consecutive hyphens',
    )
    .transform((val) => val.toLowerCase()),
  isPrivate: z.boolean().nullable(),
  category: z.string().nullable(),
  language: z.string().nullable(),
  description: z.string().min(1).max(700).optional(),
  primaryColor: z.string().min(7).max(7),
});

export const UpdateCommunityFormDetailsSchema =
  CommunityDetailsFormSchema.merge(
    z.object({
      path: z.string().min(1).max(255),
    }),
  );

export type UpdateCommunityDetailsFormValues = z.infer<
  typeof UpdateCommunityFormDetailsSchema
>;
