import { z } from 'zod';

// Define the social media schema
const SocialSchema = z.object({
  name: z.string(),
  url: z.string(),
});

export const CommunitySchema = z.object({
  id: z.string().min(1).max(255),
  name: z.string().min(1).max(255),
  slug: z.string().min(1).max(255),
  logoUrl: z.string().min(1).max(255).nullable(),
  coverUrl: z.string().min(1).max(255).nullable(),
  primaryOwnerId: z.string().min(1).max(255),
  isPrivate: z.boolean().nullable(),
  category: z.string().uuid().nullable(),
  language: z.string().uuid().nullable(),
  communityDetails: z
    .object({
      description: z.string().max(255),
      primaryColor: z.string().min(7).max(7),
      socials: z.array(SocialSchema).optional(),
    })
    .optional(),
});

export const Language = z.object({
  id: z.string().uuid(),
  name: z.string(),
  iso_code_1: z.string(),
  iso_code_2: z.string(),
  iso_code_3: z.string(),
  icon: z.string(),
});

export const CommunityCategory = z.object({
  id: z.string().uuid(),
  name: z.string(),
  icon: z.string(),
});

export const CommunitySlimSchema = z.object({
  name: z.string(),
  slug: z.string(),
  logoUrl: z.string().nullable(),
});
