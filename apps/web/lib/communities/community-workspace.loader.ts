import 'server-only';

import { cache } from 'react';

import { redirect } from 'next/navigation';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

import pathsConfig from '~/config/paths.config';
import { createCommunityService } from '~/lib/communities/community/community.service';
import { UserCommunity } from '~/lib/communities/community/types';

export type CommunityWorkspace = Awaited<
  ReturnType<typeof loadCommunityWorkspace>
>;

/**
 * Load the community workspace data.
 * We place this function into a separate file so it can be reused in multiple places across the server components.
 *
 * This function is used in the layout component for the community workspace.
 * It is cached so that the data is only fetched once per request.
 *
 * @param communitySlug
 */
export const loadCommunityWorkspace = cache(workspaceLoader);

async function workspaceLoader(communitySlug: string) {
  const client = getSupabaseServerClient();
  const communityService = createCommunityService(client);

  try {
    const userData = await client.auth.getUser();

    const workspace =
      await communityService.getCommunityWorkspace(communitySlug);

    if (!workspace.data) {
      redirect(pathsConfig.app.home);
    }

    return {
      community: workspace.data.community,
      communities: workspace.data.communities as UserCommunity[],
      user: userData.data.user,
    };
  } catch (error) {
    console.error('Failed to load community workspace:', error);

    return redirect(pathsConfig.app.home);
  }
}
