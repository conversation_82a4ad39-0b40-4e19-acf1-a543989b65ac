'use client';

import { useFormStatus } from 'react-dom';

import { Button } from '@kit/ui/button';
import { Trans } from '@kit/ui/trans';

export function InvitationSubmitButton(props: {
  communityName: string;
  email: string;
}) {
  const { pending } = useFormStatus();

  return (
    <Button type={'submit'} className={'w-full'} disabled={pending}>
      <Trans
        i18nKey={
          pending ? 'communities:joiningCommunity' : 'communities:continueAs'
        }
        values={{
          communityName: props.communityName,
          email: props.email,
        }}
      />
    </Button>
  );
}

InvitationSubmitButton.displayName = 'InvitationSubmitButton';
