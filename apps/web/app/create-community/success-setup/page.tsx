import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@kit/ui/card';
import { Spinner } from '@kit/ui/spinner';
import { Trans } from '@kit/ui/trans';

import { withI18n } from '~/lib/i18n/with-i18n';

import { SetupCommunity } from './_components/setup-community';
import { successCreateCommunityAction } from './_lib/success-create-community';

type CreateCommunitySuccessPageProps = {
  searchParams: Promise<{
    session_id: string;
  }>;
};

async function CreateCommunitySuccessPage({
  searchParams,
}: CreateCommunitySuccessPageProps) {
  const sessionId = (await searchParams).session_id as string;

  if (!sessionId) {
    return (
      <div className="flex h-screen items-center justify-center">
        <span>
          <Trans i18nKey="communities:noSessionIdOrCommunityNameProvided" />
        </span>
      </div>
    );
  }

  let communitySlug: string | undefined;
  let communityName: string | undefined;
  try {
    const result = await successCreateCommunityAction({
      sessionId,
    });
    communitySlug = result?.communitySlug;
    communityName = result?.communityName;
  } catch (error) {
    console.error('Error creating community:', error);
  }

  if (!communitySlug || !communityName) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <Spinner />
          <p className="text-muted-foreground text-center">
            <Trans i18nKey="communities:settingUpYourCommunity" />
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={'mt-4 flex flex-col items-center justify-center py-14'}>
      <Card>
        <CardHeader>
          <CardTitle>
            <Trans i18nKey="communities:yourNewCommunity" />
          </CardTitle>
        </CardHeader>

        <CardContent>
          <SetupCommunity
            communitySlug={communitySlug}
            sessionId={sessionId}
            communityName={communityName}
          />
        </CardContent>
      </Card>
    </div>
  );
}

export default withI18n(CreateCommunitySuccessPage);

CreateCommunitySuccessPage.displayName = 'CreateCommunitySuccessPage';
