import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@kit/ui/card';
import { Trans } from '@kit/ui/trans';

import { withI18n } from '~/lib/i18n/with-i18n';

import { SuccessPageContent } from './_components/success-page-content';

type CreateCommunitySuccessPageProps = {
  searchParams: Promise<{
    session_id: string;
  }>;
};

async function CreateCommunitySuccessPage({
  searchParams,
}: CreateCommunitySuccessPageProps) {
  const sessionId = (await searchParams).session_id as string;

  if (!sessionId) {
    return (
      <div className="flex h-screen items-center justify-center">
        <span>
          <Trans i18nKey="communities:noSessionIdOrCommunityNameProvided" />
        </span>
      </div>
    );
  }

  return (
    <div className={'mt-4 flex flex-col items-center justify-center py-14'}>
      <Card>
        <CardHeader>
          <CardTitle>
            <Trans i18nKey="communities:yourNewCommunity" />
          </CardTitle>
        </CardHeader>
        <CardContent>
          <SuccessPageContent sessionId={sessionId} />
        </CardContent>
      </Card>
    </div>
  );
}

export default withI18n(CreateCommunitySuccessPage);

CreateCommunitySuccessPage.displayName = 'CreateCommunitySuccessPage';
