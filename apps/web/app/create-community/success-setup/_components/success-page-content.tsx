'use client';

import { Spinner } from '@kit/ui/spinner';
import { Trans } from '@kit/ui/trans';

import { useCommunityAccess } from '../_hooks/use-community-access';
import { SetupCommunity } from './setup-community';

type SuccessPageContentProps = {
  sessionId: string;
};

export function SuccessPageContent({ sessionId }: SuccessPageContentProps) {
  const { status, communityData } = useCommunityAccess(sessionId);

  if (status === 'loading') {
    return (
      <div className="flex flex-col items-center gap-4">
        <Spinner />
        <p className="text-muted-foreground text-center">
          <Trans i18nKey="communities:settingUpYourCommunity" />
        </p>
        <p className="text-sm text-muted-foreground text-center">
          <Trans i18nKey="communities:communityCreationInProgress" />
        </p>
      </div>
    );
  }

  if (status === 'timeout') {
    return (
      <div className="flex flex-col items-center gap-4">
        <p className="text-muted-foreground text-center">
          <Trans i18nKey="communities:setupTakingLonger" />
        </p>
        <p className="text-sm text-muted-foreground text-center">
          <Trans i18nKey="communities:communityCreationTimeout" />
        </p>
      </div>
    );
  }

  if (status === 'ready' && communityData) {
    return (
      <SetupCommunity
        communitySlug={communityData.communitySlug}
        sessionId={sessionId}
        communityName={communityData.communityName}
      />
    );
  }

  return null;
}
