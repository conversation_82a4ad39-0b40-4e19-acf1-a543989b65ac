'use client';

import Link from 'next/link';

import { Button } from '@kit/ui/button';
import { Spinner } from '@kit/ui/spinner';
import { Trans } from '@kit/ui/trans';

import { useCommunityAccess } from '../_hooks/use-community-access';

type SuccessPageContentProps = {
  sessionId: string;
};

export function SuccessPageContent({ sessionId }: SuccessPageContentProps) {
  const { status, communityData } = useCommunityAccess(sessionId);

  if (status === 'loading') {
    return (
      <div className="flex flex-col items-center gap-4">
        <Spinner />
        <p className="text-muted-foreground text-center">
          <Trans i18nKey="communities:settingUpYourCommunity" />
        </p>
        <p className="text-muted-foreground text-center text-sm">
          <Trans i18nKey="communities:communityCreationInProgress" />
        </p>
      </div>
    );
  }

  if (status === 'timeout') {
    return (
      <div className="flex flex-col items-center gap-4">
        <p className="text-muted-foreground text-center">
          <Trans i18nKey="communities:setupTakingLonger" />
        </p>
        <p className="text-muted-foreground text-center text-sm">
          <Trans i18nKey="communities:communityCreationTimeout" />
        </p>
        <Button asChild className="mt-4">
          <Link href="/create-community">
            <Trans i18nKey="communities:createAnotherCommunity" />
          </Link>
        </Button>
      </div>
    );
  }

  if (status === 'ready' && communityData) {
    return (
      <div className="flex flex-col items-center gap-4">
        <div className="mb-4">
          <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
            <svg
              className="h-8 w-8 text-green-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
        </div>
        <h3 className="mb-2 text-lg font-medium">
          <Trans
            i18nKey="communities:communityCreatedSuccessfully"
            values={{ name: communityData.communityName }}
          />
        </h3>
        <p className="text-muted-foreground text-center">
          <Trans i18nKey="communities:communityCreationComplete" />
        </p>
        <p className="text-muted-foreground text-center text-sm">
          <Trans i18nKey="communities:redirectingToCommunity" />
        </p>
      </div>
    );
  }

  return null;
}
