'use client';

import Link from 'next/link';

import { Button } from '@kit/ui/button';
import { Trans } from '@kit/ui/trans';

type SetupCommunityProps = {
  communitySlug: string;
  sessionId: string;
  communityName: string;
};

export function SetupCommunity({
  communitySlug,
  sessionId,
  communityName,
}: SetupCommunityProps) {
  return (
    <div
      className="flex flex-col items-center justify-center space-y-4"
      data-testid="setup-community"
    >
      <h2 className="text-xl font-semibold">
        <Trans i18nKey={'communities:SetupCommunityTitle'} />
      </h2>

      <div className="space-y-2">
        <p className="text-muted-foreground">
          Community Name:{' '}
          <span className="text-foreground font-medium">{communityName}</span>
        </p>
        {communitySlug ? (
          <p className="text-muted-foreground">
            Community Slug:{' '}
            <span className="text-foreground font-medium">{communitySlug}</span>
          </p>
        ) : (
          <p className="text-muted-foreground">
            <Trans i18nKey="communities:communitySlugProcessing" />
          </p>
        )}
        <p className="text-muted-foreground">
          Session Id:{' '}
          <span className="text-foreground font-medium">{sessionId}</span>
        </p>
      </div>

      <Button asChild>
        <Link href={`/${communitySlug}`}>
          <Trans i18nKey="communities:goToCommunity" />
        </Link>
      </Button>
    </div>
  );
}

SetupCommunity.displayName = 'SetupCommunity';
