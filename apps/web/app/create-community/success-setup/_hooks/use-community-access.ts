'use client';

import { useEffect, useState } from 'react';

import { checkCommunityReadyAction } from '../_lib/check-community-ready';

type CommunityStatus = 'loading' | 'ready' | 'timeout';

type CommunityData = {
  communitySlug: string;
  communityName: string;
} | null;

export function useCommunityAccess(sessionId: string) {
  const [status, setStatus] = useState<CommunityStatus>('loading');
  const [communityData, setCommunityData] = useState<CommunityData>(null);

  useEffect(() => {
    let pollInterval: NodeJS.Timeout;
    let timeoutId: NodeJS.Timeout;

    const checkCommunityStatus = async () => {
      try {
        console.log('🔍 Checking community status for session:', sessionId);
        
        // Check if community exists and is ready (lightweight check)
        const result = await checkCommunityReadyAction({ sessionId });
        
        if (result?.communitySlug) {
          console.log('✅ Community ready:', result);
          setStatus('ready');
          setCommunityData(result);
          clearInterval(pollInterval);
          clearTimeout(timeoutId);
          return true;
        }
        
        console.log('⏳ Community not ready yet, continuing to poll...');
        return false;
      } catch (error) {
        console.error('❌ Error checking community status:', error);
        return false;
      }
    };

    // Start polling every 2 seconds
    pollInterval = setInterval(checkCommunityStatus, 2000);

    // Check immediately
    checkCommunityStatus();

    // Timeout after 60 seconds
    timeoutId = setTimeout(() => {
      console.log('⏰ Community creation timeout reached');
      clearInterval(pollInterval);
      if (status === 'loading') {
        setStatus('timeout');
      }
    }, 60000);

    return () => {
      clearInterval(pollInterval);
      clearTimeout(timeoutId);
    };
  }, [sessionId, status]);

  return { status, communityData };
}
