'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

export const checkCommunityReadyAction = enhanceAction(
  async ({ sessionId }, user) => {
    const logger = await getLogger();
    const client = getSupabaseServerClient();
    
    const ctx = {
      name: 'checkCommunityReadyAction',
      sessionId,
      userId: user.id,
    };

    logger.info(ctx, 'Checking if community is ready');

    // Get checkout session to find community_id and community_name
    const { data: checkoutSession, error: checkoutError } = await client
      .from('checkout_sessions')
      .select('community_id, community_name, purchase_type')
      .eq('id', sessionId)
      .eq('user_id', user.id) // Ensure user owns this session
      .eq('purchase_type', 'community_ownership') // Ensure it's a community creation session
      .single();

    if (checkoutError || !checkoutSession) {
      logger.warn({ ...ctx, error: checkoutError }, 'Checkout session not found');
      return null;
    }

    if (!checkoutSession.community_id) {
      logger.warn(ctx, 'No community_id in checkout session');
      return null;
    }

    // Lightweight check - just see if community exists and has a slug
    const { data: community, error: communityError } = await client
      .from('communities')
      .select('slug, name, is_enabled')
      .eq('id', checkoutSession.community_id)
      .single();

    if (communityError || !community) {
      logger.info({ ...ctx, error: communityError }, 'Community not found yet');
      return null;
    }

    if (!community.slug) {
      logger.info(ctx, 'Community exists but slug not generated yet');
      return null;
    }

    if (!community.is_enabled) {
      logger.info(ctx, 'Community exists but not enabled yet');
      return null;
    }

    logger.info(ctx, 'Community is ready', {
      communitySlug: community.slug,
      communityName: community.name,
    });

    return {
      communitySlug: community.slug,
      communityName: community.name || checkoutSession.community_name || 'Your Community',
    };
  },
  {
    auth: true,
    schema: z.object({ sessionId: z.string() }),
  },
);
