'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';

import { createCommunityManagementService } from '~/lib/communities/management/community-management.service';
import { createStripeSdk } from '~/lib/stripe/utils/stripe-sdk';

export const successCreateCommunityAction = enhanceAction(
  async ({ sessionId }, user) => {
    const logger = await getLogger();

    const ctx = {
      name: 'success-create-community',
      sessionId,
      userId: user.id,
    };

    const client = getSupabaseServerAdminClient();
    const stripe = createStripeSdk();

    logger.info(
      { ctx, userId: user.id },
      'Getting checkout session from stripe',
    );

    logger.info(ctx, 'Retrieving checkout session from <PERSON><PERSON>');
    const stripeCheckoutSession =
      await stripe.checkout.sessions.retrieve(sessionId);

    if (
      stripeCheckoutSession.status !== 'complete' ||
      stripeCheckoutSession.payment_status !== 'paid'
    ) {
      logger.warn(
        { ctx, stripeSubscription: stripeCheckoutSession },
        'Session not completed – aborting',
      );
      return { error: true };
    }

    logger.info({ ctx, userId: user.id }, 'Getting checkout session from db');

    const { data: checkoutSessionDb, error: checkoutSessionDbError } =
      await client
        .from('checkout_sessions')
        .select('*')
        .eq('id', sessionId)
        .maybeSingle();

    if (checkoutSessionDbError || !checkoutSessionDb) {
      logger.error(
        { ...ctx, error: checkoutSessionDbError },
        '❌ Error getting checkout session from db:',
      );
      throw new Error('Error getting checkout session from db');
    }

    const communityName = stripeCheckoutSession.metadata?.community_name;

    if (!communityName) {
      logger.error(
        ctx,
        'Community name not found in checkout session metadata',
      );
      throw new Error('Community name not found in checkout session metadata');
    }

    if (!stripeCheckoutSession.subscription) {
      logger.error(ctx, 'No subscription found in checkout session');
      throw new Error('No subscription found in checkout session');
    }

    logger.info(
      { ctx, checkoutSessionDbId: checkoutSessionDb.id },
      'Checkout session retrieved from db',
    );

    try {
      const communityId = checkoutSessionDb.community_id as string;
      const subscriptionId = stripeCheckoutSession.subscription as string;

      // Check if community already exists
      logger.info(ctx, 'Checking if community already exists');
      const { data: existingCommunity } = await client
        .from('communities')
        .select('*')
        .eq('id', communityId)
        .maybeSingle();

      if (existingCommunity) {
        logger.info(
          ctx,
          'Community already exists, returning existing community',
        );
        return { communitySlug: existingCommunity.slug, communityName };
      }

      // Check if subscription exists (might have been created by webhook)
      logger.info(ctx, 'Checking if subscription already exists');
      const { data: existingSubscription } = await client
        .from('subscriptions')
        .select('*')
        .eq('id', subscriptionId)
        .maybeSingle();

      if (existingSubscription && !existingSubscription.community_id) {
        // Subscription exists but not linked to community yet
        logger.info(
          ctx,
          'Subscription exists, creating community and linking them',
        );

        // Create community using existing service
        const communityManagementService =
          createCommunityManagementService(client);
        const { data: newCommunity, error: createError } =
          await communityManagementService.createNewCommunity({
            name: communityName,
            communityId: communityId,
            primaryOwnerUserId: user.id,
          });

        if (createError || !newCommunity?.slug) {
          logger.error(
            { ...ctx, error: createError },
            'Failed to create community',
          );
          return { error: true };
        }

        // Link subscription to community
        logger.info(ctx, 'Linking subscription to community');

        // Update subscription to link it to community
        const { error: updateSubError } = await client
          .from('subscriptions')
          .update({
            community_id: communityId,
            updated_at: new Date().toISOString(),
          })
          .eq('id', subscriptionId)
          .is('community_id', null);

        if (updateSubError) {
          logger.error(
            { ...ctx, error: updateSubError },
            'Failed to link subscription to community',
          );
          return { error: true };
        }

        // Update community to reference subscription and enable it
        const { error: updateCommError } = await client
          .from('communities')
          .update({
            subscription_id: subscriptionId,
            is_enabled: true,
            updated_at: new Date().toISOString(),
          })
          .eq('id', communityId);

        if (updateCommError) {
          logger.error(
            { ...ctx, error: updateCommError },
            'Failed to update community with subscription',
          );
          return { error: true };
        }

        logger.info(
          ctx,
          'Community created and linked to subscription successfully',
        );
        return { communitySlug: newCommunity.slug, communityName };
      } else if (existingSubscription && existingSubscription.community_id) {
        // Subscription already linked to community, get community details
        logger.info(ctx, 'Subscription already linked, retrieving community');
        const { data: linkedCommunity } = await client
          .from('communities')
          .select('slug')
          .eq('id', existingSubscription.community_id)
          .single();

        if (linkedCommunity?.slug) {
          return { communitySlug: linkedCommunity.slug, communityName };
        }
      } else if (!existingSubscription) {
        // Subscription doesn't exist yet, but we can still create the community
        // since we have all the required information from the checkout session
        logger.info(
          ctx,
          'Subscription not found, but creating community anyway since we have checkout session data',
        );

        // Create community using existing service
        const communityManagementService =
          createCommunityManagementService(client);
        const { data: newCommunity, error: createError } =
          await communityManagementService.createNewCommunity({
            name: communityName,
            communityId: communityId,
            primaryOwnerUserId: user.id,
          });

        if (createError || !newCommunity?.slug) {
          logger.error(
            { ...ctx, error: createError },
            'Failed to create community',
          );
          return { error: true };
        }

        logger.info(
          ctx,
          'Community created successfully, webhook will link subscription later',
        );
        return { communitySlug: newCommunity.slug, communityName };
      } else {
        // Subscription doesn't exist yet, create both atomically
        logger.info(
          ctx,
          'Subscription not found, creating community and subscription atomically',
        );

        // For now, log this rare case and return error
        // This should be very rare since webhooks usually fire before success page
        logger.warn(
          ctx,
          'Rare case: Success page loaded before webhook created subscription',
        );
        logger.warn(
          ctx,
          'Consider implementing atomic function if this happens frequently',
        );

        return { error: true };
      }
    } catch (error) {
      logger.error(
        {
          ctx,
          error,
          errorMessage: error instanceof Error ? error.message : String(error),
          errorStack: error instanceof Error ? error.stack : undefined,
        },
        'Error creating community with payment',
      );
      return { error: true };
    }
  },
  {
    auth: true,
    schema: z.object({
      sessionId: z.string(),
    }),
  },
);
