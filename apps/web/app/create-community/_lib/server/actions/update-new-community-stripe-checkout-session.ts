'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';

import { createStripeSdk } from '~/lib/stripe/utils/stripe-sdk';

export const updateNewCommunityStripeCheckoutSessionAction = enhanceAction(
  async ({ sessionId, communityName }, user) => {
    const logger = await getLogger();

    const ctx = {
      name: 'update-stripe-checkout-session',
      sessionId,
      communityName,
      userId: user.id,
    };

    const client = getSupabaseServerAdminClient();
    const stripe = createStripeSdk();

    logger.info(ctx, 'Getting checkout session from stripe');

    const checkoutSession = await stripe.checkout.sessions.retrieve(sessionId);

    if (!checkoutSession) {
      logger.error(ctx, '❌ No checkout session found');
      throw new Error('Checkout session not found');
    }

    logger.info(ctx, 'Checkout session retrieved', {
      checkoutSessionId: checkoutSession.id,
    });

    let clientSecret = checkoutSession.client_secret;

    try {
      const updateCheckoutSession = await stripe.checkout.sessions.update(
        sessionId,
        {
          metadata: {
            community_name: communityName,
          },
        },
      );

      logger.info(ctx, '✅ Checkout session stripe updated successfully:', {
        id: updateCheckoutSession.id,
        hasClientSecret: !!updateCheckoutSession.client_secret,
        url: updateCheckoutSession.url,
        mode: updateCheckoutSession.mode,
        communityName,
      });

      clientSecret = updateCheckoutSession.client_secret;

      // Update the checkout session in the database
      const { error: updateCheckoutSessionDbError } = await client
        .from('checkout_sessions')
        .update({
          community_name: communityName,
        })
        .eq('id', sessionId);

      if (updateCheckoutSessionDbError) {
        logger.error(
          { ...ctx, error: updateCheckoutSessionDbError },
          '❌ Error updating checkout session db',
        );
        throw new Error('Error updating checkout session db');
      } else {
        logger.info(ctx, '✅ Checkout session db updated successfully:', {
          id: updateCheckoutSession.id,
          hasClientSecret: !!updateCheckoutSession.client_secret,
          url: updateCheckoutSession.url,
          mode: updateCheckoutSession.mode,
          communityName,
        });
      }

      if (!updateCheckoutSession.client_secret) {
        logger.error(ctx, '❌ No client secret returned in checkout session');
        throw new Error('Payment setup failed - no client secret available');
      }
    } catch (error) {
      logger.error(
        { ...ctx, error },
        'Failed to update checkout server session',
      );
      throw error;
    }

    return {
      checkoutToken: clientSecret,
      communityName,
    };
  },
  {
    auth: true,
    schema: z.object({
      sessionId: z.string(),
      communityName: z.string(),
    }),
  },
);
