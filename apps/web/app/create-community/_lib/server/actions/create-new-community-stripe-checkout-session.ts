'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { getStripeCustomerService } from '~/lib/stripe/services';
import { createStripeSdk } from '~/lib/stripe/utils/stripe-sdk';

export const createNewCommunityStripeCheckoutSessionAction = enhanceAction(
  async ({ priceId, trialDays, purchaseType, returnUrl }, user) => {
    const logger = await getLogger();

    const ctx = {
      name: 'createNewCommunityStripeCheckoutSessionAction',
    };
    logger.info(
      ctx,
      '🔍 createNewCommunityStripeCheckoutSessionAction - Inputs:',
      {
        priceId,
        trialDays,
        userId: user.id,
        user: user,
      },
    );

    const client = getSupabaseServerClient();
    const stripe = createStripeSdk('V2');

    logger.info(
      { ctx, userId: user.id },
      'Getting customer for checkout session',
    );

    const customerService = getStripeCustomerService(client);

    const stripeAccount = await customerService.getStripeAccountBilling(
      user.id,
    );

    if (!stripeAccount) {
      logger.error(
        { ...ctx, userId: user.id },
        '❌ No stripe account found for user',
      );
      throw new Error('Stripe account not found');
    }

    logger.info(ctx, '🔍 Attempting to create checkout session with price:', {
      priceId,
    });

    try {
      // Create a community UUID for the metadata
      const communityUuid = crypto.randomUUID();
      const clientReferenceId = crypto.randomUUID();

      const checkoutSession = await stripe.checkout.sessions.create({
        client_reference_id: clientReferenceId,
        customer_account: stripeAccount.stripeAccountId,
        ui_mode: 'custom',
        line_items: [{ price: priceId, quantity: 1 }],
        mode: 'subscription',
        subscription_data: {
          ...(trialDays ? { trial_period_days: trialDays } : {}),
          metadata: {
            user_id: user.id,
            community_id: communityUuid,
            purchase_type: purchaseType,
          },
        },
        payment_method_types: ['card'],
        metadata: {
          user_id: user.id,
          community_id: communityUuid,
          purchase_type: purchaseType,
        },
        return_url: returnUrl,
      });

      // Insert the checkout session into the database
      await client.from('checkout_sessions').insert({
        id: checkoutSession.id,
        user_id: user.id,
        community_id: communityUuid,
        price_id: priceId,
        purchase_type: purchaseType,
        trial_days: trialDays,
        metadata: {
          user_id: user.id,
          community_id: communityUuid,
        },
      });

      if (!checkoutSession.client_secret) {
        logger.error(ctx, '❌ No client secret returned in checkout session');
        throw new Error('Payment setup failed - no client secret available');
      }

      return {
        checkoutToken: checkoutSession.client_secret,
        communityId: communityUuid,
      };
    } catch (error) {
      logger.error(
        { ...ctx, error },
        '❌ Failed to create checkout server session:',
      );
      throw error;
    }
  },
  {
    auth: true,
    schema: z.object({
      priceId: z.string(),
      trialDays: z.number(),
      purchaseType: z.enum(['community_ownership', 'community_membership']),
      returnUrl: z.string(),
    }),
  },
);
