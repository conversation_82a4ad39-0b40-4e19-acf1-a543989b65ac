import 'server-only';

import { cache } from 'react';

import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { getStripeProductsService } from '~/lib/stripe/services';
import type { PlatformProduct, ProductPrice } from '~/lib/stripe/types';

export type CreateCommunityLoader = Awaited<
  ReturnType<typeof createCommunityLoader>
>;

/**
 * Load the create community prices.
 * @returns Object containing available prices with their active prices
 */
export const createCommunityLoader = cache(createCommunityLoaderPrices);

async function createCommunityLoaderPrices() {
  const logger = await getLogger();

  const ctx = {
    name: 'createCommunityLoaderPrices',
  };
  logger.info(ctx, '🔍 Starting createCommunityLoaderPrices');

  const client = getSupabaseServerClient();
  const productsService = getStripeProductsService(client);

  logger.info(ctx, '🔍 Fetching products from Stripe');

  try {
    const products = (await productsService.getProducts({
      activeOnly: true,
      withPrices: true,
      seller: 'platform',
    })) as PlatformProduct[];

    logger.info(ctx, '✅ Products fetched:', {
      count: products.length,
      productNames: products.map((p) => p.name),
    });

    const prices = products.flatMap((p) =>
      (p.productPrices ?? []).filter((price) => price.active),
    ) as ProductPrice[];

    logger.info(ctx, '✅ Prices extracted:', {
      count: prices.length,
      firstPrice: prices[0]
        ? {
            id: prices[0].id,
            amount: prices[0].unitAmount,
            interval: prices[0].interval,
          }
        : 'None',
    });

    return {
      products,
      prices,
    };
  } catch (error) {
    logger.error({ ...ctx, error }, '❌ Error in createCommunityLoaderPrices:');
    // Return empty values to prevent app crash
    return {
      products: [],
      prices: [],
    };
  }
}
