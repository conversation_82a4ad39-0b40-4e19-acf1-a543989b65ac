'use client';

import { useCallback, useEffect, useState } from 'react';

import { zodResolver } from '@hookform/resolvers/zod';
import { CheckoutProvider } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import { useForm } from 'react-hook-form';

import { useUser } from '@kit/supabase/hooks/use-user';
import { Button } from '@kit/ui/button';
import { StatusAlert } from '@kit/ui/dojo/molecules/status-alert';
import { Skeleton } from '@kit/ui/skeleton';

import { CreateCommunitySchema } from '~/lib/communities/community/schema/create-community.schema';

import { createNewCommunityStripeCheckoutSessionAction } from '../_lib/server/actions/create-new-community-stripe-checkout-session';
import { FormWithPayment } from './form-with-payment';
import { CreateCommunityFormProps, FormData } from './types';

// Initialize Stripe on the client side
const publishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;
if (!publishableKey) {
  throw new Error('Missing NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY env variable');
}
const stripePromise = loadStripe(publishableKey);

export function CreateCommunityForm({
  products,
  prices,
}: CreateCommunityFormProps) {
  const [error, setError] = useState<string | boolean | undefined>();
  const [isLoading, setIsLoading] = useState(false);
  const [clientSecret, setClientSecret] = useState<string | null>(null);

  const [paymentError, setPaymentError] = useState<string | null>(null);

  const { data: user, isPending: isUserLoading } = useUser();

  const form = useForm<FormData>({
    defaultValues: {
      name: '',
    },
    resolver: zodResolver(CreateCommunitySchema),
    mode: 'onChange',
  });

  // Create a function to create checkout session
  const createCheckoutSession = useCallback(async (): Promise<
    string | null
  > => {
    if (!prices.length) {
      setError('No pricing options available');
      return null;
    }

    try {
      setIsLoading(true);
      const result = await createNewCommunityStripeCheckoutSessionAction({
        priceId: prices[0]?.id || '',
        trialDays: products[0]?.trialDays || 0,
        purchaseType: 'community_ownership',
      });

      return result.checkoutToken;
    } catch (err) {
      setError(`Payment setup failed ${err}`);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [prices, products, setError, setIsLoading]);

  // Initialize checkout session on component mount
  useEffect(() => {
    if (
      !clientSecret &&
      !isLoading &&
      prices.length > 0 &&
      user &&
      !isUserLoading
    ) {
      setIsLoading(true);

      createCheckoutSession()
        .then((secret) => {
          if (secret) {
            setClientSecret(secret);
          } else {
            setError('Failed to initialize payment form');
          }
          setIsLoading(false);
        })
        .catch((err) => {
          setError(`Payment setup failed ${err}`);
          setIsLoading(false);
        });
    }
  }, [
    clientSecret,
    isLoading,
    prices,
    user,
    isUserLoading,
    createCheckoutSession,
  ]);

  if (isUserLoading || !user) {
    return <Skeleton className="h-[400px]" />;
  }

  // Only render the form when we have a client secret
  if (!clientSecret) {
    return (
      <div className="flex flex-col items-center justify-center space-y-4 py-8">
        <div className="border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent" />
        <p>Preparing payment form...</p>
      </div>
    );
  }

  // Get price information to display
  const price = prices.length > 0 ? prices[0] : null;
  const priceDisplay = price
    ? `$${((price.unitAmount || 0) / 100).toFixed(2)}/${price.interval}`
    : '$99/month';

  // Display any error from the main component before rendering the payment form
  if (error) {
    return (
      <div className="space-y-6">
        <StatusAlert
          variant="destructive"
          titleKey="communities:createCommunityErrorHeading"
          descriptionKey="communities:createCommunityErrorMessage"
        />
        <Button
          onClick={() => {
            setError(undefined);
            setClientSecret(null);
          }}
          className="w-full"
        >
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="checkout-wrapper">
      <CheckoutProvider
        stripe={stripePromise}
        options={{
          fetchClientSecret: async () => clientSecret || '',
          elementsOptions: {
            appearance: { theme: 'stripe' },
          },
        }}
      >
        <FormWithPayment
          form={form}
          isLoading={isLoading}
          setIsLoading={setIsLoading}
          paymentError={paymentError}
          setPaymentError={setPaymentError}
          priceDisplay={priceDisplay}
          trialDays={products[0]?.trialDays}
        />
      </CheckoutProvider>
    </div>
  );
}

CreateCommunityForm.displayName = 'CreateCommunityForm';
