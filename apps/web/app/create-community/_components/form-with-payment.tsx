'use client';

import { useMemo } from 'react';

import { PaymentElement, useCheckout } from '@stripe/react-stripe-js';
import { useTranslation } from 'react-i18next';

import { Button } from '@kit/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { Trans } from '@kit/ui/trans';

import pathsConfig from '~/config/paths.config';

import { updateNewCommunityStripeCheckoutSessionAction } from '../_lib/server/actions/update-new-community-stripe-checkout-session';
import { FormData, FormWithPaymentProps } from './types';

export function FormWithPayment({
  form,
  isLoading,
  setIsLoading,
  paymentError,
  setPaymentError,
  priceDisplay,
  trialDays,
}: FormWithPaymentProps) {
  const checkout = useCheckout();
  const { t } = useTranslation('communities');

  // Use a static element for payment to prevent re-renders
  const paymentElement = useMemo(
    () => (checkout ? <PaymentElement /> : null),
    [checkout],
  );

  const onSubmit = async (data: FormData) => {
    if (!checkout) {
      setPaymentError(t('paymentSetupFailed'));
      return;
    }

    try {
      setIsLoading(true);

      await updateNewCommunityStripeCheckoutSessionAction({
        sessionId: checkout.id,
        communityName: data.name,
      });

      const result = await checkout.confirm({
        returnUrl: `${window.location.origin}/create-community/success-setup?session_id={CHECKOUT_SESSION_ID}`,
      });

      if (result.type === 'error') {
        setPaymentError(result.error.message);
      }
    } catch {
      setPaymentError(t('joinCommunityErrorMessage'));
    } finally {
      setIsLoading(false);
    }
  };

  // Show loading if checkout is not available yet
  if (!checkout) {
    return (
      <div className="py-4 text-center">
        <p>
          <Trans i18nKey="communities:preparingPaymentForm" />
        </p>
      </div>
    );
  }

  const today = new Date();
  const firstChargeDate = new Date(
    today.getTime() + (trialDays ?? 0) * 24 * 60 * 60 * 1000,
  );

  return (
    <Form {...form}>
      <form
        data-test="create-community-form"
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-6"
      >
        <div className="text-center">
          <h2 className="text-2xl font-bold">
            <Trans i18nKey="communities:createCommunityModalHeading" />
          </h2>
        </div>

        {paymentError && (
          <div className="text-sm text-red-500">{paymentError}</div>
        )}

        <FormField
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                <Trans i18nKey="communities:communityNameLabel" />
              </FormLabel>
              <FormControl>
                <Input
                  data-test="create-community-name-input"
                  required
                  minLength={2}
                  maxLength={50}
                  placeholder="Community name"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                <Trans
                  i18nKey="communities:communityNameDescription"
                  values={{
                    current: field.value?.length || 0,
                    max: 50,
                  }}
                />
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="space-y-2">
          <FormLabel>
            <Trans i18nKey="common:creditCard" />
          </FormLabel>

          {paymentElement || <PaymentElement />}
        </div>

        <Button
          data-test="confirm-create-community-button"
          type="submit"
          disabled={
            isLoading || !form.formState.isValid || !checkout.canConfirm
          }
          className="w-full"
        >
          {isLoading ? (
            <Trans i18nKey="communities:creatingCommunity" />
          ) : trialDays && trialDays > 0 ? (
            <Trans i18nKey="communities:startFreeTrialButton" />
          ) : (
            <Trans i18nKey="communities:createCommunitySubmitLabel" />
          )}
        </Button>

        <div className="text-muted-foreground text-center text-xs">
          <Trans
            i18nKey="communities:freeTrialBlurb"
            values={{
              trialDays,
              nextPaymentDate: firstChargeDate.toLocaleDateString(),
              price: priceDisplay,
            }}
            components={{
              Link: (
                <a
                  href={pathsConfig.app.terms}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="underline"
                />
              ),
            }}
          />
        </div>
      </form>
    </Form>
  );
}

FormWithPayment.displayName = 'FormWithPayment';
