import { UseFormReturn } from 'react-hook-form';
import { z } from 'zod';

import { CreateCommunitySchema } from '~/lib/communities/community/schema/create-community.schema';
import type { PlatformProduct, ProductPrice } from '~/lib/stripe/types';

export type FormData = z.infer<typeof CreateCommunitySchema>;

export type CreateCommunityFormProps = {
  products: PlatformProduct[];
  prices: ProductPrice[];
};

export type FormWithPaymentProps = {
  form: UseFormReturn<FormData>;
  isLoading: boolean;
  setIsLoading: (isLoading: boolean) => void;
  paymentError: string | null;
  setPaymentError: (error: string | null) => void;
  priceDisplay: string;
  trialDays: number | null | undefined;
};
