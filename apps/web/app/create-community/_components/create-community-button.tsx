import Link from 'next/link';

import { But<PERSON> } from '@kit/ui/button';
import { Trans } from '@kit/ui/trans';

import pathsConfig from '~/config/paths.config';

export function CreateCommunityButton(props: { className?: string }) {
  return (
    <Link href={pathsConfig.app.createCommunity}>
      <Button className={props.className}>
        <Trans i18nKey={'communities:createCommunityButtonLabel'} />
      </Button>
    </Link>
  );
}

CreateCommunityButton.displayName = 'CreateCommunityButton';
