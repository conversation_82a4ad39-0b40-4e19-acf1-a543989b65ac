import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@kit/ui/card';
import { Trans } from '@kit/ui/trans';

import { withI18n } from '~/lib/i18n/with-i18n';

import { CreateCommunityForm } from './_components/create-community-form';
import { createCommunityLoader } from './_lib/server/create-community.loader';

async function CreateCommunityPage() {
  try {
    const { products, prices } = await createCommunityLoader();

    if (!prices.length) {
      return (
        <div>
          <h1>No prices available for community creation</h1>
        </div>
      );
    }

    return (
      <div className={'mt-4 flex flex-col py-14'}>
        <div
          className={'container mx-auto grid grid-cols-1 gap-8 md:grid-cols-2'}
        >
          <Card>
            <CardHeader>
              <CardTitle>Benefits of Dojo</CardTitle>
            </CardHeader>

            <CardContent>
              <ul className={'list-disc space-y-2 pl-5'}>
                <li>
                  <Trans i18nKey={'communities:createCommunityBenefits1'} />
                </li>
                <li>
                  <Trans i18nKey={'communities:createCommunityBenefits2'} />
                </li>
                <li>
                  <Trans i18nKey={'communities:createCommunityBenefits3'} />
                </li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Create Community</CardTitle>
            </CardHeader>

            <CardContent>
              <CreateCommunityForm products={products} prices={prices} />
            </CardContent>
          </Card>
        </div>
      </div>
    );
  } catch (error) {
    return (
      <div className="container mx-auto mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Error Loading Community Creator</CardTitle>
          </CardHeader>
          <CardContent>
            <p>
              There was an error loading the community creation form. Please try
              again later.
            </p>
            <p className="text-muted-foreground mt-2 text-sm">
              {error instanceof Error ? error.message : 'Unknown error'}
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }
}

export default withI18n(CreateCommunityPage);

CreateCommunityPage.displayName = 'CreateCommunityPage';
