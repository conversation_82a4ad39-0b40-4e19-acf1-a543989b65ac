import { Page, PageNavigation } from '@kit/ui/page';

import { SiteHeader } from '~/_components/site-header';
import { BackgroundHue } from '~/components/background-hue';
import { withI18n } from '~/lib/i18n/with-i18n';
import { loadUserWorkspace } from '~/lib/users/user-workspace.loader';

async function ExploreLayout(props: React.PropsWithChildren) {
  const workspace = await loadUserWorkspace();

  return (
    <>
      <Page style={'header'}>
        <PageNavigation>
          <SiteHeader workspace={workspace} />
        </PageNavigation>

        {props.children}
      </Page>
      <BackgroundHue />
    </>
  );
}

export default withI18n(ExploreLayout);
