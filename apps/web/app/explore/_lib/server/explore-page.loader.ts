import 'server-only';

import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createCommunityService } from '~/lib/communities/community/community.service';
import type {
  CommunityCategory,
  Language,
  PublicCommunityView,
} from '~/lib/communities/community/types';
import { createSystemService } from '~/lib/system/services/system.service';

/**
 * Load data for the explore page
 */
export async function loadExplorePageData(
  categoryId?: string,
  languageId?: string,
  isPrivate?: boolean,
  isPaid?: boolean,
  page = 1,
  limit = 10,
) {
  const logger = await getLogger();
  const ctx = {
    name: 'load-explore-page-data',
    categoryId,
    languageId,
    page,
    limit,
  };

  try {
    logger.info(ctx, 'Loading explore page data');
    const client = getSupabaseServerClient();
    const communityService = createCommunityService(client);
    const coreService = createSystemService(client);

    const communities =
      await communityService.getAllCommunitiesWithProductPrices({
        categoryId,
        languageId,
        isPrivate,
        isPaid,
        isListed: true,
        page,
        limit,
      });

    const categories = await communityService.getCommunityCategories({
      sortByCount: true,
    });

    const languages = await coreService.getSystemLanguages();

    if (!communities) {
      logger.error(
        { ...ctx, error: 'Communities not found' },
        'Failed to load explore data',
      );
      return {
        communities: [],
        categories: [],
        languages: [],
        error: 'Communities not found',
      };
    }

    logger.info(ctx, 'Successfully loaded explore page data');
    return {
      communities: communities.communities as PublicCommunityView[],
      categories: categories.categories as CommunityCategory[],
      languages: languages.languages as Language[],
      error: null,
    };
  } catch (error) {
    logger.error(
      { ...ctx, error },
      'Unexpected error loading explore page data',
    );
    return {
      communities: [],
      categories: [],
      languages: [],
      error: 'An unexpected error occurred loading explore page data',
    };
  }
}
