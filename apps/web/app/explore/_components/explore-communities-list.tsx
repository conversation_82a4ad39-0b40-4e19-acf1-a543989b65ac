import { CommunityCard } from '@kit/ui/dojo/organisms/community-card';
import {
  EmptyState,
  EmptyStateButton,
  EmptyStateHeading,
  EmptyStateText,
} from '@kit/ui/empty-state';
import { Trans } from '@kit/ui/trans';

import { CreateCommunityButton } from '~/create-community/_components/create-community-button';
import type { PublicCommunityView } from '~/lib/communities/community/types';

type ExploreCommunitiesListProps = {
  communities: PublicCommunityView[];
};

export function ExploreCommunitiesList({
  communities,
}: ExploreCommunitiesListProps) {
  if (!communities || communities.length === 0) {
    return <ExploreCommunitiesListEmptyState />;
  }

  function getFeeAmount(community: PublicCommunityView): number {
    if (community.productPrices && community.productPrices.length > 0) {
      // Priority order for fee display: show shortest duration first
      // to present the most affordable entry point to user
      const priorityOrder = ['day', 'week', 'month', 'year'];

      // Sort prices by priority
      const sortedPrices = Array.from(community.productPrices).sort((a, b) => {
        const aPriority = priorityOrder.indexOf(a.interval);
        const bPriority = priorityOrder.indexOf(b.interval);
        return aPriority - bPriority;
      });

      // unitAmount is in cents, convert to currency units
      const firstPrice = sortedPrices[0]?.unitAmount;
      return firstPrice ? firstPrice / 100 : 0;
    }

    return 0;
  }

  return (
    <div className="flex flex-col">
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3">
        {communities.map((community) => {
          const totalMemberCount =
            (community.memberCount ?? 0) + (community.adminCount ?? 0);
          return (
            <CommunityCard
              key={community.slug}
              name={community.name ?? ''}
              description={community.description ?? ''}
              pictureUrl={community.logoUrl ?? ''}
              coverImage={community.coverUrl ?? ''}
              slug={`${community.slug}/about`}
              memberCount={totalMemberCount}
              showFooter={true}
              isPrivate={community.isPrivate}
              feeAmount={getFeeAmount(community)}
              feeInterval={community.productPrices?.[0]?.interval ?? ''}
              categoryName={community.categoryName ?? ''}
              categoryIcon={community.categoryIcon ?? ''}
              languageName={community.languageName ?? ''}
              languageIcon={community.languageIcon ?? ''}
              data-test={`explore-communities-list-item-${community.slug}`}
            />
          );
        })}
      </div>
    </div>
  );
}

function ExploreCommunitiesListEmptyState() {
  return (
    <div className={'flex items-start justify-center'}>
      <EmptyState className={'border-0 shadow-none'}>
        <EmptyStateButton asChild>
          <CreateCommunityButton className={'mt-4'} />
        </EmptyStateButton>
        <EmptyStateHeading>
          <Trans i18nKey={'explore:noCommunities'} />
        </EmptyStateHeading>
        <EmptyStateText>
          <Trans i18nKey={'explore:createACommunity'} />
        </EmptyStateText>
      </EmptyState>
    </div>
  );
}

ExploreCommunitiesList.displayName = 'ExploreCommunitiesList';
ExploreCommunitiesListEmptyState.displayName =
  'ExploreCommunitiesListEmptyState';
