'use client';

import { useState } from 'react';
import { useMemo } from 'react';

import { usePathname, useRouter, useSearchParams } from 'next/navigation';

import { FilterIcon } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { Button } from '@kit/ui/button';
import { CategoryFilter } from '@kit/ui/dojo/organisms/category-filter';
import { Popover, PopoverContent, PopoverTrigger } from '@kit/ui/popover';
import { RadioGroup, RadioGroupItem } from '@kit/ui/radio-group';

import type {
  CommunityCategory,
  Language,
} from '~/lib/communities/community/types';

type FilterType = 'language' | 'isPrivate' | 'isPaid';

type FilterOption = {
  value: string;
  label: string;
};

type FilterSectionProps = {
  title: string;
  options: FilterOption[];
  currentValue: string;
  onValueChange: (value: string) => void;
  filterType: FilterType;
  className?: string;
};

type ExploreHeaderProps = {
  categories: CommunityCategory[] | undefined;
  languages: Language[] | undefined;
};

// Reusable filter section component
function FilterSection({
  title,
  options,
  currentValue,
  onValueChange,
  filterType,
  className,
}: FilterSectionProps) {
  return (
    <div>
      <h4 className="mb-2 font-medium">{title}</h4>
      <RadioGroup
        value={currentValue}
        onValueChange={(value: string) => onValueChange(value)}
        className={className}
      >
        {options.map((option) => (
          <div key={option.value} className="flex items-center space-x-2">
            <RadioGroupItem
              value={option.value}
              id={`${filterType}-${option.value}`}
            />
            <label
              htmlFor={`${filterType}-${option.value}`}
              className="cursor-pointer truncate text-sm"
              title={option.label}
            >
              {option.label}
            </label>
          </div>
        ))}
      </RadioGroup>
    </div>
  );
}

export function ExploreHeader({ categories, languages }: ExploreHeaderProps) {
  const { t } = useTranslation('communities');
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Get current filter values from URL
  const currentLanguage = searchParams.get('language') ?? 'all';
  const currentVisibility = searchParams.get('isPrivate') ?? 'all';
  const currentPrice = searchParams.get('isPaid') ?? 'all';

  // State to track pending filter changes
  const [pendingLanguage, setPendingLanguage] = useState(currentLanguage);
  const [pendingVisibility, setPendingVisibility] = useState(currentVisibility);
  const [pendingPrice, setPendingPrice] = useState(currentPrice);

  // State to track open/closed state of the popover
  const [open, setOpen] = useState(false);

  // Reset pending filters to current values when popover opens
  const handleOpenChange = (isOpen: boolean) => {
    if (isOpen) {
      // Reset pending filters to current values when opening
      setPendingLanguage(currentLanguage);
      setPendingVisibility(currentVisibility);
      setPendingPrice(currentPrice);
    }
    setOpen(isOpen);
  };

  // Memoized language options
  const languageOptions = useMemo(
    () => [
      { value: 'all', label: t('filters.allLanguages', 'All languages') },
      ...(languages?.map((language) => ({
        value: language.id,
        label: t(`languages.${language.name}`),
      })) ?? []),
    ],
    [languages, t],
  );

  // Memoized visibility options
  const visibilityOptions = useMemo(
    () => [
      { value: 'all', label: t('filters.all', 'All') },
      { value: 'false', label: t('filters.public', 'Public') },
      { value: 'true', label: t('filters.private', 'Private') },
    ],
    [t],
  );

  // Memoized price options
  const priceOptions = useMemo(
    () => [
      { value: 'all', label: t('filters.all', 'All') },
      { value: 'false', label: t('filters.free', 'Free') },
      { value: 'true', label: t('filters.paid', 'Paid') },
    ],
    [t],
  );

  // Handle pending filter changes (doesn't apply them yet)
  const handlePendingFilterChange = (filterType: FilterType, value: string) => {
    switch (filterType) {
      case 'language':
        setPendingLanguage(value);
        break;
      case 'isPrivate':
        setPendingVisibility(value);
        break;
      case 'isPaid':
        setPendingPrice(value);
        break;
    }
  };

  // Apply all pending filter changes
  const handleApplyFilters = () => {
    const params = new URLSearchParams(searchParams.toString());

    // Apply language filter
    if (pendingLanguage === 'all') {
      params.delete('language');
    } else {
      params.set('language', pendingLanguage);
    }

    // Apply visibility filter
    if (pendingVisibility === 'all') {
      params.delete('isPrivate');
    } else {
      params.set('isPrivate', pendingVisibility);
    }

    // Apply price filter
    if (pendingPrice === 'all') {
      params.delete('isPaid');
    } else {
      params.set('isPaid', pendingPrice);
    }

    // Apply all filters at once
    router.push(`${pathname}?${params.toString()}`);
    setOpen(false);
  };

  // Count active filters (excluding 'all')
  const activeFilterCount = [
    currentLanguage !== 'all' ? 1 : 0,
    currentVisibility !== 'all' ? 1 : 0,
    currentPrice !== 'all' ? 1 : 0,
  ].reduce((sum, count) => sum + count, 0);

  // Count pending filter changes
  const hasPendingChanges =
    pendingLanguage !== currentLanguage ||
    pendingVisibility !== currentVisibility ||
    pendingPrice !== currentPrice;

  return (
    <div className="flex justify-between gap-2">
      <CategoryFilter
        categories={
          categories?.map((category) => ({
            ...category,
            name: t(`categories.${category.name}`),
          })) ?? []
        }
      />

      <Popover open={open} onOpenChange={handleOpenChange}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <FilterIcon size={16} />
            <span>Filters</span>
            {activeFilterCount > 0 && (
              <span className="bg-primary text-primary-foreground ml-1 rounded-full px-2 py-0.5 text-xs">
                {activeFilterCount}
              </span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-4" align="end">
          <div className="space-y-4">
            <FilterSection
              title="Language"
              options={languageOptions}
              currentValue={pendingLanguage}
              onValueChange={(value: string) =>
                handlePendingFilterChange('language', value)
              }
              filterType="language"
              className="grid grid-cols-2 gap-x-4 gap-y-1"
            />

            <FilterSection
              title="Visibility"
              options={visibilityOptions}
              currentValue={pendingVisibility}
              onValueChange={(value: string) =>
                handlePendingFilterChange('isPrivate', value)
              }
              filterType="isPrivate"
              className="flex space-x-4"
            />

            <FilterSection
              title="Price"
              options={priceOptions}
              currentValue={pendingPrice}
              onValueChange={(value: string) =>
                handlePendingFilterChange('isPaid', value)
              }
              filterType="isPaid"
              className="flex space-x-4"
            />

            <div className="flex justify-between pt-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const params = new URLSearchParams();
                  router.push(
                    pathname +
                      (params.toString() ? `?${params.toString()}` : ''),
                  );
                  setOpen(false);
                }}
              >
                Reset
              </Button>
              <Button
                size="sm"
                onClick={handleApplyFilters}
                disabled={!hasPendingChanges}
              >
                Apply
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}

ExploreHeader.displayName = 'ExploreHeader';
