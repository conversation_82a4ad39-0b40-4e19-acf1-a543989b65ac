import { withI18n } from '~/lib/i18n/with-i18n';

import { ExploreCommunitiesList } from './_components/explore-communities-list';
import { ExploreHeader } from './_components/explore-header';
import { loadExplorePageData } from './_lib/server/explore-page.loader';

type ExplorePageProps = {
  searchParams: {
    page?: string;
    category?: string;
    language?: string;
    isPrivate?: string;
    isPaid?: string;
  };
};

async function ExplorePage({ searchParams }: ExplorePageProps) {
  const awaitedSearchParams = await searchParams;

  const pageParam = awaitedSearchParams.page ?? '1';
  const categoryParam = awaitedSearchParams.category;
  const languageParam = awaitedSearchParams.language;
  const isPrivateParam = awaitedSearchParams.isPrivate;
  const isPaidParam = awaitedSearchParams.isPaid;
  const page = Number(pageParam);
  const limit = 10;

  // Helper function to convert string parameters to boolean
  const parseStringToBoolean = (value?: string): boolean | undefined => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return undefined;
  };

  // Convert string parameters to boolean when needed
  const isPrivateBoolean = parseStringToBoolean(isPrivateParam);
  const isPaidBoolean = parseStringToBoolean(isPaidParam);

  const { communities, categories, languages } = await loadExplorePageData(
    categoryParam,
    languageParam,
    isPrivateBoolean,
    isPaidBoolean,
    page,
    limit,
  );
  return (
    <div className={'mt-4 flex flex-col gap-2 py-14'}>
      <div className={'container mx-auto flex flex-col gap-6'}>
        <ExploreHeader categories={categories} languages={languages} />
        <ExploreCommunitiesList communities={communities} />
      </div>
    </div>
  );
}

export default withI18n(ExplorePage);
