'use server';

import { revalidatePath } from 'next/cache';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';

const UpdateExploreRequestStatusSchema = z.object({
  communityId: z.string().uuid(),
  status: z.enum(['approved', 'rejected']),
  reason: z.string().optional(),
});

export const updateExploreRequestStatusAction = enhanceAction(
  async (data) => {
    const client = getSupabaseServerAdminClient();
    const { communityId, status, reason } = data;

    // Call the database function to update the status
    const { error } = await client.rpc(
      'update_community_explore_request_status',
      {
        p_community_id: communityId,
        p_status: status,
        p_reason: reason || undefined,
      },
    );

    if (error) {
      throw new Error(
        `Failed to update explore request status: ${error.message}`,
      );
    }

    // Revalidate the admin explore requests page to reflect the changes
    revalidatePath('/admin/explore-requests');

    return {
      success: true,
      status,
      message:
        status === 'approved'
          ? 'Community request approved successfully'
          : 'Community request rejected successfully',
    };
  },
  {
    auth: true,
    schema: UpdateExploreRequestStatusSchema,
  },
);
