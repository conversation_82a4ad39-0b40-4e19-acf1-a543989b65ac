import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';

export interface ExploreRequest {
  id: string;
  community_id: string;
  status: 'pending' | 'approved' | 'rejected';
  reason: string | null;
  created_at: string;
  updated_at: string;
  created_by_user_id: string | null;
  created_by_member_id: string | null;
  updated_by_user_id: string | null;
  community: {
    id: string;
    name: string;
    slug: string;
    primary_owner_user_id: string;
    users: {
      id: string;
      first_name: string;
      last_name: string | null;
      username: string | null;
    };
  };
}

export async function loadExploreRequestsLoader(): Promise<ExploreRequest[]> {
  const client = getSupabaseServerAdminClient();

  const { data, error } = await client
    .from('community_explore_requests')
    .select(
      `
      id,
      community_id,
      status,
      reason,
      created_at,
      updated_at,
      created_by_user_id,
      created_by_member_id,
      updated_by_user_id,
      community:communities(
        id,
        name,
        slug,
        primary_owner_user_id,
        users:users!primary_owner_user_id(
          id,
          first_name,
          last_name,
          username
        )
      )
    `,
    )
    .order('created_at', { ascending: false });

  if (error) {
    throw new Error(`Failed to fetch explore requests: ${error.message}`);
  }

  return (data as ExploreRequest[]) || [];
}
