'use client';

import { useState, useTransition } from 'react';

import Link from 'next/link';

import { format } from 'date-fns';
import { ExternalLink, Eye, User } from 'lucide-react';
import { toast } from 'sonner';

import { Badge } from '@kit/ui/badge';
import { But<PERSON> } from '@kit/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@kit/ui/dialog';
import { Label } from '@kit/ui/label';
import { Spinner } from '@kit/ui/spinner';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@kit/ui/table';
import { Textarea } from '@kit/ui/textarea';

import { updateExploreRequestStatusAction } from '../_lib/server/actions/update-explore-request-status-action';
import type { ExploreRequest } from '../_lib/server/loaders/explore-requests.loader';

interface RejectDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: (reason: string) => void;
  isLoading: boolean;
}

function RejectDialog({
  open,
  onOpenChange,
  onConfirm,
  isLoading,
}: RejectDialogProps) {
  const [reason, setReason] = useState('');

  const handleConfirm = () => {
    if (reason.trim()) {
      onConfirm(reason);
      setReason('');
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Reject Explore Request</DialogTitle>
          <DialogDescription>
            Please provide a reason for rejecting this community&apos;s request
            to be listed on the explore page.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="reason">Reason for rejection</Label>
            <Textarea
              id="reason"
              placeholder="Enter the reason for rejection..."
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              rows={4}
            />
          </div>
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleConfirm}
            disabled={!reason.trim() || isLoading}
          >
            {isLoading && <Spinner className="mr-2 h-4 w-4" />}
            Reject Request
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

function getStatusBadgeVariant(status: string) {
  switch (status) {
    case 'pending':
      return 'secondary';
    case 'approved':
      return 'default';
    case 'rejected':
      return 'destructive';
    default:
      return 'outline';
  }
}

// Helper function to create display name from first_name and last_name
function getDisplayName(
  user: { first_name: string; last_name: string | null } | null,
): string {
  if (!user) return 'Unknown';

  const firstName = user.first_name || '';
  const lastName = user.last_name || '';

  return `${firstName} ${lastName}`.trim() || 'Unknown';
}

interface ExploreRequestsTableProps {
  initialData: ExploreRequest[];
}

export function ExploreRequestsTable({
  initialData,
}: ExploreRequestsTableProps) {
  const [isPending, startTransition] = useTransition();
  const [rejectDialog, setRejectDialog] = useState<{
    open: boolean;
    communityId: string | null;
  }>({
    open: false,
    communityId: null,
  });

  const handleApprove = (communityId: string) => {
    startTransition(async () => {
      try {
        const result = await updateExploreRequestStatusAction({
          communityId,
          status: 'approved',
        });
        toast.success(result.message);
      } catch (error) {
        toast.error(
          `Failed to approve request: ${error instanceof Error ? error.message : 'Unknown error'}`,
        );
      }
    });
  };

  const handleReject = (communityId: string, reason: string) => {
    startTransition(async () => {
      try {
        const result = await updateExploreRequestStatusAction({
          communityId,
          status: 'rejected',
          reason,
        });
        toast.success(result.message);
        setRejectDialog({ open: false, communityId: null });
      } catch (error) {
        toast.error(
          `Failed to reject request: ${error instanceof Error ? error.message : 'Unknown error'}`,
        );
      }
    });
  };

  const openRejectDialog = (communityId: string) => {
    setRejectDialog({ open: true, communityId });
  };

  if (!initialData || initialData.length === 0) {
    return (
      <div className="text-muted-foreground flex items-center justify-center p-8">
        No explore requests found.
      </div>
    );
  }

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Community</TableHead>
              <TableHead>Owner</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Submitted</TableHead>
              <TableHead>Reason</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {initialData.map((request) => (
              <TableRow key={request.id}>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <span className="font-medium">
                      {request.community.name}
                    </span>
                    <Button variant="ghost" size="sm" asChild>
                      <Link
                        href={`/${request.community.slug}`}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <ExternalLink className="h-3 w-3" />
                      </Link>
                    </Button>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <span>{getDisplayName(request.community.users)}</span>
                    {request.community.users?.username && (
                      <Button variant="ghost" size="sm" asChild>
                        <Link
                          href={`/@${request.community.users.username}`}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <User className="h-3 w-3" />
                        </Link>
                      </Button>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant={getStatusBadgeVariant(request.status)}>
                    {request.status}
                  </Badge>
                </TableCell>
                <TableCell>
                  {format(new Date(request.created_at), 'MMM d, yyyy')}
                </TableCell>
                <TableCell>
                  {request.reason ? (
                    <div className="flex items-center gap-2">
                      <span className="max-w-32 truncate">
                        {request.reason}
                      </span>
                      <Button variant="ghost" size="sm" title={request.reason}>
                        <Eye className="h-3 w-3" />
                      </Button>
                    </div>
                  ) : (
                    <span className="text-muted-foreground">-</span>
                  )}
                </TableCell>
                <TableCell>
                  {request.status === 'pending' && (
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        onClick={() => handleApprove(request.community_id)}
                        disabled={isPending}
                      >
                        {isPending && <Spinner className="mr-2 h-3 w-3" />}
                        Approve
                      </Button>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => openRejectDialog(request.community_id)}
                        disabled={isPending}
                      >
                        Reject
                      </Button>
                    </div>
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <RejectDialog
        open={rejectDialog.open}
        onOpenChange={(open) => setRejectDialog({ open, communityId: null })}
        onConfirm={(reason) => {
          if (rejectDialog.communityId) {
            handleReject(rejectDialog.communityId, reason);
          }
        }}
        isLoading={isPending}
      />
    </>
  );
}
