import { AdminGuard } from '../_components/admin-guard';
import { ExploreRequestsTable } from './_components/explore-requests-table';
import { loadExploreRequestsLoader } from './_lib/server/loaders/explore-requests.loader';

async function ExploreRequestsPage() {
  // Load data on the server side using the admin client
  const requests = await loadExploreRequestsLoader();

  return (
    <div className="flex flex-1 flex-col space-y-8 p-8">
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">
            Community Explore Requests
          </h2>
          <p className="text-muted-foreground">
            Review and manage community listing requests for the explore page.
          </p>
        </div>
      </div>
      <ExploreRequestsTable initialData={requests} />
    </div>
  );
}

export default AdminGuard(ExploreRequestsPage);

ExploreRequestsPage.displayName = 'ExploreRequestsPage';
