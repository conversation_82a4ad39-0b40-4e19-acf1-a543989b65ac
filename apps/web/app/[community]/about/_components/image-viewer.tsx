'use client';

import { useState } from 'react';

import Image from 'next/image';

import { AlertCircle } from 'lucide-react';

import { Spinner } from '@kit/ui/spinner';

type ImageViewerProps = {
  url: string;
  altText?: string | null;
};

export function ImageViewer({ url, altText }: ImageViewerProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  const handleImageError = () => {
    setIsLoading(false);
    setHasError(true);
    console.error(`Failed to load image: ${url}`);
  };

  return (
    <div className="space-y-4" data-test="image-viewer-container">
      <div className="flex justify-center">
        <div className="relative w-full overflow-hidden rounded-lg">
          {isLoading && (
            <div className="bg-muted/20 absolute inset-0 z-10 flex items-center justify-center">
              <Spinner
                className="text-primary h-8 w-8 animate-spin"
                aria-label="Loading image"
              />
            </div>
          )}
          {hasError && (
            <div
              className="bg-muted/20 absolute inset-0 z-10 flex flex-col items-center justify-center gap-2"
              role="alert"
            >
              <AlertCircle className="text-destructive h-10 w-10" />
              <p className="text-muted-foreground text-sm">
                Failed to load image
              </p>
            </div>
          )}

          <div className="flex h-full w-full items-center justify-center">
            <Image
              src={url}
              alt={altText || 'Media image'}
              className="max-h-[60vh] w-auto max-w-full"
              data-test="image-viewer-img"
              width={1792}
              height={800}
              style={{ objectFit: 'contain' }}
              sizes="(max-width: 1792px) 100vw, (max-width: 1792px) 50vw, 33vw"
              loading="lazy"
              onLoad={handleImageLoad}
              onError={handleImageError}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

ImageViewer.displayName = 'ImageViewer';
