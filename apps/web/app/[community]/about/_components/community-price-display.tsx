import { DollarSign } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { ProductPrice } from '~/lib/stripe/types';

import { calculatePriceInfo } from '../_lib/pricing-helper';

type CommunityPriceDisplayProps = {
  defaultProductId: string | null;
  prices: ProductPrice[] | undefined;
};

export function CommunityPriceDisplay({
  defaultProductId,
  prices,
}: CommunityPriceDisplayProps) {
  const { t } = useTranslation('communities');

  if (!prices || prices.length === 0) {
    return (
      <div
        className="flex items-center gap-2"
        data-test="price-display-free-fallback"
        aria-label="Price: Free to join"
      >
        <DollarSign size={16} />
        <span>Free to join</span>
      </div>
    );
  }

  const pricesWithSavings = calculatePriceInfo(prices, t);

  return (
    <div className="flex flex-col gap-2">
      {defaultProductId && pricesWithSavings.length > 0 ? (
        pricesWithSavings.map((price) => (
          <div
            key={price.id}
            className="flex items-center gap-2"
            aria-label={`Price: $${(price.unitAmount / 100).toFixed(2)} per ${price.displayInterval === 'one_time' ? 'once' : price.displayInterval}`}
            data-test={`price-display-${price.id}`}
          >
            <DollarSign size={16} />
            <span>
              {price.unitAmount === 0 ? (
                'Free to join'
              ) : (
                <>
                  ${(price.unitAmount / 100).toFixed(2)} /{' '}
                  {price.displayInterval === 'one_time'
                    ? 'once'
                    : price.displayInterval}
                  {price.savingsPercent && price.savingsPercent > 0 && (
                    <span className="ml-2 inline-block rounded-md bg-green-100 px-2 py-0.5 text-xs font-medium text-green-800">
                      Save {price.savingsPercent}%
                    </span>
                  )}
                </>
              )}
            </span>
          </div>
        ))
      ) : (
        // Fallback if no product ID or prices
        <div
          className="flex items-center gap-2"
          data-test="price-display-free-fallback"
          aria-label="Price: Free to join"
        >
          <DollarSign size={16} />
          <span>Free to join</span>
        </div>
      )}
    </div>
  );
}
