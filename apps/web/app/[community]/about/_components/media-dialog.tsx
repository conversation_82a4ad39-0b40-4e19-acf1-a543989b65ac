'use client';

import { useCallback, useEffect, useState } from 'react';

import { PlusIcon } from 'lucide-react';
import { toast } from 'sonner';

import { But<PERSON> } from '@kit/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@kit/ui/dialog';
import { ImageUploadContainer } from '@kit/ui/dojo/organisms/image-upload-container';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';
import { Textarea } from '@kit/ui/textarea';
import { Trans } from '@kit/ui/trans';

import { addVideoUrlAction } from '~/[community]/about/_lib/server/actions/add-video-url.action';
import { uploadPageImageAction } from '~/[community]/about/_lib/server/actions/upload-page-image.action';

const MAX_CAPTION_LENGTH = 50;
const ASPECT_RATIO = 16 / 9;

type MediaDialogProps = {
  isEnabled: boolean;
  isUploading: boolean;
  pageId: string;
  communityId: string;
  mediaLength: number;
  onSuccess: () => void;
  startUploadingTransition: (callback: () => Promise<void>) => void;
  maxHeight?: number;
};

export function MediaDialog({
  isEnabled,
  isUploading,
  pageId,
  communityId,
  mediaLength,
  onSuccess,
  startUploadingTransition,
  maxHeight = 120,
}: MediaDialogProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('image');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [croppedFile, setCroppedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [imageCaption, setImageCaption] = useState('');
  const [videoUrl, setVideoUrl] = useState('');
  const [videoCaption, setVideoCaption] = useState('');

  const resetForm = useCallback(() => {
    setSelectedFile(null);
    setCroppedFile(null);
    setPreviewUrl(null);
    setImageCaption('');
    setVideoUrl('');
    setVideoCaption('');
  }, []);

  useEffect(() => {
    if (selectedFile) {
      const objectUrl = URL.createObjectURL(selectedFile);
      setPreviewUrl(objectUrl);

      return () => URL.revokeObjectURL(objectUrl);
    } else {
      setPreviewUrl(null);
    }
  }, [selectedFile]);

  const handleDialogClose = useCallback(
    (open: boolean) => {
      if (!open) {
        resetForm();
      }
      setIsDialogOpen(open);
    },
    [resetForm],
  );

  // --- Callbacks for ImageUploadContainer --- //
  const handleMediaDelete = useCallback(() => {
    // Called when the user clicks X in ImageUploadContainer
    // We just clear the local state, no server action needed here
    setSelectedFile(null);
    setCroppedFile(null);
  }, []);

  const handleMediaCropped = useCallback(async (file: File) => {
    // Called when ImageUploadContainer successfully crops
    setCroppedFile(file);

    // We don't need to update selectedFile or previewUrl here,
    // as ImageUploadContainer manages its own internal preview after crop.
  }, []);

  // --- Main Submit Handlers --- //

  const handleImageUpload = useCallback(() => {
    // Now uses croppedFile instead of selectedFile
    if (!croppedFile) return;

    startUploadingTransition(async () => {
      try {
        const result = await uploadPageImageAction({
          file: croppedFile, // Use the cropped file!
          pageId,
          communityId,
          caption: imageCaption,
          altText: imageCaption,
          displayOrder: mediaLength,
        });

        if (result.success) {
          toast.success('Image uploaded successfully');
          resetForm();
          handleDialogClose(false);
          onSuccess();
        } else {
          toast.error(`Failed to upload: ${result.error}`);
        }
      } catch {
        toast.error('Failed to upload image');
      }
    });
  }, [
    croppedFile,
    pageId,
    communityId,
    imageCaption,
    mediaLength,
    onSuccess,
    startUploadingTransition,
    resetForm,
    handleDialogClose,
  ]);

  const handleVideoAdd = useCallback(() => {
    if (!videoUrl) return;

    startUploadingTransition(async () => {
      try {
        const result = await addVideoUrlAction({
          communityId,
          url: videoUrl,
          pageId,
          mediaType: 'video',
          caption: videoCaption,
          // Use caption as alt text
          altText: videoCaption,
          displayOrder: mediaLength,
        });

        if (result.success) {
          toast.success('Video added successfully');
          resetForm();
          handleDialogClose(false);
          onSuccess();
        } else {
          toast.error(`Failed to add video: ${result.error}`);
        }
      } catch {
        toast.error('Failed to add video');
      }
    });
  }, [
    videoUrl,
    pageId,
    videoCaption,
    mediaLength,
    startUploadingTransition,
    resetForm,
    handleDialogClose,
    onSuccess,
    communityId,
  ]);

  // Handle caption input with character limit
  const handleImageCaptionChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>,
  ) => {
    if (e.target.value.length <= MAX_CAPTION_LENGTH) {
      setImageCaption(e.target.value);
    }
  };

  // Handle video caption input with character limit
  const handleVideoCaptionChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>,
  ) => {
    if (e.target.value.length <= MAX_CAPTION_LENGTH) {
      setVideoCaption(e.target.value);
    }
  };

  // If not enabled, don't render anything
  if (!isEnabled) return null;

  return (
    <Dialog open={isDialogOpen} onOpenChange={handleDialogClose}>
      <DialogTrigger asChild>
        <Button
          data-test="add-media-button"
          aria-label="Add media"
          variant="outline"
          size="sm"
          className="flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-red-500"
          style={{
            height: `${maxHeight}px`,
            width: `${maxHeight}px`,
          }}
        >
          <PlusIcon className="mb-2 h-6 w-6" />
          <span className="text-xs">
            <Trans i18nKey="communities:addMediaButton" />
          </span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]" data-test="media-dialog">
        <DialogHeader>
          <DialogTitle>
            <Trans i18nKey="communities:addMediaTitle" />
          </DialogTitle>
          <DialogDescription>
            <Trans i18nKey="communities:addMediaDescription" />
          </DialogDescription>
        </DialogHeader>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger
              value="image"
              data-test="media-dialog-image-tab"
              aria-label="Select image tab"
            >
              <Trans i18nKey="communities:imageTab" />
            </TabsTrigger>
            <TabsTrigger
              value="video"
              data-test="media-dialog-video-tab"
              aria-label="Select video tab"
            >
              <Trans i18nKey="communities:videoTab" />
            </TabsTrigger>
          </TabsList>
          <TabsContent value="image" className="space-y-4 py-4">
            <ImageUploadContainer
              imageUrl={previewUrl}
              headingKey="about:mediaDialogImageHeading"
              uploadHeadingKey="about:mediaDialogImageUploadHeading"
              uploadSubheadingKey="about:mediaDialogImageUploadSubheading"
              aspectRatio={ASPECT_RATIO}
              imageRounded="rounded-lg"
              cropShape="rect"
              imageSize="w-full"
              onDelete={handleMediaDelete}
              onUpload={handleMediaCropped}
            />

            <div className="space-y-2">
              <Label htmlFor="caption">Caption</Label>
              <Textarea
                id="caption"
                onChange={handleImageCaptionChange}
                placeholder="Add a caption for this image"
                rows={2}
                maxLength={MAX_CAPTION_LENGTH}
                data-test="image-caption-input"
                aria-label="Image caption"
              />
              <p className="text-muted-foreground text-xs">
                {imageCaption.length}/{MAX_CAPTION_LENGTH}
              </p>
            </div>
          </TabsContent>
          <TabsContent value="video" className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="video-url">Video URL</Label>
              <Input
                id="video-url"
                value={videoUrl}
                onChange={(e) => setVideoUrl(e.target.value)}
                placeholder="https://www.youtube.com/watch?v=..."
                data-test="video-url-input"
                aria-label="Video URL"
              />
              <p className="text-muted-foreground text-xs">
                Paste a YouTube or Vimeo video link
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="video-caption">Caption</Label>
              <Textarea
                id="video-caption"
                onChange={handleVideoCaptionChange}
                placeholder="Add a caption for this video"
                rows={2}
                maxLength={MAX_CAPTION_LENGTH}
                data-test="video-caption-input"
                aria-label="Video caption"
              />
              <p className="text-muted-foreground text-xs">
                {videoCaption.length}/{MAX_CAPTION_LENGTH}
              </p>
            </div>
          </TabsContent>
        </Tabs>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => handleDialogClose(false)}
            disabled={isUploading}
            data-test="media-dialog-cancel-button"
            aria-label="Cancel adding media"
          >
            <Trans i18nKey="common:cancel" />
          </Button>
          <Button
            onClick={activeTab === 'image' ? handleImageUpload : handleVideoAdd}
            disabled={
              isUploading ||
              (activeTab === 'image' && !croppedFile) ||
              (activeTab === 'video' && !videoUrl)
            }
            data-test="media-dialog-add-button"
            aria-label="Add media"
          >
            {isUploading ? (
              <Trans i18nKey="communities:processingMedia" />
            ) : (
              <Trans i18nKey="communities:addMedia" />
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

MediaDialog.displayName = 'MediaDialog';
