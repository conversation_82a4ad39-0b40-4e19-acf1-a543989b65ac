'use client';

import Image from 'next/image';

import { X } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { Button } from '@kit/ui/button';

import { MediaItem } from '../_lib/types';
import { MediaDialog } from './media-dialog';

type MediaThumbnailGalleryProps = {
  medias: MediaItem[];
  selectedMediaId: string | null;
  hasEditPermissions: boolean;
  maxHeight?: number;
  onMediaClick: (media: MediaItem) => void;
  onDeleteMedia: (mediaId: string) => void;
  isUploading?: boolean;
  pageId: string;
  communityId: string;
  onSuccess?: () => void;
  startUploadingTransition?: (callback: () => Promise<void>) => void;
};

export function MediaThumbnailGallery({
  medias,
  selectedMediaId,
  hasEditPermissions,
  maxHeight = 120,
  onMediaClick,
  onDeleteMedia,
  isUploading = false,
  pageId,
  communityId,
  onSuccess = () => {},
  startUploadingTransition = () => Promise.resolve(),
}: MediaThumbnailGalleryProps) {
  const { t } = useTranslation('communities');
  const isAddEnabled = medias.length < 7;
  return (
    <div className="flex grow flex-wrap items-center gap-4">
      {medias.map((media) => (
        <div key={media.id} className="relative">
          <div
            className={`group cursor-pointer rounded-lg border ${selectedMediaId === media.id ? 'ring-primary ring-2' : 'bg-background'}`}
            onClick={() => onMediaClick(media)}
            data-test={`media-thumbnail-${media.id}`}
            aria-label={`View media ${media.caption || media.altText || 'thumbnail'}`}
          >
            {media.mediaType === 'video' ? (
              media.thumbnailUrl ? (
                <div
                  className="relative overflow-hidden rounded-lg"
                  style={{
                    maxHeight: `${maxHeight}px`,
                    maxWidth: `${maxHeight * 2}px`,
                    height: `${maxHeight}px`,
                    width: `${maxHeight * 2}px`,
                  }}
                >
                  <Image
                    src={media.thumbnailUrl}
                    alt={media.caption || media.altText || 'Video thumbnail'}
                    fill
                    className="rounded-lg object-cover"
                    sizes="(max-width: 768px) 200px, 120px"
                  />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="bg-background/80 rounded-full p-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="h-6 w-6"
                      >
                        <polygon points="5 3 19 12 5 21 5 3" />
                      </svg>
                    </div>
                  </div>
                </div>
              ) : (
                <div
                  className="flex items-center justify-center overflow-hidden rounded-lg bg-black"
                  style={{
                    maxHeight: `${maxHeight}px`,
                    maxWidth: `${maxHeight * 2}px`,
                    height: `${maxHeight}px`,
                    width: `${maxHeight * 2}px`,
                  }}
                >
                  <video
                    src={media.url}
                    className="h-full w-full rounded-lg object-cover opacity-70"
                  />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="bg-background/80 rounded-full p-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="h-6 w-6"
                      >
                        <polygon points="5 3 19 12 5 21 5 3" />
                      </svg>
                    </div>
                  </div>
                </div>
              )
            ) : (
              <div
                className="relative overflow-hidden rounded-lg"
                style={{
                  maxHeight: `${maxHeight}px`,
                  maxWidth: `${maxHeight}px`,
                  height: `${maxHeight}px`,
                  width: `${maxHeight}px`,
                }}
              >
                <Image
                  src={media.url}
                  alt={media.altText || 'Media image'}
                  fill
                  className="rounded-lg object-cover"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
              </div>
            )}
            <div className="absolute inset-0 flex items-center justify-center rounded-lg opacity-0 transition-opacity group-hover:bg-black/20 group-hover:opacity-100">
              {hasEditPermissions && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute top-1 right-1 h-6 w-6 rounded-full bg-black/40 text-white hover:bg-black/60"
                  onClick={(e) => {
                    e.stopPropagation();
                    onDeleteMedia(media.id);
                  }}
                  data-test={`delete-media-button-${media.id}`}
                  aria-label={t('ariaLabels.deleteMedia', {
                    caption: media.caption || media.altText || 'thumbnail',
                  })}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </div>
      ))}

      {hasEditPermissions && isAddEnabled && pageId && communityId && (
        <div>
          <MediaDialog
            isEnabled={isAddEnabled}
            isUploading={isUploading}
            pageId={pageId}
            communityId={communityId}
            mediaLength={medias.length}
            onSuccess={onSuccess}
            startUploadingTransition={startUploadingTransition}
            maxHeight={maxHeight}
          />
        </div>
      )}
    </div>
  );
}

MediaThumbnailGallery.displayName = 'MediaThumbnailGallery';
