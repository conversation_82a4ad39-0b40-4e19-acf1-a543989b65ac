'use client';

import { useEffect, useState, useTransition } from 'react';

import { toast } from 'sonner';

import { VideoPlayer } from '@kit/ui/dojo/organisms/video-player';
import { Spinner } from '@kit/ui/spinner';

import { deletePageMediaAction } from '~/[community]/about/_lib/server/actions/delete-page-media.action';
import { getAboutPageMediaAction } from '~/[community]/about/_lib/server/actions/get-page-media.action';

import { MediaItem } from '../_lib/types';
import { ImageViewer } from './image-viewer';
import { MediaThumbnailGallery } from './media-thumbnail-gallery';

type MediaDropzoneProps = {
  communityId: string;
  pageId: string;
  hasEditPermissions?: boolean;
  maxHeight?: number;
};

export function MediaDropzone({
  communityId,
  pageId,
  hasEditPermissions = false,
  maxHeight = 100,
}: MediaDropzoneProps) {
  const [isUploading, startTransition] = useTransition();
  const [medias, setMedias] = useState<MediaItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState<MediaItem | null>(null);

  const fetchMedia = async () => {
    setIsLoading(true);
    try {
      const result = await getAboutPageMediaAction({ pageId });
      if (result.success && result.data) {
        // Transform PageMediaItem[] to MediaItem[] by ensuring altText is never undefined
        const transformedMedias = result.data.map((item) => ({
          ...item,
          altText: item.altText ?? null,
          caption: item.caption ?? null,
        }));
        setMedias(transformedMedias);

        // Auto-select the first media if available and no media currently selected
        if (transformedMedias.length > 0 && !selectedMedia) {
          const firstItem = transformedMedias[0];
          if (firstItem && firstItem.id) {
            setSelectedMedia(firstItem);
          }
        }
      }
    } catch (error) {
      console.error('Error fetching media:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchMedia();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pageId]); // Don't include selectedMedia in dependencies to prevent loops

  const handleDeleteMedia = async (mediaId: string) => {
    try {
      const result = await deletePageMediaAction({
        mediaId,
      });

      if (result.success) {
        toast.success('Media deleted successfully');

        if (selectedMedia?.id === mediaId) {
          setSelectedMedia(null);
        }
        fetchMedia();
      } else {
        toast.error(`Failed to delete: ${result.error}`);
      }
    } catch (error) {
      console.error('Error deleting media:', error);
      toast.error('Error deleting media');
    }
  };

  const handleMediaClick = (media: MediaItem) => {
    if (selectedMedia?.id === media.id) {
      setSelectedMedia(null);
    } else {
      setSelectedMedia(media);
    }
  };

  const isVideo = selectedMedia?.mediaType === 'video';

  if (isLoading) {
    return (
      <div className="flex h-full min-h-[575px] items-center justify-center rounded-lg border border-dashed p-8 md:min-h-[575px]">
        <Spinner className="h-8 w-8 animate-spin" aria-label="Loading media" />
      </div>
    );
  }

  return (
    <div className="relative space-y-6">
      <div className="min-h-[575px] rounded-lg border md:min-h-[575px]">
        {selectedMedia ? (
          <div className="relative p-4">
            <div className="space-y-4">
              {isVideo ? (
                <VideoPlayer videoUrl={selectedMedia.url} shortCutKeysEnabled />
              ) : (
                <ImageViewer
                  url={selectedMedia.url}
                  altText={selectedMedia.altText}
                />
              )}
              {selectedMedia.caption && (
                <p className="text-muted-foreground mt-2 text-center">
                  {selectedMedia.caption}
                </p>
              )}
            </div>
          </div>
        ) : (
          <div
            className="flex h-full min-h-[575px] items-center justify-center rounded-lg border border-dashed p-8 md:min-h-[575px]"
            data-test="no-media-placeholder"
          >
            <div className="text-center">
              <p className="text-muted-foreground">
                {hasEditPermissions
                  ? 'Add a media item to the page below'
                  : 'No media uploaded yet'}
              </p>
            </div>
          </div>
        )}
      </div>

      <div className="flex items-start gap-4 pt-1">
        <MediaThumbnailGallery
          medias={medias}
          selectedMediaId={selectedMedia?.id || null}
          hasEditPermissions={hasEditPermissions}
          maxHeight={maxHeight}
          onMediaClick={handleMediaClick}
          onDeleteMedia={handleDeleteMedia}
          isUploading={isUploading}
          pageId={pageId}
          communityId={communityId}
          onSuccess={fetchMedia}
          startUploadingTransition={startTransition}
        />
      </div>
    </div>
  );
}

MediaDropzone.displayName = 'MediaDropzone';
