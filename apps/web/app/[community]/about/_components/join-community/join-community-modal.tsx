'use client';

import {
  useCallback,
  useEffect,
  useMemo,
  useState,
  useTransition,
} from 'react';

import { useRouter } from 'next/navigation';

import { CheckoutProvider, useCheckout } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import { useTranslation } from 'react-i18next';

import { Button } from '@kit/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@kit/ui/dialog';
import { If } from '@kit/ui/if';
import { Trans } from '@kit/ui/trans';

import pathsConfig from '~/config/paths.config';
import type { ProductPrice } from '~/lib/stripe/types';

import { calculatePriceInfo, formatPrice } from '../../_lib/pricing-helper';
import { createJoinCommunityCheckoutSessionAction } from './_lib/create-join-community-stripe-checkout-session';
import { expireJoinCommunityCheckoutSessionAction } from './_lib/expire-join-community-stripe-checkout-session';
import { ErrorAlert } from './error-alert';
import { ModalFooter } from './modal-footer';
import { PaymentElementsSection } from './payment-elements-section';
import { PricingSection } from './pricing-section';
import { JoinCommunityModalProps } from './types';

// Initialize Stripe on the client side
const publishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;
if (!publishableKey) {
  throw new Error('Missing NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY env variable');
}
const stripePromise = loadStripe(publishableKey);

// Step 1: Price Selection Component
function PriceSelectionStep({
  prices,
  trialDays,
  onContinue,
  onCancel,
}: {
  prices: ProductPrice[];
  trialDays?: number;
  onContinue: (selectedPriceId: string) => void;
  onCancel: () => void;
}) {
  const { t } = useTranslation('communities');
  const [selectedPriceId, setSelectedPriceId] = useState<string | undefined>(
    prices && prices.length > 0
      ? prices.sort((a, b) => a.unitAmount - b.unitAmount)[0]?.id
      : undefined,
  );

  const pricesWithSavings = useMemo(
    () => calculatePriceInfo(prices, t),
    [prices, t],
  );
  const selectedPrice = useMemo(
    () => pricesWithSavings.find((price) => price.id === selectedPriceId),
    [pricesWithSavings, selectedPriceId],
  );

  const handleContinue = () => {
    if (selectedPriceId) {
      onContinue(selectedPriceId);
    }
  };

  return (
    <div className="space-y-6">
      <PricingSection
        prices={prices}
        selectedPriceId={selectedPriceId}
        setSelectedPriceId={setSelectedPriceId}
        trialDays={trialDays}
      />

      <div className="flex justify-end gap-3">
        <Button variant="outline" onClick={onCancel} data-test="cancel-join">
          <Trans i18nKey="common:cancel" />
        </Button>
        <Button
          onClick={handleContinue}
          disabled={!selectedPriceId}
          data-test="continue-to-payment"
        >
          <Trans
            i18nKey="communities:continueToPayment"
            values={{
              price: selectedPrice ? formatPrice(selectedPrice, t) : '',
            }}
          />
        </Button>
      </div>
    </div>
  );
}

// Step 2: Payment Component
function PaymentStep({
  communitySlug,
  userId,
  selectedPrice,
  trialDays,
  onBack,
  onOpenChange,
  showChangeButton = true,
}: {
  communitySlug: string;
  userId: string | null;
  selectedPrice: ProductPrice;
  trialDays?: number;
  onBack: () => void;
  onOpenChange: (open: boolean) => void;
  showChangeButton?: boolean;
}) {
  const { t } = useTranslation('communities');
  const router = useRouter();
  const checkout = useCheckout();
  const [isPending, startTransition] = useTransition();
  const [error, setError] = useState<string | null>(null);

  // Check if this is a free price (unitAmount === 0)
  const isFreePrice = selectedPrice.unitAmount === 0;

  // Get payment readiness directly from checkout.canConfirm boolean property
  const isPaymentReady = checkout?.canConfirm ?? false;

  const handleJoinCommunity = useCallback(async () => {
    if (!userId) {
      const communityPath = pathsConfig.app.communityAbout.replace(
        '[community]',
        communitySlug,
      );
      const redirectPath = `${pathsConfig.auth.signIn}?next=${encodeURIComponent(communityPath)}`;
      router.push(redirectPath);
      return;
    }

    // For free prices, no client-side confirmation needed
    // The checkout session auto-completes and webhook handles the rest
    if (isFreePrice) {
      checkout.confirm();

      return;
    }

    try {
      // For paid prices only - ensure checkout is available
      if (!checkout) {
        setError('Payment form not ready');
        return;
      }

      startTransition(async () => {
        try {
          // Confirm payment with redirect
          const result = await checkout.confirm();

          if (result.type === 'error') {
            const errorMessage =
              'error' in result &&
              result.error &&
              typeof result.error === 'object' &&
              'message' in result.error
                ? String(result.error.message)
                : 'Unknown error';
            setError(`Payment failed: ${errorMessage}`);
            return;
          }

          // If we get here without redirect, payment was successful
          onOpenChange(false);
          router.refresh();
        } catch (err) {
          console.error('❌ Error in payment confirmation:', err);
          setError(
            `Payment failed: ${err instanceof Error ? err.message : String(err)}`,
          );
        }
      });
    } catch (err) {
      setError(
        `Error in join community flow: ${err instanceof Error ? err.message : String(err)}`,
      );
    }
  }, [
    userId,
    communitySlug,
    router,
    checkout,
    startTransition,
    onOpenChange,
    isFreePrice,
  ]);

  // For free prices, the checkout session will automatically complete
  // and trigger the checkout.session.completed webhook - no client-side action needed

  // Calculate next payment date based on trial days
  const nextPaymentDate = useMemo(
    () =>
      new Date(
        Date.now() + (trialDays || 0) * 24 * 60 * 60 * 1000,
      ).toLocaleDateString(),
    [trialDays],
  );

  return (
    <div className="space-y-6">
      <If condition={error}>
        <ErrorAlert error={error} />
      </If>

      {/* Show selected price summary */}
      <If condition={showChangeButton}>
        <div className="bg-muted/50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">
                <Trans i18nKey="communities:selectedPlan" />
              </p>
              <p className="text-muted-foreground text-sm">
                {formatPrice(selectedPrice, t)}
              </p>
            </div>

            <Button
              variant="ghost"
              size="sm"
              onClick={onBack}
              data-test="change-plan"
            >
              <Trans i18nKey="common:change" />
            </Button>
          </div>
        </div>
      </If>

      {/* Payment Elements - only show for paid prices */}
      {userId && !isFreePrice && <PaymentElementsSection />}

      {/* Free membership processing - NO PaymentElement per Stripe docs */}
      {userId && isFreePrice && (
        <div className="flex flex-col items-center justify-center py-8">
          <div
            className="border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent"
            role="status"
            aria-label="Setting up free membership"
          />
          <p className="mt-4 text-center text-lg font-medium">
            <Trans i18nKey="communities:settingUpFreeMembership" />
          </p>
          <p className="text-muted-foreground mt-2 text-center text-sm">
            Processing your free membership...
          </p>
          {/* NO PaymentElement for free checkout sessions per Stripe documentation */}
        </div>
      )}

      {/* Only show footer for paid prices or free prices that aren't auto-processing */}
      {!isFreePrice && (
        <ModalFooter
          isPending={isPending}
          onCancel={onBack}
          onJoin={handleJoinCommunity}
          paymentMethodReady={isPaymentReady}
          selectedPrice={selectedPrice}
          nextPaymentDate={nextPaymentDate}
          trialDays={trialDays ?? 0}
        />
      )}
    </div>
  );
}

export function JoinCommunityModal(props: JoinCommunityModalProps) {
  const { t } = useTranslation('communities');
  const { open, onOpenChange, communityId } = props;

  // Determine if we should skip price selection (only one price available)
  const shouldSkipPriceSelection = props.prices && props.prices.length === 1;

  // Check if the community has only a free price
  const hasOnlyFreePrice =
    shouldSkipPriceSelection && props.prices?.[0]?.unitAmount === 0;

  // Set initial step based on number of prices and if it's free
  const [step, setStep] = useState<
    'price-selection' | 'confirmation' | 'payment'
  >(() => {
    if (hasOnlyFreePrice) {
      return 'confirmation'; // Show confirmation for free communities
    }
    return shouldSkipPriceSelection ? 'payment' : 'price-selection';
  });

  // Auto-select the price if there's only one option
  const [selectedPriceId, setSelectedPriceId] = useState<string | null>(() => {
    if (shouldSkipPriceSelection && props.prices && props.prices.length === 1) {
      return props.prices[0]?.id || null;
    }
    return null;
  });

  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [initializationError, setInitializationError] = useState<string | null>(
    null,
  );
  const [isInitializing, setIsInitializing] = useState(false);

  // Get the selected price object
  const selectedPrice = useMemo(() => {
    if (!selectedPriceId || !props.prices) return null;
    return props.prices.find((p) => p.id === selectedPriceId);
  }, [selectedPriceId, props.prices]);

  // Reset state when modal closes
  useEffect(() => {
    if (!open) {
      // Reset to initial step based on number of prices and if it's free
      if (hasOnlyFreePrice) {
        setStep('confirmation');
      } else {
        const newStep = shouldSkipPriceSelection
          ? 'payment'
          : 'price-selection';

        setStep(newStep);
      }
      // Reset to initial price selection
      setSelectedPriceId(
        shouldSkipPriceSelection && props.prices && props.prices.length === 1
          ? props.prices[0]?.id || null
          : null,
      );
      setClientSecret(null);
      setSessionId(null);
      setInitializationError(null);
      setIsInitializing(false);
    }
  }, [open, shouldSkipPriceSelection, hasOnlyFreePrice, props.prices]);

  // Create checkout session when moving to payment step
  // This now only triggers when we actually go to the payment step (not for confirmation)
  useEffect(() => {
    // Create checkout session for:
    // 1. Payment step (paid communities)
    // 2. Confirmation step (free communities - need clientSecret for join button)
    const shouldCreateSession =
      (step === 'payment' || (step === 'confirmation' && hasOnlyFreePrice)) &&
      selectedPriceId &&
      props.userId &&
      !clientSecret;

    if (shouldCreateSession) {
      setIsInitializing(true);
      setInitializationError(null);

      const appUrl = process.env.NEXT_PUBLIC_APP_URL ?? window.location.origin;
      const returnUrlPath = `${appUrl}/${props.communitySlug}/about/success?session_id={CHECKOUT_SESSION_ID}`;

      createJoinCommunityCheckoutSessionAction({
        communityId: props.communityId,
        communitySlug: props.communitySlug,
        priceId: selectedPriceId,
        trialDays: props.trialDays || null,
        returnUrl: returnUrlPath,
      })
        .then((result) => {
          setClientSecret(result.checkoutToken);
          setSessionId(result.sessionId);
          setIsInitializing(false);
        })
        .catch((err) => {
          console.error('❌ Checkout session creation failed:', err);
          const errorMessage =
            err instanceof Error
              ? err.message
              : 'Failed to initialize payment form';
          setInitializationError(`Payment setup failed: ${errorMessage}`);
          setIsInitializing(false);
        });
    }
  }, [
    step,
    selectedPriceId,
    props.userId,
    props.communityId,
    props.communitySlug,
    props.trialDays,
    clientSecret,
    hasOnlyFreePrice,
  ]);

  const handleContinueToPayment = useCallback((priceId: string) => {
    setSelectedPriceId(priceId);
    setStep('payment');
  }, []);

  const handleBackToPricing = useCallback(async () => {
    // Expire the current session if it exists
    if (sessionId && props.userId) {
      try {
        await expireJoinCommunityCheckoutSessionAction({ sessionId });
      } catch (error) {
        // Log error but don't block the UI - user can still go back
        console.error('Failed to expire checkout session:', error);
      }
    }

    // If there's only one price, close the modal instead of going back to price selection
    if (shouldSkipPriceSelection) {
      onOpenChange(false);
    } else {
      setStep('price-selection');
      setClientSecret(null);
      setSessionId(null);
      setInitializationError(null);
    }
  }, [sessionId, props.userId, shouldSkipPriceSelection, onOpenChange]);

  const handleCancel = useCallback(async () => {
    // Expire any open session when canceling
    if (sessionId && props.userId) {
      try {
        await expireJoinCommunityCheckoutSessionAction({ sessionId });
      } catch (error) {
        // Log error but don't block the UI
        console.error('Failed to expire checkout session on cancel:', error);
      }
    }
    onOpenChange(false);
  }, [sessionId, props.userId, onOpenChange]);

  // Log when CheckoutProvider is ready to render
  useEffect(() => {
    if (clientSecret && selectedPrice && step === 'payment') {
    }
  }, [clientSecret, selectedPrice, sessionId, step]);

  return (
    <Dialog
      open={open}
      onOpenChange={onOpenChange}
      data-test="join-community-modal"
    >
      <DialogContent className="sm:top-[15%] sm:max-w-[500px] sm:translate-y-0">
        <DialogHeader>
          <DialogTitle>
            <Trans
              i18nKey="communities:joinCommunityTitle"
              values={{ name: props.communityName }}
            />
          </DialogTitle>
          <DialogDescription>
            <If condition={step === 'price-selection'}>
              <Trans
                i18nKey="communities:selectPricingPlan"
                values={{ name: props.communityName }}
              />
            </If>
            <If condition={step === 'confirmation'}>
              <Trans
                i18nKey="communities:confirmFreeMembership"
                values={{ name: props.communityName }}
              />
            </If>
            <If condition={step === 'payment' && selectedPrice}>
              <Trans
                i18nKey="communities:completePaymentFor"
                values={{
                  price: selectedPrice ? formatPrice(selectedPrice, t) : '',
                  name: props.communityName,
                }}
              />
            </If>
          </DialogDescription>
        </DialogHeader>

        {step === 'price-selection' ? (
          <PriceSelectionStep
            prices={props.prices || []}
            trialDays={props.trialDays}
            onContinue={handleContinueToPayment}
            onCancel={handleCancel}
          />
        ) : step === 'confirmation' ? (
          <div className="space-y-6">
            <div className="text-center">
              <div className="mb-4">
                <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
                  <svg
                    className="h-8 w-8 text-green-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
              </div>
              <h3 className="mb-2 text-lg font-medium">
                <Trans i18nKey="communities:joinFreeCommunityTitle" />
              </h3>
              <p className="text-muted-foreground">
                <Trans
                  i18nKey="communities:joinFreeCommunityDescription"
                  values={{ name: props.communityName }}
                />
              </p>
            </div>

            {clientSecret && (
              <CheckoutProvider
                stripe={stripePromise}
                options={{
                  fetchClientSecret: async () => {
                    return clientSecret;
                  },
                  elementsOptions: {
                    appearance: { theme: 'stripe' },
                  },
                }}
              >
                <FreeConfirmationStep handleCancel={handleCancel} />
              </CheckoutProvider>
            )}
          </div>
        ) : (
          <>
            {clientSecret && selectedPrice ? (
              <CheckoutProvider
                stripe={stripePromise}
                options={{
                  fetchClientSecret: async () => {
                    return clientSecret;
                  },
                  elementsOptions: {
                    appearance: { theme: 'stripe' },
                  },
                }}
              >
                <PaymentStep
                  communitySlug={props.communitySlug}
                  userId={props.userId}
                  selectedPrice={selectedPrice}
                  trialDays={props.trialDays}
                  onBack={handleBackToPricing}
                  onOpenChange={onOpenChange}
                  showChangeButton={
                    props.prices ? props.prices.length > 1 : false
                  }
                />
              </CheckoutProvider>
            ) : (
              <div className="flex flex-col items-center justify-center py-8">
                <If condition={initializationError}>
                  <ErrorAlert error={initializationError} />
                  <div className="mt-4 flex gap-2">
                    <Button
                      onClick={() => {
                        setInitializationError(null);
                        setClientSecret(null);
                      }}
                      data-test="retry-payment-setup"
                    >
                      <Trans i18nKey="common:retry" />
                    </Button>
                    <Button
                      variant="outline"
                      onClick={handleBackToPricing}
                      data-test="back-to-pricing"
                    >
                      <Trans i18nKey="common:back" />
                    </Button>
                  </div>
                </If>

                <If condition={!initializationError && isInitializing}>
                  <div
                    className="border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent"
                    role="status"
                    aria-label="Loading payment form"
                  />
                  <p className="mt-4">
                    <Trans i18nKey="communities:preparingPaymentForm" />
                  </p>
                  <Button
                    variant="outline"
                    onClick={handleBackToPricing}
                    className="mt-4"
                    data-test="back-while-loading"
                  >
                    <Trans i18nKey="common:back" />
                  </Button>
                </If>
              </div>
            )}
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}

const FreeConfirmationStep = ({
  handleCancel,
}: {
  handleCancel: () => void;
}) => {
  const checkout = useCheckout();

  const handleConfirmFreeMembership = async () => {
    const confirmResult = await checkout.confirm();
    if (confirmResult.error) {
      console.error('checkout.confirm error:', confirmResult.error.message);
    }
  };

  return (
    <div className="flex justify-end gap-3">
      <PaymentElementsSection />
      <Button
        variant="outline"
        onClick={handleCancel}
        data-test="cancel-free-join"
      >
        <Trans i18nKey="common:cancel" />
      </Button>
      <Button
        onClick={handleConfirmFreeMembership}
        data-test="confirm-free-membership"
        className="bg-green-600 hover:bg-green-700"
      >
        <Trans i18nKey="communities:joinFreeCommunityButton" />
      </Button>
    </div>
  );
};
