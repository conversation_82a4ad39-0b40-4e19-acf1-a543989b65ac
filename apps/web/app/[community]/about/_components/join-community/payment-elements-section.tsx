'use client';

import { PaymentElement } from '@stripe/react-stripe-js';

import { Trans } from '@kit/ui/trans';

/**
 * Simple wrapper for the Stripe PaymentElement.
 * No props needed as the checkout context is provided by the parent.
 */
export function PaymentElementsSection() {
  return (
    <div className="py-4" data-test="stripe-payment-element-container">
      <div className="mb-2 font-medium">
        <Trans i18nKey="common:creditCard" />
      </div>
      <PaymentElement />
    </div>
  );
}

PaymentElementsSection.displayName = 'PaymentElementsSection';
