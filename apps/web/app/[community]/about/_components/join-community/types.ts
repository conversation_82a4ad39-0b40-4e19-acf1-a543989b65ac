import { ProductPrice } from '~/lib/stripe/types';

// Type for payment method ref
export type PaymentMethodRefType = {
  requestSubmit: () => Promise<{
    success: boolean;
    paymentMethodId?: string;
    error?: string;
  }>;
};

export type JoinCommunityModalProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  communityId: string;
  communitySlug: string;
  communityName: string;
  userId: string | null;
  prices: ProductPrice[] | undefined;
  trialDays?: number;
};

export type PaymentSectionProps = {
  userId: string;
  onPaymentMethodCreated: (id: string) => void;
  onPaymentMethodReady: (isReady: boolean) => void;
};

export type PricingSectionProps = {
  prices: ProductPrice[] | undefined;
  selectedPriceId: string | undefined;
  setSelectedPriceId: (id: string) => void;
  trialDays?: number;
};
