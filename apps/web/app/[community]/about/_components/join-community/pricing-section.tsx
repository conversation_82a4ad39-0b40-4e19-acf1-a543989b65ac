import { memo } from 'react';

import { useTranslation } from 'react-i18next';

import { RadioGroup, RadioGroupItem } from '@kit/ui/radio-group';
import { Trans } from '@kit/ui/trans';

import {
  calculatePriceInfo,
  formatBillingDetails,
  formatPrice,
} from '../../_lib/pricing-helper';
import { PricingSectionProps } from './types';

// Helper to determine if we should show the pricing options UI
const shouldShowPricingOptions = (
  prices: PricingSectionProps['prices'],
): boolean => {
  if (!prices || prices.length <= 1) return false;

  const paidPrices = prices.filter(
    (price) => price.unitAmount && price.unitAmount > 0,
  );
  return paidPrices.length > 1;
};

export const PricingSection = memo(function PricingSection({
  prices,
  selectedPriceId,
  setSelectedPriceId,
  trialDays = 0,
}: PricingSectionProps) {
  const { t } = useTranslation('communities');

  if (!prices || !shouldShowPricingOptions(prices)) return null;

  // Use our shared helper to get consistent price information
  const pricesWithSavings = calculatePriceInfo(prices, t).filter(
    (price) => price.unitAmount && price.unitAmount > 0,
  );

  return (
    <div className="py-4">
      <div className="mb-3">
        <div className="flex items-center gap-2">
          <h3 className="text-lg font-semibold">Membership</h3>
          {trialDays > 0 && (
            <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-200">
              <Trans
                i18nKey="communities:freeTrialBadge"
                values={{ trialDays }}
              />
            </span>
          )}
        </div>
        {trialDays > 0 && (
          <p className="text-muted-foreground mt-1 text-sm">
            <Trans
              i18nKey="communities:membershipTrialInfo"
              values={{ trialDays }}
            />
          </p>
        )}
      </div>
      <RadioGroup
        value={selectedPriceId}
        onValueChange={setSelectedPriceId}
        className="flex space-x-2"
      >
        {pricesWithSavings.map((price) => (
          <div
            key={price.id}
            className={`hover:bg-accent hover:text-accent-foreground flex-1 rounded-md border p-4 transition-colors ${
              selectedPriceId === price.id
                ? 'border-primary bg-primary/5'
                : 'border-border'
            }`}
            data-test={`price-option-${price.id}`}
          >
            <RadioGroupItem
              value={price.id}
              id={price.id}
              className="peer sr-only"
              aria-label={`Select price ${price.displayLabel}`}
              data-test={`price-radio-${price.id}`}
            />
            <label
              htmlFor={price.id}
              className="block cursor-pointer font-medium"
            >
              {price.displayLabel}
              {price.savingsPercent && price.savingsPercent > 0 && (
                <span className="ml-2 inline-block rounded-md bg-green-100 px-2 py-0.5 text-xs font-medium text-green-800">
                  {t('communities:pricing.savePercent', {
                    defaultValue: 'Save {{percent}}%',
                    percent: price.savingsPercent,
                  })}
                </span>
              )}
            </label>
            <div className="mt-1 text-xl font-bold">
              {formatPrice(price, t)}
            </div>
            <div className="text-muted-foreground text-sm">
              {formatBillingDetails(price, t)}
            </div>
          </div>
        ))}
      </RadioGroup>
    </div>
  );
});
