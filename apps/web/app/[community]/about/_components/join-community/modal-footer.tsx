import Link from 'next/link';

import { But<PERSON> } from '@kit/ui/button';
import { DialogFooter } from '@kit/ui/dialog';
import { Trans } from '@kit/ui/trans';

import { ProductPrice } from '~/lib/stripe/types';

import { formatPrice } from '../../_lib/pricing-helper';

type ModalFooterProps = {
  isPending: boolean;
  onCancel: () => void;
  onJoin: () => void;
  paymentMethodReady: boolean;
  selectedPrice: ProductPrice | undefined;
  nextPaymentDate: string;
  trialDays: number;
};

export function ModalFooter({
  isPending,
  onCancel,
  onJoin,
  paymentMethodReady,
  selectedPrice,
  nextPaymentDate,
  trialDays,
}: ModalFooterProps) {
  return (
    <>
      <div className="text-muted-foreground mt-2 text-xs">
        {trialDays > 0 && (
          <Trans
            i18nKey="communities:freeTrialBlurb"
            values={{
              trialDays,
              nextPaymentDate,
              price: selectedPrice ? formatPrice(selectedPrice) : '$0',
            }}
            components={{
              Link: (
                <Link
                  href="/terms"
                  className="underline"
                  data-test="terms-link"
                  aria-label="View terms and conditions"
                />
              ),
            }}
          />
        )}
        {selectedPrice?.unitAmount === 0 && (
          <Trans
            i18nKey="communities:oneTimePaymentBlurb"
            values={{
              price: selectedPrice ? formatPrice(selectedPrice) : '$0',
            }}
          />
        )}

        {selectedPrice?.unitAmount &&
          selectedPrice.unitAmount > 0 &&
          trialDays === 0 && (
            <Trans
              i18nKey="communities:recurringPaymentBlurb"
              values={{
                price: selectedPrice ? formatPrice(selectedPrice) : '$0',
              }}
            />
          )}
      </div>

      <DialogFooter className="mt-4">
        <Button
          variant="outline"
          onClick={onCancel}
          disabled={isPending}
          data-test="cancel-join-community-button"
          aria-label="Cancel joining community"
        >
          <Trans i18nKey="common:cancel" />
        </Button>
        <Button
          onClick={onJoin}
          disabled={isPending || !paymentMethodReady}
          data-test="confirm-join-community-button"
          aria-label={
            isPending
              ? 'Joining community'
              : trialDays > 0
                ? 'Start free trial and join community'
                : 'Join community now'
          }
        >
          {isPending ? (
            <Trans i18nKey="communities:joiningCommunity" />
          ) : trialDays > 0 ? (
            <Trans i18nKey="communities:startFreeTrial" />
          ) : (
            <Trans i18nKey="communities:joinNow" />
          )}
        </Button>
      </DialogFooter>
    </>
  );
}
