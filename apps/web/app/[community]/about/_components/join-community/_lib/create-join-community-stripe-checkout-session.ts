'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { getStripeCustomerService } from '~/lib/stripe/services';
import { createStripeSdk } from '~/lib/stripe/utils/stripe-sdk';

export const createJoinCommunityCheckoutSessionAction = enhanceAction(
  async (
    { communityId, communitySlug, priceId, trialDays, returnUrl },
    user,
  ) => {
    const logger = await getLogger();

    const ctx = {
      name: 'createJoinCommunityCheckoutSessionAction',
      communityId,
      communitySlug,
    };

    logger.info(ctx, '🔍 Creating checkout session for joining community:', {
      priceId,
      trialDays,
      userId: user.id,
    });

    const client = getSupabaseServerClient();
    const stripe = createStripeSdk('V2');

    logger.info(
      { ...ctx, userId: user.id },
      'Getting customer for checkout session',
    );

    const customerService = getStripeCustomerService(client);
    const stripeAccount = await customerService.getStripeAccountBilling(
      user.id,
    );

    if (!stripeAccount) {
      logger.error(
        { ...ctx, userId: user.id },
        '❌ No Stripe account found for user',
      );
      throw new Error('Stripe account not found');
    }

    logger.info(ctx, '🔍 Attempting to create checkout session with price:', {
      priceId,
    });

    try {
      // First, get the price details from Stripe to determine if it's recurring or one-time
      const priceDetails = await stripe.prices.retrieve(priceId);
      const price = priceDetails as unknown as {
        type: 'recurring' | 'one_time';
      };
      const isRecurring = price.type === 'recurring';
      const mode: 'subscription' | 'payment' = isRecurring
        ? 'subscription'
        : 'payment';

      logger.info(ctx, '🔍 Price details retrieved:', {
        priceId,
        type: price.type,
        mode,
        isRecurring,
        unitAmount:
          (priceDetails as unknown as { unit_amount?: number })?.unit_amount ||
          'unknown',
      });

      const clientReferenceId = crypto.randomUUID();

      // Build checkout session parameters declaratively based on price type
      const baseSessionParams = {
        client_reference_id: clientReferenceId,
        customer_account: stripeAccount.stripeAccountId,
        ui_mode: 'custom' as const, // Required for custom UI integration

        line_items: [
          {
            price: priceId,
            quantity: 1,
          },
        ],
        mode,
        metadata: {
          user_id: user.id,
          community_id: communityId,
          community_slug: communitySlug,
          purchase_type: 'community_membership',
        },
        return_url: returnUrl,
      };

      // Build subscription-specific parameters
      const subscriptionData = isRecurring
        ? {
            ...(trialDays ? { trial_period_days: trialDays } : {}),
            metadata: {
              user_id: user.id,
              community_id: communityId,
              purchase_type: 'community_membership',
            },
          }
        : undefined;

      // Build invoice creation parameters for one-time payments
      const invoiceCreation = !isRecurring
        ? {
            enabled: true,
            invoice_data: {
              metadata: {
                user_id: user.id,
                community_id: communityId,
                purchase_type: 'community_membership',
              },
            },
          }
        : undefined;

      // Combine all parameters declaratively
      const sessionParams = {
        ...baseSessionParams,
        ...(subscriptionData ? { subscription_data: subscriptionData } : {}),
        ...(invoiceCreation ? { invoice_creation: invoiceCreation } : {}),
      };

      console.log(`🚀 ~ sessionParams:`, sessionParams);

      const checkoutSessionResponse =
        await stripe.checkout.sessions.create(sessionParams);

      console.log(`🚀 ~ checkoutSessionResponse:`, checkoutSessionResponse);

      const checkoutSession = checkoutSessionResponse as unknown as {
        id: string;
        client_secret: string;
        url: string | null;
        mode: 'subscription' | 'payment';
      };

      // Insert the checkout session into the database
      await client.from('checkout_sessions').insert({
        id: checkoutSession.id,
        user_id: user.id,
        community_id: communityId,
        price_id: priceId,
        purchase_type: 'community_membership',
        trial_days: trialDays || undefined,
        metadata: {
          user_id: user.id,
          community_id: communityId,
          community_slug: communitySlug,
        },
      });

      if (!checkoutSession.client_secret) {
        logger.error(ctx, '❌ No client secret returned in checkout session');
        throw new Error('Payment setup failed - no client secret available');
      }

      logger.info(
        {
          ctx,
          sessionId: checkoutSession.id,
          id: checkoutSession.id,
          hasClientSecret: !!checkoutSession.client_secret,
          url: checkoutSession.url || null,
          mode: checkoutSession.mode,
        },
        '✅ Checkout session created successfully:',
      );

      return {
        checkoutToken: checkoutSession.client_secret,
        sessionId: checkoutSession.id,
      };
    } catch (error) {
      logger.error({ ...ctx, error }, '❌ Failed to create checkout session');
      throw error;
    }
  },
  {
    auth: true,
    schema: z.object({
      communityId: z.string().uuid(),
      communitySlug: z.string(),
      priceId: z.string(),
      trialDays: z.number().nullable().optional(),
      returnUrl: z.string(),
    }),
  },
);
