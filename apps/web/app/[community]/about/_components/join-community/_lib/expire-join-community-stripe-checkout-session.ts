'use server';

import type Strip<PERSON> from 'stripe';
import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createStripeSdk } from '~/lib/stripe/utils/stripe-sdk';

export const expireJoinCommunityCheckoutSessionAction = enhanceAction(
  async ({ sessionId }, user) => {
    const logger = await getLogger();

    const ctx = {
      name: 'expire-join-community-checkout-session',
      sessionId,
      userId: user.id,
    };

    const client = getSupabaseServerClient();
    const stripe = createStripeSdk('V2');

    logger.info(ctx, 'Expiring checkout session');

    try {
      // Verify the user owns this checkout session
      const { data: checkoutSessionData, error: fetchError } = await client
        .from('checkout_sessions')
        .select('user_id, status')
        .eq('id', sessionId)
        .single();

      if (fetchError) {
        logger.error(
          { ...ctx, error: fetchError },
          'Failed to fetch checkout session ownership data',
        );
        throw new Error('Failed to verify session ownership');
      }

      if (!checkoutSessionData) {
        logger.error(ctx, '❌ No checkout session found in database');
        throw new Error('Checkout session not found in database');
      }

      if (checkoutSessionData.user_id !== user.id) {
        logger.error(
          { ...ctx, sessionOwnerId: checkoutSessionData.user_id },
          '❌ Unauthorized attempt to expire checkout session',
        );
        throw new Error('Unauthorized: You do not own this checkout session');
      }

      // Check if session is already expired or completed
      if (checkoutSessionData.status !== 'open') {
        logger.info(
          { ...ctx, status: checkoutSessionData.status },
          'Session already expired or completed, skipping Stripe call',
        );
        return { success: true };
      }

      logger.info(
        ctx,
        '✅ User ownership verified, expiring session in Stripe',
      );

      // Expire the session in Stripe
      await stripe.checkout.sessions.expire(sessionId);

      // Update the session status in our database
      const { error: updateError } = await client
        .from('checkout_sessions')
        .update({ status: 'expired' })
        .eq('id', sessionId);

      if (updateError) {
        logger.error(
          { ...ctx, error: updateError },
          'Failed to update session status in database',
        );
        // Don't throw here since Stripe session is already expired
      }

      logger.info(ctx, '✅ Checkout session expired successfully');

      return { success: true };
    } catch (error) {
      logger.error({ ...ctx, error }, 'Failed to expire checkout session');

      // Check if it's a Stripe error about session already being expired/completed
      // First check for specific Stripe error codes (more reliable)
      if (error && typeof error === 'object' && 'code' in error) {
        const stripeError = error as Stripe.StripeRawError;
        const stripeErrorCode = stripeError.code;
        if (
          stripeErrorCode === 'checkout_session_expired' ||
          stripeErrorCode === 'checkout_session_already_expired' ||
          stripeErrorCode === 'resource_missing'
        ) {
          logger.info(
            { ...ctx, stripeErrorCode },
            'Session was already expired/completed (via Stripe error code)',
          );
          return { success: true };
        }
      }

      // Fallback to string matching for backward compatibility
      if (error instanceof Error && error.message.includes('already')) {
        logger.info(
          { ...ctx, errorMessage: error.message },
          'Session was already expired/completed (via message string)',
        );
        return { success: true };
      }

      throw error;
    }
  },
  {
    auth: true,
    schema: z.object({
      sessionId: z.string(),
    }),
  },
);
