'use server';

import { redirect } from 'next/navigation';

import { getLogger } from '@kit/shared/logger';
import { requireUser } from '@kit/supabase/require-user';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import pathsConfig from '~/config/paths.config';
import { createCommunityService } from '~/lib/communities/community/community.service';
import { syncStripeInvoiceToDb } from '~/lib/stripe/_lib/stripe-sync-utils/sync-stripe-invoice-to-db';
import { syncStripeSubscriptionToDb } from '~/lib/stripe/_lib/stripe-sync-utils/sync-stripe-subscription-to-db';
import { createStripeSdk } from '~/lib/stripe/utils/stripe-sdk';

import { JoinCommunityAction } from './join-community-action';

/**
 * Server component that handles the completion of a Stripe checkout session.
 * This is shown when the user returns from the Stripe checkout flow with a session_id in the URL.
 */
export async function PaymentCompletionHandler({
  sessionId,
  communitySlug,
}: {
  sessionId: string;
  communitySlug: string;
}) {
  const logger = await getLogger();

  const ctx = {
    name: 'PaymentCompletionHandler',
    communitySlug,
    sessionId,
  };

  const stripe = createStripeSdk('V2');
  const client = getSupabaseServerClient();

  // Get the authenticated user
  const auth = await requireUser(client);

  if (auth.error || !auth.data) {
    logger.error(
      { ...ctx, error: auth.error },
      'User not authenticated when completing payment',
    );

    redirect(pathsConfig.auth.signIn);
  }

  const userId = auth.data.id;

  // Get the community
  const communityService = createCommunityService(client);
  const { communityPublicData: community } =
    await communityService.getCommunityPublicData({
      communitySlug,
    });

  if (!community) {
    logger.error(
      { ...ctx, userId },
      'Community not found when completing payment',
    );

    redirect(pathsConfig.app.home);
  }

  logger.info(
    { ...ctx, userId, communityId: community.id },
    'Processing completed checkout session',
  );

  // Get the checkout session from Stripe to ensure it's completed and get subscription ID
  const stripeCheckoutSession =
    await stripe.checkout.sessions.retrieve(sessionId);

  console.log(`🚀 ~ stripeCheckoutSession:`, stripeCheckoutSession);

  // Access the actual session data from the response
  const sessionData = stripeCheckoutSession.data || stripeCheckoutSession;

  if (
    sessionData.status !== 'complete' ||
    sessionData.payment_status !== 'paid'
  ) {
    logger.warn(
      { ...ctx, userId, stripeSession: sessionData },
      'Session not completed – redirecting to home',
    );
    redirect(pathsConfig.app.home);
  }

  // Get the checkout session details from database to retrieve price_id and trial_days
  // Use admin client to bypass RLS policies
  const adminClient = getSupabaseServerAdminClient();
  const { data: dbCheckoutSession, error: dbCheckoutError } = await adminClient
    .from('checkout_sessions')
    .select('price_id, trial_days')
    .eq('id', sessionId)
    .single();

  if (dbCheckoutError || !dbCheckoutSession) {
    logger.error(
      { ...ctx, userId, error: dbCheckoutError },
      'Failed to retrieve checkout session from database',
    );
    redirect(pathsConfig.app.home);
  }

  // Get the line items from the checkout session
  const stripeCheckoutSessionLineItems =
    await stripe.checkout.sessions.listLineItems(sessionId);
  console.log(
    `🚀 ~ stripeCheckoutSessionLineItems:`,
    stripeCheckoutSessionLineItems,
  );

  // Access line items data properly - use the database session for reliable data
  const priceId = dbCheckoutSession.price_id;
  const trialDays = dbCheckoutSession.trial_days;
  const invoiceId = sessionData.invoice as string;

  if (!sessionData.subscription) {
    logger.warn(
      { ...ctx, userId },
      'No subscription found in checkout session',
    );
    // If no subscription then we need to get the stripe session line items and invoice
    // since it is a one-time payment

    if (invoiceId) {
      await syncStripeInvoiceToDb(adminClient, stripe, invoiceId);
    }
  } else {
    logger.info({ ...ctx, userId }, 'Subscription found in checkout session');

    const subscriptionId = sessionData.subscription as string;
    // Check if subscription exists in our database (might have been created by webhook)
    logger.info(
      { ...ctx, userId, subscriptionId },
      'Checking if subscription exists in database',
    );

    const { data: existingSubscription } = await adminClient
      .from('subscriptions')
      .select('*')
      .eq('id', subscriptionId)
      .maybeSingle();

    if (!existingSubscription) {
      logger.info(
        { ...ctx, userId, subscriptionId },
        'Subscription not found in database - creating it now',
      );

      try {
        // Create the subscription in our database using the sync utility
        await syncStripeSubscriptionToDb(adminClient, stripe, subscriptionId);

        logger.info(
          { ...ctx, userId, subscriptionId },
          'Successfully created subscription in database',
        );
      } catch (syncError) {
        logger.error(
          { ...ctx, userId, subscriptionId, error: syncError },
          'Failed to sync subscription to database',
        );
        redirect(pathsConfig.app.home);
      }
    }

    logger.info(
      {
        ...ctx,
        userId,
        communityId: community.id,
        priceId: dbCheckoutSession.price_id,
        trialDays: dbCheckoutSession.trial_days,
        subscriptionId,
      },
      'Retrieved checkout session details and confirmed subscription exists',
    );
  }

  // JoinCommunityAction will handle the redirect internally
  // Don't wrap in try-catch as it will interfere with the redirect
  await JoinCommunityAction({
    communityId: community.id!,
    communitySlug,
    userId,
    paymentMethodId: null,
    priceId: priceId || null,
    trialDays: trialDays || null,
    checkoutSessionId: sessionId,
    latestInvoiceId: invoiceId || null,
  });
}
