'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createStripeSdk } from '~/lib/stripe/utils/stripe-sdk';

export const updateJoinCommunityCheckoutSessionAction = enhanceAction(
  async ({ sessionId, communityName }, user) => {
    const logger = await getLogger();

    const ctx = {
      name: 'update-join-community-checkout-session',
      sessionId,
      communityName,
      userId: user.id,
    };

    const client = getSupabaseServerClient();
    const stripe = createStripeSdk();

    // Verify the user owns this checkout session
    logger.info(ctx, 'Verifying user ownership of checkout session');
    try {
      const { data: checkoutSessionData, error: fetchError } = await client
        .from('checkout_sessions')
        .select('user_id')
        .eq('id', sessionId)
        .single();

      if (fetchError) {
        logger.error(
          { ...ctx, error: fetchError },
          'Failed to fetch checkout session ownership data',
        );
        throw new Error('Failed to verify session ownership');
      }

      if (!checkoutSessionData) {
        logger.error(ctx, '❌ No checkout session found in database');
        throw new Error('Checkout session not found in database');
      }

      if (checkoutSessionData.user_id !== user.id) {
        logger.error(
          { ...ctx, sessionOwnerId: checkoutSessionData.user_id },
          '❌ Unauthorized attempt to update checkout session',
        );
        throw new Error('Unauthorized: You do not own this checkout session');
      }

      logger.info(ctx, '✅ User ownership of checkout session verified');
    } catch (error) {
      logger.error({ ...ctx, error }, 'Error during ownership verification');
      throw error;
    }

    logger.info(ctx, 'Getting checkout session from stripe');

    const checkoutSession = await stripe.checkout.sessions.retrieve(sessionId);

    if (!checkoutSession) {
      logger.error(ctx, '❌ No checkout session found');
      throw new Error('Checkout session not found');
    }

    logger.info(ctx, 'Checkout session retrieved', {
      checkoutSessionId: checkoutSession.id,
    });

    try {
      const updateCheckoutSession = await stripe.checkout.sessions.update(
        sessionId,
        {
          metadata: {
            community_name: communityName,
          },
        },
      );

      // Update the checkout session in the database
      const { error: dbError } = await client
        .from('checkout_sessions')
        .update({
          community_name: communityName,
        })
        .eq('id', sessionId);

      if (dbError) {
        logger.error(
          { ...ctx, error: dbError },
          'Failed to update checkout session in database',
        );
        throw new Error(`Database update failed: ${dbError.message}`);
      }

      logger.info(
        {
          ctx,
          id: updateCheckoutSession.id,
          hasClientSecret: !!updateCheckoutSession.client_secret,
          url: updateCheckoutSession.url,
          mode: updateCheckoutSession.mode,
          communityName,
        },
        '✅ Checkout session updated successfully:',
      );

      if (!updateCheckoutSession.client_secret) {
        logger.error(ctx, '❌ No client secret returned in checkout session');
        throw new Error('Payment setup failed - no client secret available');
      }

      return {
        checkoutToken: updateCheckoutSession.client_secret,
        communityName,
      };
    } catch (error) {
      logger.error(
        { ...ctx, error },
        'Failed to update checkout server session',
      );
      throw error;
    }
  },
  {
    auth: true,
    schema: z.object({
      sessionId: z.string(),
      communityName: z.string(),
    }),
  },
);
