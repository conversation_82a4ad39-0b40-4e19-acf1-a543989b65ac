'use server';

import { redirect } from 'next/navigation';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import pathsConfig from '~/config/paths.config';
import { createCommunityMembersService } from '~/lib/communities/members/community-members.service';
import { getStripeProductsService } from '~/lib/stripe/services';

const JoinCommunityActionSchema = z.object({
  communityId: z.string().uuid(),
  communitySlug: z.string(),
  userId: z.string().uuid(),
  paymentMethodId: z.string().nullable(),
  priceId: z.string().nullable(),
  trialDays: z.number().nullable(),
  checkoutSessionId: z.string().nullable().optional(),
  latestInvoiceId: z.string().nullable().optional(),
  subscriptionId: z.string().nullable().optional(),
});

/**
 * Adds a user to a community after successful payment/checkout completion
 * This action is called after checkout sessions (both free and paid) are completed
 */
export const JoinCommunityAction = enhanceAction(
  async function (params, user) {
    const logger = await getLogger();
    const ctx = {
      name: 'JoinCommunityAction',
      userId: user.id,
      communityId: params.communityId,
      communitySlug: params.communitySlug,
      priceId: params.priceId,
      trialDays: params.trialDays,
      paymentMethodId: params.paymentMethodId,
      checkoutSessionId: params.checkoutSessionId,
      latestInvoiceId: params.latestInvoiceId,
      subscriptionId: params.subscriptionId,
    };

    logger.info(ctx, 'Adding user to community after checkout completion...');

    const client = getSupabaseServerClient();
    const communityMembersService = createCommunityMembersService(client);

    try {
      if (params.priceId) {
        // Get price details to determine order type
        const stripeProductsService = getStripeProductsService(client);
        const priceDetails = await stripeProductsService.getPrice(
          params.priceId,
        );

        // Access the actual price data from the response
        const priceData = priceDetails.data || priceDetails;

        // Determine order type based on price type
        const orderType =
          priceData.type === 'recurring' ? 'subscription' : 'one-time';

        logger.info(
          ctx,
          `Adding user to community with order type: ${orderType}`,
        );

        await communityMembersService.joinCommunity({
          communityId: params.communityId,
          userId: user.id,
          orderType,
          priceId: params.priceId || '',
          latestInvoiceId: params.latestInvoiceId || '',
          subscriptionId: params.subscriptionId || undefined,
        });
      } else {
        // Fallback for communities without pricing
        logger.info(ctx, 'Adding user to free community without pricing');

        await communityMembersService.joinCommunity({
          communityId: params.communityId,
          userId: user.id,
          orderType: 'subscription', // Default to subscription for free communities
          priceId: params.priceId || '',
          latestInvoiceId: params.latestInvoiceId || '',
          subscriptionId: params.subscriptionId || undefined,
        });
      }

      logger.info(ctx, 'Successfully added user to community');
    } catch (error) {
      logger.error({ ...ctx, error }, 'Failed to add user to community');

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }

    // Redirect to community forums
    const path = pathsConfig.app.forums.replace(
      '[community]',
      params.communitySlug,
    );

    redirect(path);
  },
  {
    auth: true,
    schema: JoinCommunityActionSchema,
  },
);
