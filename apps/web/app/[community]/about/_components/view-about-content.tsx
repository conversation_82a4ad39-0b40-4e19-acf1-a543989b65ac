'use client';

import {
  type J<PERSON>NContent,
  TIPTAP_EXTENSIONS,
  generateHTML,
} from '@kit/ui/dojo/organisms/tiptap-editor';

type ViewAboutContentProps = {
  content: JSONContent | null;
};

export function ViewAboutContent({ content }: ViewAboutContentProps) {
  // Safely render HTML from content, handling potential errors
  if (!content) {
    return <p className="text-muted-foreground">No content available</p>;
  }

  try {
    const html = generateHTML(content, TIPTAP_EXTENSIONS);
    return (
      <div
        className="prose prose-stone dark:prose-invert max-w-none"
        dangerouslySetInnerHTML={{ __html: html }}
        data-test="about-page-content-view"
      />
    );
  } catch (error) {
    console.error('Error rendering content:', error);
    return (
      <div className="text-muted-foreground">
        <p>Content preview unavailable</p>
      </div>
    );
  }
}

ViewAboutContent.displayName = 'ViewAboutContent';
