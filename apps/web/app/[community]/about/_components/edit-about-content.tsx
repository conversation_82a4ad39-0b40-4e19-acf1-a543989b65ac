'use client';

import { useState, useTransition } from 'react';

import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';

import { Button } from '@kit/ui/button';
import { CharacterCountDisplay } from '@kit/ui/dojo/molecules/character-count-display';
import type { Editor, JSONContent } from '@kit/ui/dojo/organisms/tiptap-editor';
import {
  TipTapEditor,
  type TipTapProps,
  useTipTapCharacterCount,
} from '@kit/ui/dojo/organisms/tiptap-editor';
import { TooltipProvider } from '@kit/ui/tooltip';
import { Trans } from '@kit/ui/trans';

import { ViewAboutContent } from '~/[community]/about/_components/view-about-content';
import { saveAboutContentAction } from '~/[community]/about/_lib/server/actions/save-about-content-action';
import {
  createImageDeleteHandler,
  createImageUploadHandler,
} from '~/lib/images/handler/image-handlers';
import { transformTiptapContent } from '~/lib/utils/transform-tiptap-content';

const ABOUT_PAGE_CHARACTER_LIMIT = 5000;

type EditAboutContentProps = {
  pageId: string;
  communityId: string;
  content: JSONContent | null;
  hasEditPermissions?: boolean;
};

export function EditAboutContent({
  pageId,
  communityId,
  content,
  hasEditPermissions = false,
}: EditAboutContentProps) {
  const { t } = useTranslation('communities');
  const [isEditing, setIsEditing] = useState(false);
  const [editorContent, setEditorContent] = useState<JSONContent | null>(
    content,
  );
  const [isPending, startTransition] = useTransition();

  // State to store the editor instance
  const [editor, setEditor] = useState<Editor | null>(null);

  // Use the character count hook to get counts from the editor
  const { characterCount: rawCharacterCount, wordCount: rawWordCount } =
    useTipTapCharacterCount(editor);

  // defend against the void
  const characterCount = rawCharacterCount ?? 0;
  const wordCount = rawWordCount ?? 0;

  const handleEditorChange: TipTapProps['onChange'] = (newContent) => {
    setEditorContent(newContent as JSONContent);
  };

  const handleSave = () => {
    startTransition(async () => {
      try {
        if (!editorContent) {
          return;
        }

        const result = await saveAboutContentAction({
          communityId,
          content: transformTiptapContent(editorContent),
          characterCount,
          wordCount,
        });

        if (result.success) {
          toast.success('About content saved successfully');
          setIsEditing(false);
        } else {
          toast.error(`Failed to save: ${result.error}`);
        }
      } catch {
        toast.error('Failed to save content');
      }
    });
  };

  const featureDir = 'pages';
  const storageDirPath = pageId;

  const handleImageUpload = createImageUploadHandler({
    featureDir,
    storageDirPath,
    communityId: communityId,
  });

  const handleImageDelete = createImageDeleteHandler({
    featureDir,
    storageDirPath,
    communityId: communityId,
  });

  if (isEditing) {
    return (
      <div className="space-y-4">
        <TooltipProvider>
          <TipTapEditor
            value={editorContent}
            onChange={handleEditorChange}
            contextType="communityAbout"
            onImageUpload={handleImageUpload}
            onImageDelete={handleImageDelete}
            className="border-input focus-within:border-primary shadow-xs grow rounded-xl border"
            editorContentClassName="p-5 grow"
            editorClassName="focus:outline-hidden h-full"
            output="json"
            placeholder={t('aboutPageContentPlaceholder')}
            autofocus={true}
            editable={true}
            characterLimit={ABOUT_PAGE_CHARACTER_LIMIT}
            data-test="about-page-content-edit-editor"
            onReady={setEditor}
            onDestroy={() => setEditor(null)}
          />
        </TooltipProvider>

        {/* Display character and word counts */}
        <CharacterCountDisplay
          characterCount={characterCount}
          wordCount={wordCount}
          characterLimit={ABOUT_PAGE_CHARACTER_LIMIT}
        />
        <div className="flex justify-end space-x-2">
          <Button
            variant="outline"
            onClick={() => setIsEditing(false)}
            disabled={isPending}
            data-test="cancel-edit-about-button"
            aria-label="Cancel editing about page"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={isPending}
            data-test="save-about-content-button"
            aria-label="Save about page content"
          >
            {isPending ? 'Saving...' : 'Save'}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {hasEditPermissions && (
        <div className="flex justify-end">
          <Button
            onClick={() => setIsEditing(true)}
            data-test="edit-about-content-button"
            aria-label="Edit about page content"
          >
            <Trans i18nKey="communities:about.editAbout" />
          </Button>
        </div>
      )}
      <ViewAboutContent content={editorContent ?? content} />
    </div>
  );
}

EditAboutContent.displayName = 'EditAboutContent';
