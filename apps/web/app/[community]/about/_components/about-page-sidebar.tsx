'use client';

import { useState } from 'react';

import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

import { Building2, Calendar, Globe, Lock, PlusIcon } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { Button } from '@kit/ui/button';
import { Card, CardContent } from '@kit/ui/card';
import { If } from '@kit/ui/if';
import { Trans } from '@kit/ui/trans';

import { LeaveCommunityContainer } from '~/[community]/_components/leave-community-container';
import { AvatarHoverWrapper } from '~/community/_components/avatar-hover-wrapper';
import pathsConfig from '~/config/paths.config';
import type { CommunityWorkspace } from '~/lib/communities/community-workspace.loader';
import type { PublicCommunityView } from '~/lib/communities/community/types';
import type { ProductPrice } from '~/lib/stripe/types';

import { formatPrice } from '../_lib/pricing-helper';
import { CommunityPriceDisplay } from './community-price-display';
import { JoinCommunityModal } from './join-community/join-community-modal';

type AboutPageSidebarProps = {
  workspace: CommunityWorkspace;
  community: PublicCommunityView;
  hasEditPermissions: boolean;
  prices?: ProductPrice[];
  trialDays?: number;
};

export function AboutPageSidebar({
  workspace,
  community,
  hasEditPermissions,
  prices,
  trialDays,
}: AboutPageSidebarProps) {
  const router = useRouter();
  const { t } = useTranslation('communities');
  const [isJoinModalOpen, setIsJoinModalOpen] = useState(false);
  const formattedCreatedAt = new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(new Date(community.createdAt || new Date()));

  const formattedCategory = t(
    `communities:categories.${community.categoryName}`,
  );
  const formattedLanguage = t(
    `communities:languages.${community.languageName}`,
  );

  const communitySettingsPath = `${pathsConfig.app.communitySettings.replace(
    '[community]',
    community.slug || '',
  )}?tab=images`;

  // Find if there are any paid prices (unitAmount > 0)
  const hasPaidPrices = prices?.some((price) => price.unitAmount > 0);

  // Find the lowest paid price for display purposes
  const lowestPaidPrice = prices
    ?.filter((price) => price.unitAmount > 0)
    ?.sort((a, b) => (a.unitAmount || 0) - (b.unitAmount || 0))[0];

  // Determine button text based on pricing and trial status
  const getJoinButtonText = () => {
    // If has trial days, show trial button
    if (hasPaidPrices && trialDays && trialDays > 0) {
      return 'communities:startFreeTrialButton';
    }

    // If has paid prices, show price
    if (hasPaidPrices && lowestPaidPrice) {
      return 'communities:joinPaidCommunityButton';
    }

    // If only free prices or no prices, show free community
    return 'communities:joinFreeCommunityButton';
  };

  // Get the formatted price for paid communities
  const getFormattedPrice = () => {
    if (lowestPaidPrice) {
      return formatPrice(lowestPaidPrice, t);
    }
    return '';
  };

  const handleJoinCommunity = () => {
    console.log('🔍 Join community button clicked:', {
      hasUser: !!workspace.user,
      userId: workspace.user?.id,
      communityId: community.id,
      communitySlug: community.slug,
      hasPaidPrices,
      pricesCount: prices?.length || 0,
    });

    if (!workspace.user || !workspace.user.id) {
      console.log('🔄 No user, redirecting to sign in');
      const communityPath = pathsConfig.app.communityAbout.replace(
        '[community]',
        community.slug || '',
      );
      const redirectPath = `${pathsConfig.auth.signIn}?next=${encodeURIComponent(communityPath)}`;

      router.push(redirectPath);
    } else {
      console.log(
        `🚀 ~ AboutPageSidebar: community data:`,
        community.id,
        community.slug,
      );
      // Always open modal for both free and paid communities
      // The modal will handle the different flows internally
      setIsJoinModalOpen(true);
    }
  };

  if (!community || !community.id || !community.slug || !community.name) {
    return (
      <div className="sticky top-16 max-h-[calc(100vh-4rem)] space-y-6 self-start overflow-y-auto">
        <Card>
          <CardContent className="pt-6">
            <div className="mb-6 flex flex-col items-center text-center">
              <div className="mb-4 h-24 w-24 items-center justify-center rounded-lg bg-gray-200">
                <Building2 className="h-12 w-12 text-gray-400" />
              </div>
              <h2 className="text-2xl font-bold">Community Not Found</h2>
              <p className="text-muted-foreground text-sm">
                The community you are looking for does not exist.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="sticky top-16 max-h-[calc(100vh-4rem)] space-y-6 self-start overflow-y-auto">
      <Card>
        <CardContent className="pt-6">
          <div className="mb-6 flex flex-col items-center text-center">
            <div className="mb-4 h-24 w-24 overflow-hidden rounded-lg">
              {community.logoUrl ? (
                <Image
                  src={community.logoUrl}
                  alt={community.name || 'Community'}
                  width={96}
                  height={96}
                  className="object-cover"
                />
              ) : (
                <div className="flex h-24 w-24 items-center justify-center rounded-lg bg-gray-200">
                  {hasEditPermissions ? (
                    <Button variant="outline" size="icon" asChild>
                      <Link
                        href={communitySettingsPath}
                        data-test="edit-community-logo-button"
                        aria-label="Edit community logo"
                      >
                        <PlusIcon size={16} />
                      </Link>
                    </Button>
                  ) : (
                    <Building2 className="h-12 w-12 text-gray-400" />
                  )}
                </div>
              )}
            </div>
            <h2 className="text-2xl font-bold">
              {community.name || 'Community'}
            </h2>
            <p className="text-muted-foreground text-sm">
              {community.slug || ''}
            </p>
          </div>

          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4 border p-2">
              <div className="border-r text-center">
                <p className="text-2xl font-bold">
                  {(community.memberCount || 0).toLocaleString()}
                </p>
                <p className="text-muted-foreground text-sm">Members</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold">
                  {(community.adminCount || 0).toLocaleString()}
                </p>
                <p className="text-muted-foreground text-sm">Admins</p>
              </div>
            </div>

            <div className="space-y-2 pt-4">
              <div className="flex flex-col items-center gap-2">
                <AvatarHoverWrapper
                  userId={community.primaryOwnerUserId || ''}
                  pictureUrl={community.ownerPictureUrl || ''}
                  firstName={community.ownerFirstName || ''}
                  lastName={community.ownerLastName || ''}
                />
              </div>
              <div className="flex items-center gap-2">
                <Calendar size={16} />
                <span>Created: {formattedCreatedAt}</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="text-sm">{community.categoryIcon}</div>
                <span>{formattedCategory}</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="text-sm">{community.languageIcon}</div>
                <span>{formattedLanguage}</span>
              </div>
              <div className="flex items-center gap-2">
                {community.isPrivate ? (
                  <>
                    <Lock size={16} />
                    <span>Private</span>
                  </>
                ) : (
                  <>
                    <Globe size={16} />
                    <span>Public</span>
                  </>
                )}
              </div>
              {!workspace.community.memberId && (
                <CommunityPriceDisplay
                  defaultProductId={community.defaultProductId}
                  prices={prices}
                />
              )}
              {!workspace.community.memberId ? (
                <Button
                  onClick={handleJoinCommunity}
                  className="bg-primary mt-6 w-full py-6 text-lg font-bold tracking-wider uppercase shadow-xl transition-all hover:scale-[1.02] hover:shadow-2xl"
                  data-test="join-community-button"
                  aria-label={
                    hasPaidPrices && trialDays && trialDays > 0
                      ? 'Start free trial and join community'
                      : hasPaidPrices && lowestPaidPrice
                        ? `Join community for ${getFormattedPrice()}`
                        : 'Join free community'
                  }
                >
                  <Trans
                    i18nKey={getJoinButtonText()}
                    values={{ price: getFormattedPrice() }}
                  />
                </Button>
              ) : (
                <If
                  condition={
                    workspace.community.primaryOwnerUserId !==
                    workspace.user?.id
                  }
                >
                  <LeaveCommunityContainer
                    communityName={community.name}
                    communityId={community.id}
                  />
                </If>
              )}
            </div>
          </div>

          <JoinCommunityModal
            open={isJoinModalOpen}
            onOpenChange={(open) => {
              console.log('🔍 Modal open state changing to:', open);
              setIsJoinModalOpen(open);
            }}
            communityId={community.id}
            communitySlug={community.slug}
            communityName={community.name}
            userId={workspace.user?.id || null}
            prices={prices}
            trialDays={trialDays}
          />
        </CardContent>
      </Card>
    </div>
  );
}

AboutPageSidebar.displayName = 'AboutPageSidebar';
