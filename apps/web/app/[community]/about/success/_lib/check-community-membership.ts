'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

export const checkCommunityMembershipAction = enhanceAction(
  async ({ communitySlug }, user) => {
    const client = getSupabaseServerClient();

    // Get community info from slug
    const { data: community } = await client
      .from('communities')
      .select('id, name')
      .eq('slug', communitySlug)
      .single();

    if (!community) return null;

    // Check if user is now a member of this community
    const { data: membership } = await client
      .from('community_memberships')
      .select('id, status')
      .eq('user_id', user.id)
      .eq('community_id', community.id)
      .eq('status', 'active')
      .single();

    if (membership) {
      return {
        isMember: true,
        communityName: community.name,
      };
    }

    return null;
  },
  {
    auth: true,
    schema: z.object({
      communitySlug: z.string(),
    }),
  },
);
