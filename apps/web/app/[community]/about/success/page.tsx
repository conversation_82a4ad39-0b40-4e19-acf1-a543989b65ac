import { <PERSON>, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Trans } from '@kit/ui/trans';

import { withI18n } from '~/lib/i18n/with-i18n';

import { JoinSuccessContent } from './_components/join-success-content';

type JoinSuccessPageProps = {
  params: Promise<{ community: string }>;
  searchParams: Promise<{ session_id?: string }>;
};

async function JoinSuccessPage({ params, searchParams }: JoinSuccessPageProps) {
  const communitySlug = (await params).community;
  const sessionId = (await searchParams).session_id;

  if (!sessionId) {
    return (
      <div className="flex h-screen items-center justify-center">
        <span>
          <Trans i18nKey="communities:noSessionIdProvided" />
        </span>
      </div>
    );
  }

  return (
    <div className={'mt-4 flex flex-col items-center justify-center py-14'}>
      <Card>
        <CardHeader>
          <CardTitle>
            <Trans i18nKey="communities:joiningCommunity" />
          </CardTitle>
        </CardHeader>
        <CardContent>
          <JoinSuccessContent communitySlug={communitySlug} />
        </CardContent>
      </Card>
    </div>
  );
}

export default withI18n(JoinSuccessPage);

JoinSuccessPage.displayName = 'JoinSuccessPage';
