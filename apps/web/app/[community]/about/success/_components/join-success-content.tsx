'use client';

import Link from 'next/link';

import { Button } from '@kit/ui/button';
import { Spinner } from '@kit/ui/spinner';
import { Trans } from '@kit/ui/trans';

import { useCommunityMembership } from '../_hooks/use-community-membership';

type JoinSuccessContentProps = {
  communitySlug: string;
};

export function JoinSuccessContent({ communitySlug }: JoinSuccessContentProps) {
  const { status, membershipData } = useCommunityMembership(communitySlug);

  if (status === 'loading') {
    return (
      <div className="flex flex-col items-center gap-4">
        <Spinner />
        <p className="text-muted-foreground text-center">
          <Trans i18nKey="communities:processingMembership" />
        </p>
        <p className="text-muted-foreground text-center text-sm">
          Adding you to the community...
        </p>
      </div>
    );
  }

  if (status === 'timeout') {
    return (
      <div className="flex flex-col items-center gap-4">
        <p className="text-muted-foreground text-center">
          <Trans i18nKey="communities:membershipTakingLonger" />
        </p>
        <p className="text-muted-foreground text-center text-sm">
          You&apos;ll receive an email confirmation when your membership is
          ready.
        </p>
        <Button asChild>
          <Link href={`/${communitySlug}`}>
            <Trans i18nKey="communities:goToCommunity" />
          </Link>
        </Button>
      </div>
    );
  }

  if (status === 'ready' && membershipData) {
    return (
      <div className="flex flex-col items-center gap-4">
        <div className="mb-4">
          <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
            <svg
              className="h-8 w-8 text-green-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
        </div>
        <h3 className="mb-2 text-lg font-medium">
          <Trans
            i18nKey="communities:welcomeToCommuity"
            values={{ name: membershipData.communityName }}
          />
        </h3>
        <p className="text-muted-foreground text-center">
          <Trans i18nKey="communities:membershipComplete" />
        </p>
        <p className="text-muted-foreground text-center text-sm">
          Redirecting you to the community...
        </p>
      </div>
    );
  }

  return null;
}
