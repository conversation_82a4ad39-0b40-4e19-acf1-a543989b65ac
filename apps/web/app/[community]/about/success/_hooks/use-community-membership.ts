'use client';

import { useEffect, useState } from 'react';

import { useRouter } from 'next/navigation';

import pathsConfig from '~/config/paths.config';

import { checkCommunityMembershipAction } from '../_lib/check-community-membership';

type MembershipStatus = 'loading' | 'ready' | 'timeout';

type MembershipData = {
  isMember: boolean;
  communityName: string;
} | null;

export function useCommunityMembership(communitySlug: string) {
  const [status, setStatus] = useState<MembershipStatus>('loading');
  const [membershipData, setMembershipData] = useState<MembershipData>(null);
  const router = useRouter();

  useEffect(() => {
    let pollInterval: NodeJS.Timeout;
    let timeoutId: NodeJS.Timeout;

    const checkMembershipStatus = async () => {
      try {
        const result = await checkCommunityMembershipAction({
          communitySlug,
        });

        if (result?.isMember) {
          setStatus('ready');
          setMembershipData(result);
          clearInterval(pollInterval);
          clearTimeout(timeoutId);

          // Redirect to forums after short delay
          setTimeout(() => {
            const forumsPath = pathsConfig.app.forums.replace(
              '[community]',
              communitySlug,
            );
            router.push(forumsPath);
          }, 2000);

          return true;
        }
        return false;
      } catch (error) {
        console.error('Error checking membership status:', error);
        return false;
      }
    };

    // Start polling every 2 seconds
    pollInterval = setInterval(checkMembershipStatus, 2000);

    // Check immediately
    checkMembershipStatus();

    // Timeout after 60 seconds
    timeoutId = setTimeout(() => {
      clearInterval(pollInterval);
      if (status === 'loading') {
        setStatus('timeout');
      }
    }, 60000);

    return () => {
      clearInterval(pollInterval);
      clearTimeout(timeoutId);
    };
  }, [communitySlug, status, router]);

  return { status, membershipData };
}
