import { redirect } from 'next/navigation';

import { <PERSON>, CardContent, CardHeader } from '@kit/ui/card';
import type { JSONContent } from '@kit/ui/dojo/organisms/tiptap-editor';
import { PageBody } from '@kit/ui/page';

import { loadCommunityWorkspace } from '~/lib/communities/community-workspace.loader';
import type { PermissionsEnum } from '~/lib/communities/members/types';
import { hasCommunityPermission } from '~/lib/communities/permissions.utils';
import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { withI18n } from '~/lib/i18n/with-i18n';

import { CommunityNotFound } from '../_components/community-not-found';
import { AboutPageSidebar } from './_components/about-page-sidebar';
import { EditAboutContent } from './_components/edit-about-content';
import { MediaDropzone } from './_components/media-dropzone';
import { ViewAboutContent } from './_components/view-about-content';
import { loadAboutPageData } from './_lib/server/about-page.loader';

export const generateMetadata = async () => {
  const i18n = await createI18nServerInstance();
  const title = i18n.t('communities:about.pageTitle');

  return {
    title,
  };
};

type AboutPageProps = {
  params: Promise<{ community: string }>;
  searchParams?: Promise<{ session_id?: string }>;
};

async function AboutPage({ params, searchParams }: AboutPageProps) {
  const communitySlug = (await params).community;
  const sessionId = (await searchParams)?.session_id;

  // Handle payment completion by redirecting to success page
  if (sessionId) {
    redirect(`/${communitySlug}/about/success?session_id=${sessionId}`);
  }

  const [
    workspace,
    { communityPublicData, aboutPageData, defaultProduct, prices },
  ] = await Promise.all([
    loadCommunityWorkspace(communitySlug),
    loadAboutPageData(communitySlug),
  ]);

  if (!workspace || !workspace.community || !communityPublicData) {
    return <CommunityNotFound />;
  }

  const isMember = !!workspace.community.memberId;
  const permissions = (workspace.community.permissions ??
    []) as PermissionsEnum[];

  const hasEditPermissions = hasCommunityPermission(
    permissions,
    'community.pages.update',
  );

  const community = communityPublicData;
  const aboutContent = (aboutPageData?.content ?? null) as JSONContent | null;
  const pageId = aboutPageData?.id ?? null;

  const isPublicView = !isMember;

  if (!pageId) {
    console.warn(
      'About page data does not exist, cannot render edit components.',
    );
    return (
      <p data-test="about-content-not-created">
        About page content not yet created.
      </p>
    );
  }

  return (
    <PageBody>
      <div className="mb-8 grid grid-cols-1 gap-8 md:grid-cols-3">
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <MediaDropzone
                communityId={community.id || ''}
                pageId={pageId}
                hasEditPermissions={hasEditPermissions}
              />
            </CardHeader>

            <CardContent className="space-y-6 pt-6">
              <div>
                <p className="mb-4 text-lg">{community.description}</p>
              </div>

              <div>
                {pageId && (isPublicView || !hasEditPermissions) ? (
                  aboutContent ? (
                    <ViewAboutContent content={aboutContent} />
                  ) : (
                    <p className="text-muted-foreground">
                      No content available yet.
                    </p>
                  )
                ) : pageId && hasEditPermissions ? (
                  <EditAboutContent
                    pageId={pageId}
                    communityId={community.id || ''}
                    content={aboutContent}
                    hasEditPermissions={hasEditPermissions}
                  />
                ) : (
                  <p className="text-muted-foreground">
                    No content available yet.
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        <AboutPageSidebar
          workspace={workspace}
          community={community}
          hasEditPermissions={hasEditPermissions}
          prices={prices ?? []}
          trialDays={defaultProduct?.trialDays ?? undefined}
        />
      </div>
    </PageBody>
  );
}

export default withI18n(AboutPage);

AboutPage.displayName = 'AboutPage';
