import { ProductPrice } from '~/lib/stripe/types';

export type PriceWithSavings = ProductPrice & {
  savingsPercent?: number;
  displayLabel: string;
  displayInterval: string;
};

// Translation function type
export type TranslationFunction = (
  key: string,
  options?: { defaultValue?: string },
) => string;

/**
 * Calculate normalized daily rates and savings percentages for pricing options
 */
export function calculatePriceInfo(
  prices?: ProductPrice[],
  t?: TranslationFunction,
): PriceWithSavings[] {
  if (!prices || prices.length === 0) {
    return [];
  }

  // Filter out prices with no amount
  const validPrices = prices.filter(
    (price) => price.unitAmount !== null && price.unitAmount !== undefined,
  );

  // Create a map of daily rates to normalize across different intervals
  const dailyRates = new Map<string, number>();

  // Calculate daily rate for each price
  validPrices.forEach((price) => {
    const unitAmount = price.unitAmount || 0;
    const interval = price.interval?.toLowerCase() || '';
    let daysInPeriod = 0;

    // Convert each interval to number of days
    switch (interval) {
      case 'day':
        daysInPeriod = 1;
        break;
      case 'week':
        daysInPeriod = 7;
        break;
      case 'month':
        daysInPeriod = 30.44; // Average days in a month (365.25/12)
        break;
      case 'year':
        daysInPeriod = 365.25; // Account for leap years
        break;
      default:
        // Skip unknown intervals
        return;
    }

    const dailyRate = unitAmount / daysInPeriod;
    dailyRates.set(price.id, dailyRate);
  });

  // Calculate savings between each longer subscription vs. shorter ones
  const savingsMap = new Map<string, number>();

  // For each price, calculate savings compared to other prices
  validPrices.forEach((longerPrice) => {
    const longerInterval = longerPrice.interval?.toLowerCase();
    if (!longerInterval || !dailyRates.has(longerPrice.id)) return;

    // Find the shorter interval prices to compare with
    validPrices.forEach((shorterPrice) => {
      const shorterInterval = shorterPrice.interval?.toLowerCase();
      if (!shorterInterval || !dailyRates.has(shorterPrice.id)) return;

      // Skip comparing prices with the same interval
      if (longerInterval === shorterInterval) return;

      const longerIntervalRank = getIntervalRank(longerInterval);
      const shorterIntervalRank = getIntervalRank(shorterInterval);

      // Only calculate savings if this is a longer interval
      if (longerIntervalRank <= shorterIntervalRank) return;

      const longerDailyRate = dailyRates.get(longerPrice.id) || 0;
      const shorterDailyRate = dailyRates.get(shorterPrice.id) || 0;

      // Calculate savings percentage (how much cheaper is the longer subscription per day)
      const savingsPercent =
        ((shorterDailyRate - longerDailyRate) / shorterDailyRate) * 100;

      if (savingsPercent > 0) {
        // Store the highest savings percentage if multiple comparisons exist
        const currentSavings = savingsMap.get(longerPrice.id) || 0;
        if (savingsPercent > currentSavings) {
          savingsMap.set(longerPrice.id, Math.round(savingsPercent));
        }
      }
    });
  });

  // Create enriched price objects with display information
  return validPrices
    .map((price) => {
      const interval = price.interval?.toLowerCase() || '';

      // Lookup table for interval translations to avoid repetitive if/else blocks
      const intervalConfig = {
        day: {
          labelKey: 'labels.daily',
          labelDefault: 'Daily',
          intervalKey: 'intervals.day',
          intervalDefault: 'day',
        },
        week: {
          labelKey: 'labels.weekly',
          labelDefault: 'Weekly',
          intervalKey: 'intervals.week',
          intervalDefault: 'week',
        },
        month: {
          labelKey: 'labels.monthly',
          labelDefault: 'Monthly',
          intervalKey: 'intervals.month',
          intervalDefault: 'month',
        },
        year: {
          labelKey: 'labels.annual',
          labelDefault: 'Annual',
          intervalKey: 'intervals.year',
          intervalDefault: 'year',
        },
      } as const;

      const config = intervalConfig[interval as keyof typeof intervalConfig];

      // Human-friendly label with translation support
      const displayLabel = config
        ? (t?.(`communities:pricing.${config.labelKey}`, {
            defaultValue: config.labelDefault,
          }) ?? config.labelDefault)
        : price.interval || '';

      // Formatted interval for display with translation support
      const displayInterval = config
        ? (t?.(`communities:pricing.${config.intervalKey}`, {
            defaultValue: config.intervalDefault,
          }) ?? config.intervalDefault)
        : price.interval || '';

      return {
        ...price,
        savingsPercent: savingsMap.get(price.id),
        displayLabel,
        displayInterval,
      };
    })
    .sort((a, b) => {
      // Sort by interval - shorter intervals first
      const getIntervalPriority = (p: PriceWithSavings) => {
        const interval = p.interval?.toLowerCase();
        if (interval === 'day') return 1;
        if (interval === 'week') return 2;
        if (interval === 'month') return 3;
        if (interval === 'year') return 4;
        return 5; // Unknown intervals last
      };

      // First sort free prices to the top
      if ((a.unitAmount || 0) === 0 && (b.unitAmount || 0) > 0) return -1;
      if ((b.unitAmount || 0) === 0 && (a.unitAmount || 0) > 0) return 1;

      // Then sort by interval
      return getIntervalPriority(a) - getIntervalPriority(b);
    });
}

/**
 * Helper function to get numerical rank of intervals for comparison
 */
function getIntervalRank(interval: string): number {
  switch (interval) {
    case 'day':
      return 1;
    case 'week':
      return 2;
    case 'month':
      return 3;
    case 'year':
      return 4;
    default:
      return 0;
  }
}

/**
 * Format price for display with currency symbol and interval
 */
export function formatPrice(
  price: ProductPrice,
  t?: TranslationFunction,
): string {
  const amount = price.unitAmount ?? 0;
  const formattedAmount = `$${(amount / 100).toFixed(0)}`; // Remove decimals for cleaner display

  // Handle one-time payments
  if (price.type === 'one_time') {
    const onceText =
      t?.('communities:pricing.intervals.once', { defaultValue: 'Once' }) ??
      'Once';
    return `${formattedAmount} / ${onceText}`;
  }

  // Handle recurring payments with proper interval formatting
  if (price.interval) {
    const interval = price.interval.toLowerCase();

    // Lookup table for interval formatting to avoid repetitive switch cases
    const intervalFormatConfig = {
      day: { key: 'intervals.day', default: 'Day' },
      week: { key: 'intervals.week', default: 'Week' },
      month: { key: 'intervals.month', default: 'Month' },
      year: { key: 'intervals.year', default: 'Year' },
    } as const;

    const config =
      intervalFormatConfig[interval as keyof typeof intervalFormatConfig];

    if (config) {
      const intervalText =
        t?.(`communities:pricing.${config.key}`, {
          defaultValue: config.default,
        }) ?? config.default;
      return `${formattedAmount} / ${intervalText}`;
    } else {
      return `${formattedAmount} / ${price.interval}`;
    }
  }

  return formattedAmount;
}

/**
 * Format billing details for display
 */
export function formatBillingDetails(
  price: ProductPrice,
  t?: TranslationFunction,
): string {
  const amount = price.unitAmount ?? 0;
  const amountFormatted = `$${(amount / 100).toFixed(2)}`;
  const interval = price.interval?.toLowerCase();

  // Lookup table for billing details to avoid repetitive if/else blocks
  const billingConfig = {
    day: { key: 'billing.billedDaily', default: 'billed daily' },
    week: { key: 'billing.billedWeekly', default: 'billed weekly' },
    month: { key: 'billing.billedMonthly', default: 'billed monthly' },
    year: { key: 'billing.billedAnnually', default: 'billed annually' },
  } as const;

  const config = billingConfig[interval as keyof typeof billingConfig];

  if (config) {
    const billingText =
      t?.(`communities:pricing.${config.key}`, {
        defaultValue: config.default,
      }) ?? config.default;
    return `${amountFormatted} ${billingText}`;
  } else {
    return `${amountFormatted}${interval ? ` / ${interval}` : ''}`;
  }
}
