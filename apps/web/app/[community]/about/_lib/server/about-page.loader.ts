import 'server-only';

import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createCommunityService } from '~/lib/communities/community/community.service';
import { createCommunityPagesService } from '~/lib/communities/pages/community-pages.service';
import { getStripeProductsService } from '~/lib/stripe/services';

/**
 * Load data specific to the About page (public details and page content).
 * Workspace data (auth, membership, permissions) should be loaded separately.
 * @param communitySlug
 */
export async function loadAboutPageData(communitySlug: string) {
  const logger = await getLogger();
  const client = getSupabaseServerClient();

  const ctx = {
    name: 'about.page.load',
    communitySlug,
  };

  logger.info(ctx, 'Loading About page specific data...');

  const communityService = createCommunityService(client);
  const pagesService = createCommunityPagesService(client);
  const stripeProductsService = await getStripeProductsService(client);

  try {
    const { communityPublicData } =
      await communityService.getCommunityPublicData({
        communitySlug,
      });

    if (!communityPublicData) {
      logger.error(
        {
          ...ctx,
          error: 'Public community data not found',
        },
        'Public community data not found, cannot load about page content.',
      );
      return {
        communityPublicData: null,
        aboutPageData: null,
      };
    }

    if (!communityPublicData.id) {
      logger.error(
        { ...ctx, error: 'Community ID is null' },
        'Community ID is null, cannot load about page content.',
      );
      return {
        communityPublicData,
        aboutPageData: null,
      };
    }

    const aboutPageData = await pagesService.getSystemPage({
      communityId: communityPublicData.id,
      pageSlug: 'about',
    });

    const { product: defaultProduct, prices } =
      await stripeProductsService.getCommunityDefaultProduct(
        communityPublicData.id,
      );

    // Handle case where default product is not configured or found
    if (!defaultProduct) {
      logger.warn(
        {
          ...ctx,
          communityId: communityPublicData.id,
        },
        'Default product not found for community. About page will show no pricing info.',
      );
    }

    logger.info(ctx, 'Loaded About page specific data');

    return {
      communityPublicData,
      aboutPageData,
      defaultProduct,
      prices: prices,
    };
  } catch (error) {
    logger.error({ ...ctx, error }, 'Failed to load about page data');
    return {
      communityPublicData: null,
      aboutPageData: null,
      defaultProduct: null,
      prices: null,
    };
  }
}
