'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createCommunityPagesService } from '~/lib/communities/pages/community-pages.service';

// Helper to retrieve media for a page
export const getAboutPageMediaAction = enhanceAction(
  async function (data) {
    const logger = await getLogger();
    const ctx = {
      name: 'getAboutPageMediaAction',
      pageId: data.pageId,
    };

    logger.info(ctx, 'Getting page media...');

    const client = getSupabaseServerClient();
    const communityPagesService = createCommunityPagesService(client);

    try {
      // Call the service method - it returns an array of media objects
      const mediaItems = await communityPagesService.getPageMedia({
        pageId: data.pageId,
      });

      // Service returns empty array if no media found, which is a valid success case.
      // No need for a separate check here unless specific logic is required for empty results.

      logger.info(ctx, 'Page media retrieved successfully');

      return {
        success: true,
        data: mediaItems, // Return the array of media items
      };
    } catch (error) {
      // Catch errors thrown by the service
      logger.error({ ...ctx, error }, 'Failed to get page media');
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : 'Unknown error retrieving media',
      };
    }
  },
  {
    auth: false,
    schema: z.object({
      pageId: z.string().uuid(),
    }),
  },
);
