'use server';

import { revalidatePath } from 'next/cache';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createCommunityPagesService } from '~/lib/communities/pages/community-pages.service';
import { createImageStorageService } from '~/lib/images/services/image-storage.service';

// Schema for image upload
const UploadPageMediaSchema = z.object({
  file: z.instanceof(File),
  pageId: z.string().uuid(),
  communityId: z.string().uuid(),
  altText: z.string().optional(),
  caption: z.string().optional(),
  displayOrder: z.number().int().min(0).default(0),
});

/**
 * Uploads an image for a community page and adds it to the community_page_media table
 */
export const uploadPageImageAction = enhanceAction(
  async function (data, user) {
    const logger = await getLogger();
    const ctx = {
      name: 'uploadPageImageAction',
      userId: user.id,
      pageId: data.pageId,
      communityId: data.communityId,
    };

    logger.info(ctx, 'Uploading page image...');

    const client = getSupabaseServerClient();
    const imageService = createImageStorageService(client);
    const communityPagesService = createCommunityPagesService(client);

    try {
      // Storage path follows the format: community_storage/<communityId>/pages/<pageId>/<pageId>-<increment>.<ext>
      // Get current count of media for this page to determine the increment using the service
      const existingMedia = await communityPagesService.getPageMedia({
        pageId: data.pageId,
      });

      // No need for explicit error check here, service throws on error
      const increment = existingMedia.length;

      // Extract file extension
      const fileNameParts = data.file.name.split('.');
      const extension = fileNameParts.length > 1 ? fileNameParts.pop() : '';
      const targetFilename = `${data.pageId}-${increment}.${extension}`;

      // Upload the image using the updated context structure
      const uploadResult = await imageService.uploadImage(data.file, {
        communityId: data.communityId,
        featureDir: 'pages',
        storageDirPath: data.pageId,
        targetFilename: targetFilename,
        // subPath is no longer needed here as filename is handled by targetFilename
      });

      // Add entry to community_page_media table using the service
      await communityPagesService.addPageMedia({
        pageId: data.pageId,
        mediaType: 'image',
        url: uploadResult.url,
        altText: data.altText,
        caption: data.caption,
        displayOrder: data.displayOrder,
        // TODO: Populate dimensions correctly from the uploaded image metadata
        dimensions: {}, // Placeholder - requires image processing
        createdBy: user.id,
      });

      // Revalidate the page to show updated content
      revalidatePath(`/[community]/about`, 'page');

      return {
        success: true,
        data: uploadResult,
      };
    } catch (error) {
      logger.error({ ...ctx, error }, 'Image upload failed');
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },
  {
    auth: true,
    schema: UploadPageMediaSchema,
  },
);
