'use server';

import { revalidatePath } from 'next/cache';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createCommunityPagesService } from '~/lib/communities/pages/community-pages.service';
import { getVideoThumbnailAction } from '~/lib/images/actions/get-video-thumbnail.action';
import { createImageStorageService } from '~/lib/images/services/image-storage.service';
import type { UploadImageContext } from '~/lib/images/types';

const AddVideoUrlSchema = z.object({
  communityId: z.string().uuid(),
  url: z.string().url(),
  pageId: z.string().uuid(),
  mediaType: z.literal('video'),
  altText: z.string().optional(),
  caption: z.string().optional(),
  displayOrder: z.number().int().min(0).default(0),
});

/**
 * Adds a video URL to the community_page_media table
 */
export const addVideoUrlAction = enhanceAction(
  async function (data, user) {
    const logger = await getLogger();
    const ctx = {
      name: 'addVideoUrlAction',
      userId: user.id,
      pageId: data.pageId,
      url: data.url,
    };

    logger.info(ctx, 'Adding video URL...');

    const client = getSupabaseServerClient();
    const communityPagesService = createCommunityPagesService(client);

    const imageService = createImageStorageService(client);

    let internalThumbnailUrl: string | null = null;

    try {
      const thumbnailResult = await getVideoThumbnailAction({
        videoUrl: data.url,
      });

      // Proceed only if the action was successful and returned a URL
      if (thumbnailResult.success && thumbnailResult.data) {
        const externalThumbnailUrl = thumbnailResult.data;
        logger.info(
          { ...ctx, externalThumbnailUrl },
          'Successfully fetched external video thumbnail URL via action.',
        );

        // --- Download and Store Thumbnail ---
        try {
          const imageResponse = await fetch(externalThumbnailUrl);
          if (!imageResponse.ok) {
            throw new Error(
              `Failed to download thumbnail image. Status: ${imageResponse.status}`,
            );
          }

          const imageBlob = await imageResponse.blob();
          const contentType =
            imageResponse.headers.get('content-type') ||
            imageBlob.type ||
            'image/jpeg';
          const fileExtension = contentType.split('/')[1] || 'jpg';

          // Create a unique filename (e.g., using pageId and timestamp or a UUID)
          // For simplicity, using timestamp here. Consider a more robust unique ID.
          const uniqueFilename = `${data.pageId}-thumbnail-${Date.now()}.${fileExtension}`;

          const imageFile = new File([imageBlob], uniqueFilename, {
            type: contentType,
          });

          // Define storage context (assuming pageId belongs to a community)
          // We need communityId for the path structure used by the service
          // Let's assume we need to fetch the communityId based on pageId if not directly available
          // ** IMPORTANT: This assumes data.pageId is sufficient context, or you fetch communityId **
          // ** If communityId is needed, it must be added to the action's input schema or fetched **
          const uploadContext: UploadImageContext = {
            communityId: data.communityId,
            featureDir: 'pages',
            storageDirPath: data.pageId,
            targetFilename: uniqueFilename,
          };

          logger.info({ ...ctx }, 'Uploading thumbnail to internal storage...');
          const uploadResult = await imageService.uploadImage(
            imageFile,
            uploadContext,
          );
          internalThumbnailUrl = uploadResult.url;
          logger.info(
            { ...ctx, internalThumbnailUrl },
            'Successfully uploaded thumbnail to internal storage.',
          );
        } catch (thumbError) {
          logger.error(
            { ...ctx, error: thumbError },
            'Failed to download or store video thumbnail. Proceeding without thumbnail.',
          );
          // Ensure internalThumbnailUrl remains null if upload fails
          internalThumbnailUrl = null;
        }
        // --- End Download and Store ---
      } else {
        // Log if action failed or didn't return a URL
        if (!thumbnailResult.success) {
          logger.error(
            { ...ctx, error: thumbnailResult.error },
            'getVideoThumbnailAction failed.',
          );
        } else {
          logger.warn(
            { ...ctx },
            'Could not fetch external video thumbnail URL (action returned null).',
          );
        }
      }

      // Call the service method, passing the INTERNAL thumbnail URL (or null)
      await communityPagesService.addPageMedia({
        pageId: data.pageId,
        mediaType: data.mediaType,
        url: data.url,
        altText: data.altText,
        caption: data.caption,
        displayOrder: data.displayOrder,
        dimensions: {},
        createdBy: user.id,
        thumbnailUrl: internalThumbnailUrl, // Pass the internal URL or null
      });

      // Revalidate the page to show updated content
      revalidatePath(`/[community]/about`, 'page');

      return {
        success: true,
      };
    } catch (error) {
      logger.error({ ...ctx, error }, 'Video URL addition failed');
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },
  {
    auth: true,
    schema: AddVideoUrlSchema,
  },
);
