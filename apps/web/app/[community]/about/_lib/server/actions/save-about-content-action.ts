'use server';

import { revalidatePath } from 'next/cache';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

const SaveAboutContentSchema = z.object({
  communityId: z.string().uuid(),
  content: z.any(), // JSONContent from TipTap
  characterCount: z.number(),
  wordCount: z.number(),
});

export const saveAboutContentAction = enhanceAction(
  async function (data, user) {
    const logger = await getLogger();
    logger.info(
      {
        name: 'saveAboutContentAction',
      },
      'Saving about content',
    );
    const supabase = getSupabaseServerClient();

    // Check if user is authenticated before proceeding
    if (!user) {
      return {
        success: false,
        error: 'Authentication required to save content',
      };
    }

    // Update with new content
    const { error: updateError } = await supabase
      .from('community_pages')
      .update({
        content: data.content,
        character_count: data.characterCount,
        word_count: data.wordCount,
      })
      .eq('community_id', data.communityId)
      .eq('page_type', 'system')
      .eq('slug', 'about');

    if (updateError) {
      return {
        success: false,
        error: updateError.message,
      };
    }

    // Revalidate the page to show the updated content
    revalidatePath(`/${data.communityId}/about`, 'page');
    return {
      success: true,
    };
  },
  {
    auth: true,
    schema: SaveAboutContentSchema,
  },
);
