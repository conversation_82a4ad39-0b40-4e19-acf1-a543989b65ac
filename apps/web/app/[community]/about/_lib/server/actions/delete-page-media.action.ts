'use server';

import { revalidatePath } from 'next/cache';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createCommunityPagesService } from '~/lib/communities/pages/community-pages.service';
import { createImageStorageService } from '~/lib/images/services/image-storage.service';

/**
 * Deletes media from the community_page_media table and storage if applicable
 */
export const deletePageMediaAction = enhanceAction(
  async function (data, user) {
    const logger = await getLogger();
    const ctx = {
      name: 'deletePageMediaAction',
      userId: user.id,
      mediaId: data.mediaId,
    };

    logger.info(ctx, 'Deleting page media...');

    const client = getSupabaseServerClient();
    const imageService = createImageStorageService(client);
    const communityPagesService = createCommunityPagesService(
      client,
      imageService,
    );

    try {
      // Call the service method to handle deletion
      await communityPagesService.deletePageMedia({ mediaId: data.mediaId });

      // Revalidate the page to show updated content
      revalidatePath(`/[community]/about`, 'page');

      return {
        success: true,
      };
    } catch (error) {
      logger.error({ ...ctx, error }, 'Media deletion failed');
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },
  {
    auth: true,
    schema: z.object({
      mediaId: z.string().uuid(),
    }),
  },
);
