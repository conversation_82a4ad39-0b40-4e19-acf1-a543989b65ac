'use client';

import { useState, useTransition } from 'react';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';

import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@kit/ui/alert-dialog';
import { Button } from '@kit/ui/button';
import { StatusAlert } from '@kit/ui/dojo/molecules/status-alert';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { If } from '@kit/ui/if';
import { Input } from '@kit/ui/input';
import { Trans } from '@kit/ui/trans';

import { transferOwnershipAction } from '~/lib/communities/members/actions/community-members-server-actions';
import { TransferOwnershipConfirmationSchema } from '~/lib/communities/members/schema/transfer-ownership-confirmation.schema';

// TODO: Break this down into smaller components
export function TransferOwnershipDialog({
  isOpen,
  setIsOpen,
  targetDisplayName,
  communityId,
  userId,
}: {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  communityId: string;
  userId: string;
  targetDisplayName: string;
}) {
  return (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialogContent data-test="transfer-ownership-dialog">
        <AlertDialogHeader>
          <AlertDialogTitle>
            <Trans i18nKey="communities:transferOwnership" />
          </AlertDialogTitle>

          <AlertDialogDescription>
            <Trans i18nKey="communities:transferOwnershipDescription" />
          </AlertDialogDescription>
        </AlertDialogHeader>

        <TransferOrganizationOwnershipForm
          communityId={communityId}
          userId={userId}
          targetDisplayName={targetDisplayName}
          setIsOpen={setIsOpen}
        />
      </AlertDialogContent>
    </AlertDialog>
  );
}

function TransferOrganizationOwnershipForm({
  communityId,
  userId,
  targetDisplayName,
  setIsOpen,
}: {
  userId: string;
  communityId: string;
  targetDisplayName: string;
  setIsOpen: (isOpen: boolean) => void;
}) {
  const [pending, startTransition] = useTransition();
  const [error, setError] = useState<boolean>();

  const form = useForm({
    resolver: zodResolver(TransferOwnershipConfirmationSchema),
    defaultValues: {
      confirmation: '',
      communityId,
      userId,
    },
  });

  return (
    <Form {...form}>
      <form
        className={'flex flex-col space-y-4 text-sm'}
        onSubmit={form.handleSubmit((data) => {
          startTransition(async () => {
            try {
              await transferOwnershipAction(data);

              setIsOpen(false);
            } catch {
              setError(true);
            }
          });
        })}
      >
        <If condition={error}>
          <StatusAlert
            variant="destructive"
            titleKey="communities:transferCommunityErrorHeading"
            descriptionKey="communities:transferCommunityErrorMessage"
          />
        </If>

        <p>
          <Trans
            i18nKey={'communities:transferOwnershipDisclaimer'}
            values={{
              member: targetDisplayName,
            }}
            components={{ b: <b /> }}
          />
        </p>

        <FormField
          name={'confirmation'}
          render={({ field }) => {
            return (
              <FormItem>
                <FormLabel>
                  <Trans i18nKey={'communities:transferOwnershipInputLabel'} />
                </FormLabel>

                <FormControl>
                  <Input
                    autoComplete={'off'}
                    type={'text'}
                    required
                    {...field}
                    data-test="transfer-ownership-confirm-input"
                    aria-label="Type TRANSFER to confirm ownership transfer"
                  />
                </FormControl>

                <FormDescription>
                  <Trans
                    i18nKey={'communities:transferOwnershipInputDescription'}
                  />
                </FormDescription>

                <FormMessage />
              </FormItem>
            );
          }}
        />

        <div>
          <p className={'text-muted-foreground'}>
            <Trans i18nKey={'common:modalConfirmationQuestion'} />
          </p>
        </div>

        <AlertDialogFooter>
          <AlertDialogCancel
            data-test="cancel-transfer-ownership-button"
            aria-label="Cancel transferring ownership"
          >
            <Trans i18nKey={'common:cancel'} />
          </AlertDialogCancel>

          <Button
            type={'submit'}
            data-test={'confirm-transfer-ownership-button'}
            variant={'destructive'}
            disabled={pending}
            aria-label="Confirm transfer ownership"
          >
            <If
              condition={pending}
              fallback={<Trans i18nKey={'communities:transferOwnership'} />}
            >
              <Trans i18nKey={'communities:transferringOwnership'} />
            </If>
          </Button>
        </AlertDialogFooter>
      </form>
    </Form>
  );
}

TransferOrganizationOwnershipForm.displayName =
  'TransferOrganizationOwnershipForm';
TransferOwnershipDialog.displayName = 'TransferOwnershipDialog';
