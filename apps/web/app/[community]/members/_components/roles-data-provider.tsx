import { useQuery } from '@tanstack/react-query';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { LoadingOverlay } from '@kit/ui/loading-overlay';

export function RolesDataProvider(props: {
  maxRoleHierarchy: number;
  isPrimaryOwner: boolean;
  children: (roles: string[]) => React.ReactNode;
}) {
  const rolesQuery = useFetchRoles(props);

  if (rolesQuery.isLoading) {
    return <LoadingOverlay fullPage={false} />;
  }

  if (rolesQuery.isError) {
    return null;
  }

  return <>{props.children(rolesQuery.data ?? [])}</>;
}

// TODO: Move this to a server action
function useFetchRoles(props: {
  maxRoleHierarchy: number;
  isPrimaryOwner: boolean;
}) {
  const supabase = useSupabase();

  return useQuery({
    queryKey: ['roles', props.maxRoleHierarchy],
    queryFn: async () => {
      const { error, data } = await supabase
        .from('community_roles')

        .select('name')
        .gt('hierarchy_level', props.maxRoleHierarchy)
        .order('hierarchy_level', { ascending: true });

      if (error) {
        throw new Error('Failed to fetch roles');
      }

      // If primary owner, add `owner` role from the list
      if (props.isPrimaryOwner) {
        data.push({ name: 'owner' });
      }

      return data.map((item) => item.name);
    },
  });
}

RolesDataProvider.displayName = 'RolesDataProvider';
useFetchRoles.displayName = 'useFetchRoles';
