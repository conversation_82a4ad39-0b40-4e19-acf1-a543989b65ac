import { useState, useTransition } from 'react';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { Button } from '@kit/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@kit/ui/dialog';
import { StatusAlert } from '@kit/ui/dojo/molecules/status-alert';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { If } from '@kit/ui/if';
import { Trans } from '@kit/ui/trans';

import { updateMemberRoleAction } from '~/lib/communities/members/actions/community-members-server-actions';
import { RoleSchema } from '~/lib/communities/members/schema/update-member-role.schema';

import { MembershipRoleSelector } from './membership-role-selector';
import { RolesDataProvider } from './roles-data-provider';

type Role = string;

// TODO: Break this down into smaller components
export function UpdateMemberRoleDialog({
  isOpen,
  setIsOpen,
  userId,
  communityId,
  userRole,
  userRoleHierarchy,
  isPrimaryOwner,
}: {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  userId: string;
  communityId: string;
  userRole: Role;
  userRoleHierarchy: number;
  isPrimaryOwner: boolean;
}) {
  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent data-test="update-member-role-dialog">
        <DialogHeader>
          <DialogTitle>
            <Trans i18nKey={'communities:updateMemberRoleModalHeading'} />
          </DialogTitle>

          <DialogDescription>
            <Trans i18nKey={'communities:updateMemberRoleModalDescription'} />
          </DialogDescription>
        </DialogHeader>

        <RolesDataProvider
          maxRoleHierarchy={userRoleHierarchy}
          isPrimaryOwner={isPrimaryOwner}
        >
          {(data) => (
            <UpdateMemberForm
              setIsOpen={setIsOpen}
              userId={userId}
              communityId={communityId}
              userRole={userRole}
              roles={data}
            />
          )}
        </RolesDataProvider>
      </DialogContent>
    </Dialog>
  );
}

function UpdateMemberForm({
  userId,
  userRole,
  communityId,
  setIsOpen,
  roles,
}: React.PropsWithChildren<{
  userId: string;
  userRole: Role;
  communityId: string;
  setIsOpen: (isOpen: boolean) => void;
  roles: Role[];
}>) {
  const [pending, startTransition] = useTransition();
  const [error, setError] = useState<boolean>();
  const { t } = useTranslation('communities');

  const onSubmit = ({ role }: { role: Role }) => {
    startTransition(async () => {
      try {
        await updateMemberRoleAction({
          communityId,
          userId,
          role,
        });

        setIsOpen(false);
      } catch {
        setError(true);
      }
    });
  };

  const form = useForm({
    resolver: zodResolver(
      RoleSchema.refine(
        (data) => {
          return data.role !== userRole;
        },
        {
          message: t(`roleMustBeDifferent`),
          path: ['role'],
        },
      ),
    ),
    reValidateMode: 'onChange',
    mode: 'onChange',
    defaultValues: {
      role: userRole,
    },
  });

  return (
    <Form {...form}>
      <form
        data-test={'update-member-role-form'}
        onSubmit={form.handleSubmit(onSubmit)}
        className={'flex flex-col space-y-6'}
      >
        <If condition={error}>
          <StatusAlert
            variant="destructive"
            titleKey="communities:updateRoleErrorHeading"
            descriptionKey="communities:updateRoleErrorMessage"
          />
        </If>

        <FormField
          name={'role'}
          render={({ field }) => {
            return (
              <FormItem>
                <FormLabel>{t('roleLabel')}</FormLabel>

                <FormControl>
                  <MembershipRoleSelector
                    roles={roles}
                    currentUserRole={userRole}
                    value={field.value}
                    onChange={(newRole) => form.setValue('role', newRole)}
                  />
                </FormControl>

                <FormDescription>{t('updateRoleDescription')}</FormDescription>

                <FormMessage />
              </FormItem>
            );
          }}
        />

        <Button
          data-test={'confirm-update-member-role'}
          disabled={pending}
          aria-label="Update member role"
        >
          <Trans i18nKey={'communities:updateRoleSubmitLabel'} />
        </Button>
      </form>
    </Form>
  );
}

UpdateMemberForm.displayName = 'UpdateMemberForm';
UpdateMemberRoleDialog.displayName = 'UpdateMemberRoleDialog';
