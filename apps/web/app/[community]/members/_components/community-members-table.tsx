'use client';

import { useMemo, useState } from 'react';

import { ColumnDef } from '@tanstack/react-table';
import { Ellipsis } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { DataTable } from '@kit/ui/data-table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@kit/ui/dropdown-menu';
import { If } from '@kit/ui/if';
import { Input } from '@kit/ui/input';
import { Trans } from '@kit/ui/trans';

import { AvatarHoverWrapper } from '~/community/_components/avatar-hover-wrapper';
import { MembersOnly } from '~/lib/communities/members/types';

import { RemoveMemberDialog } from './remove-member-dialog';
import { RoleBadge } from './role-badge';
import { TransferOwnershipDialog } from './transfer-ownership-dialog';
import { UpdateMemberRoleDialog } from './update-member-role-dialog';

type Permissions = {
  canUpdateRole: (roleHierarchy: number) => boolean;
  canRemoveFromAccount: (roleHierarchy: number) => boolean;
  canSendMemberEmail: boolean;
  canTransferOwnership: boolean;
  canManageInvitations: boolean;
  canBanMembers: boolean;
};

type Members = MembersOnly;

type CommunityMembersTableProps = {
  members: Members;
  currentUserId?: string;
  currentCommunityId: string;
  userRoleHierarchy: number;
  permissions: Permissions;
  publicView: boolean;
};

export function CommunityMembersTable({
  members,
  currentUserId,
  currentCommunityId,
  userRoleHierarchy,
  permissions,
  publicView,
}: CommunityMembersTableProps) {
  const [search, setSearch] = useState('');
  const { t } = useTranslation('communities');

  const columns = useGetColumns(permissions, publicView, {
    currentUserId: currentUserId ?? '',
    currentCommunityId,
    currentRoleHierarchy: userRoleHierarchy,
  });

  const filteredMembers = members
    .filter((member) => {
      const searchString = search.toLowerCase();

      const displayName =
        `${member.firstName} ${member.lastName}`.toLowerCase();

      return (
        displayName.includes(searchString) ||
        member.role.toLowerCase().includes(searchString)
      );
    })
    .sort((prev, next) => {
      if (prev.primaryOwnerUserId === prev.userId) {
        return -1;
      }

      if (prev.roleHierarchyLevel < next.roleHierarchyLevel) {
        return -1;
      }

      return 1;
    });

  return (
    <div className={'flex flex-col space-y-2'}>
      <Input
        value={search}
        onInput={(e) => setSearch((e.target as HTMLInputElement).value)}
        placeholder={t(`searchMembersPlaceholder`)}
        data-test="members-search-input"
        aria-label="Search members"
      />

      <DataTable columns={columns} data={filteredMembers} />
    </div>
  );
}

function useGetColumns(
  permissions: Permissions,
  publicView: boolean,
  params: {
    currentUserId: string;
    currentCommunityId: string;
    currentRoleHierarchy: number;
  },
): ColumnDef<Members[0]>[] {
  const { t } = useTranslation('communities');

  return useMemo(
    () =>
      [
        {
          header: t('memberName'),
          size: 200,
          cell: ({ row }: { row: { original: Members[0] } }) => {
            const member = row.original;
            const isSelf = member.userId === params.currentUserId;

            return (
              <span className={'flex items-center space-x-4 text-left'}>
                <span>
                  <AvatarHoverWrapper
                    userId={member.userId || ''}
                    pictureUrl={member.pictureUrl || ''}
                    firstName={member.firstName || ''}
                    lastName={member.lastName || ''}
                  />
                </span>

                <If condition={isSelf}>
                  <Badge variant={'outline'} data-test="you-badge">
                    {t('youLabel')}
                  </Badge>
                </If>
              </span>
            );
          },
        },
        {
          header: t('roleLabel'),
          cell: ({ row }: { row: { original: Members[0] } }) => {
            const { role, primaryOwnerUserId, userId } = row.original;
            const isPrimaryOwner = primaryOwnerUserId === userId;

            let memberRole = role;

            if (memberRole === 'free_member' || memberRole === 'paid_member') {
              memberRole = 'member';
            }

            return (
              <span className={'flex items-center space-x-1'}>
                <RoleBadge role={memberRole} />

                <If condition={isPrimaryOwner}>
                  <span
                    className={
                      'rounded-md bg-yellow-400 px-2.5 py-1 text-xs font-medium dark:text-black'
                    }
                  >
                    {t('primaryOwnerLabel')}
                  </span>
                </If>
              </span>
            );
          },
        },
        {
          header: t('joinedAtLabel'),
          cell: ({ row }: { row: { original: Members[0] } }) => {
            return new Date(row.original.createdAt).toLocaleDateString();
          },
        },
        permissions != null &&
          !publicView && {
            id: 'actions',

            cell: ({ row }: { row: { original: Members[0] } }) => (
              <ActionsDropdown
                permissions={permissions}
                member={row.original}
                currentUserId={params.currentUserId}
                currentCommunityId={params.currentCommunityId}
                currentRoleHierarchy={params.currentRoleHierarchy}
              />
            ),
          },
      ].filter(Boolean) as ColumnDef<Members[0]>[],
    [t, permissions, params, publicView],
  );
}

function ActionsDropdown({
  permissions,
  member,
  currentUserId,
  currentCommunityId,
  currentRoleHierarchy,
}: {
  permissions: Permissions;
  member: Members[0];
  currentUserId: string;
  currentCommunityId: string;
  currentRoleHierarchy: number;
}) {
  const [isRemoving, setIsRemoving] = useState(false);
  const [isTransferring, setIsTransferring] = useState(false);
  const [isUpdatingRole, setIsUpdatingRole] = useState(false);
  const isCurrentUser = member.userId === currentUserId;
  const isPrimaryOwner = member.primaryOwnerUserId === member.userId;

  if (isCurrentUser || isPrimaryOwner) {
    return null;
  }

  const memberRoleHierarchy = member.roleHierarchyLevel;
  const canUpdateRole = permissions.canUpdateRole(memberRoleHierarchy);

  const canTransferOwnership = permissions.canTransferOwnership;

  const canSendMemberEmail = permissions.canSendMemberEmail;

  const canRemoveFromAccount =
    permissions.canRemoveFromAccount(memberRoleHierarchy);

  // if has no permission to update role, transfer ownership,
  // remove from account or send email
  // do not render the dropdown menu

  if (
    !canUpdateRole &&
    !canTransferOwnership &&
    !canSendMemberEmail &&
    !canRemoveFromAccount
  ) {
    return null;
  }

  const displayName = `${member.firstName} ${member.lastName}`;

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant={'ghost'}
            size={'icon'}
            data-test={`member-actions-trigger-${member.userId}`}
            aria-label={`Actions for member ${member.firstName} ${member.lastName}`}
          >
            <Ellipsis className={'h-5 w-5'} />
          </Button>
        </DropdownMenuTrigger>

        <DropdownMenuContent>
          <If condition={canUpdateRole}>
            <DropdownMenuItem
              onClick={() => setIsUpdatingRole(true)}
              data-test={`member-update-role-${member.userId}`}
              aria-label="Update role"
            >
              <Trans i18nKey={'communities:updateRole'} />
            </DropdownMenuItem>
          </If>

          <If
            condition={
              canTransferOwnership && member.role === 'owner' && !isPrimaryOwner
            }
          >
            <DropdownMenuItem
              onClick={() => setIsTransferring(true)}
              data-test={`member-transfer-ownership-${member.userId}`}
              aria-label="Transfer ownership"
            >
              <Trans i18nKey={'communities:transferOwnership'} />
            </DropdownMenuItem>
          </If>

          <If condition={canRemoveFromAccount}>
            <DropdownMenuItem
              onClick={() => setIsRemoving(true)}
              data-test={`member-remove-${member.userId}`}
              aria-label="Remove member"
            >
              <Trans i18nKey={'communities:removeMember'} />
            </DropdownMenuItem>
          </If>
          <If condition={canSendMemberEmail}>
            <DropdownMenuItem
              onClick={() => {
                // TODO: Create a server function to send an email to the member
                // window.location.href = `mailto:${member.email}`;
              }}
              data-test={`member-send-email-${member.userId}`}
              aria-label="Send email to member"
            >
              <Trans i18nKey={'communities:sendMemberEmail'} />
            </DropdownMenuItem>
          </If>
        </DropdownMenuContent>
      </DropdownMenu>

      <If condition={isRemoving}>
        <RemoveMemberDialog
          isOpen
          setIsOpen={setIsRemoving}
          communityId={currentCommunityId}
          userId={member.userId}
        />
      </If>

      <If condition={isUpdatingRole}>
        <UpdateMemberRoleDialog
          isOpen
          setIsOpen={setIsUpdatingRole}
          userId={member.userId}
          userRole={member.role}
          communityId={currentCommunityId}
          userRoleHierarchy={currentRoleHierarchy}
          isPrimaryOwner={isPrimaryOwner}
        />
      </If>

      <If condition={isTransferring}>
        <TransferOwnershipDialog
          isOpen
          setIsOpen={setIsTransferring}
          targetDisplayName={displayName}
          communityId={member.communityId}
          userId={member.userId}
        />
      </If>
    </>
  );
}

ActionsDropdown.displayName = 'ActionsDropdown';
