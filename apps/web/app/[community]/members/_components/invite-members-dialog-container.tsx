'use client';

import { useState, useTransition } from 'react';

import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@kit/ui/dialog';
import { Trans } from '@kit/ui/trans';

import { createInvitationsAction } from '~/lib/communities/invitations/actions/community-invitations-server-actions';

import { InviteMembersForm } from './invite-members-form';
import { RolesDataProvider } from './roles-data-provider';

/**
 * The maximum number of invites that can be sent at once.
 * Useful to avoid spamming the server with too large payloads
 */
// const MAX_INVITES = 5; // Removed as it's unused here

export function InviteMembersDialogContainer({
  communityId,
  userRoleHierarchy,
  isPrimaryOwner,
  children,
}: React.PropsWithChildren<{
  communityId: string;
  userRoleHierarchy: number;
  isPrimaryOwner: boolean;
}>) {
  const [pending, startTransition] = useTransition();
  const [isOpen, setIsOpen] = useState(false);
  const { t } = useTranslation('communities'); // Keep t for toast messages

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen} modal>
      <DialogTrigger asChild>{children}</DialogTrigger>

      <DialogContent
        onInteractOutside={(e) => e.preventDefault()}
        data-test="invite-members-dialog"
      >
        <DialogHeader>
          <DialogTitle>
            {/* Reverted to using Trans component */}
            <Trans i18nKey={'communities:inviteMembersHeading'} />
          </DialogTitle>

          <DialogDescription>
            {/* Reverted to using Trans component */}
            <Trans i18nKey={'communities:inviteMembersDescription'} />
          </DialogDescription>
        </DialogHeader>

        <RolesDataProvider
          maxRoleHierarchy={userRoleHierarchy}
          isPrimaryOwner={isPrimaryOwner}
        >
          {(roles) => (
            <InviteMembersForm
              pending={pending}
              roles={roles}
              onSubmit={(data) => {
                startTransition(() => {
                  const promise = createInvitationsAction({
                    communityId: communityId,
                    invitations: data.invitations,
                  });

                  toast.promise(() => promise, {
                    loading: t('invitingMembers'),
                    success: t('inviteMembersSuccessMessage'),
                    error: t('inviteMembersErrorMessage'),
                  });

                  setIsOpen(false);
                });
              }}
            />
          )}
        </RolesDataProvider>
      </DialogContent>
    </Dialog>
  );
}

InviteMembersDialogContainer.displayName = 'InviteMembersDialogContainer';
// Removed InviteMembersForm.displayName assignment
