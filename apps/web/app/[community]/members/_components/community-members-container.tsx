'use client';

import { PlusCircle } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { If } from '@kit/ui/if';
import { Tabs, TabsContent, TabsList } from '@kit/ui/tabs';
import { TabsTriggersList } from '@kit/ui/tabs-triggers-list';
import { Trans } from '@kit/ui/trans';

import { CommunityWorkspaceDb } from '~/lib/communities/community/types';
import { Invitations } from '~/lib/communities/invitations/types';
import { MembersOnly, TeamMembers } from '~/lib/communities/members/types';
import { hasCommunityPermission } from '~/lib/communities/permissions.utils';

import { CommunityMembersTable } from './community-members-table';
import { AccountInvitationsTable } from './invitations/account-invitations-table';
import { InviteMembersDialogContainer } from './invite-members-dialog-container';

type CommunityMembersContainerProps = {
  community: CommunityWorkspaceDb;
  userId?: string;
  teamMembers: TeamMembers;
  members: MembersOnly;
  invitations: Invitations;
  publicView: boolean;
};

type Permissions = {
  canUpdateRole: (roleHierarchy: number) => boolean;
  canRemoveFromAccount: (roleHierarchy: number) => boolean;
  canSendMemberEmail: boolean;
  canTransferOwnership: boolean;
  canManageInvitations: boolean;
  canBanMembers: boolean;
};

export default function CommunityMembersContainer({
  community,
  userId,
  teamMembers,
  members,
  invitations,
  publicView,
}: CommunityMembersContainerProps) {
  const { t } = useTranslation('communities');

  const isPrimaryOwner = community.primaryOwnerUserId === userId;
  const currentUserRoleHierarchy = community.roleHierarchyLevel || 999;

  const canManageRoles =
    hasCommunityPermission(community.permissions, 'roles.manage') || false;

  let hasAddMemberPermissions = false;

  if (community && 'permissions' in community) {
    hasAddMemberPermissions = hasCommunityPermission(
      community.permissions,
      'invites.manage',
    );
  }

  const permissions = {
    canUpdateRole: (targetRole: number) => {
      return (
        isPrimaryOwner ||
        (canManageRoles && currentUserRoleHierarchy < targetRole)
      );
    },

    canRemoveFromAccount: (targetRole: number) => {
      return (
        isPrimaryOwner ||
        (canManageRoles &&
          hasCommunityPermission(
            community.permissions,
            'community.members.remove',
          ) &&
          currentUserRoleHierarchy < targetRole) ||
        false
      );
    },
    canSendMemberEmail:
      hasCommunityPermission(
        community.permissions,
        'community.members.send_email',
      ) || false,
    canTransferOwnership: isPrimaryOwner,
    canManageInvitations:
      hasCommunityPermission(community.permissions, 'invites.manage') || false,
    canBanMembers:
      hasCommunityPermission(
        community.permissions,
        'community.members.ban_member',
      ) || false,
  } as Permissions;

  const tabListValues = [
    'communityMembers',
    'communityTeamMembers',
    'communityPendingInvites',
  ];
  const tabListLabels = [
    t('communities:members.communityMembersTabLabel'),
    t('communities:members.communityTeamMembersTabLabel'),
    t('communities:members.communityPendingInvitesTabLabel'),
  ];

  // Filter out pendingInvites tab if user can't manage invitations
  const filteredTabListValues = permissions.canManageInvitations
    ? tabListValues
    : tabListValues.filter((value) => value !== 'communityPendingInvites');

  const filteredTabListLabels = permissions.canManageInvitations
    ? tabListLabels
    : tabListLabels.slice(0, 3); // Only keep the first two labels

  return (
    <Tabs defaultValue="communityMembers" className="flex h-full flex-col">
      <div className="flex">
        <TabsList className="inline-flex">
          <TabsTriggersList
            values={filteredTabListValues}
            labels={filteredTabListLabels}
            className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground mr-4 h-10 justify-start rounded pr-2 pl-2 text-left hover:bg-slate-200"
          />
        </TabsList>
      </div>

      <TabsContent value="communityMembers">
        <Card>
          <CardHeader className="flex flex-row justify-between">
            <div className="flex flex-col space-y-1.5">
              <CardTitle>
                <Trans i18nKey={'common:accountMembers'} />
              </CardTitle>

              <CardDescription>
                <Trans i18nKey={'common:membersTabDescription'} />
              </CardDescription>
            </div>
          </CardHeader>

          <CardContent>
            <CommunityMembersTable
              userRoleHierarchy={currentUserRoleHierarchy}
              currentUserId={userId}
              currentCommunityId={community.id}
              members={members}
              permissions={permissions}
              publicView={publicView}
            />
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="communityTeamMembers">
        <Card>
          <CardHeader className="flex flex-row justify-between">
            <div className="flex flex-col space-y-1.5">
              <CardTitle>
                <Trans i18nKey={'common:accountTeamMembers'} />
              </CardTitle>

              <CardDescription>
                <Trans i18nKey={'common:teamMembersTabDescription'} />
              </CardDescription>
            </div>

            <If
              condition={
                permissions.canManageInvitations && hasAddMemberPermissions
              }
            >
              <InviteMembersDialogContainer
                userRoleHierarchy={currentUserRoleHierarchy}
                communityId={community.id}
                isPrimaryOwner={isPrimaryOwner}
              >
                <Button
                  size={'sm'}
                  data-test={'invite-members-form-trigger'}
                  aria-label="Invite new members"
                >
                  <PlusCircle className="mr-2 w-4" />

                  <span>
                    <Trans i18nKey={'communities:inviteMembersButton'} />
                  </span>
                </Button>
              </InviteMembersDialogContainer>
            </If>
          </CardHeader>

          <CardContent>
            <CommunityMembersTable
              permissions={permissions}
              userRoleHierarchy={currentUserRoleHierarchy}
              currentUserId={userId}
              currentCommunityId={community.id}
              members={teamMembers}
              publicView={publicView}
            />
          </CardContent>
        </Card>
      </TabsContent>

      {permissions.canManageInvitations && !publicView && (
        <TabsContent value="communityPendingInvites">
          <Card>
            <CardHeader className="flex flex-row justify-between">
              <div className="flex flex-col space-y-1.5">
                <CardTitle>
                  <Trans i18nKey={'communities:pendingInvitesHeading'} />
                </CardTitle>

                <CardDescription>
                  <Trans i18nKey={'communities:pendingInvitesDescription'} />
                </CardDescription>
              </div>
            </CardHeader>

            <CardContent>
              <AccountInvitationsTable
                permissions={{
                  canUpdateInvitation: canManageRoles,
                  canRemoveInvitation: canManageRoles,
                  currentUserRoleHierarchy,
                }}
                invitations={invitations}
                isPrimaryOwner={isPrimaryOwner}
              />
            </CardContent>
          </Card>
        </TabsContent>
      )}
    </Tabs>
  );
}

CommunityMembersContainer.displayName = 'CommunityMembersContainer';
