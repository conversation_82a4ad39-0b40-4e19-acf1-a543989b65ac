'use client';

import { useMemo, useState } from 'react';

import { ColumnDef } from '@tanstack/react-table';
import { Ellipsis } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { DataTable } from '@kit/ui/data-table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@kit/ui/dropdown-menu';
import { If } from '@kit/ui/if';
import { Input } from '@kit/ui/input';
import { ProfileAvatar } from '@kit/ui/profile-avatar';
import { Trans } from '@kit/ui/trans';

import { Invitations } from '~/lib/communities/invitations/types';

import { RoleBadge } from '../role-badge';
import { DeleteInvitationDialog } from './delete-invitation-dialog';
import { RenewInvitationDialog } from './renew-invitation-dialog';
import { UpdateInvitationDialog } from './update-invitation-dialog';

type AccountInvitationsTableProps = {
  invitations: Invitations;
  permissions: {
    canUpdateInvitation: boolean;
    canRemoveInvitation: boolean;
    currentUserRoleHierarchy: number;
  };
  isPrimaryOwner: boolean;
};

export function AccountInvitationsTable({
  invitations,
  permissions,
  isPrimaryOwner,
}: AccountInvitationsTableProps) {
  const { t } = useTranslation('communities');
  const [search, setSearch] = useState('');
  const columns = useGetColumns(permissions, isPrimaryOwner);

  const filteredInvitations = invitations.filter((member) => {
    const searchString = search.toLowerCase();

    const email = (
      member.email.split('@')[0]?.toLowerCase() ?? ''
    ).toLowerCase();

    return (
      email.includes(searchString) ||
      member.role.toLowerCase().includes(searchString)
    );
  });

  return (
    <div className={'flex flex-col space-y-2'}>
      <Input
        value={search}
        onInput={(e) => setSearch((e.target as HTMLInputElement).value)}
        placeholder={t(`searchInvitations`)}
        data-test="invitations-search-input"
        aria-label={t('ariaLabels.searchInvitationsInput')}
      />

      <DataTable
        data-cy={'invitations-table'}
        columns={columns}
        data={filteredInvitations}
      />
    </div>
  );
}

function useGetColumns(
  permissions: {
    canUpdateInvitation: boolean;
    canRemoveInvitation: boolean;
    currentUserRoleHierarchy: number;
  },
  isPrimaryOwner: boolean,
): ColumnDef<Invitations[0]>[] {
  const { t } = useTranslation('communities');

  return useMemo(
    () => [
      {
        header: t('emailLabel'),
        size: 200,
        cell: ({ row }) => {
          const member = row.original;
          const email = member.email;

          return (
            <span
              data-test={'invitation-email'}
              className={'flex items-center space-x-4 text-left'}
            >
              <span>
                <ProfileAvatar text={email} />
              </span>

              <span>{email}</span>
            </span>
          );
        },
      },
      {
        header: t('roleLabel'),
        cell: ({ row }) => {
          const { role } = row.original;

          return <RoleBadge role={role} />;
        },
      },
      {
        header: t('invitedAtLabel'),
        cell: ({ row }) => {
          return new Date(row.original.createdAt).toLocaleDateString();
        },
      },
      {
        header: t('expiresAtLabel'),
        cell: ({ row }) => {
          return new Date(row.original.expiresAt).toLocaleDateString();
        },
      },
      {
        header: t('inviteStatus'),
        cell: ({ row }) => {
          const isExpired = getIsInviteExpired(row.original.expiresAt);

          if (isExpired) {
            return (
              <Badge
                variant={'warning'}
                data-test={`invitation-status-${row.original.id}`}
                aria-label={t('ariaLabels.invitationExpired')}
              >
                {t('expired')}
              </Badge>
            );
          }

          return (
            <Badge
              variant={'success'}
              data-test={`invitation-status-${row.original.id}`}
              aria-label={t('ariaLabels.invitationActive')}
            >
              {t('active')}
            </Badge>
          );
        },
      },
      {
        id: 'actions',
        cell: ({ row }) => (
          <ActionsDropdown
            permissions={permissions}
            invitation={row.original}
            isPrimaryOwner={isPrimaryOwner}
          />
        ),
      },
    ],
    [permissions, isPrimaryOwner, t],
  );
}

function ActionsDropdown({
  permissions,
  invitation,
  isPrimaryOwner,
}: {
  permissions: AccountInvitationsTableProps['permissions'];
  invitation: Invitations[0];
  isPrimaryOwner: boolean;
}) {
  const { t } = useTranslation('communities');
  const [isDeletingInvite, setIsDeletingInvite] = useState(false);
  const [isUpdatingRole, setIsUpdatingRole] = useState(false);
  const [isRenewingInvite, setIsRenewingInvite] = useState(false);

  if (!permissions.canUpdateInvitation && !permissions.canRemoveInvitation) {
    return null;
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant={'ghost'}
            size={'icon'}
            data-test={`invitation-actions-trigger-${invitation.id}`}
            aria-label={t('ariaLabels.actionsForInvitation', {
              email: invitation.email,
            })}
          >
            <Ellipsis className={'h-5 w-5'} />
          </Button>
        </DropdownMenuTrigger>

        <DropdownMenuContent>
          <If condition={permissions.canUpdateInvitation}>
            <DropdownMenuItem
              data-test={'update-invitation-trigger'}
              onClick={() => setIsUpdatingRole(true)}
              aria-label={t('ariaLabels.updateInvitationAction')}
            >
              <Trans i18nKey={'communities:updateInvitation'} />
            </DropdownMenuItem>

            <If condition={getIsInviteExpired(invitation.expiresAt)}>
              <DropdownMenuItem
                data-test={'renew-invitation-trigger'}
                onClick={() => setIsRenewingInvite(true)}
                aria-label={t('ariaLabels.renewInvitationAction')}
              >
                <Trans i18nKey={'communities:renewInvitation'} />
              </DropdownMenuItem>
            </If>
          </If>

          <If condition={permissions.canRemoveInvitation}>
            <DropdownMenuItem
              data-test={'remove-invitation-trigger'}
              onClick={() => setIsDeletingInvite(true)}
              aria-label={t('ariaLabels.removeInvitationAction')}
            >
              <Trans i18nKey={'communities:removeInvitation'} />
            </DropdownMenuItem>
          </If>
        </DropdownMenuContent>
      </DropdownMenu>

      <If condition={isDeletingInvite}>
        <DeleteInvitationDialog
          isOpen
          setIsOpen={setIsDeletingInvite}
          invitationId={invitation.id}
        />
      </If>

      <If condition={isUpdatingRole}>
        <UpdateInvitationDialog
          isOpen
          setIsOpen={setIsUpdatingRole}
          invitationId={invitation.id}
          userRole={invitation.role}
          userRoleHierarchy={permissions.currentUserRoleHierarchy}
          isPrimaryOwner={isPrimaryOwner}
        />
      </If>

      <If condition={isRenewingInvite}>
        <RenewInvitationDialog
          isOpen
          setIsOpen={setIsRenewingInvite}
          invitationId={invitation.id}
          email={invitation.email}
        />
      </If>
    </>
  );
}

function getIsInviteExpired(isoExpiresAt: string) {
  const currentIsoTime = new Date().toISOString();

  const isoExpiresAtDate = new Date(isoExpiresAt);
  const currentIsoTimeDate = new Date(currentIsoTime);

  return isoExpiresAtDate < currentIsoTimeDate;
}

AccountInvitationsTable.displayName = 'AccountInvitationsTable';
