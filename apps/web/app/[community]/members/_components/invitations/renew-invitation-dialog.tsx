import { useState, useTransition } from 'react';

import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@kit/ui/alert-dialog';
import { Button } from '@kit/ui/button';
import { StatusAlert } from '@kit/ui/dojo/molecules/status-alert';
import { If } from '@kit/ui/if';
import { Trans } from '@kit/ui/trans';

import { renewInvitationAction } from '~/lib/communities/invitations/actions/community-invitations-server-actions';

// TODO: Break this down into smaller components
export function RenewInvitationDialog({
  isOpen,
  setIsOpen,
  invitationId,
  email,
}: {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  invitationId: string;
  email: string;
}) {
  return (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialogContent data-test="renew-invitation-dialog">
        <AlertDialogHeader>
          <AlertDialogTitle>
            <Trans i18nKey="communities:renewInvitation" />
          </AlertDialogTitle>

          <AlertDialogDescription>
            <Trans
              i18nKey="communities:renewInvitationDialogDescription"
              values={{ email }}
            />
          </AlertDialogDescription>
        </AlertDialogHeader>

        <RenewInvitationForm
          setIsOpen={setIsOpen}
          invitationId={invitationId}
        />
      </AlertDialogContent>
    </AlertDialog>
  );
}

function RenewInvitationForm({
  invitationId,
  setIsOpen,
}: {
  invitationId: string;
  setIsOpen: (isOpen: boolean) => void;
}) {
  const [isSubmitting, startTransition] = useTransition();
  const [error, setError] = useState<boolean>();

  const inInvitationRenewed = () => {
    startTransition(async () => {
      try {
        await renewInvitationAction({ invitationId });

        setIsOpen(false);
      } catch {
        setError(true);
      }
    });
  };

  return (
    <form action={inInvitationRenewed}>
      <div className={'flex flex-col space-y-6'}>
        <p className={'text-muted-foreground text-sm'}>
          <Trans i18nKey={'common:modalConfirmationQuestion'} />
        </p>

        <If condition={error}>
          <StatusAlert
            variant="destructive"
            titleKey="communities:renewInvitationErrorTitle"
            descriptionKey="communities:renewInvitationErrorDescription"
          />
        </If>

        <AlertDialogFooter>
          <AlertDialogCancel
            data-test="cancel-renew-invitation-button"
            aria-label="Cancel renewing invitation"
          >
            <Trans i18nKey={'common:cancel'} />
          </AlertDialogCancel>

          <Button
            data-test={'confirm-renew-invitation-button'}
            disabled={isSubmitting}
            aria-label="Confirm renew invitation"
          >
            <Trans i18nKey={'communities:renewInvitation'} />
          </Button>
        </AlertDialogFooter>
      </div>
    </form>
  );
}

RenewInvitationDialog.displayName = 'RenewInvitationDialog';
RenewInvitationForm.displayName = 'RenewInvitationForm';
