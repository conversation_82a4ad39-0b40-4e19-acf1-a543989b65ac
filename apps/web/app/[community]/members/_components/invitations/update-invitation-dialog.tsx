import { useState, useTransition } from 'react';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { Button } from '@kit/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@kit/ui/dialog';
import { StatusAlert } from '@kit/ui/dojo/molecules/status-alert';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { If } from '@kit/ui/if';
import { Trans } from '@kit/ui/trans';

import { updateInvitationAction } from '~/lib/communities/invitations/actions/community-invitations-server-actions';
import { RoleSchema } from '~/lib/communities/members/schema/update-member-role.schema';

import { MembershipRoleSelector } from '../membership-role-selector';
import { RolesDataProvider } from '../roles-data-provider';

type Role = string;

// TODO: Break this down into smaller components
export function UpdateInvitationDialog({
  isOpen,
  setIsOpen,
  invitationId,
  userRole,
  userRoleHierarchy,
  isPrimaryOwner,
}: {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  invitationId: string;
  userRole: Role;
  userRoleHierarchy: number;
  isPrimaryOwner: boolean;
}) {
  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent data-test="update-invitation-dialog">
        <DialogHeader>
          <DialogTitle>
            <Trans i18nKey={'communities:updateMemberRoleModalHeading'} />
          </DialogTitle>

          <DialogDescription>
            <Trans i18nKey={'communities:updateMemberRoleModalDescription'} />
          </DialogDescription>
        </DialogHeader>
        <UpdateInvitationForm
          invitationId={invitationId}
          userRole={userRole}
          userRoleHierarchy={userRoleHierarchy}
          setIsOpen={setIsOpen}
          isPrimaryOwner={isPrimaryOwner}
        />
      </DialogContent>
    </Dialog>
  );
}

function UpdateInvitationForm({
  invitationId,
  userRole,
  userRoleHierarchy,
  setIsOpen,
  isPrimaryOwner,
}: React.PropsWithChildren<{
  invitationId: string;
  userRole: Role;
  userRoleHierarchy: number;
  setIsOpen: (isOpen: boolean) => void;
  isPrimaryOwner: boolean;
}>) {
  const { t } = useTranslation('communities');
  const [pending, startTransition] = useTransition();
  const [error, setError] = useState<boolean>();

  const onSubmit = ({ role }: { role: Role }) => {
    startTransition(async () => {
      try {
        await updateInvitationAction({
          invitationId,
          role,
        });

        setIsOpen(false);
      } catch {
        setError(true);
      }
    });
  };

  const form = useForm({
    resolver: zodResolver(
      RoleSchema.refine(
        (data) => {
          return data.role !== userRole;
        },
        {
          message: t('roleMustBeDifferent'),
          path: ['role'],
        },
      ),
    ),
    reValidateMode: 'onChange',
    mode: 'onChange',
    defaultValues: {
      role: userRole,
    },
  });

  return (
    <Form {...form}>
      <form
        data-test={'update-invitation-form'}
        onSubmit={form.handleSubmit(onSubmit)}
        className={'flex flex-col space-y-6'}
      >
        <If condition={error}>
          <StatusAlert
            variant="destructive"
            titleKey="communities:updateRoleErrorHeading"
            descriptionKey="communities:updateRoleErrorMessage"
          />
        </If>

        <FormField
          name={'role'}
          render={({ field }) => {
            return (
              <FormItem>
                <FormLabel>
                  <Trans i18nKey={'communities:roleLabel'} />
                </FormLabel>

                <FormControl>
                  <RolesDataProvider
                    maxRoleHierarchy={userRoleHierarchy}
                    isPrimaryOwner={isPrimaryOwner}
                  >
                    {(roles) => (
                      <MembershipRoleSelector
                        roles={roles}
                        currentUserRole={userRole}
                        value={field.value}
                        onChange={(newRole) =>
                          form.setValue(field.name, newRole)
                        }
                      />
                    )}
                  </RolesDataProvider>
                </FormControl>

                <FormDescription>
                  <Trans i18nKey={'communities:updateRoleDescription'} />
                </FormDescription>

                <FormMessage />
              </FormItem>
            );
          }}
        />

        <Button
          type={'submit'}
          disabled={pending}
          data-test="confirm-update-invitation-button"
          aria-label="Update invitation role"
        >
          <Trans i18nKey={'communities:updateRoleSubmitLabel'} />
        </Button>
      </form>
    </Form>
  );
}

UpdateInvitationDialog.displayName = 'UpdateInvitationDialog';
UpdateInvitationForm.displayName = 'UpdateInvitationForm';
