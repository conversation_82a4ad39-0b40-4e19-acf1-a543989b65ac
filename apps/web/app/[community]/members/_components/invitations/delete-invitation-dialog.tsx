import { useState, useTransition } from 'react';

import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@kit/ui/alert-dialog';
import { Button } from '@kit/ui/button';
import { StatusAlert } from '@kit/ui/dojo/molecules/status-alert';
import { If } from '@kit/ui/if';
import { Trans } from '@kit/ui/trans';

import { deleteInvitationAction } from '~/lib/communities/invitations/actions/community-invitations-server-actions';

// TODO: Break this down into smaller components
export function DeleteInvitationDialog({
  isOpen,
  setIsOpen,
  invitationId,
}: {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  invitationId: string;
}) {
  return (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialogContent data-test="delete-invitation-dialog">
        <AlertDialogHeader>
          <AlertDialogTitle>
            <Trans i18nKey="communities:deleteInvitation" />
          </AlertDialogTitle>

          <AlertDialogDescription>
            <Trans i18nKey="communities:deleteInvitationDialogDescription" />
          </AlertDialogDescription>
        </AlertDialogHeader>

        <DeleteInvitationForm
          setIsOpen={setIsOpen}
          invitationId={invitationId}
        />
      </AlertDialogContent>
    </AlertDialog>
  );
}

function DeleteInvitationForm({
  invitationId,
  setIsOpen,
}: {
  invitationId: string;
  setIsOpen: (isOpen: boolean) => void;
}) {
  const [isSubmitting, startTransition] = useTransition();
  const [error, setError] = useState<boolean>();

  const onInvitationRemoved = () => {
    startTransition(async () => {
      try {
        await deleteInvitationAction({ invitationId });

        setIsOpen(false);
      } catch {
        setError(true);
      }
    });
  };

  return (
    <form data-test={'delete-invitation-form'} action={onInvitationRemoved}>
      <div className={'flex flex-col space-y-6'}>
        <p className={'text-muted-foreground text-sm'}>
          <Trans i18nKey={'common:modalConfirmationQuestion'} />
        </p>

        <If condition={error}>
          <StatusAlert
            variant="destructive"
            titleKey="communities:deleteInvitationErrorTitle"
            descriptionKey="communities:deleteInvitationErrorMessage"
          />
        </If>

        <AlertDialogFooter>
          <AlertDialogCancel
            data-test="cancel-delete-invitation-button"
            aria-label="Cancel deleting invitation"
          >
            <Trans i18nKey={'common:cancel'} />
          </AlertDialogCancel>

          <Button
            type={'submit'}
            variant={'destructive'}
            disabled={isSubmitting}
            data-test="confirm-delete-invitation-button"
            aria-label="Confirm delete invitation"
          >
            <Trans i18nKey={'communities:deleteInvitation'} />
          </Button>
        </AlertDialogFooter>
      </div>
    </form>
  );
}

DeleteInvitationDialog.displayName = 'DeleteInvitationDialog';
DeleteInvitationForm.displayName = 'DeleteInvitationForm';
