import { useState, useTransition } from 'react';

import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@kit/ui/alert-dialog';
import { Button } from '@kit/ui/button';
import { StatusAlert } from '@kit/ui/dojo/molecules/status-alert';
import { If } from '@kit/ui/if';
import { Trans } from '@kit/ui/trans';

import { removeMemberFromCommunityAction } from '~/lib/communities/members/actions/community-members-server-actions';

// TODO: Break this down into smaller components
export function RemoveMemberDialog({
  isOpen,
  setIsOpen,
  communityId,
  userId,
}: {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  communityId: string;
  userId: string;
}) {
  return (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialogContent data-test="remove-member-dialog">
        <AlertDialogHeader>
          <AlertDialogTitle>
            <Trans i18nKey="communities:removeMemberModalHeading" />
          </AlertDialogTitle>

          <AlertDialogDescription>
            <Trans i18nKey={'communities:removeMemberModalDescription'} />
          </AlertDialogDescription>
        </AlertDialogHeader>

        <RemoveMemberForm
          setIsOpen={setIsOpen}
          communityId={communityId}
          userId={userId}
        />
      </AlertDialogContent>
    </AlertDialog>
  );
}

function RemoveMemberForm({
  communityId,
  userId,
  setIsOpen,
}: {
  communityId: string;
  userId: string;
  setIsOpen: (isOpen: boolean) => void;
}) {
  const [isSubmitting, startTransition] = useTransition();
  const [error, setError] = useState<boolean>();

  const onMemberRemoved = () => {
    startTransition(async () => {
      try {
        await removeMemberFromCommunityAction({ communityId, userId });

        setIsOpen(false);
      } catch {
        setError(true);
      }
    });
  };

  return (
    <form action={onMemberRemoved}>
      <div className={'flex flex-col space-y-6'}>
        <p className={'text-muted-foreground text-sm'}>
          <Trans i18nKey={'common:modalConfirmationQuestion'} />
        </p>

        <If condition={error}>
          <StatusAlert
            variant="destructive"
            titleKey="communities:removeMemberErrorHeading"
            descriptionKey="communities:removeMemberErrorMessage"
          />
        </If>

        <AlertDialogFooter>
          <AlertDialogCancel
            data-test="cancel-remove-member-button"
            aria-label="Cancel removing member"
          >
            <Trans i18nKey={'common:cancel'} />
          </AlertDialogCancel>

          <Button
            data-test={'confirm-remove-member'}
            variant={'destructive'}
            disabled={isSubmitting}
            aria-label="Confirm remove member"
          >
            <Trans i18nKey={'communities:removeMemberSubmitLabel'} />
          </Button>
        </AlertDialogFooter>
      </div>
    </form>
  );
}

RemoveMemberForm.displayName = 'RemoveMemberForm';
RemoveMemberDialog.displayName = 'RemoveMemberDialog';
