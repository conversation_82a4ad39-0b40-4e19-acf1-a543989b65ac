import { LogIn, Users } from 'lucide-react';

import { Join<PERSON>om<PERSON>nityCTA } from '@kit/ui/dojo/organisms/join-community-cta';
import { PageBody } from '@kit/ui/page';

import pathsConfig from '~/config/paths.config';
import { loadCommunityWorkspace } from '~/lib/communities/community-workspace.loader';
import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { withI18n } from '~/lib/i18n/with-i18n';

import { CommunityNotFound } from '../_components/community-not-found';
import CommunityMembersContainer from './_components/community-members-container';
import { loadMembersPageData } from './_lib/server/members-page.loader';

type CommunityMembersPageProps = {
  params: Promise<{ community: string }>;
};

export const generateMetadata = async () => {
  const i18n = await createI18nServerInstance();
  const title = i18n.t('communities:members.pageTitle');

  return {
    title,
  };
};

async function CommunityMembersPage({ params }: CommunityMembersPageProps) {
  const communitySlug = (await params).community;

  const workspace = await loadCommunityWorkspace(communitySlug);

  if (!workspace || !workspace.community?.id) {
    return <CommunityNotFound />;
  }

  const communityId = workspace.community.id;
  const user = workspace.user;
  const isMember = !!workspace.community.memberId;

  const { teamMembers, members, invitations } =
    await loadMembersPageData(communityId);

  const publicView = !isMember;

  const communityAboutPath = pathsConfig.app.communityAbout.replace(
    '[community]',
    communitySlug,
  );

  const communitySignInPath = `${pathsConfig.auth.signIn}?next=/${communitySlug}/forums`;
  const i18n = await createI18nServerInstance();

  return (
    <PageBody>
      {!isMember && !workspace.user && (
        <div className="mb-2">
          <JoinCommunityCTA
            title="communities:members.joinCommunityCTA.loginTitle"
            titleIcon={<LogIn className="h-5 w-5 text-white" />}
            buttonLink={communitySignInPath}
            buttonLabel="communities:members.joinCommunityCTA.loginButtonLabel"
            buttonIcon={<LogIn className="ml-1 h-4 w-4" />}
            descriptionText="communities:members.joinCommunityCTA.loginDescription"
            dataTestButton="cta-login-members-button"
            ariaLabelButton={i18n.t(
              'communities:members.joinCommunityCTA.loginButtonLabel',
            )}
          />
        </div>
      )}

      {!isMember && workspace.user && (
        <div className="mb-2">
          <JoinCommunityCTA
            title="communities:members.joinCommunityCTA.joinTitle"
            titleIcon={<Users className="h-5 w-5 text-white" />}
            buttonLink={communityAboutPath}
            buttonLabel="communities:members.joinCommunityCTA.joinButtonLabel"
            buttonIcon={<Users className="ml-1 h-4 w-4" />}
            descriptionText="communities:members.joinCommunityCTA.joinDescription"
            dataTestButton="cta-join-members-button"
            ariaLabelButton={i18n.t(
              'communities:members.joinCommunityCTA.joinButtonLabel',
            )}
          />
        </div>
      )}
      <CommunityMembersContainer
        community={workspace.community}
        userId={user?.id}
        teamMembers={teamMembers}
        members={members}
        invitations={invitations}
        publicView={publicView}
      />
    </PageBody>
  );
}

export default withI18n(CommunityMembersPage);

CommunityMembersPage.displayName = 'CommunityMembersPage';
