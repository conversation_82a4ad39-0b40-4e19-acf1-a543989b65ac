import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createCommunityInvitationsService } from '~/lib/communities/invitations/community-invitations.service';
import { createCommunityMembersService } from '~/lib/communities/members/community-members.service';
import type { Database } from '~/lib/database.types';

const namespace = 'members-page-loader';

/**
 * Load data specific to the members page (team members, members, invitations).
 * Workspace context (auth, permissions) is loaded separately.
 * @param communityId
 */
export async function loadMembersPageData(communityId: string) {
  const logger = await getLogger();
  const client = getSupabaseServerClient();

  const ctx = {
    namespace,
    communityId,
  };

  logger.info(ctx, 'Loading members page specific data...');

  try {
    const [teamMembers, members, invitations] = await Promise.all([
      loadCommunityTeamMembers(client, communityId),
      loadCommunityMembers(client, communityId),
      loadCommunityInvitations(client, communityId),
    ]);

    logger.info(ctx, 'Successfully loaded members page specific data', {
      hasTeamMembers: !!teamMembers?.length,
      hasMembers: !!members?.length,
      hasInvitations: !!invitations?.length,
    });

    return { teamMembers, members, invitations };
  } catch (error) {
    logger.error(
      { ...ctx, error },
      'Failed to load members page specific data',
    );

    throw new Error('Failed to load members page data');
  }
}

/**
 * Load community team members
 * @param client
 * @param community
 */
async function loadCommunityTeamMembers(
  client: SupabaseClient<Database>,
  communityId: string,
) {
  const logger = await getLogger();
  const ctx = {
    namespace,
    communityId,
    operation: 'loadCommunityTeamMembers',
  };

  logger.info(ctx, 'Loading community team members...');

  try {
    const membersService = createCommunityMembersService(client);
    const data = await membersService.getCommunityTeamMembers(communityId);
    logger.info(ctx, 'Successfully loaded community team members');
    return data;
  } catch (error) {
    logger.error({ ...ctx, error }, 'Failed to load community team members');
    throw new Error('Failed to load community team members');
  }
}

/**
 * Load community members
 * @param client
 * @param communityId
 */
async function loadCommunityMembers(
  client: SupabaseClient<Database>,
  communityId: string,
) {
  const logger = await getLogger();
  const ctx = {
    namespace,
    communityId,
    operation: 'loadCommunityMembers',
  };

  logger.info(ctx, 'Loading community members...');

  try {
    const membersService = createCommunityMembersService(client);
    const data = await membersService.getCommunityMembersOnly(communityId);
    logger.info(ctx, 'Successfully loaded community members');
    return data;
  } catch (error) {
    logger.error({ ...ctx, error }, 'Failed to load community members');
    throw new Error('Failed to load community members');
  }
}

/**
 * Load community invitations
 * @param client
 * @param communityId
 */
async function loadCommunityInvitations(
  client: SupabaseClient<Database>,
  communityId: string,
) {
  const logger = await getLogger();
  const ctx = {
    namespace,
    communityId,
    operation: 'loadCommunityInvitations',
  };

  logger.info(ctx, 'Loading community invitations...');

  try {
    const invitationsService = createCommunityInvitationsService(client);
    const data = await invitationsService.getInvitations(communityId);
    logger.info(ctx, 'Successfully loaded community invitations');
    return data;
  } catch (error) {
    logger.error({ ...ctx, error }, 'Failed to load community invitations');
    throw new Error('Failed to load community invitations');
  }
}
