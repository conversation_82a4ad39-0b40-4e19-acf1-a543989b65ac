'use server';

import { revalidatePath } from 'next/cache';

import { validate as isUUID } from 'uuid';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import pathsConfig from '~/config/paths.config';
import { createCommunityCoursesService } from '~/lib/communities/courses/community-courses.service';
import { LessonContentTypeEnum } from '~/lib/communities/courses/types';

import { UpdateCourseLessonSchema } from '../../../schema/course-lesson.schema';

export const updateCourseLessonAction = enhanceAction(
  async (
    params,
  ): Promise<{
    error?: boolean;
    message?: string;
    success?: boolean;
    data?: {
      id: string;
      title: string;
      icon: string;
      lessonText: JSON;
      contentType: LessonContentTypeEnum;
      contentData: JSON;
      characterCount: number;
      wordCount: number;
    };
  }> => {
    const client = getSupabaseServerClient();
    const logger = await getLogger();
    const {
      lessonId,
      title,
      icon,
      lessonText,
      contentData,
      contentType,
      characterCount,
      wordCount,
    } = params;

    // Validate lessonId is a valid UUID
    if (!lessonId || !isUUID(lessonId)) {
      return { error: true, message: 'Invalid lesson ID' };
    }

    const ctx = {
      name: 'course_lesson.update',
      lessonId: lessonId,
    };

    logger.info(ctx, `Updating course lesson details...`);

    // Parse JSON strings here before passing to service
    const parsedLessonText = lessonText ? JSON.parse(lessonText) : null;
    const parsedContentData = contentData ? JSON.parse(contentData) : null;

    try {
      const communityCourseService = createCommunityCoursesService(client);

      await communityCourseService.updateLesson({
        lessonId,
        title,
        icon,
        lessonText: parsedLessonText,
        contentData: parsedContentData,
        contentType,
        characterCount,
        wordCount,
      });

      logger.info(ctx, `Course lesson details updated via service`);
      revalidatePath(`${pathsConfig.app.course}`, 'page');
      revalidatePath(`${pathsConfig.app.courses}`, 'page');

      return {
        success: true,
        data: {
          id: lessonId,
          title: title,
          icon: icon ?? '',
          // Return the parsed JSON data
          lessonText: parsedLessonText,
          contentType: contentType ?? 'none',
          contentData: parsedContentData,
          characterCount: characterCount ?? 0,
          wordCount: wordCount ?? 0,
        },
      };
    } catch (error) {
      logger.error({ ...ctx, error }, `Failed to update course lesson details`);
      const message = error instanceof Error ? error.message : 'Unknown error';
      return {
        error: true,
        message: message,
      };
    }
  },
  {
    auth: true,
    schema: UpdateCourseLessonSchema,
  },
);
