'use server';

import { revalidatePath } from 'next/cache';

import { validate as isUUID } from 'uuid';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import pathsConfig from '~/config/paths.config';
import { createCommunityCoursesService } from '~/lib/communities/courses/community-courses.service';

import { UpdateCourseChapterSchema } from '../../../schema/course-chapter.schema';

export const updateCourseChapterAction = enhanceAction(
  async (
    params,
  ): Promise<{
    error?: boolean;
    message?: string;
    success?: boolean;
    data?: {
      id: string;
      title: string;
      description: string;
      icon: string;
    };
  }> => {
    const client = getSupabaseServerClient();
    const logger = await getLogger();
    const { chapterId, title, description, icon } = params;

    // Validate chapterId is a valid UUID
    if (!chapterId || !isUUID(chapterId)) {
      return { error: true, message: 'Invalid chapter ID' };
    }

    const ctx = {
      name: 'course_chapter.update',
      chapterId: chapterId,
    };

    logger.info(ctx, `Updating course chapter details...`);

    try {
      const communityCourseService = createCommunityCoursesService(client);

      await communityCourseService.updateChapter({
        chapterId,
        title,
        description,
        icon,
      });

      logger.info(ctx, `Course chapter details updated via service`);
      revalidatePath(`${pathsConfig.app.course}`, 'page');
      revalidatePath(`${pathsConfig.app.courses}`, 'page');

      return {
        success: true,
        data: {
          id: chapterId,
          title: title,
          description: description ?? '',
          icon: icon ?? '',
        },
      };
    } catch (error) {
      logger.error(
        { ...ctx, error },
        `Failed to update course chapter details`,
      );
      const message = error instanceof Error ? error.message : 'Unknown error';
      return {
        error: true,
        message: message,
      };
    }
  },
  {
    auth: true,
    schema: UpdateCourseChapterSchema,
  },
);
