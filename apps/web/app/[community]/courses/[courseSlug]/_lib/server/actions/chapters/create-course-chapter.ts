'use server';

import { revalidatePath } from 'next/cache';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import pathsConfig from '~/config/paths.config';
import { createCommunityCoursesService } from '~/lib/communities/courses/community-courses.service';

import { CreateCourseChapterSchema } from '../../../schema/course-chapter.schema';

export const createCourseChapterAction = enhanceAction(
  async (
    params,
  ): Promise<{
    error?: boolean;
    success?: boolean;
    message?: string;
    data?: {
      id: string;
      title: string;
      description: string;
      icon: string;
    };
  }> => {
    const client = getSupabaseServerClient();
    const logger = await getLogger();
    const { courseSlug, title, description, icon } = params;

    const ctx = {
      name: 'course_chapter.create',
      courseSlug: courseSlug,
    };

    logger.info(ctx, `Creating course chapter...`);

    const communityCourseService = createCommunityCoursesService(client);

    const newChapterId = await communityCourseService.createChapter({
      courseSlug,
      title,
      description: description ?? undefined,
      icon: icon ?? undefined,
    });

    logger.info(ctx, `Course chapter created via service`);
    revalidatePath(`${pathsConfig.app.course}`, 'page');
    revalidatePath(`${pathsConfig.app.courses}`, 'page');

    return {
      success: true,
      data: {
        id: newChapterId,
        title: title,
        description: description ?? '',
        icon: icon ?? '',
      },
    };
  },
  {
    auth: true,
    schema: CreateCourseChapterSchema,
  },
);
