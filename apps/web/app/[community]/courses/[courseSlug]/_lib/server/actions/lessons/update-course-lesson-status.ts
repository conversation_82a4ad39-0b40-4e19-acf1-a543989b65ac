'use server';

import { revalidatePath } from 'next/cache';

import { validate as isUUID } from 'uuid';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import pathsConfig from '~/config/paths.config';
import { createCommunityCoursesService } from '~/lib/communities/courses/community-courses.service';
import { LessonStatusEnum } from '~/lib/communities/courses/types';

export const updateCourseLessonStatusAction = enhanceAction(
  async (
    params: { lessonId: string; targetStatus: LessonStatusEnum },
    user,
  ) => {
    const client = getSupabaseServerClient();
    const logger = await getLogger();

    const ctx = {
      name: 'course_lesson.update_status',
      userId: user.id,
      lessonId: params.lessonId,
      targetStatus: params.targetStatus,
    };

    logger.info(ctx, `Updating course lesson status...`);

    // Validate lessonId is a valid UUID
    if (!params.lessonId || !isUUID(params.lessonId)) {
      return { error: true, message: 'Invalid lesson ID' };
    }

    try {
      const communityCourseService = createCommunityCoursesService(client);

      await communityCourseService.updateLessonStatus({
        lessonId: params.lessonId,
        targetStatus: params.targetStatus,
      });

      logger.info(ctx, `Course lesson status updated via service`);

      // Revalidate paths after successful update
      revalidatePath(`${pathsConfig.app.course}`, 'page');
      revalidatePath(`${pathsConfig.app.courses}`, 'page');

      return {
        success: true,
      };
    } catch (error) {
      logger.error({ ...ctx, error }, `Failed to update course lesson status`);
      const message = error instanceof Error ? error.message : 'Unknown error';
      return {
        error: true,
        message: message,
      };
    }
  },
  {
    auth: true,
  },
);
