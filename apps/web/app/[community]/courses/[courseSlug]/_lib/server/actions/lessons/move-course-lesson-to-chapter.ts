'use server';

import { revalidatePath } from 'next/cache';

import { validate as isUUID } from 'uuid';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import pathsConfig from '~/config/paths.config';
import { createCommunityCoursesService } from '~/lib/communities/courses/community-courses.service';

import { MoveCourseLessonToChapterSchema } from '../../../schema/course-lesson.schema';

export const moveCourseLessonToChapterAction = enhanceAction(
  async (params: { lessonId: string; chapterId: string | null }, user) => {
    const client = getSupabaseServerClient();
    const logger = await getLogger();

    const ctx = {
      name: 'course_lesson.move',
      userId: user.id, // Add user ID
      lessonId: params.lessonId,
      chapterId: params.chapterId,
    };

    logger.info(ctx, `Moving course lesson to chapter...`);

    // Validate lessonId is a valid UUID
    if (!params.lessonId || !isUUID(params.lessonId)) {
      return { error: true, message: 'Invalid lesson ID' };
    }

    // Map 'none' to null for the service call
    const targetChapterId =
      params.chapterId === 'none' ? null : params.chapterId;

    // Validate chapterId is a valid UUID when provided (and not null)
    if (targetChapterId && !isUUID(targetChapterId)) {
      return { error: true, message: 'Invalid chapter ID' };
    }

    try {
      const communityCourseService = createCommunityCoursesService(client);

      await communityCourseService.moveLessonToChapter({
        lessonId: params.lessonId,
        newChapterId: targetChapterId,
      });

      logger.info(ctx, `Course lesson moved to chapter via service`);

      // Revalidate paths after successful move
      revalidatePath(`${pathsConfig.app.course}`, 'page');
      revalidatePath(`${pathsConfig.app.courses}`, 'page');

      return {
        success: true,
      };
    } catch (error) {
      logger.error(
        { ...ctx, error },
        `Failed to move course lesson to chapter`,
      );
      const message = error instanceof Error ? error.message : 'Unknown error';
      return {
        error: true,
        message: message,
      };
    }
  },
  {
    auth: true,
    schema: MoveCourseLessonToChapterSchema,
  },
);
