'use server';

import { revalidatePath } from 'next/cache';

import { validate as isUUID } from 'uuid';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import pathsConfig from '~/config/paths.config';
import { createCommunityCoursesService } from '~/lib/communities/courses/community-courses.service';

import { MoveCourseChapterUpDownSchema } from '../../../schema/course-chapter.schema';

export const moveCourseChapterUpDownAction = enhanceAction(
  async (params: { chapterId: string; direction: 'up' | 'down' }, user) => {
    const client = getSupabaseServerClient();
    const logger = await getLogger();

    const ctx = {
      name: 'course_chapter.move',
      userId: user.id,
      chapterId: params.chapterId,
      direction: params.direction,
    };

    logger.info(ctx, `Moving course chapter...`);

    // Validate chapterId is a valid UUID
    if (!params.chapterId || !isUUID(params.chapterId)) {
      // Keep validation in the action layer
      return { error: true, message: 'Invalid chapter ID' };
    }

    try {
      const communityCourseService = createCommunityCoursesService(client);

      await communityCourseService.moveChapter({
        chapterId: params.chapterId,
        direction: params.direction,
      });

      logger.info(ctx, `Course chapter moved via service`);

      // Revalidate paths after successful move
      revalidatePath(`${pathsConfig.app.course}`, 'page');
      revalidatePath(`${pathsConfig.app.courses}`, 'page');

      return {
        success: true,
      };
    } catch (error) {
      logger.error({ ...ctx, error }, `Failed to move course chapter`);
      const message = error instanceof Error ? error.message : 'Unknown error';
      return {
        error: true,
        message: message,
      };
    }
  },
  {
    auth: true,
    schema: MoveCourseChapterUpDownSchema,
  },
);
