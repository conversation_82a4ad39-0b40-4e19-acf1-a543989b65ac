'use server';

import { revalidatePath } from 'next/cache';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import pathsConfig from '~/config/paths.config';
import { createCommunityCoursesService } from '~/lib/communities/courses/community-courses.service';

import { CreateCourseLessonSchema } from '../../../schema/course-lesson.schema';

export const createCourseLessonAction = enhanceAction(
  async (
    params,
    user,
  ): Promise<{
    error?: boolean;
    success?: boolean;
    message?: string;
    data?: {
      id: string;
    };
  }> => {
    const client = getSupabaseServerClient();
    const logger = await getLogger();
    const { courseSlug, title, chapterId } = params;

    const ctx = {
      name: 'course_lesson.create',
      userId: user.id,
      courseSlug: courseSlug,
      chapterId: chapterId,
      title: title,
    };

    logger.info(ctx, `Creating course lesson...`);

    if (!chapterId) {
      const errorMsg = 'Chapter ID is required to create a lesson.';
      logger.error({ ...ctx, error: errorMsg }, errorMsg);
      return { error: true, message: errorMsg };
    }

    try {
      const communityCourseService = createCommunityCoursesService(client);

      const newLessonId = await communityCourseService.createLesson({
        courseSlug,
        title,
        chapterId,
      });

      logger.info(ctx, `Course lesson created via service`);
      revalidatePath(`${pathsConfig.app.course}`, 'page');
      revalidatePath(`${pathsConfig.app.courses}`, 'page');

      return {
        success: true,
        data: {
          id: newLessonId,
        },
      };
    } catch (error) {
      logger.error({ ...ctx, error }, `Failed to create course lesson`);
      const message = error instanceof Error ? error.message : 'Unknown error';
      return {
        error: true,
        message: message,
      };
    }
  },
  {
    auth: true,
    schema: CreateCourseLessonSchema,
  },
);
