import 'server-only';

import { redirect } from 'next/navigation';

import { SupabaseClient } from '@supabase/supabase-js';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

import pathsConfig from '~/config/paths.config';
import { loadCommunityWorkspace } from '~/lib/communities/community-workspace.loader';
import { createCommunityCoursesService } from '~/lib/communities/courses/community-courses.service';
import { CourseChapter, CourseLesson } from '~/lib/communities/courses/types';
import type { Database } from '~/lib/database.types';

type CoursePageData = {
  courseDetails: NonNullable<
    Awaited<ReturnType<typeof loadCourseDetails>>
  >['courseDetails'];
  courseChapters: CourseChapter[];
  courseLessons: CourseLesson[];
  communityWorkspace: Awaited<ReturnType<typeof loadCommunityWorkspace>>;
};

/**
 * Load data for the course page
 * @param communitySlug - Community slug
 * @param courseSlug - Course slug
 */
export async function loadCoursePageData(
  communitySlug: string,
  courseSlug?: string,
): Promise<CoursePageData> {
  const client = getSupabaseServerClient();
  const communityWorkspace = await loadCommunityWorkspace(communitySlug);

  if (!communityWorkspace.community.memberId || !communityWorkspace.user) {
    // Redirect to about page
    const aboutPath = pathsConfig.app.communityAbout.replace(
      '[community]',
      communitySlug,
    );

    redirect(aboutPath);
  }

  const { courseDetails, courseChapters, courseLessons } =
    await loadCourseDetails(client, courseSlug);

  // If no course is found, return early with empty data
  if (!courseDetails) {
    return {
      courseDetails: null,
      courseChapters: [],
      courseLessons: [],
      communityWorkspace,
    };
  }

  return {
    courseDetails,
    courseChapters: courseChapters as CourseChapter[],
    courseLessons,
    communityWorkspace,
  };
}

/**
 * Load course details
 * @param client - Supabase client
 * @param courseSlug - Course slug
 */
async function loadCourseDetails(
  client: SupabaseClient<Database>,
  courseSlug?: string,
) {
  const service = createCommunityCoursesService(client);

  const [courseDetails] = await service.getCourseDetails({
    courseSlug,
  });

  if (!courseDetails?.id) {
    return {
      courseDetails: null,
      courseChapters: [],
      courseLessons: [],
    };
  }

  try {
    const [courseChapters, courseLessons] = await Promise.all([
      service.getCourseChapters({ courseId: courseDetails.id }),
      service.getCourseLessons({ courseId: courseDetails.id }),
    ]);

    return {
      courseDetails,
      courseChapters: courseChapters.map((chapter) => ({
        ...chapter,
        description: chapter.description || '',
        icon: chapter.icon || '',
        lessonCount: courseLessons.filter(
          (lesson) => lesson.chapterId === chapter.id,
        ).length,
      })),
      courseLessons,
    };
  } catch (error) {
    console.error('Failed to load course content:', error);
    throw new Error('Failed to load course content');
  }
}
