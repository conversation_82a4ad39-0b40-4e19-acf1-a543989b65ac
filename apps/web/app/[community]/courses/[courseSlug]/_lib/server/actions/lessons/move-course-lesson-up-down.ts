'use server';

import { revalidatePath } from 'next/cache';

import { validate as isUUID } from 'uuid';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import pathsConfig from '~/config/paths.config';
import { createCommunityCoursesService } from '~/lib/communities/courses/community-courses.service';

import { MoveCourseLessonUpDownSchema } from '../../../schema/course-lesson.schema';

export const moveCourseLessonUpDownAction = enhanceAction(
  async (
    params: {
      lessonId: string;
      targetSequence: number;
      direction: 'up' | 'down';
    },
    user,
  ) => {
    const client = getSupabaseServerClient();
    const logger = await getLogger();

    const ctx = {
      name: 'course_lesson.move',
      userId: user.id,
      lessonId: params.lessonId,
      targetSequence: params.targetSequence,
      direction: params.direction,
    };

    logger.info(ctx, `Moving course lesson...`);

    // Validate lessonId is a valid UUID
    if (!params.lessonId || !isUUID(params.lessonId)) {
      return { error: true, message: 'Invalid lesson ID' };
    }

    try {
      const communityCourseService = createCommunityCoursesService(client);

      await communityCourseService.moveLessonSequence({
        lessonId: params.lessonId,
        targetSequence: params.targetSequence,
        direction: params.direction,
      });

      logger.info(ctx, `Course lesson moved via service`);

      // Revalidate paths after successful move
      revalidatePath(`${pathsConfig.app.course}`, 'page');
      revalidatePath(`${pathsConfig.app.courses}`, 'page');

      return {
        success: true,
      };
    } catch (error) {
      logger.error({ ...ctx, error }, `Failed to move course lesson`);
      const message = error instanceof Error ? error.message : 'Unknown error';
      return {
        error: true,
        message: message,
      };
    }
  },
  {
    auth: true,
    schema: MoveCourseLessonUpDownSchema,
  },
);
