'use server';

import { revalidatePath } from 'next/cache';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import pathsConfig from '~/config/paths.config';
import { createCommunityCoursesService } from '~/lib/communities/courses/community-courses.service';

import { DeleteCourseChapterSchema } from '../../../schema/course-chapter.schema';

export const deleteCourseChapterAction = enhanceAction(
  async (params: { chapterId: string }, user) => {
    const client = getSupabaseServerClient();
    const logger = await getLogger();

    const ctx = {
      name: 'course_chapter.delete',
      userId: user.id,
      chapterId: params.chapterId,
    };

    logger.info(ctx, `Deleting course chapter...`);

    try {
      const communityCourseService = createCommunityCoursesService(client);

      await communityCourseService.deleteChapter({
        chapterId: params.chapterId,
      });

      logger.info(ctx, `Course chapter deleted via service`);

      // Revalidate paths after successful deletion
      revalidatePath(`${pathsConfig.app.course}`, 'page');
      revalidatePath(`${pathsConfig.app.courses}`, 'page');

      return {
        success: true,
      };
    } catch (error) {
      logger.error({ ...ctx, error }, `Failed to delete course chapter`);
      const message = error instanceof Error ? error.message : 'Unknown error';
      return {
        error: true,
        message: message,
      };
    }
  },
  {
    auth: true,
    schema: DeleteCourseChapterSchema,
  },
);
