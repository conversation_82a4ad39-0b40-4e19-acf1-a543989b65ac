import { CourseChapter, CourseLesson } from '~/lib/communities/courses/types';

export type CourseContentItem =
  | { type: 'chapter'; item: CourseChapter }
  | { type: 'lesson'; item: CourseLesson };

type SortOptions = {
  lessonsOnly?: boolean;
};

/**
 * Sorts course content (chapters and lessons) by their sequence order.
 * Can optionally return only lessons in the correct sequence.
 *
 * @param lessons - Array of course lessons
 * @param chapters - Array of course chapters
 * @param options - Sorting options
 * @returns Sorted array of content items or lessons depending on options
 */
export function sortCourseContent(
  lessons: readonly CourseLesson[],
  chapters: readonly CourseChapter[],
  options: SortOptions = {},
): CourseContentItem[] | CourseLesson[] {
  const chapterMap = new Map(chapters.map((chapter) => [chapter.id, chapter]));

  if (options.lessonsOnly) {
    const compareSequence = (a: CourseLesson, b: CourseLesson): number => {
      const aChapter = a.chapterId ? chapterMap.get(a.chapterId) : undefined;
      const bChapter = b.chapterId ? chapterMap.get(b.chapterId) : undefined;

      // If lessons are in same chapter or both without chapter
      if ((!aChapter && !bChapter) || aChapter?.id === bChapter?.id) {
        return a.sequenceOrder - b.sequenceOrder;
      }

      // If only one lesson has chapter
      if (aChapter !== bChapter) {
        return a.sequenceOrder - b.sequenceOrder;
      }

      // Different chapters - using type guard instead of non-null assertion
      if (
        aChapter &&
        bChapter &&
        aChapter.sequenceOrder !== bChapter.sequenceOrder
      ) {
        return aChapter.sequenceOrder - bChapter.sequenceOrder;
      }

      return a.sequenceOrder - b.sequenceOrder;
    };

    return [...lessons].sort(compareSequence);
  }

  // For sidebar view, separate standalone lessons and chapters
  const standaloneLessons = lessons.filter((lesson) => !lesson.chapterId);

  // Using reduce for better performance when creating minimum sequence map
  const chapterMinSequenceMap = lessons.reduce((acc, lesson) => {
    if (lesson.chapterId) {
      const currentMin = acc.get(lesson.chapterId);
      if (currentMin === undefined || lesson.sequenceOrder < currentMin) {
        acc.set(lesson.chapterId, lesson.sequenceOrder);
      }
    }
    return acc;
  }, new Map<string, number>());

  // Combine standalone lessons and chapters, sorted by sequence_order
  const allContent: CourseContentItem[] = [
    ...standaloneLessons.map((lesson) => ({
      type: 'lesson' as const,
      item: lesson,
    })),
    ...chapters.map((chapter) => ({
      type: 'chapter' as const,
      item: chapter,
    })),
  ].sort((a, b) => {
    const aOrder = a.item.sequenceOrder;
    const bOrder = b.item.sequenceOrder;

    // If comparing a standalone lesson with a chapter
    if (a.type === 'lesson' && b.type === 'chapter') {
      const chapterMinSequence = chapterMinSequenceMap.get(b.item.id);
      if (chapterMinSequence !== undefined && aOrder < chapterMinSequence) {
        return -1; // Lesson goes before chapter
      }
    } else if (a.type === 'chapter' && b.type === 'lesson') {
      const chapterMinSequence = chapterMinSequenceMap.get(a.item.id);
      if (chapterMinSequence !== undefined && bOrder < chapterMinSequence) {
        return 1; // Lesson goes before chapter
      }
    }

    // Default to sequence_order comparison
    return aOrder - bOrder;
  });

  return allContent;
}

// Keep the old function name for backward compatibility
export function sortLessonsBySequence(
  lessons: readonly CourseLesson[],
  chapters: readonly CourseChapter[],
): CourseLesson[] {
  return sortCourseContent(lessons, chapters, {
    lessonsOnly: true,
  }) as CourseLesson[];
}
