import { useMemo } from 'react';

import { groupBy } from 'lodash';

import { CourseLesson } from '~/lib/communities/courses/types';

type MovementOptions = {
  canMoveUp: boolean;
  canMoveDown: boolean;
  targetSequenceUp?: number;
  targetSequenceDown?: number;
};

/**
 * Groups lessons into segments (chapters or standalone) and orders them by sequence
 * This ensures we can move between adjacent segments without skipping
 */
function groupLessonsIntoSegments(lessons: CourseLesson[]) {
  // First, separate lessons into chapters and standalone
  const segments: { lessons: CourseLesson[]; isChapter: boolean }[] = [];

  // Group chapter lessons
  const chapterLessons = lessons.filter((l) => l.chapterId);
  const chapters = groupBy(chapterLessons, 'chapterId');

  // Add chapter segments
  Object.values(chapters).forEach((chapterLessons) => {
    if (chapterLessons.length) {
      segments.push({
        lessons: chapterLessons,
        isChapter: true,
      });
    }
  });

  // Add standalone lessons as individual segments
  const standaloneLessons = lessons.filter((l) => !l.chapterId);
  standaloneLessons.forEach((lesson) => {
    segments.push({
      lessons: [lesson],
      isChapter: false,
    });
  });

  // Sort segments by their first lesson's sequence_order
  return segments.sort((a, b) => {
    const aMin = Math.min(...a.lessons.map((l) => l.sequenceOrder));
    const bMin = Math.min(...b.lessons.map((l) => l.sequenceOrder));
    return aMin - bMin;
  });
}

/**
 * Finds the target sequence for moving a standalone lesson
 */
function findStandaloneMoveTarget(
  currentLesson: CourseLesson,
  direction: 'up' | 'down',
  lessons: CourseLesson[],
): number | undefined {
  // Group all lessons into ordered segments
  const segments = groupLessonsIntoSegments(lessons);

  // Find current segment index
  const currentSegmentIndex = segments.findIndex((segment) =>
    segment.lessons.some((l) => l.id === currentLesson.id),
  );

  if (currentSegmentIndex === -1) return undefined;

  // Find target segment
  const targetSegmentIndex =
    direction === 'up' ? currentSegmentIndex - 1 : currentSegmentIndex + 1;

  if (targetSegmentIndex < 0 || targetSegmentIndex >= segments.length) {
    return undefined;
  }

  const targetSegment = segments[targetSegmentIndex];
  if (!targetSegment) return undefined;

  // If target is a chapter, get first/last lesson based on direction
  if (targetSegment.isChapter) {
    const sequences = targetSegment.lessons.map((l) => l.sequenceOrder);
    return direction === 'up' ? Math.min(...sequences) : Math.max(...sequences);
  }

  // For standalone target, just get its sequence
  const targetLesson = targetSegment.lessons[0];
  return targetLesson?.sequenceOrder;
}

export function useCourseLessonMovement(lessons: CourseLesson[]) {
  const movementMap = useMemo(() => {
    const map: Record<string, MovementOptions> = {};

    // Sort all lessons by sequence order
    const sortedLessons = [...lessons].sort(
      (a, b) => a.sequenceOrder - b.sequenceOrder,
    );

    // Process each lesson
    sortedLessons.forEach((lesson, index) => {
      if (lesson.chapterId) {
        // Handle chapter lessons - can only move within their chapter
        const chapterLessons = sortedLessons.filter(
          (l) => l.chapterId === lesson.chapterId,
        );
        const chapterIndex = chapterLessons.findIndex(
          (l) => l.id === lesson.id,
        );
        const isFirstInChapter = chapterIndex === 0;
        const isLastInChapter = chapterIndex === chapterLessons.length - 1;

        map[lesson.id] = {
          canMoveUp: !isFirstInChapter,
          canMoveDown: !isLastInChapter,
          targetSequenceUp: isFirstInChapter
            ? undefined
            : chapterLessons[chapterIndex - 1]?.sequenceOrder,
          targetSequenceDown: isLastInChapter
            ? undefined
            : chapterLessons[chapterIndex + 1]?.sequenceOrder,
        };
      } else {
        // Handle standalone lessons
        const isFirst = lesson.sequenceOrder === 1;
        const isLast = index === sortedLessons.length - 1;

        // Find target sequences using segment-based movement
        const targetUp = findStandaloneMoveTarget(lesson, 'up', sortedLessons);
        const targetDown = findStandaloneMoveTarget(
          lesson,
          'down',
          sortedLessons,
        );

        map[lesson.id] = {
          canMoveUp: !isFirst && targetUp !== undefined,
          canMoveDown: !isLast && targetDown !== undefined,
          targetSequenceUp: targetUp,
          targetSequenceDown: targetDown,
        };
      }
    });

    return map;
  }, [lessons]);

  const getMovementOptions = (
    lessonId: string,
  ): MovementOptions | undefined => {
    return movementMap[lessonId];
  };

  return {
    getMovementOptions,
  };
}
