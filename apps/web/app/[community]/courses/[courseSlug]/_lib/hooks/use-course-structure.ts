import { useMemo } from 'react';

import type {
  CourseChapter,
  CourseLesson,
} from '~/lib/communities/courses/types';

import {
  type CourseContentItem,
  sortCourseContent,
} from '../utils/sort-lessons-by-sequence';

export function useCourseStructure(
  chapters: CourseChapter[],
  lessons: CourseLesson[],
) {
  const organizedContent = useMemo<CourseContentItem[]>(() => {
    if (!lessons || !chapters) {
      return [];
    }
    // Ensure the items are correctly typed after sorting
    return sortCourseContent(lessons, chapters) as CourseContentItem[];
  }, [chapters, lessons]);

  const lessonsByChapter = useMemo<Record<string, CourseLesson[]>>(() => {
    const mapping: Record<string, CourseLesson[]> = {};
    if (!lessons) return mapping;

    lessons.forEach((lesson) => {
      const chapterId = lesson.chapterId;
      if (chapterId) {
        if (!mapping[chapterId]) {
          mapping[chapterId] = [];
        }
        mapping[chapterId].push(lesson);
      }
    });

    // Sort lessons within each chapter
    Object.keys(mapping).forEach((chapterId) => {
      mapping[chapterId]?.sort((a, b) => a.sequenceOrder - b.sequenceOrder);
    });

    return mapping;
  }, [lessons]);

  return { organizedContent, lessonsByChapter };
}
