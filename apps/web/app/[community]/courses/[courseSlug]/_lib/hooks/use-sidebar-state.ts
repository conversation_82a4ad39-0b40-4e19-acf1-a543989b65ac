import { useCallback, useEffect, useState } from 'react';

import type {
  CourseChapter,
  CourseLesson,
} from '~/lib/communities/courses/types';

export function useSidebarState(
  initialChapters: CourseChapter[],
  initialLessons: CourseLesson[],
  currentLessonId: string | undefined,
) {
  const [expandedChapters, setExpandedChapters] = useState<Set<string>>(
    () => new Set(initialChapters.map((chapter) => chapter.id)),
  );
  const [selectedChapter, setSelectedChapter] = useState<CourseChapter | null>(
    null,
  );
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [showUnsavedDialog, setShowUnsavedDialog] = useState(false);

  const toggleChapter = useCallback((chapterId: string) => {
    setExpandedChapters((prev) => {
      const next = new Set(prev);
      if (next.has(chapterId)) {
        next.delete(chapterId);
      } else {
        next.add(chapterId);
      }
      return next;
    });
  }, []);

  const openChapterUpdateDialog = useCallback((chapter: CourseChapter) => {
    setSelectedChapter(chapter);
    setIsUpdateDialogOpen(true);
  }, []);

  const openDeleteChapterDialog = useCallback((chapter: CourseChapter) => {
    setSelectedChapter(chapter);
    setIsDeleteDialogOpen(true);
  }, []);

  // Effect to expand the chapter of the current lesson on initial load
  useEffect(() => {
    const currentLesson = initialLessons?.find((l) => l.id === currentLessonId);
    if (currentLesson?.chapterId) {
      setExpandedChapters((prev) => {
        if (currentLesson.chapterId && !prev.has(currentLesson.chapterId)) {
          const next = new Set(prev);
          next.add(currentLesson.chapterId);
          return next;
        }
        return prev;
      });
    }
    // Run only once on initial load based on initial lessons/current ID
  }, [initialLessons, currentLessonId]);

  return {
    expandedChapters,
    selectedChapter,
    isUpdateDialogOpen,
    isDeleteDialogOpen,
    showUnsavedDialog,
    toggleChapter,
    openChapterUpdateDialog,
    setIsUpdateDialogOpen, // Expose setters for dialogs
    openDeleteChapterDialog,
    setIsDeleteDialogOpen, // Expose setters for dialogs
    setShowUnsavedDialog,
  };
}
