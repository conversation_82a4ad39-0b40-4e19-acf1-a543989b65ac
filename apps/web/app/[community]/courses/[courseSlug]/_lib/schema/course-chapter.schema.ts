import { z } from 'zod';

export const CreateCourseChapterSchema = z.object({
  courseSlug: z.string().min(2).max(100),
  title: z.string().min(2).max(100),
  description: z.string().max(500).optional(),
  icon: z.string().optional(),
  sequenceOrder: z.number().nullable().optional(),
});

export const UpdateCourseChapterSchema = z.object({
  chapterId: z.string(),
  title: z.string().trim().min(2).max(100),
  description: z.string().trim().max(500).optional(),
  icon: z.string().optional(),
  sequenceOrder: z.number().nullable().optional(),
});

export const DeleteCourseChapterSchema = z.object({
  chapterId: z.string().uuid(),
});

export const MoveCourseChapterUpDownSchema = z.object({
  chapterId: z.string().uuid({ message: 'Invalid chapter ID format' }),
  direction: z.enum(['up', 'down'], {
    errorMap: () => ({ message: 'Direction must be either "up" or "down"' }),
  }),
});

export type CreateCourseChapterData = z.infer<typeof CreateCourseChapterSchema>;
export type UpdateCourseChapterData = z.infer<typeof UpdateCourseChapterSchema>;
export type ChapterFormData = CreateCourseChapterData | UpdateCourseChapterData;

/**
 * Represents the data structure for a course chapter
 * @type ChapterData
 * @property {string} id - Unique identifier for the chapter
 * @property {string} title - Title of the chapter
 * @property {string | null} description - Optional description of the chapter
 * @property {string | null} icon - Optional icon URL for the chapter
 * @property {number | null} sequenceOrder - Optional order of the chapter in the sequence
 */
export type ChapterData = {
  id: string;
  title: string;
  description: string | null;
  icon: string | null;
  sequenceOrder: number | null;
};
