import { z } from 'zod';

// Helper function to check if a Tiptap document is empty
function isEmptyTiptapDocument(str: string): boolean {
  return str === '{"type":"doc","content":[{"type":"paragraph"}]}';
}

export const CreateCourseLessonSchema = z.object({
  courseSlug: z.string().min(2).max(100),
  title: z.string().min(2).max(100),
  chapterId: z.string().optional(),
});

export const UpdateCourseLessonSchema = z.object({
  lessonId: z.string(),
  title: z.string().min(1),
  icon: z.string().optional(),
  lessonText: z
    .string()
    .min(1, 'Content is required')
    .refine((val) => {
      if (isEmptyTiptapDocument(val)) {
        return false;
      }
      return true;
    }, 'Content is required')
    .optional(),
  contentType: z.enum(['video', 'quiz', 'exercise', 'none']).optional(),
  contentData: z
    .string()
    .min(1, 'Content is required')
    .refine((val) => {
      if (isEmptyTiptapDocument(val)) {
        return false;
      }
      return true;
    }, 'Content is required')
    .optional(),
  characterCount: z.number().optional(),
  wordCount: z.number().optional(),
});

export const DeleteCourseLessonSchema = z.object({
  lessonId: z.string().uuid(),
});

export const MoveCourseLessonToChapterSchema = z.object({
  lessonId: z.string().uuid(),
  chapterId: z.union([z.string().uuid(), z.literal('none')]),
});

// Add back the schema with target sequence parameter
export const MoveCourseLessonUpDownSchema = z.object({
  lessonId: z.string().uuid(),
  targetSequence: z.number(),
  direction: z.enum(['up', 'down'], {
    errorMap: () => ({ message: 'Direction must be either "up" or "down"' }),
  }),
});

export type CreateCourseLessonData = z.infer<typeof CreateCourseLessonSchema>;
export type UpdateCourseLessonData = z.infer<typeof UpdateCourseLessonSchema>;
export type LessonFormData = CreateCourseLessonData | UpdateCourseLessonData;

export type LessonData = {
  id: string;
  title: string;
  description: string | null;
  icon: string | null;
  sequenceOrder: number | null;
};

const ALLOWED_VIDEO_DOMAINS = ['youtube.com', 'vimeo.com'] as const;

export const VideoUrlSchema = z
  .string()
  .url()
  .refine((url) => url.startsWith('https://'), {
    message: 'URL must start with https://',
  })
  .refine(
    (url) => {
      const domain = new URL(url).hostname;
      return ALLOWED_VIDEO_DOMAINS.some((d) => domain.includes(d));
    },
    {
      message: `URL must be from ${ALLOWED_VIDEO_DOMAINS.join(' or ')}`,
    },
  );
