'use client';

import { deleteCourseLessonAction } from '../../_lib/server/actions/lessons/delete-course-lesson';
import { SharedDeleteDialog } from '../shared/shared-delete-dialog';

type LessonDeleteDialogProps = {
  lesson: {
    id: string;
    title: string;
  };
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  onDeleted: (lessonId: string) => void;
};

export function DeleteLessonDialog(props: LessonDeleteDialogProps) {
  return (
    <SharedDeleteDialog
      type="lesson"
      item={props.lesson}
      deleteAction={async ({ lessonId }) => {
        const result = await deleteCourseLessonAction({ lessonId });
        return { error: result.error ? new Error(result.message) : undefined };
      }}
      {...props}
    />
  );
}

DeleteLessonDialog.displayName = 'LessonDeleteDialog';
