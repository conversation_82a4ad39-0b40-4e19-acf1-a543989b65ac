'use client';

import { Suspense, memo } from 'react';

import dynamic from 'next/dynamic';

import { SidebarToggle } from '../../sidebar/sidebar-toggle';

const LoadingSpinner = () => (
  <div
    className="flex items-center justify-center space-x-2 p-4"
    role="status"
    aria-label="Loading video"
  >
    <div className="border-primary h-4 w-4 animate-spin rounded-full border-2 border-t-transparent" />
    <span>Preparing your video journey...</span>
  </div>
);

// Dynamically import VideoPlayer with no SSR
const VideoPlayer = dynamic(
  () =>
    import('@kit/ui/dojo/organisms/video-player').then(
      (mod) => mod.VideoPlayer,
    ),
  {
    ssr: false,
    loading: LoadingSpinner,
  },
);

// Memoize the VideoPlayer component
const MemoizedVideoPlayer = memo(VideoPlayer);

// Memoize the SidebarToggle component
const MemoizedSidebarToggle = memo(SidebarToggle);

type VideoContentProps = {
  videoUrl: string;
  shortCutKeysEnabled?: boolean;
};

export function VideoContent({
  videoUrl,
  shortCutKeysEnabled = true,
}: VideoContentProps) {
  return (
    <div className="mb-6">
      <Suspense fallback={<LoadingSpinner />}>
        <MemoizedVideoPlayer
          videoUrl={videoUrl}
          shortCutKeysEnabled={shortCutKeysEnabled}
          additionalControls={<MemoizedSidebarToggle variant="video" />}
        />
      </Suspense>
    </div>
  );
}

VideoContent.displayName = 'VideoContent';
