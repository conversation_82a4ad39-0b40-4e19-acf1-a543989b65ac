'use client';

import { useMemo, useState, useTransition } from 'react';

import { isRedirectError } from 'next/dist/client/components/redirect-error';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';
import { z } from 'zod';

import { Button } from '@kit/ui/button';
import { StatusAlert } from '@kit/ui/dojo/molecules/status-alert';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { If } from '@kit/ui/if';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Trans } from '@kit/ui/trans';

import type {
  CourseChapter,
  CourseLesson,
} from '~/lib/communities/courses/types';

import { MoveCourseLessonToChapterSchema } from '../../_lib/schema/course-lesson.schema';
import { moveCourseLessonToChapterAction } from '../../_lib/server/actions/lessons/move-course-lesson-to-chapter';

export function CourseLessonMoveForm(props: {
  lesson: CourseLesson;
  chapters: CourseChapter[];
  onClose: () => void;
  onSuccess?: (chapter: CourseChapter) => void;
}) {
  const { t } = useTranslation('courses');
  const [error, setError] = useState<string | null>(null);
  const [isPending, startTransition] = useTransition();

  const currentChapter = useMemo(() => {
    return props.chapters?.find(
      (chapter) => chapter.id === props.lesson.chapterId,
    );
  }, [props.chapters, props.lesson.chapterId]);

  const form = useForm<z.infer<typeof MoveCourseLessonToChapterSchema>>({
    defaultValues: {
      lessonId: props.lesson.id,
      chapterId: currentChapter ? currentChapter.id : 'none',
    },
    resolver: zodResolver(MoveCourseLessonToChapterSchema),
  });

  return (
    <Form {...form}>
      <form
        data-test={'move-lesson-form'}
        onSubmit={form.handleSubmit((data) => {
          startTransition(async () => {
            try {
              const action = moveCourseLessonToChapterAction;

              const response = await action({
                lessonId: data.lessonId,
                chapterId: data.chapterId,
              });

              if ('error' in response && response.error) {
                setError('An error occurred while moving the lesson.');
                toast.error('Failed to move lesson. Please try again.');
              } else {
                props.onClose();
                toast.success('Lesson moved successfully');
              }
            } catch (error) {
              if (!isRedirectError(error)) {
                setError('An unexpected error occurred.');
                toast.error('An unexpected error occurred. Please try again.');
              }
            }
          });
        })}
      >
        <div className={'flex flex-col space-y-4'}>
          <If condition={!!error}>
            <StatusAlert
              variant="destructive"
              titleKey="courses:moveLessonToChapterErrorHeading"
              descriptionKey="courses:moveLessonToChapterErrorMessage"
            />
          </If>

          <FormField
            name="chapterId"
            render={({ field }) => {
              return (
                <FormItem>
                  <FormLabel>
                    <Trans i18nKey={'courses:moveLessonToChapterLabel'} />
                  </FormLabel>

                  <FormControl>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <SelectTrigger
                        data-test={'move-lesson-to-chapter-select'}
                        aria-label="Select chapter to move lesson to"
                      >
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem
                          key="none"
                          value="none"
                          data-test="move-lesson-to-chapter-option-none"
                        >
                          <Trans i18nKey={'courses:noChapterSelected'} />
                        </SelectItem>
                        {props.chapters?.map((chapter) => {
                          return (
                            <SelectItem
                              key={chapter.id}
                              value={chapter.id}
                              data-test={`move-lesson-to-chapter-option-${chapter.id}`}
                            >
                              {chapter.title}
                            </SelectItem>
                          );
                        })}
                      </SelectContent>
                    </Select>
                  </FormControl>

                  <FormDescription>
                    <Trans i18nKey={'courses:moveLessonToChapterDescription'} />
                  </FormDescription>

                  <FormMessage />
                </FormItem>
              );
            }}
          />

          <div className={'flex justify-end space-x-2'}>
            <Button
              variant={'outline'}
              type={'button'}
              disabled={isPending}
              onClick={() => {
                props.onClose();
              }}
              data-test="cancel-move-lesson-button"
              aria-label="Cancel moving lesson"
            >
              <Trans i18nKey={'common:cancel'} />
            </Button>

            <Button
              data-test={'confirm-move-lesson-to-chapter-button'}
              disabled={isPending || !form.formState.isValid}
              type="submit"
              aria-label={
                isPending
                  ? t('movingLessonToSelectedChapter')
                  : t('moveLessonToSelectedChapter')
              }
            >
              {isPending ? (
                <Trans i18nKey={'courses:movingLessonToChapterInProgress'} />
              ) : (
                <Trans i18nKey={'courses:movingLessonToChapter'} />
              )}
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
}

CourseLessonMoveForm.displayName = 'CourseLessonMoveForm';
