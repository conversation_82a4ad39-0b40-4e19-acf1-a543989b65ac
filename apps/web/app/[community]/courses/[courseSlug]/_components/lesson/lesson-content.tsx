'use client';

import React, { useCallback, useEffect, useMemo, useState } from 'react';

import { CheckCircle } from 'lucide-react';

import { Button } from '@kit/ui/button';
import {
  type JSONContent,
  TIPTAP_EXTENSIONS,
  generateHTML,
} from '@kit/ui/dojo/organisms/tiptap-editor';

import type {
  CourseChapter,
  CourseLesson,
  LessonContentData,
} from '~/lib/communities/courses/types';

import userContentProgressData from '../../_data/userContentProgress.json';
import { SidebarToggle } from '../sidebar/sidebar-toggle';
import { ExerciseContent } from './content_types/exercise-content';
import { QuizContent } from './content_types/quiz-content';
import { VideoContent } from './content_types/video-content';
import { EditLessonButton } from './edit-lesson-button';
import { EditLessonContent } from './edit-lesson-content';
import { LessonNavigation } from './lesson-navigation';

type LessonContentProps = {
  lesson: CourseLesson | undefined;
  lessons: CourseLesson[] | undefined;
  chapters?: CourseChapter[];
  currentLessonId: string | undefined;
  onLessonSelect: (lessonId: string) => void;
  canEditLesson: boolean;
  isEditing: boolean;
  setIsEditing: (isEditing: boolean) => void;
  setHasUnsavedChanges: (hasChanges: boolean) => void;
  showUnsavedDialog: boolean;
  setShowUnsavedDialog: (show: boolean) => void;
  onProceedToNewLesson: () => void;
  onStayOnCurrentLesson: () => void;
};

export function LessonContent({
  lesson,
  lessons,
  chapters,
  currentLessonId,
  onLessonSelect,
  canEditLesson,
  isEditing,
  setIsEditing,
  setHasUnsavedChanges,
  showUnsavedDialog,
  setShowUnsavedDialog,
  onProceedToNewLesson,
  onStayOnCurrentLesson,
}: LessonContentProps) {
  const [isCompleted, setIsCompleted] = useState<boolean | undefined>(
    undefined,
  );

  useEffect(() => {
    const userProgress = userContentProgressData.find(
      (item) => item.lesson_id === lesson?.id,
    );
    setIsCompleted(!!userProgress?.completed_at);
  }, [lesson?.id]);

  const lessonText = useMemo(() => {
    if (!lesson?.lessonText || Object.keys(lesson.lessonText).length === 0) {
      return '';
    }

    try {
      if (typeof lesson.lessonText === 'object' && lesson.lessonText !== null) {
        return generateHTML(
          lesson.lessonText as JSONContent,
          TIPTAP_EXTENSIONS,
        );
      }
      return '';
    } catch (error) {
      console.error('Error generating HTML:', error);
      return '';
    }
  }, [lesson?.lessonText]);

  const contentData = useMemo(() => {
    if (!lesson?.contentData || Object.keys(lesson.contentData).length === 0) {
      return null;
    }

    try {
      if (typeof lesson.contentData === 'string') {
        return JSON.parse(lesson.contentData) as LessonContentData;
      }
      return lesson.contentData as LessonContentData;
    } catch (error) {
      console.error('Error parsing content data:', error);
      return null;
    }
  }, [lesson?.contentData]);

  const renderLessonContentType = useCallback(() => {
    if (!lesson?.contentType || !contentData) return null;

    switch (lesson.contentType) {
      case 'video':
        return (
          <VideoContent
            videoUrl={contentData?.videoUrl ?? ''}
            shortCutKeysEnabled={true}
          />
        );
      case 'quiz':
        return <QuizContent questions={contentData?.questions ?? []} />;
      case 'exercise':
        return (
          <ExerciseContent
            instructions={contentData?.exercise?.instructions ?? ''}
            starter_code={contentData?.exercise?.starter_code ?? ''}
            solution={contentData?.exercise?.solution ?? ''}
          />
        );
      case 'none':
        return null;
      default:
        return null;
    }
  }, [lesson?.contentType, contentData]);

  const handleEditorClose = useCallback(
    (showingEditor: boolean) => {
      setIsEditing(showingEditor);
      setHasUnsavedChanges(false);
    },
    [setIsEditing, setHasUnsavedChanges],
  );

  return isEditing ? (
    <EditLessonContent
      lesson={lesson}
      onEditorClose={handleEditorClose}
      setHasUnsavedChanges={setHasUnsavedChanges}
      showUnsavedDialog={showUnsavedDialog}
      setShowUnsavedDialog={setShowUnsavedDialog}
      onProceedToNewLesson={onProceedToNewLesson}
      onStayOnCurrentLesson={onStayOnCurrentLesson}
    />
  ) : (
    <div
      className="space-y-4 rounded-2xl bg-white p-2 dark:bg-gray-800"
      data-test={`lesson-content-container-${lesson?.id}`}
    >
      <div className="relative flex items-center justify-end gap-2">
        <div className="absolute top-4 left-4 z-10 mb-2 flex">
          <SidebarToggle variant="handle" />
        </div>
        {!isEditing && (
          <>
            <EditLessonButton
              lessonId={lesson?.id ?? ''}
              canEditLesson={canEditLesson}
              onEdit={() => setIsEditing(true)}
            />
            <Button
              variant={isCompleted ? 'default' : 'outline'}
              size="icon"
              onClick={() => setIsCompleted(!isCompleted)}
              className="cursor-pointer gap-2 rounded-full"
              data-test="mark-lesson-complete-toggle"
              aria-label={
                isCompleted
                  ? 'Mark lesson as incomplete'
                  : 'Mark lesson as complete'
              }
            >
              <CheckCircle className="h-4 w-4" />
            </Button>
          </>
        )}
      </div>
      <div className="space-y-2 pt-2">
        {!isEditing && (
          <div className="flex items-center gap-2">
            <span className="mr-2 text-3xl">{lesson?.icon}</span>
            <h1 className="text-foreground text-2xl font-bold">
              {lesson?.title ?? 'Untitled Lesson'}
            </h1>
          </div>
        )}

        {renderLessonContentType()}

        {lessonText && (
          <div className="space-y-4">
            <h2 className="text-foreground text-lg font-semibold">Lesson:</h2>
            <div
              className="text-muted-foreground"
              dangerouslySetInnerHTML={{
                __html: lessonText,
              }}
            />
          </div>
        )}

        <LessonNavigation
          lessons={lessons ?? []}
          chapters={chapters ?? []}
          currentLessonId={currentLessonId ?? ''}
          onLessonSelect={onLessonSelect}
        />
      </div>
    </div>
  );
}

LessonContent.displayName = 'LessonContent';
