import {
  ChangeEvent,
  FC,
  FormEvent,
  useCallback,
  useRef,
  useState,
} from 'react';

import { z } from 'zod';

import { Button } from '@kit/ui/button';
import { StatusAlert } from '@kit/ui/dojo/molecules/status-alert';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';

import { VideoUrlSchema } from '../../_lib/schema/course-lesson.schema';

/**
 * Props for the VideoEditBlock component
 * @type VideoEditBlockProps
 * @property {() => void} close - The function to close the VideoEditBlock
 */

export type VideoEditBlockProps = {
  videoUrl?: string;
  close: () => void;
  onSetVideo: (videoUrl: string) => void;
};

/**
 * Video edit block for the Video extension
 * @param {VideoEditBlockProps} props - The props for the VideoEditBlock
 * @returns {JSX.Element} The VideoEditBlock component
 */

export const VideoEditBlock: FC<VideoEditBlockProps> = ({
  videoUrl,
  close,
  onSetVideo,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [link, setLink] = useState(videoUrl);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const handleClick = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const handleFile = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      const files = e.target.files;
      if (!files?.length) return;

      const insertVideos = () => {
        const contentBucket = [];
        const filesArray = Array.from(files);

        for (const file of filesArray) {
          // Example: Validate file type and size
          if (!file.type.startsWith('video/')) {
            continue;
          }

          contentBucket.push({ src: file });
        }
      };

      void insertVideos();
      close();
    },
    [close],
  );

  const handleSubmit = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      e.stopPropagation();

      try {
        VideoUrlSchema.parse(link ?? '');
        onSetVideo(link ?? '');
        close();
      } catch (error) {
        if (error instanceof z.ZodError) {
          setErrorMessage(error.errors[0]?.message ?? 'Invalid video URL');
        } else {
          setErrorMessage('An error occurred. Please try again.');
        }
      }
    },
    [link, close, onSetVideo],
  );

  const handleInputChange = useCallback((e: ChangeEvent<HTMLInputElement>) => {
    setLink(e.target.value);
    setErrorMessage(null);
  }, []);

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-1">
        <Label htmlFor="video-link">Attach a video link</Label>
        <div className="flex">
          <Input
            id="video-link"
            type="url"
            required
            placeholder="https://example.com"
            value={link}
            className="grow"
            onChange={handleInputChange}
            data-test="video-link-input"
            aria-label="Video link"
          />
          <Button
            type="submit"
            className="ml-2"
            data-test="submit-video-link-button"
            aria-label="Submit video link"
          >
            Submit
          </Button>
        </div>
        {errorMessage && (
          <StatusAlert
            variant="destructive"
            titleKey="courses:videoEditBlockErrorHeading"
            descriptionKey="courses:videoEditBlockErrorMessage"
          />
        )}
      </div>
      <Button
        type="button"
        className="w-full"
        onClick={handleClick}
        data-test="upload-video-computer-button"
        aria-label="Upload video from computer"
      >
        Upload from your computer
      </Button>
      <input
        type="file"
        accept="video/*"
        ref={fileInputRef}
        multiple
        className="hidden"
        onChange={handleFile}
      />
    </form>
  );
};

VideoEditBlock.displayName = 'VideoEditBlock';
