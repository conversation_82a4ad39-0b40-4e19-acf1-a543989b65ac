import { useState } from 'react';

import { Video } from 'lucide-react';

import { Button } from '@kit/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@kit/ui/dialog';

import { VideoEditBlock } from './video-edit-block';

/**
 * Props for the VideoEditDialog component
 * @type VideoEditDialogProps
 * @property {string} videoUrl - The URL of the video
 * @property {(videoUrl: string) => void} onSetVideo - The function to set the video
 */

type VideoEditDialogProps = {
  videoUrl?: string;
  onSetVideo: (videoUrl: string) => void;
};

/**
 * Video edit dialog for adding a video to a lesson
 * @param {VideoEditDialogProps} props - The props for the VideoEditDialog
 * @param {string} videoUrl - The URL of the video
 * @param {(videoUrl: string) => void} onSetVideo - The function to set the video
 * @returns {JSX.Element} The VideoEditDialog component
 */

export const VideoEditDialog = ({
  videoUrl = '',
  onSetVideo,
}: VideoEditDialogProps) => {
  const [open, setOpen] = useState(false);

  // Validate the video URL
  const isValidOrEmptyUrl = (url: string) => {
    if (!url) return true;
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };
  const validVideoUrl = isValidOrEmptyUrl(videoUrl);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          data-test="video-edit-dialog-trigger"
          aria-label="Add or edit video"
        >
          <Video className="size-5" />
        </Button>
      </DialogTrigger>
      <DialogContent
        className="sm:max-w-lg"
        data-test="video-edit-dialog"
      >
        <DialogHeader>
          <DialogTitle>Select video</DialogTitle>
          <DialogDescription className="sr-only">
            Upload a video from your computer
          </DialogDescription>
        </DialogHeader>
        {validVideoUrl ? (
          <VideoEditBlock
            videoUrl={videoUrl}
            close={() => setOpen(false)}
            onSetVideo={onSetVideo}
          />
        ) : (
          <p className="text-red-500">Invalid video URL</p>
        )}
      </DialogContent>
    </Dialog>
  );
};

VideoEditDialog.displayName = 'VideoEditDialog';
