import React, { useState } from 'react';

import { Button } from '@kit/ui/button';
import { Textarea } from '@kit/ui/textarea';

import type { ExerciseContent } from '~/lib/communities/courses/types';

export function ExerciseContent({
  instructions,
  starter_code,
  solution,
}: ExerciseContent) {
  const [code, setCode] = useState(starter_code);
  const [showSolution, setShowSolution] = useState(false);

  return (
    <div className="mb-6 space-y-6">
      <div className="prose dark:prose-invert max-w-none">
        <h3 className="text-lg font-semibold">Instructions</h3>
        <p>{instructions}</p>
      </div>
      <div>
        <h3 className="mb-2 text-lg font-semibold">Your Code</h3>
        <Textarea
          value={code}
          onChange={(e) => setCode(e.target.value)}
          className="font-mono"
          rows={10}
          data-test="exercise-code-input"
          aria-label="Enter your code for the exercise"
        />
      </div>
      <div className="flex justify-between">
        <Button
          onClick={() => setShowSolution(!showSolution)}
          data-test="toggle-solution-button"
          aria-label={showSolution ? 'Hide solution' : 'Show solution'}
        >
          {showSolution ? 'Hide Solution' : 'Show Solution'}
        </Button>
        <Button data-test="submit-exercise-button" aria-label="Submit exercise">
          Submit
        </Button>
      </div>
      {showSolution && (
        <div>
          <h3 className="mb-2 text-lg font-semibold">Solution</h3>
          <pre className="overflow-x-auto rounded-md bg-gray-100 p-4 dark:bg-gray-800">
            <code>{solution}</code>
          </pre>
        </div>
      )}
    </div>
  );
}

ExerciseContent.displayName = 'ExerciseContent';
