'use client';

import React, { useCallback, useEffect, useMemo, useState } from 'react';

import { ChevronLeft, ChevronRight, Loader2 } from 'lucide-react';

import { Button } from '@kit/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@kit/ui/tooltip';
import { getShortcutSymbol } from '@kit/ui/utils';

import type {
  CourseChapter,
  CourseLesson,
} from '~/lib/communities/courses/types';

import { sortCourseContent } from '../../_lib/utils/sort-lessons-by-sequence';

type LessonNavigationProps = {
  lessons: readonly CourseLesson[];
  chapters: readonly CourseChapter[];
  currentLessonId: string;
  onLessonSelect: (lessonId: string) => void;
};

export function LessonNavigation({
  lessons,
  chapters,
  currentLessonId,
  onLessonSelect,
}: LessonNavigationProps) {
  const [isNavigating, setIsNavigating] = useState(false);

  // Sort lessons and calculate navigation state
  const sortedLessons = useMemo(
    () =>
      sortCourseContent(lessons, chapters, {
        lessonsOnly: true,
      }) as CourseLesson[],
    [lessons, chapters],
  );

  const { currentIndex, prevLesson, nextLesson } = useMemo(
    () => ({
      currentIndex: sortedLessons.findIndex(
        (lesson) => lesson.id === currentLessonId,
      ),
      get prevLesson() {
        return currentIndex > 0 ? sortedLessons[currentIndex - 1] : null;
      },
      get nextLesson() {
        return currentIndex < sortedLessons.length - 1
          ? sortedLessons[currentIndex + 1]
          : null;
      },
    }),
    [sortedLessons, currentLessonId],
  );

  const handleLessonSelect = useCallback(
    (lessonId: string) => {
      setIsNavigating(true);
      onLessonSelect(lessonId);
      setIsNavigating(false);
    },
    [onLessonSelect],
  );

  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      // Ignore shortcuts when user is typing in an input
      if (
        e.target instanceof HTMLInputElement ||
        e.target instanceof HTMLTextAreaElement
      ) {
        return;
      }

      if (e.altKey && e.key === 'ArrowRight' && nextLesson && !isNavigating) {
        e.preventDefault();
        handleLessonSelect(nextLesson.id);
      } else if (
        e.altKey &&
        e.key === 'ArrowLeft' &&
        prevLesson &&
        !isNavigating
      ) {
        e.preventDefault();
        handleLessonSelect(prevLesson.id);
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [nextLesson, prevLesson, isNavigating, handleLessonSelect]);

  return (
    <div className="mt-8 flex items-center justify-between border-t p-4 pt-4 dark:border-gray-700">
      <TooltipProvider>
        {prevLesson && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                className="text-foreground hover:bg-accent hover:text-accent-foreground flex cursor-pointer items-center gap-2"
                onClick={() => handleLessonSelect(prevLesson.id)}
                aria-label={`Previous lesson: ${prevLesson.title}`}
                disabled={isNavigating}
                data-test="prev-lesson-button"
              >
                {isNavigating ? (
                  <Loader2
                    className="h-4 w-4 animate-spin"
                    role="status"
                    aria-label="Loading lesson"
                  />
                ) : (
                  <ChevronLeft className="h-4 w-4" />
                )}
                <div className="flex flex-col items-start">
                  <span className="text-muted-foreground text-xs">
                    Previous
                  </span>
                  <span className="text-sm font-medium">
                    {prevLesson.title}
                  </span>
                </div>
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Previous lesson ({getShortcutSymbol('alt')} + ←)</p>
            </TooltipContent>
          </Tooltip>
        )}
        {nextLesson && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                className="text-foreground hover:bg-accent hover:text-accent-foreground ml-auto flex cursor-pointer items-center gap-2"
                onClick={() => handleLessonSelect(nextLesson.id)}
                aria-label={`Next lesson: ${nextLesson.title}`}
                disabled={isNavigating}
                data-test="next-lesson-button"
              >
                <div className="flex flex-col items-end">
                  <span className="text-muted-foreground text-xs">Next</span>
                  <span className="text-sm font-medium">
                    {nextLesson.title}
                  </span>
                </div>
                {isNavigating ? (
                  <Loader2
                    className="h-4 w-4 animate-spin"
                    role="status"
                    aria-label="Loading lesson"
                  />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Next lesson ({getShortcutSymbol('alt')} + →)</p>
            </TooltipContent>
          </Tooltip>
        )}
      </TooltipProvider>
    </div>
  );
}

LessonNavigation.displayName = 'LessonNavigation';
