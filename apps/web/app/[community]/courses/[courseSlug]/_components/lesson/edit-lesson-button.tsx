import React from 'react';

import { Pencil } from 'lucide-react';

import { But<PERSON> } from '@kit/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@kit/ui/tooltip';
import { getShortcutKey } from '@kit/ui/utils';

export type EditLessonButtonProps = {
  lessonId: string;
  canEditLesson: boolean;
  onEdit: (lessonId: string) => void;
};

export const EditLessonButton: React.FC<EditLessonButtonProps> = (props) => {
  const { lessonId, canEditLesson, onEdit } = props;

  // TODO: Implement shortcut key

  return (
    canEditLesson && (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              className="cursor-pointer rounded-full"
              size="icon"
              onClick={() => onEdit(lessonId)}
              data-test={`edit-lesson-button-${lessonId}`}
              aria-label="Edit lesson"
            >
              <Pencil className="h-4 w-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Edit Lesson ({getShortcutKey('mod').symbol}+E)</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    )
  );
};

EditLessonButton.displayName = 'EditLessonButton';
