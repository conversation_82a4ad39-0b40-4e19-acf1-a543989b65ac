'use client';

import React, {
  startTransition,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';

import { Trash } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';
import { z } from 'zod';

import { Button } from '@kit/ui/button';
import { CharacterCountDisplay } from '@kit/ui/dojo/molecules/character-count-display';
import { EmojiPicker } from '@kit/ui/dojo/molecules/emoji-picker';
import { UnsavedChangesDialog } from '@kit/ui/dojo/molecules/unsaved-changes-dialog';
import {
  type Content,
  type Editor,
  type JSONContent,
  TIPTAP_EXTENSIONS,
  TipTapEditor,
  generateHTML,
  useTipTapCharacterCount,
} from '@kit/ui/dojo/organisms/tiptap-editor';
import { Input } from '@kit/ui/input';
import { TooltipProvider } from '@kit/ui/tooltip';

import { useCommunityWorkspace } from '~/lib/communities/community/hooks/use-community-workspace';
import type {
  CourseLesson,
  LessonContentData,
} from '~/lib/communities/courses/types';
import {
  createImageDeleteHandler,
  createImageUploadHandler,
} from '~/lib/images/handler/image-handlers';

import { UpdateCourseLessonSchema } from '../../_lib/schema/course-lesson.schema';
import { updateCourseLessonAction } from '../../_lib/server/actions/lessons/update-course-lesson';
import { ExerciseContent } from './content_types/exercise-content';
import { QuizContent } from './content_types/quiz-content';
import { VideoContent } from './content_types/video-content';
import { VideoEditDialog } from './video-edit-dialog';

const LESSON_CONTENT_CHARACTER_LIMIT = 5000;

type LessonContentEditProps = {
  lesson: CourseLesson | undefined;
  onEditorClose: (showingEditor: boolean) => void;
  setHasUnsavedChanges: (hasChanges: boolean) => void;
  showUnsavedDialog: boolean;
  setShowUnsavedDialog: (show: boolean) => void;
  onProceedToNewLesson: () => void;
  onStayOnCurrentLesson: () => void;
};

export function EditLessonContent({
  lesson,
  onEditorClose,
  setHasUnsavedChanges,
  showUnsavedDialog,
  setShowUnsavedDialog,
  onProceedToNewLesson,
  onStayOnCurrentLesson,
}: LessonContentEditProps) {
  const { t } = useTranslation('courses');
  const [title, setTitle] = useState<string>(() => lesson?.title ?? '');
  const { community } = useCommunityWorkspace();
  const [lessonIcon, setLessonIcon] = useState<string>(
    () => lesson?.icon ?? '',
  );

  // State to store the editor instance
  const [editor, setEditor] = useState<Editor | null>(null);

  // Use the character count hook to get counts from the editor
  const { characterCount: rawCharacterCount, wordCount: rawWordCount } =
    useTipTapCharacterCount(editor);

  // defend against the void
  const characterCount = rawCharacterCount ?? 0;
  const wordCount = rawWordCount ?? 0;

  const [content, setContentData] = useState<LessonContentData | null>(() => {
    try {
      if (!lesson?.contentData) {
        return null;
      }

      const parsedContent =
        typeof lesson.contentData === 'string'
          ? JSON.parse(lesson.contentData)
          : lesson.contentData;

      return parsedContent as LessonContentData;
    } catch {
      return null;
    }
  });

  const [lessonTextContent, setLessonTextContent] =
    useState<JSONContent | null>(() => {
      if (!lesson?.lessonText) return null;

      try {
        const lessonTextContent =
          typeof lesson.lessonText === 'string'
            ? JSON.parse(lesson.lessonText)
            : lesson.lessonText;

        // Check if it's an empty object or doesn't have required JSONContent structure
        if (
          !lessonTextContent ||
          Object.keys(lessonTextContent).length === 0 ||
          !lessonTextContent.type
        ) {
          return null;
        }

        return lessonTextContent as JSONContent;
      } catch (error) {
        console.error('Error parsing lesson text in effect:', error);
        return null;
      }
    });

  useEffect(() => {
    if (lesson) {
      setTitle(lesson.title);
      setLessonIcon(lesson.icon ?? '');

      if (!lesson.lessonText) {
        setLessonTextContent(null);
        return;
      }

      try {
        const lessonTextContent =
          typeof lesson.lessonText === 'string'
            ? JSON.parse(lesson.lessonText)
            : lesson.lessonText;

        // Check if it's an empty object or doesn't have required JSONContent structure
        if (
          !lessonTextContent ||
          Object.keys(lessonTextContent).length === 0 ||
          !lessonTextContent.type
        ) {
          setLessonTextContent(null);
          return;
        }

        setLessonTextContent(lessonTextContent as JSONContent);
      } catch (error) {
        console.error('Error parsing lesson text in effect:', error);
        setLessonTextContent(null);
      }
    }
  }, [lesson]);

  const contentData = useMemo(() => {
    try {
      return content
        ? typeof content === 'string'
          ? (JSON.parse(content) as LessonContentData)
          : content
        : null;
    } catch {
      return null;
    }
  }, [content]);

  const lessonText = useMemo(() => {
    if (
      !lessonTextContent ||
      Object.keys(lessonTextContent).length === 0 ||
      !lessonTextContent.type // Check for valid JSONContent structure
    ) {
      return '';
    }

    try {
      return generateHTML(lessonTextContent, TIPTAP_EXTENSIONS);
    } catch (error) {
      toast.error('Error generating Lesson Text HTML');
      console.error('Error generating HTML:', error);
      return '';
    }
  }, [lessonTextContent]);

  const [contentType, setContentType] = useState<string>(
    () => lesson?.contentType ?? 'none',
  );

  const renderLessonContentType = useCallback(() => {
    if (!lesson || !contentData) return null;

    switch (contentType) {
      case 'video':
        return (
          <VideoContent
            videoUrl={contentData?.videoUrl ?? ''}
            shortCutKeysEnabled={false}
          />
        );
      case 'quiz':
        return <QuizContent questions={contentData?.questions ?? []} />;
      case 'exercise':
        return (
          <ExerciseContent
            instructions={contentData?.exercise?.instructions ?? ''}
            starter_code={contentData?.exercise?.starter_code ?? ''}
            solution={contentData?.exercise?.solution ?? ''}
          />
        );
      case 'none':
        return null;
      default:
        return <p>Unsupported content type</p>;
    }
  }, [contentType, contentData, lesson]);

  const handleContentChange = (content: Content) => {
    setLessonTextContent(content as JSONContent);
    setHasUnsavedChanges(true);
  };

  const handleCancel = () => {
    onEditorClose(false);
    setTitle(lesson?.title ?? '');
    setLessonTextContent(null);
    setContentType(lesson?.contentType ?? 'none');

    try {
      const parsedContent = lesson?.contentData
        ? typeof lesson.contentData === 'string'
          ? JSON.parse(lesson.contentData)
          : lesson.contentData
        : null;
      setContentData(parsedContent as LessonContentData);
    } catch {
      setContentData(null);
    }

    setHasUnsavedChanges(false);
  };

  const handleSetVideo = (videoUrl: string) => {
    if (!lesson) return;

    const newContentData = {
      video_url: videoUrl,
    };

    // Update the lesson's content_data
    lesson.contentData = JSON.stringify(newContentData);

    // Update local state and mark as unsaved
    setContentData(newContentData as LessonContentData);
    setContentType('video');
    setHasUnsavedChanges(true);
  };

  const handleSaveLesson = () => {
    const contentJson = content ? JSON.stringify(content) : null;
    const lessonTextJson = lessonTextContent
      ? JSON.stringify(lessonTextContent)
      : null;

    try {
      UpdateCourseLessonSchema.parse({
        lessonId: lesson?.id ?? '',
        title: title,
        icon: lessonIcon,
        lessonText: lessonTextJson,
        contentData: contentJson,
        contentType: contentType,
      });

      startTransition(async () => {
        await updateCourseLessonAction({
          lessonId: lesson?.id ?? '',
          title: title,
          icon: lessonIcon,
          lessonText: lessonTextJson ?? undefined,
          contentData: contentJson ?? undefined,
          contentType: contentType as 'video' | 'quiz' | 'exercise' | 'none',
          characterCount: characterCount,
          wordCount: wordCount,
        });
        // Reset form and state on success
        toast.success('Lesson saved successfully');
        setTitle('');
        setLessonTextContent(null);
        setContentData(null);
        setHasUnsavedChanges(false);
        setShowUnsavedDialog(false);
        onEditorClose(false);
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        toast.error(error.errors[0]?.message ?? 'An error occurred');
      } else {
        console.error('Save error:', error);
        toast.error('An error occurred while saving the lesson');
      }
    }
  };

  const handleRemoveVideo = () => {
    setContentData({ videoUrl: undefined });
    setContentType('none');
    setHasUnsavedChanges(true);
    toast.success('Video removed successfully');
  };

  // Define storage context parts
  const featureDir = 'courses';
  // Use lesson ID as storageDirPath (optional, so handle potential undefined)
  const storageDirPath = lesson?.id;

  const handleImageUpload = createImageUploadHandler({
    featureDir,
    storageDirPath,
    communityId: community.id,
  });

  const handleImageDelete = createImageDeleteHandler({
    featureDir,
    storageDirPath,
    communityId: community.id,
  });

  return (
    <>
      <div className="flex items-center justify-between gap-2">
        <EmojiPicker
          onChange={(icon) => {
            setLessonIcon(icon);
            setHasUnsavedChanges(true);
          }}
          currentIcon={lessonIcon}
          data-test="lesson-icon-picker"
          aria-label="Choose lesson icon"
        />
        <Input
          placeholder={t('lessonTitle')}
          value={title}
          onChange={(e) => {
            setTitle(e.target.value);
            setHasUnsavedChanges(true);
          }}
          className="text-foreground my-4 rounded-xl bg-white text-2xl font-bold dark:bg-gray-900"
          data-test="lesson-title-input"
          aria-label="Lesson title"
        />
      </div>
      {renderLessonContentType()}
      <TooltipProvider>
        <TipTapEditor
          value={lessonText}
          onChange={handleContentChange}
          onImageUpload={handleImageUpload}
          onImageDelete={handleImageDelete}
          className="border-input focus-within:border-primary shadow-xs grow rounded-xl border"
          editorContentClassName="p-5 grow"
          editorClassName="focus:outline-hidden h-full"
          output="json"
          placeholder={t('lessonTextPlaceholder')}
          autofocus={true}
          editable={true}
          contextType="courseLesson"
          characterLimit={LESSON_CONTENT_CHARACTER_LIMIT}
          onReady={setEditor}
          onDestroy={() => setEditor(null)}
          data-test="lesson-content-edit-editor"
        />
      </TooltipProvider>
      <div className="mt-2 flex justify-between gap-2">
        <div className="flex gap-2">
          <VideoEditDialog
            videoUrl={contentData?.videoUrl}
            onSetVideo={handleSetVideo}
          />
          {contentData?.videoUrl && (
            <Button
              variant="outline"
              onClick={handleRemoveVideo}
              data-test="remove-video-button"
              aria-label="Remove video from lesson"
            >
              <Trash className="mr-2 size-5" />
              Remove video
            </Button>
          )}
        </div>
        <div className="flex gap-2">
          <CharacterCountDisplay
            characterCount={characterCount}
            wordCount={wordCount}
            characterLimit={LESSON_CONTENT_CHARACTER_LIMIT}
            className="ml-2"
          />
          <Button
            onClick={handleSaveLesson}
            data-test="save-lesson-button"
            aria-label="Save lesson"
          >
            Save
          </Button>
          <Button
            variant="outline"
            onClick={handleCancel}
            data-test="cancel-edit-lesson-button"
            aria-label="Cancel editing lesson"
          >
            Cancel
          </Button>
        </div>
      </div>

      <UnsavedChangesDialog
        open={showUnsavedDialog}
        onOpenChange={setShowUnsavedDialog}
        onProceed={onProceedToNewLesson}
        onStay={onStayOnCurrentLesson}
        data-test="lesson-content-edit-unsaved-dialog"
      />
    </>
  );
}

EditLessonContent.displayName = 'LessonContentEdit';
