'use client';

import React, { ReactNode, useCallback, useTransition } from 'react';

import { useRouter, useSearchParams } from 'next/navigation';

import { NotebookPen } from 'lucide-react';
import { toast } from 'sonner';

import { Button } from '@kit/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@kit/ui/tooltip';

import { createCourseLessonAction } from '../../_lib/server/actions/lessons/create-course-lesson';

export type LessonCreateButtonProps = {
  courseSlug: string;
  variant?: 'default' | 'icon';
  className?: string;
  icon?: ReactNode;
  containerClassName?: string;
};

export const CreateLessonButton: React.FC<LessonCreateButtonProps> = ({
  courseSlug,
  variant = 'icon',
  className = '',
  icon = (
    <NotebookPen
      className={variant === 'default' ? 'mr-2 h-5 w-5' : 'h-4 w-4'}
    />
  ),
  containerClassName = '',
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();

  const handleCreateLesson = useCallback(() => {
    startTransition(async () => {
      try {
        const result = await createCourseLessonAction({
          courseSlug: courseSlug,
          title: 'New Lesson',
        });

        if (result.success && result.data?.id) {
          // Update URL with new lesson ID
          const params = new URLSearchParams(searchParams);
          params.set('lessonId', result.data.id);
          router.push(`?${params.toString()}`);
          toast.success('Lesson created successfully');
        }
      } catch (error) {
        console.error('Failed to create lesson:', error);
        toast.error('Failed to create lesson. Please try again.');
      }
    });
  }, [courseSlug, router, searchParams]);

  // Icon variant (small rounded button with tooltip)
  if (variant === 'icon') {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              size="icon"
              className={`h-8 w-8 cursor-pointer rounded-full ${className}`}
              onClick={handleCreateLesson}
              disabled={isPending}
              data-test="create-lesson-icon-button"
              aria-label="Create lesson"
            >
              {icon}
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Create Lesson</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  // Default variant (regular button)
  return (
    <div
      className={`flex h-full w-full items-center justify-center ${containerClassName}`}
    >
      <Button
        disabled={isPending}
        onClick={handleCreateLesson}
        className={`px-8 py-4 ${className}`}
        data-test="create-lesson-text-button"
        aria-label="Create a lesson"
      >
        {icon}
        {isPending ? 'Creating lesson...' : 'Create a Lesson'}
      </Button>
    </div>
  );
};

CreateLessonButton.displayName = 'LessonCreateButton';
