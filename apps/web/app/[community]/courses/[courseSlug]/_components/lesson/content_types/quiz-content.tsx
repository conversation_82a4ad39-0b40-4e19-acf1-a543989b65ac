import React, { useState } from 'react';

import { Button } from '@kit/ui/button';
import { Label } from '@kit/ui/label';
import { RadioGroup, RadioGroupItem } from '@kit/ui/radio-group';

import type { Question } from '~/lib/communities/courses/types';

type QuizContentProps = {
  questions: Question[];
};

export function QuizContent({ questions }: QuizContentProps) {
  const [answers, setAnswers] = useState<Record<string, string>>({});
  const [showResults, setShowResults] = useState(false);

  const handleAnswerChange = (questionId: string, answer: string) => {
    setAnswers((prev) => ({ ...prev, [questionId]: answer }));
  };

  const handleSubmit = () => {
    setShowResults(true);
  };

  const calculateScore = () => {
    let correct = 0;
    questions.forEach((question) => {
      if (answers[question.id] === question.correctAnswer) {
        correct++;
      }
    });
    return `${correct}/${questions.length}`;
  };

  return (
    <div className="mb-6 space-y-6">
      {questions.map((question) => (
        <div key={question.id} className="space-y-4">
          <h3 className="text-lg font-semibold">{question.question}</h3>
          <RadioGroup
            onValueChange={(value) => handleAnswerChange(question.id, value)}
            value={answers[question.id]}
            data-test={`quiz-question-${question.id}-options`}
            aria-label={`Options for question: ${question.question}`}
          >
            {question.options.map((option) => (
              <div
                key={`${question.id}-${option}`}
                className="flex items-center space-x-2"
              >
                <RadioGroupItem
                  value={option}
                  id={`${question.id}-${option}`}
                  data-test={`quiz-question-${question.id}-option-${option}`}
                  aria-label={option}
                />
                <Label htmlFor={`${question.id}-${option}`}>{option}</Label>
              </div>
            ))}
          </RadioGroup>
          {showResults && (
            <p
              className={
                answers[question.id] === question.correctAnswer
                  ? 'text-green-500'
                  : 'text-red-500'
              }
            >
              {answers[question.id] === question.correctAnswer
                ? 'Correct!'
                : `Incorrect. The correct answer is: ${question.correctAnswer}`}
            </p>
          )}
        </div>
      ))}
      {!showResults && (
        <Button
          onClick={handleSubmit}
          data-test="submit-quiz-button"
          aria-label="Submit quiz answers"
        >
          Submit Answers
        </Button>
      )}
      {showResults && (
        <p className="font-semibold">Your score: {calculateScore()}</p>
      )}
    </div>
  );
}

QuizContent.displayName = 'QuizContent';
