'use client';

import React from 'react';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@kit/ui/dialog';
import { Trans } from '@kit/ui/trans';

import type {
  CourseChapter,
  CourseLesson,
} from '~/lib/communities/courses/types';

import { CourseLessonMoveForm } from './course-lesson-move-form';

export function MoveLessonToChapterDialog(
  props: React.PropsWithChildren<{
    lesson: CourseLesson;
    chapters: CourseChapter[];
    isOpen: boolean;
    setIsOpen: (isOpen: boolean) => void;
    onSuccess?: (chapter: CourseChapter) => void;
  }>,
) {
  return (
    <Dialog open={props.isOpen} onOpenChange={props.setIsOpen}>
      <DialogContent data-test="move-lesson-to-chapter-dialog">
        <DialogHeader>
          <DialogTitle>
            <Trans i18nKey={'courses:moveLessonModalHeading'} />
          </DialogTitle>

          <DialogDescription>
            <Trans i18nKey={'courses:moveLessonModalDescription'} />
          </DialogDescription>
        </DialogHeader>

        <CourseLessonMoveForm
          lesson={props.lesson}
          chapters={props.chapters}
          onClose={() => props.setIsOpen(false)}
          onSuccess={props.onSuccess}
        />
      </DialogContent>
    </Dialog>
  );
}

MoveLessonToChapterDialog.displayName = 'MoveLessonToChapterDialog';
