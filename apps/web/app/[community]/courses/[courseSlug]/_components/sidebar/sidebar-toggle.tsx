import React, { useCallback } from 'react';

import { ChevronLeft, ChevronRight, Minimize, TvMinimal } from 'lucide-react';

import { Button } from '@kit/ui/button';
import { useSidebar } from '@kit/ui/shadcn-sidebar';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@kit/ui/tooltip';
import { getShortcutKey } from '@kit/ui/utils';

export type SidebarToggleProps = {
  variant?: 'handle' | 'default' | 'video';
  title?: string;
  'aria-label'?: string;
};

export const SidebarToggle: React.FC<SidebarToggleProps> = (props) => {
  const { variant = 'default' } = props;
  const { open, setOpen } = useSidebar();

  const tooltipText = `Toggle Sidebar (${getShortcutKey('mod').symbol}+B)`;

  // Prevent extra rerenders by using useCallback
  const handleToggle = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      // Toggle the sidebar context state
      setOpen(!open);
    },
    [open, setOpen],
  );

  if (variant === 'handle') {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild className="cursor-pointer">
            <Button
              onClick={handleToggle}
              size="icon"
              className="absolute top-1/2 left-[-12px] z-9999 h-8 w-8 -translate-y-1/2 rounded-full"
              data-test="sidebar-toggle-handle"
              aria-label={open ? 'Close sidebar' : 'Open sidebar'}
            >
              {!open ? (
                <ChevronRight className="h-4 w-4" />
              ) : (
                <ChevronLeft className="h-4 w-4" />
              )}
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>{tooltipText}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  if (variant === 'video') {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              onClick={handleToggle}
              variant="ghost"
              size="icon"
              className="rounded-full"
              data-test="sidebar-toggle-video"
              aria-label={open ? 'Hide lesson details' : 'Show lesson details'}
            >
              {!open ? (
                <Minimize className="h-4 w-4" />
              ) : (
                <TvMinimal className="h-4 w-4" />
              )}
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>{tooltipText}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  // For default variant, wrap SidebarTrigger with custom handling
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            onClick={handleToggle}
            size="icon"
            className="h-8 w-8 rounded-full"
            data-test="sidebar-toggle-default"
              aria-label={open ? 'Close sidebar' : 'Open sidebar'}
          >
            {!open ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronLeft className="h-4 w-4" />
            )}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>{tooltipText}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

SidebarToggle.displayName = 'SidebarToggle';
