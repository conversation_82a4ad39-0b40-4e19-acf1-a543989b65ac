'use client';

import React from 'react';

import { ChevronDown, ChevronLeft } from 'lucide-react';

import { SidebarGroupLabel, useSidebar } from '@kit/ui/shadcn-sidebar';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@kit/ui/tooltip';
import { cn } from '@kit/ui/utils';

import type { CourseChapter } from '~/lib/communities/courses/types';

type ChapterLabelProps = {
  chapter: Pick<CourseChapter, 'id' | 'title' | 'icon'>;
  isExpanded: boolean;
  onClick: () => void;
  onKeyDown: (e: React.KeyboardEvent) => void;
};

export function ChapterLabel({
  chapter,
  isExpanded,
  onClick,
  onKeyDown,
}: ChapterLabelProps) {
  const { open: sidebarOpen } = useSidebar();

  const content = (
    <div
      className="flex flex-1 items-center gap-2"
      data-test="chapter-label-content"
    >
      {chapter?.icon && (
        <span
          className={cn(
            'flex-shrink-0 text-xl transition-transform duration-200',
          )}
          data-test="chapter-icon"
        >
          {chapter.icon}
        </span>
      )}
      <span
        className={cn(
          'text-foreground text-base font-bold',
          !sidebarOpen && 'sr-only',
        )}
        data-test="chapter-title"
      >
        {chapter?.title}
      </span>
      <span
        className="ml-auto shrink-0 transition-transform duration-200"
        data-test="chapter-expand-icon"
        aria-label={isExpanded ? 'Collapse chapter' : 'Expand chapter'}
      >
        {!isExpanded ? (
          <ChevronLeft className="h-4 w-4" />
        ) : (
          <ChevronDown className="h-4 w-4" />
        )}
      </span>
    </div>
  );

  if (!sidebarOpen) {
    return (
      <TooltipProvider>
        <Tooltip delayDuration={300}>
          <TooltipTrigger asChild>
            <SidebarGroupLabel
              onClick={onClick}
              onKeyDown={onKeyDown}
              data-test={`sidebar-chapter-item-${chapter.id}`}
              className="flex w-full cursor-pointer items-center"
              aria-label={chapter.title}
            >
              {content}
            </SidebarGroupLabel>
          </TooltipTrigger>
          <TooltipContent
            side="right"
            className="z-50"
            data-test="chapter-tooltip"
          >
            <div className="flex flex-col text-xs">
              <span
                className="text-sm font-bold"
                data-test="chapter-tooltip-title"
              >
                {chapter?.title}
              </span>
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return (
    <SidebarGroupLabel
      onClick={onClick}
      onKeyDown={onKeyDown}
      data-test={`sidebar-chapter-item-${chapter.id}`}
      className="w-full cursor-pointer"
      aria-label={chapter.title}
    >
      {content}
    </SidebarGroupLabel>
  );
}

ChapterLabel.displayName = 'ChapterLabel';
