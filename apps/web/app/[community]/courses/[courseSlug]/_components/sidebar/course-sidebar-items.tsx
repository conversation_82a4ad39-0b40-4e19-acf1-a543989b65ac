'use client';

import React, { useCallback } from 'react';

import { use<PERSON>ara<PERSON>, useRouter, useSearchParams } from 'next/navigation';

import { toast } from 'sonner';

import type {
  CourseChapter,
  CourseLesson,
} from '~/lib/communities/courses/types';
import type { PermissionsEnum } from '~/lib/communities/members/types';
import { hasCommunityPermission } from '~/lib/communities/permissions.utils';

import { useCourseLessonMovement } from '../../_lib/hooks/use-course-lesson-movement';
// Import hooks
import { useCourseStructure } from '../../_lib/hooks/use-course-structure';
import { useSidebarState } from '../../_lib/hooks/use-sidebar-state';
// Import server actions
import { moveCourseChapterUpDownAction } from '../../_lib/server/actions/chapters/move-course-chapter';
import { createCourseLessonAction } from '../../_lib/server/actions/lessons/create-course-lesson';
import { moveCourseLessonUpDownAction } from '../../_lib/server/actions/lessons/move-course-lesson-up-down';
// Import components and types
import { CreateUpdateChapterDialog } from '../chapter/create-update-chapter-dialog';
import { DeleteChapterDialog } from '../chapter/delete-chapter-dialog';
import { DeleteLessonDialog } from '../lesson/delete-lesson-dialog';
import { MoveLessonToChapterDialog } from '../lesson/move-lesson-to-chapter-dialog';
import {
  LessonRenderProps as ChapterLessonRenderProps,
  SidebarChapterItem,
} from './sidebar-chapter-item';
import {
  LessonMovementOptions,
  SidebarLessonItem,
  SidebarLessonItemProps,
} from './sidebar-lesson-item';

type CourseSidebarItemsProps = {
  chapters: CourseChapter[];
  lessons: CourseLesson[]; // Assume lessons is always defined here now
  currentLessonId: string | undefined;
  onLessonSelect: (lessonId: string) => void;
  permissions: PermissionsEnum[];
};

// Correct LessonRenderProps definition used within CourseSidebarItems
// (Matches SidebarLessonItemProps structure for permissions)
type LessonRenderProps = {
  key?: string;
  lesson: CourseLesson;
  chapters: CourseChapter[];
  isActive: boolean;
  isComplete: boolean;
  onLessonClick: (lessonId: string) => void;
  hasUnsavedChanges: boolean;
  setShowUnsavedDialog: React.Dispatch<React.SetStateAction<boolean>>;
  movementOptions: LessonMovementOptions;
  hasUpdateLessonPermission: boolean;
  hasDeleteLessonPermission: boolean;
  hasPublishLessonPermission: boolean;
};

export function CourseSidebarItems({
  chapters,
  lessons,
  currentLessonId,
  onLessonSelect,
  permissions,
}: CourseSidebarItemsProps): React.ReactElement {
  const router = useRouter();
  const searchParams = useSearchParams();
  const params = useParams();
  const courseSlug = params.courseSlug as string;
  const communitySlug = params.communitySlug as string;

  // Use hooks
  const { organizedContent, lessonsByChapter } = useCourseStructure(
    chapters,
    lessons,
  );
  const {
    expandedChapters,
    selectedChapter,
    isUpdateDialogOpen,
    isDeleteDialogOpen,
    showUnsavedDialog,
    toggleChapter,
    openChapterUpdateDialog,
    setIsUpdateDialogOpen,
    openDeleteChapterDialog,
    setIsDeleteDialogOpen,
    setShowUnsavedDialog,
  } = useSidebarState(chapters, lessons, currentLessonId);
  const { getMovementOptions } = useCourseLessonMovement(lessons);

  // Calculate ALL permissions ONCE
  const canUpdateChapter = hasCommunityPermission(
    permissions,
    'community.courses.chapters.update',
  );
  const canDeleteChapter = hasCommunityPermission(
    permissions,
    'community.courses.chapters.delete',
  );
  const hasUpdateLessonPermission = hasCommunityPermission(
    permissions,
    'community.courses.lessons.update',
  );
  const hasDeleteLessonPermission = hasCommunityPermission(
    permissions,
    'community.courses.lessons.delete',
  );
  const hasPublishLessonPermission = hasCommunityPermission(
    permissions,
    'community.courses.lessons.publish',
  );

  const handleMoveChapter = useCallback(
    async (chapterId: string, direction: 'up' | 'down') => {
      await moveCourseChapterUpDownAction({ chapterId, direction });
      toast.success('Chapter moved successfully');
    },
    [],
  );

  const handleAddLessonToChapter = useCallback(
    async (chapterId: string) => {
      const result = await createCourseLessonAction({
        courseSlug: courseSlug,
        title: 'New Lesson', // Consider making this editable
        chapterId,
      });

      if (result.success && result.data?.id) {
        const params = new URLSearchParams(searchParams);
        params.set('lessonId', result.data.id);
        router.push(`?${params.toString()}`);
        // Invalidation/refresh likely handled by action or parent
      } else {
        throw new Error(String(result.error ?? 'Failed to create lesson'));
      }
    },
    [courseSlug, router, searchParams],
  );

  // Update renderLesson callback signature and usage
  const renderLesson = useCallback(
    (props: LessonRenderProps): React.ReactNode => {
      // Directly use props matching SidebarLessonItemProps
      const lessonProps: SidebarLessonItemProps = {
        lesson: props.lesson,
        chapters: props.chapters,
        communitySlug: communitySlug,
        courseSlug: courseSlug,
        isActive: props.isActive,
        isComplete: props.isComplete,
        hasUpdateLessonPermission: props.hasUpdateLessonPermission,
        hasDeleteLessonPermission: props.hasDeleteLessonPermission,
        hasPublishLessonPermission: props.hasPublishLessonPermission,
        onLessonClick: onLessonSelect,
        hasUnsavedChanges: showUnsavedDialog,
        setShowUnsavedDialog: setShowUnsavedDialog,
        movementOptions: props.movementOptions,
        onMoveLessonUpDown: async (lesson, direction, targetSequence) => {
          await moveCourseLessonUpDownAction({
            lessonId: lesson.id,
            targetSequence,
            direction,
          });
          toast.success(`Lesson moved ${direction} successfully`);
          // Invalidation/refresh likely handled by action or parent
        },
        renderDeleteDialog: ({ lesson, isOpen, setIsOpen, onDeleted }) => (
          <DeleteLessonDialog
            lesson={lesson}
            isOpen={isOpen}
            setIsOpen={setIsOpen}
            onDeleted={onDeleted}
          />
        ),
        renderMoveToChapterDialog: ({
          lesson,
          chapters,
          isOpen,
          setIsOpen,
          onSuccess,
        }) => (
          <MoveLessonToChapterDialog
            lesson={lesson}
            chapters={chapters}
            isOpen={isOpen}
            setIsOpen={setIsOpen}
            onSuccess={onSuccess}
          />
        ),
      };
      return (
        <SidebarLessonItem
          key={props.key ?? props.lesson.id}
          {...lessonProps}
        />
      );
    },
    [
      communitySlug,
      courseSlug,
      onLessonSelect,
      showUnsavedDialog,
      setShowUnsavedDialog,
    ],
  );

  // Render logic
  return (
    <>
      {organizedContent.map((item) => {
        if (item.type === 'lesson') {
          const lesson = item.item;
          const isActive = lesson.id === currentLessonId;
          const movementOptions = getMovementOptions(lesson.id) ?? {
            canMoveUp: false,
            canMoveDown: false,
          };

          // Prepare props for standalone lesson
          const lessonProps: LessonRenderProps = {
            key: lesson.id,
            lesson,
            chapters,
            isActive,
            isComplete: false,
            onLessonClick: onLessonSelect,
            hasUnsavedChanges: showUnsavedDialog,
            setShowUnsavedDialog,
            movementOptions,
            hasUpdateLessonPermission,
            hasDeleteLessonPermission,
            hasPublishLessonPermission,
          };
          return (
            <div
              key={`lesson-${lesson.id}`}
              className="list-none"
              data-test={`standalone-lesson-${lesson.id}`}
            >
              {renderLesson(lessonProps)}
            </div>
          );
        } else {
          const chapter = item.item;
          const chapterLessons = lessonsByChapter[chapter.id] ?? [];
          const canMoveChapterUp =
            chapter.sequenceOrder > 1 && chapters.length > 1;
          const canMoveChapterDown = chapter.sequenceOrder < chapters.length;

          const renderLessonForChapter = (props: ChapterLessonRenderProps) =>
            renderLesson(props);

          return (
            <div
              key={`chapter-${chapter.id}`}
              data-test={`course-sidebar-chapter-container-${chapter.id}`}
            >
              <SidebarChapterItem
                chapter={chapter}
                isExpanded={expandedChapters.has(chapter.id)}
                isPending={false}
                canMoveChapterUp={canMoveChapterUp}
                canMoveChapterDown={canMoveChapterDown}
                chapterLessons={chapterLessons}
                currentLessonId={currentLessonId}
                chapters={chapters}
                canUpdateChapter={canUpdateChapter}
                canDeleteChapter={canDeleteChapter}
                hasUpdateLessonPermission={hasUpdateLessonPermission}
                hasDeleteLessonPermission={hasDeleteLessonPermission}
                hasPublishLessonPermission={hasPublishLessonPermission}
                onToggle={toggleChapter}
                onKeyDown={
                  (/* e, chapterId */) => {
                    /* Keyboard handling */
                  }
                }
                onMoveChapter={handleMoveChapter}
                onUpdate={openChapterUpdateDialog}
                onDelete={openDeleteChapterDialog}
                onAddLessonToChapter={handleAddLessonToChapter}
                onLessonSelect={onLessonSelect}
                hasUnsavedChanges={showUnsavedDialog}
                setShowUnsavedDialog={setShowUnsavedDialog}
                getMovementOptions={(lessonId: string) =>
                  getMovementOptions(lessonId) ?? {
                    canMoveUp: false,
                    canMoveDown: false,
                  }
                }
                renderLesson={renderLessonForChapter}
              />
            </div>
          );
        }
      })}

      {/* Render Modals */}
      {selectedChapter && (
        <>
          <CreateUpdateChapterDialog
            courseSlug={courseSlug}
            chapter={{
              id: selectedChapter.id,
              title: selectedChapter.title,
              description: selectedChapter.description,
              icon: selectedChapter.icon,
              sequenceOrder: selectedChapter.sequenceOrder,
            }}
            isOpen={isUpdateDialogOpen}
            onClose={() => setIsUpdateDialogOpen(false)} // Use setter from hook
            data-test="chapter-update-dialog"
          />
          <DeleteChapterDialog
            chapter={selectedChapter}
            isOpen={isDeleteDialogOpen}
            setIsOpen={setIsDeleteDialogOpen} // Use setter from hook
            onDeleted={() => setIsDeleteDialogOpen(false)} // Close on delete
            data-test="chapter-delete-dialog"
          />
        </>
      )}
    </>
  );
}

CourseSidebarItems.displayName = 'CourseSidebarItems';
