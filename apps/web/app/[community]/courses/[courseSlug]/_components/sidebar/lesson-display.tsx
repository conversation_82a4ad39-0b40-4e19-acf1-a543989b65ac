'use client';

import React from 'react';

import Link from 'next/link';

import { CheckCircle } from 'lucide-react';

import { Badge } from '@kit/ui/badge';
import { useSidebar } from '@kit/ui/shadcn-sidebar';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@kit/ui/tooltip';
import { cn } from '@kit/ui/utils';

import type { CourseLesson } from '~/lib/communities/courses/types';

type LessonDisplayProps = {
  lesson: CourseLesson;
  communitySlug: string;
  courseSlug: string;
  isActive: boolean;
  isComplete: boolean;
  onClick: (e: React.MouseEvent) => void;
};

export function LessonDisplay({
  lesson,
  communitySlug,
  courseSlug,
  isActive,
  isComplete,
  onClick,
}: LessonDisplayProps) {
  const { open: sidebarOpen } = useSidebar();

  const content = (
    <div
      className={cn(
        'flex w-full items-center',
        isComplete && 'text-green-500',
        isActive && 'text-primary',
      )}
      data-test="lesson-content"
    >
      {lesson.icon && (
        <span
          className={cn(
            'flex-shrink-0 text-xl transition-transform duration-200',
          )}
          data-test="lesson-icon"
        >
          {lesson.icon}
        </span>
      )}
      <span
        className={cn('flex-1 pl-2', !sidebarOpen && 'sr-only')}
        data-test="lesson-title"
      >
        {lesson.title}
      </span>
      {isComplete && (
        <CheckCircle
          className="ml-2 h-4 w-4 opacity-80"
          data-test="lesson-complete-icon"
          aria-label="Completed"
        />
      )}
      {lesson.status === 'draft' && (
        <Badge
          className={cn('ml-2', !sidebarOpen && 'hidden')}
          data-test="lesson-draft-badge"
        >
          Draft
        </Badge>
      )}
    </div>
  );

  const linkHref = `/${communitySlug}/courses/${courseSlug}?lessonId=${lesson.id}`;

  if (!sidebarOpen) {
    return (
      <TooltipProvider>
        <Tooltip delayDuration={300}>
          <TooltipTrigger asChild>
            <Link
              href={linkHref}
              onClick={onClick}
              className={cn(
                'flex flex-1 items-center justify-between px-2 py-1 text-left text-sm',
                'group min-h-[32px]',
              )}
              role="menuitem"
              tabIndex={0}
              data-test={`sidebar-lesson-button-${lesson.id}`}
              aria-label={lesson.title}
            >
              {content}
            </Link>
          </TooltipTrigger>
          <TooltipContent
            side="right"
            className="z-50"
            data-test="lesson-tooltip"
          >
            <div className="flex flex-col text-xs">
              <span className="font-medium" data-test="lesson-tooltip-title">
                {lesson.title}
              </span>
              {lesson.status === 'draft' && (
                <span
                  className="text-muted-foreground"
                  data-test="lesson-tooltip-draft"
                >
                  Draft
                </span>
              )}
              {isComplete && (
                <span
                  className="text-green-500"
                  data-test="lesson-tooltip-complete"
                >
                  Completed
                </span>
              )}
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return (
    <Link
      href={linkHref}
      onClick={onClick}
      className={cn(
        'flex flex-1 items-center justify-between px-2 py-1 text-left text-sm',
        'group min-h-[32px]',
      )}
      role="menuitem"
      tabIndex={0}
      data-test={`sidebar-lesson-button-${lesson.id}`}
      aria-label={lesson.title}
    >
      {content}
    </Link>
  );
}

LessonDisplay.displayName = 'LessonDisplay';
