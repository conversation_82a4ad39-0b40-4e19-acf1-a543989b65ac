'use client';

import React from 'react';

import {
  BookPlus,
  CircleArrowDown,
  CircleArrowUp,
  MoreHorizontal,
  Pencil,
  Trash,
} from 'lucide-react';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@kit/ui/dropdown-menu';
import { SidebarMenuAction } from '@kit/ui/shadcn-sidebar';

type ChapterMenuProps = {
  canUpdateChapter: boolean;
  canDeleteChapter: boolean;
  canMoveUp: boolean;
  canMoveDown: boolean;
  isPending: boolean; // General pending state for async actions
  onUpdate: () => void;
  onAddLesson: () => void;
  onMoveUp: () => void;
  onMoveDown: () => void;
  onDelete: () => void;
};

export function ChapterMenu({
  canUpdateChapter,
  canDeleteChapter,
  canMoveUp,
  canMoveDown,
  isPending,
  onUpdate,
  onAddLesson,
  onMoveUp,
  onMoveDown,
  onDelete,
}: ChapterMenuProps) {
  if (!canUpdateChapter) return null; // If user can't update, don't show the menu

  const isDisabled = isPending; // Disable all items if any action is pending

  return (
    <SidebarMenuAction asChild>
      <DropdownMenu>
        <DropdownMenuTrigger
          disabled={isDisabled}
          data-test="sidebar-chapter-menu-trigger"
          className="hover:bg-muted cursor-pointer p-1"
          aria-label="Chapter options"
        >
          <MoreHorizontal className="h-5 w-5" />
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" data-test="chapter-dropdown-content">
          <DropdownMenuItem
            onClick={onUpdate}
            disabled={isDisabled}
            data-test="sidebar-chapter-edit-button"
            aria-label="Edit chapter"
          >
            <Pencil className="mr-2 h-5 w-5" />
            Edit Chapter
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={onAddLesson}
            disabled={isDisabled}
            data-test="sidebar-chapter-add-lesson-button"
            aria-label="Add lesson to this chapter"
          >
            <BookPlus className="mr-2 h-5 w-5" />
            Add Lesson to Chapter
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={onMoveUp}
            disabled={isDisabled || !canMoveUp}
            data-test="sidebar-chapter-move-up-button"
            aria-label="Move chapter up"
          >
            <CircleArrowUp className="mr-2 h-5 w-5" />
            Move up
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={onMoveDown}
            disabled={isDisabled || !canMoveDown}
            data-test="sidebar-chapter-move-down-button"
            aria-label="Move chapter down"
          >
            <CircleArrowDown className="mr-2 h-5 w-5" />
            Move down
          </DropdownMenuItem>
          {canDeleteChapter && (
            <DropdownMenuItem
              onClick={onDelete}
              disabled={isDisabled}
              data-test="sidebar-chapter-delete-button"
              className="text-destructive focus:text-destructive"
              aria-label="Delete chapter"
            >
              <Trash className="mr-2 h-5 w-5" />
              Delete Chapter
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </SidebarMenuAction>
  );
}

ChapterMenu.displayName = 'ChapterMenu';
