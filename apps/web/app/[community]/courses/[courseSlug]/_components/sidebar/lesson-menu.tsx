'use client';

import React from 'react';

import {
  CircleArrowDown,
  CircleArrowRight,
  CircleArrowUp,
  MoreHorizontal,
  Notebook,
  NotebookText,
  Trash,
} from 'lucide-react';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@kit/ui/dropdown-menu';
import { SidebarMenuAction } from '@kit/ui/shadcn-sidebar';
import { cn } from '@kit/ui/utils';

import type {
  CourseLesson,
  LessonStatusEnum,
} from '~/lib/communities/courses/types';

type LessonMenuProps = {
  lesson: CourseLesson;
  isPending: boolean;
  isMoveInProgress: boolean;
  canUpdateLesson: boolean;
  canDeleteLesson: boolean;
  canPublishLesson: boolean;
  movementOptions: {
    canMoveUp: boolean;
    canMoveDown: boolean;
  };
  onMoveUp: () => void;
  onMoveDown: () => void;
  onMoveToChapter: () => void;
  onUpdateStatus: (status: LessonStatusEnum) => void;
  onDelete: () => void;
};

export function LessonMenu({
  lesson,
  isPending,
  isMoveInProgress,
  canUpdateLesson,
  canDeleteLesson,
  canPublishLesson,
  movementOptions,
  onMoveUp,
  onMoveDown,
  onMoveToChapter,
  onUpdateStatus,
  onDelete,
}: LessonMenuProps) {
  if (!canUpdateLesson) return null;

  const isMovementDisabled = isPending || isMoveInProgress;

  return (
    <SidebarMenuAction
      className={cn('opacity-100 transition-opacity duration-200')}
      asChild
    >
      <DropdownMenu>
        <DropdownMenuTrigger
          data-test="sidebar-lesson-menu-trigger"
          disabled={isMovementDisabled}
          className={cn(lesson.chapterId ? 'mr-1' : 'mr-3', 'cursor-pointer')}
          aria-label="Lesson options"
        >
          <MoreHorizontal className="h-5 w-5" />
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem
            onClick={onMoveUp}
            disabled={isMovementDisabled || !movementOptions.canMoveUp}
            data-test="sidebar-lesson-move-up-button"
            aria-label="Move lesson up"
          >
            <CircleArrowUp className="mr-2 h-5 w-5" />
            Move up
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={onMoveDown}
            disabled={isMovementDisabled || !movementOptions.canMoveDown}
            data-test="sidebar-lesson-move-down-button"
            aria-label="Move lesson down"
          >
            <CircleArrowDown className="mr-2 h-5 w-5" />
            Move down
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={onMoveToChapter}
            disabled={isMovementDisabled}
            data-test="sidebar-lesson-move-to-chapter-button"
            aria-label="Move lesson to another chapter"
          >
            <CircleArrowRight className="mr-2 h-5 w-5" />
            Move to Chapter
          </DropdownMenuItem>
          {lesson.status === 'published' && (
            <DropdownMenuItem
              onClick={() => onUpdateStatus('draft')}
              disabled={isMovementDisabled || isPending}
              data-test="sidebar-lesson-move-to-draft-button"
              aria-label="Unpublish lesson"
            >
              <Notebook className="mr-2 h-5 w-5" />
              Unpublish Lesson
            </DropdownMenuItem>
          )}
          {lesson.status === 'draft' && canPublishLesson && (
            <DropdownMenuItem
              onClick={() => onUpdateStatus('published')}
              disabled={isMovementDisabled || isPending}
              data-test="sidebar-lesson-move-to-published-button"
              aria-label="Publish lesson"
            >
              <NotebookText className="mr-2 h-5 w-5" />
              Publish Lesson
            </DropdownMenuItem>
          )}
          {canDeleteLesson && (
            <DropdownMenuItem
              onClick={onDelete}
              disabled={isMovementDisabled}
              data-test="sidebar-lesson-delete-button"
              className="text-destructive focus:text-destructive"
              aria-label="Delete lesson"
            >
              <Trash className="mr-2 h-5 w-5" />
              Delete Lesson
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </SidebarMenuAction>
  );
}

LessonMenu.displayName = 'LessonMenu';
