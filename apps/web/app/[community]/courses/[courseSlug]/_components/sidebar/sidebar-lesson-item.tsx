'use client';

import React, { useCallback, useMemo, useState, useTransition } from 'react';

import { toast } from 'sonner';

import { SidebarMenuItem, useSidebar } from '@kit/ui/shadcn-sidebar';
import { cn } from '@kit/ui/utils';

import type {
  CourseChapter,
  CourseLesson,
  LessonStatusEnum,
} from '~/lib/communities/courses/types';

import { updateCourseLessonStatusAction } from '../../_lib/server/actions/lessons/update-course-lesson-status';
// Import new components
import { LessonDisplay } from './lesson-display';
import { LessonMenu } from './lesson-menu';

// Define props for modals if they are passed down
type DeleteDialogProps = {
  lesson: CourseLesson;
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  onDeleted: () => void;
};

type MoveToChapterDialogProps = {
  lesson: CourseLesson;
  chapters: CourseChapter[];
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  onSuccess: () => void;
};

// Define LessonMovementOptions locally or import if needed elsewhere
export type LessonMovementOptions = {
  canMoveUp: boolean;
  canMoveDown: boolean;
  targetSequenceUp?: number;
  targetSequenceDown?: number;
};

type LessonMovementCallback = (
  lesson: CourseLesson,
  direction: 'up' | 'down',
  targetSequence: number,
) => Promise<void>;

export type SidebarLessonItemProps = {
  lesson: CourseLesson;
  chapters: CourseChapter[]; // Needed for MoveToChapterDialog
  communitySlug: string; // Needed for LessonDisplay
  courseSlug: string; // Needed for LessonDisplay
  isActive: boolean;
  isComplete: boolean;
  onLessonClick: (lessonId: string) => void;
  hasUnsavedChanges: boolean;
  setShowUnsavedDialog: React.Dispatch<React.SetStateAction<boolean>>;
  movementOptions: LessonMovementOptions;
  onMoveLessonUpDown?: LessonMovementCallback;
  renderDeleteDialog?: (props: DeleteDialogProps) => React.ReactNode;
  renderMoveToChapterDialog?: (
    props: MoveToChapterDialogProps,
  ) => React.ReactNode;
  hasUpdateLessonPermission: boolean;
  hasDeleteLessonPermission: boolean;
  hasPublishLessonPermission: boolean;
};

export const SidebarLessonItem = React.memo(
  ({
    lesson,
    chapters, // Keep chapters for modal
    communitySlug,
    courseSlug,
    isActive,
    isComplete,
    onLessonClick,
    hasUnsavedChanges,
    setShowUnsavedDialog,
    movementOptions,
    onMoveLessonUpDown,
    renderDeleteDialog,
    renderMoveToChapterDialog,
    hasUpdateLessonPermission,
    hasDeleteLessonPermission,
    hasPublishLessonPermission,
  }: SidebarLessonItemProps) => {
    const [isPending, startTransition] = useTransition();
    const { open: sidebarOpen } = useSidebar();

    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [isMoveDialogOpen, setIsMoveDialogOpen] = useState(false);
    const [isMoveInProgress, setIsMoveInProgress] = useState(false);

    const safeMovementOptions = useMemo(
      () => ({
        canMoveUp: movementOptions?.canMoveUp ?? false,
        canMoveDown: movementOptions?.canMoveDown ?? false,
        targetSequenceUp: movementOptions?.targetSequenceUp,
        targetSequenceDown: movementOptions?.targetSequenceDown,
      }),
      [movementOptions],
    );

    const handleMoveLessonUpDownInternal = useCallback(
      (direction: 'up' | 'down') => {
        startTransition(async () => {
          if (!onMoveLessonUpDown || isMoveInProgress) return;

          setIsMoveInProgress(true);
          try {
            const targetSequence =
              direction === 'up'
                ? safeMovementOptions.targetSequenceUp
                : safeMovementOptions.targetSequenceDown;

            if (targetSequence === undefined) {
              throw new Error('Invalid move direction');
            }

            await onMoveLessonUpDown(lesson, direction, targetSequence);
            // Toast moved to parent/action
          } catch (error) {
            // Error handling moved to parent/action
            console.error('Move lesson error:', error);
            toast.error('Failed to move lesson. Please try again.');
          } finally {
            setIsMoveInProgress(false);
          }
        });
      },
      [
        safeMovementOptions,
        onMoveLessonUpDown,
        isMoveInProgress,
        lesson,
        startTransition,
      ],
    );

    const handleMoveUp = useCallback(() => {
      handleMoveLessonUpDownInternal('up');
    }, [handleMoveLessonUpDownInternal]);

    const handleMoveDown = useCallback(() => {
      handleMoveLessonUpDownInternal('down');
    }, [handleMoveLessonUpDownInternal]);

    const handleLessonMoveToChapter = useCallback(() => {
      setIsMoveDialogOpen(true);
    }, []);

    const handleLessonMoved = useCallback(() => {
      setIsMoveDialogOpen(false);
      // Optionally trigger refresh or state update here if needed
    }, []);

    const handleLessonDelete = useCallback(() => {
      setIsDeleteDialogOpen(true);
    }, []);

    const handleLessonDeleted = useCallback(() => {
      setIsDeleteDialogOpen(false);
      // Optionally trigger refresh or state update here if needed
    }, []);

    const handleClick = useCallback(
      (e: React.MouseEvent) => {
        e.preventDefault();
        if (hasUnsavedChanges) {
          setShowUnsavedDialog(true);
        } else {
          onLessonClick(lesson.id);
        }
      },
      [hasUnsavedChanges, setShowUnsavedDialog, onLessonClick, lesson.id],
    );

    const handleUpdateStatus = useCallback(
      (targetStatus: LessonStatusEnum) => {
        startTransition(() => {
          updateCourseLessonStatusAction({
            lessonId: lesson.id,
            targetStatus,
          })
            .then(() =>
              toast.success(`Lesson status updated to ${targetStatus}`),
            )
            .catch((error) => {
              console.error('Update status error:', error);
              toast.error('Failed to update lesson status.');
            });
        });
      },
      [lesson.id, startTransition],
    );

    return (
      <SidebarMenuItem
        key={lesson.id}
        data-test={`sidebar-lesson-item-${lesson.id}`}
        aria-label={lesson.title}
        className={cn(
          'rounded-xl border-2 border-transparent p-0 focus-visible:border-blue-500 focus-visible:outline-none', // Adjusted styling
          isActive
            ? 'bg-primary/10 border-primary/20' // Active state styling
            : 'hover:bg-primary/5',
        )}
      >
        <div className={cn('flex w-full items-center justify-between')}>
          <LessonDisplay
            lesson={lesson}
            communitySlug={communitySlug}
            courseSlug={courseSlug}
            isActive={isActive}
            isComplete={isComplete}
            onClick={handleClick}
          />

          {hasUpdateLessonPermission && sidebarOpen && (
            <LessonMenu
              lesson={lesson}
              isPending={isPending}
              isMoveInProgress={isMoveInProgress}
              canUpdateLesson={hasUpdateLessonPermission}
              canDeleteLesson={hasDeleteLessonPermission}
              canPublishLesson={hasPublishLessonPermission}
              movementOptions={safeMovementOptions}
              onMoveUp={handleMoveUp}
              onMoveDown={handleMoveDown}
              onMoveToChapter={handleLessonMoveToChapter}
              onUpdateStatus={handleUpdateStatus}
              onDelete={handleLessonDelete}
            />
          )}
        </div>

        {/* Render modals if render props are provided */}
        {renderDeleteDialog &&
          renderDeleteDialog({
            lesson,
            isOpen: isDeleteDialogOpen,
            setIsOpen: setIsDeleteDialogOpen,
            onDeleted: handleLessonDeleted,
          })}
        {renderMoveToChapterDialog &&
          renderMoveToChapterDialog({
            lesson,
            chapters,
            isOpen: isMoveDialogOpen,
            setIsOpen: setIsMoveDialogOpen,
            onSuccess: handleLessonMoved,
          })}
      </SidebarMenuItem>
    );
  },
);

SidebarLessonItem.displayName = 'SidebarLessonItem';
