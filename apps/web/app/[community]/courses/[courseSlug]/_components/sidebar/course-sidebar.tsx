'use client';

import React from 'react';

import { SidebarContent, SidebarHeader } from '@kit/ui/shadcn-sidebar';

import type {
  CourseChapter,
  CourseLesson,
} from '~/lib/communities/courses/types';
import type { PermissionsEnum } from '~/lib/communities/members/types';
import { hasCommunityPermission } from '~/lib/communities/permissions.utils';

import { CreateChapterButton } from '../chapter/create-chapter-button';
import { CreateLessonButton } from '../lesson/create-lesson-button';
import { CourseSidebarItems } from './course-sidebar-items';

type CourseSidebarProps = {
  chapters: CourseChapter[] | undefined;
  lessons: CourseLesson[] | undefined;
  currentLessonId: string | undefined;
  onLessonSelect: (lessonId: string) => void;
  courseSlug: string;
  permissions: PermissionsEnum[];
};

export function CourseSidebar({
  chapters,
  lessons,
  currentLessonId,
  onLessonSelect,
  courseSlug,
  permissions,
}: CourseSidebarProps) {
  const hasCreateChapterPermission = hasCommunityPermission(
    permissions,
    'community.courses.chapters.create',
  );

  const hasCreateLessonPermission = hasCommunityPermission(
    permissions,
    'community.courses.lessons.create',
  );

  return (
    <div className="flex h-full flex-col" data-test="course-sidebar">
      <SidebarHeader
        className="flex items-end justify-between p-2"
        data-test="course-sidebar-header"
      >
        <div className="flex items-center gap-2">
          {hasCreateChapterPermission && (
            <CreateChapterButton courseSlug={courseSlug} />
          )}
          {hasCreateLessonPermission && (
            <CreateLessonButton courseSlug={courseSlug} />
          )}
        </div>
      </SidebarHeader>
      <SidebarContent
        className="flex-1 overflow-y-auto"
        data-test="course-sidebar-content"
      >
        <CourseSidebarItems
          key={chapters?.map((ch) => ch.id).join(',')}
          chapters={chapters ?? []}
          lessons={lessons ?? []}
          currentLessonId={currentLessonId}
          onLessonSelect={onLessonSelect}
          permissions={permissions}
        />
      </SidebarContent>
    </div>
  );
}

CourseSidebar.displayName = 'CourseSidebar';
