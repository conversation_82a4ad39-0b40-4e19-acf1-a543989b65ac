'use client';

import React, { useCallback, useState } from 'react';

import { toast } from 'sonner';

import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  useSidebar,
} from '@kit/ui/shadcn-sidebar';

import type {
  CourseChapter,
  CourseLesson,
} from '~/lib/communities/courses/types';

import { ChapterLabel } from './chapter-label';
import { ChapterMenu } from './chapter-menu';
import type { LessonMovementOptions } from './sidebar-lesson-item';

export type LessonRenderProps = {
  key?: string;
  lesson: CourseLesson;
  chapters: CourseChapter[];
  isActive: boolean;
  isComplete: boolean;
  onLessonClick: (lessonId: string) => void;
  hasUnsavedChanges: boolean;
  setShowUnsavedDialog: React.Dispatch<React.SetStateAction<boolean>>;
  movementOptions: LessonMovementOptions;
  hasUpdateLessonPermission: boolean;
  hasDeleteLessonPermission: boolean;
  hasPublishLessonPermission: boolean;
};

type ChapterMovementProps = {
  canMoveChapterUp: boolean;
  canMoveChapterDown: boolean;
  onMoveChapter: (chapterId: string, direction: 'up' | 'down') => Promise<void>;
};

type ChapterInteractionHandlers = {
  onToggle: (chapterId: string) => void;
  onKeyDown: (e: React.KeyboardEvent, chapterId: string) => void;
  onUpdate: (chapter: CourseChapter) => void;
  onDelete: (chapter: CourseChapter) => void;
  onAddLessonToChapter: (chapterId: string) => Promise<void>;
};

type ChapterLessonHandlers = {
  onLessonSelect: (lessonId: string) => void;
  hasUnsavedChanges: boolean;
  setShowUnsavedDialog: React.Dispatch<React.SetStateAction<boolean>>;
  getMovementOptions: (lessonId: string) => LessonMovementOptions;
};

export type SidebarChapterItemProps = ChapterMovementProps &
  ChapterInteractionHandlers &
  ChapterLessonHandlers & {
    chapter: CourseChapter;
    isExpanded: boolean;
    isPending: boolean;
    chapterLessons: CourseLesson[];
    currentLessonId: string | undefined;
    chapters: CourseChapter[];
    canUpdateChapter: boolean;
    canDeleteChapter: boolean;
    hasUpdateLessonPermission: boolean;
    hasDeleteLessonPermission: boolean;
    hasPublishLessonPermission: boolean;
    renderLesson: (props: LessonRenderProps) => React.ReactNode;
  };

export const SidebarChapterItem = React.memo(function SidebarChapterItem({
  chapter,
  isExpanded,
  isPending,
  canMoveChapterUp,
  canMoveChapterDown,
  chapterLessons,
  currentLessonId,
  chapters,
  canUpdateChapter,
  canDeleteChapter,
  hasUpdateLessonPermission,
  hasDeleteLessonPermission,
  hasPublishLessonPermission,
  onToggle,
  onKeyDown,
  onMoveChapter,
  onUpdate,
  onDelete,
  onAddLessonToChapter,
  onLessonSelect,
  hasUnsavedChanges,
  setShowUnsavedDialog,
  getMovementOptions,
  renderLesson,
}: SidebarChapterItemProps) {
  const [isMovingChapter, setIsMovingChapter] = useState(false);
  const { open: sidebarOpen } = useSidebar();

  const handleToggle = useCallback(() => {
    onToggle(chapter.id);
  }, [chapter.id, onToggle]);

  const handleKeyDownInternal = useCallback(
    (e: React.KeyboardEvent) => {
      onKeyDown(e, chapter.id);
    },
    [chapter.id, onKeyDown],
  );

  const handleUpdateInternal = useCallback(() => {
    onUpdate(chapter);
  }, [chapter, onUpdate]);

  const handleAddLessonToChapterInternal = useCallback(async () => {
    if (!onAddLessonToChapter) return;
    try {
      await onAddLessonToChapter(chapter.id);
    } catch (error) {
      toast.error('Error adding lesson to chapter');
      console.error('Error adding lesson to chapter:', error);
    }
  }, [chapter.id, onAddLessonToChapter]);

  const handleMoveChapterInternal = useCallback(
    async (direction: 'up' | 'down') => {
      if (!onMoveChapter || isMovingChapter) return;
      setIsMovingChapter(true);
      try {
        await onMoveChapter(chapter.id, direction);
      } catch (error) {
        toast.error(`Failed to move chapter ${direction}`);
        console.error(`Error moving chapter ${direction}:`, error);
      } finally {
        setIsMovingChapter(false);
      }
    },
    [chapter.id, onMoveChapter, isMovingChapter],
  );

  const handleMoveChapterUp = useCallback(() => {
    handleMoveChapterInternal('up');
  }, [handleMoveChapterInternal]);

  const handleMoveChapterDown = useCallback(() => {
    handleMoveChapterInternal('down');
  }, [handleMoveChapterInternal]);

  const handleDeleteInternal = useCallback(() => {
    onDelete(chapter);
  }, [chapter, onDelete]);

  const getSafeMovementOptions = useCallback(
    (lesson: CourseLesson) => {
      const options = getMovementOptions?.(lesson.id);
      return {
        canMoveUp: options?.canMoveUp ?? false,
        canMoveDown: options?.canMoveDown ?? false,
        targetSequenceUp: options?.targetSequenceUp ?? lesson.sequenceOrder,
        targetSequenceDown: options?.targetSequenceDown ?? lesson.sequenceOrder,
      };
    },
    [getMovementOptions],
  );

  return (
    <SidebarGroup
      key={chapter.id}
      className="space-y-1 rounded-xl border-2 border-transparent p-2 focus-within:border-blue-500"
      data-test={`sidebar-chapter-group-${chapter.id}`}
    >
      <div className="group flex items-center gap-2">
        <div className="flex-1 grow">
          <ChapterLabel
            chapter={chapter}
            isExpanded={isExpanded}
            onClick={handleToggle}
            onKeyDown={handleKeyDownInternal}
          />
        </div>

        {hasUpdateLessonPermission && sidebarOpen && (
          <ChapterMenu
            canUpdateChapter={canUpdateChapter}
            canDeleteChapter={canDeleteChapter}
            canMoveUp={canMoveChapterUp}
            canMoveDown={canMoveChapterDown}
            isPending={isPending || isMovingChapter}
            onUpdate={handleUpdateInternal}
            onAddLesson={handleAddLessonToChapterInternal}
            onMoveUp={handleMoveChapterUp}
            onMoveDown={handleMoveChapterDown}
            onDelete={handleDeleteInternal}
          />
        )}
      </div>

      {isExpanded && (
        <SidebarGroupContent className="block!" style={{ display: 'block' }}>
          <SidebarMenu className="pl-8">
            {chapterLessons.map((lesson) => {
              const isActive = lesson.id === currentLessonId;
              const movementOptions = getSafeMovementOptions(lesson);

              const lessonRenderProps: LessonRenderProps = {
                key: lesson.id,
                lesson,
                chapters,
                isActive,
                isComplete: false,
                onLessonClick: onLessonSelect,
                hasUnsavedChanges,
                setShowUnsavedDialog,
                movementOptions,
                hasUpdateLessonPermission,
                hasDeleteLessonPermission,
                hasPublishLessonPermission,
              };

              return renderLesson(lessonRenderProps);
            })}
          </SidebarMenu>
        </SidebarGroupContent>
      )}
    </SidebarGroup>
  );
});

SidebarChapterItem.displayName = 'SidebarChapterItem';
