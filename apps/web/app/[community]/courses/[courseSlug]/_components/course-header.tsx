'use client';

import { useRouter } from 'next/navigation';

import { ChevronLeft } from 'lucide-react';

import { Button } from '@kit/ui/button';

import pathsConfig from '~/config/paths.config';
import type { CourseDetails } from '~/lib/communities/courses/types';
import type { PermissionsEnum } from '~/lib/communities/members/types';
import { hasCommunityPermission } from '~/lib/communities/permissions.utils';

import { PublishCourseSplitButton } from '../../_components/publish-course-split-button';

export function CourseHeader(props: {
  communitySlug: string;
  courseDetails: CourseDetails;
  permissions: PermissionsEnum[];
}) {
  const router = useRouter();

  const handleBackClick = () => {
    const path = pathsConfig.app.courses.replace(
      '[community]',
      props.communitySlug,
    );
    router.push(path);
  };

  const hasUpdatePermission = hasCommunityPermission(
    props.permissions,
    'community.courses.update',
  );

  return (
    <header className="relative flex items-center p-4">
      <Button
        variant="ghost"
        onClick={handleBackClick}
        className="flex cursor-pointer items-center gap-2"
        data-test="back-to-all-courses-button"
        aria-label="Back to all courses"
      >
        <ChevronLeft className="h-4 w-4" />
        Back to all courses
      </Button>
      <div className="flex-1 justify-end text-center">
        <span className="text-xl font-bold">{props.courseDetails.title}</span>
        {hasUpdatePermission && (
          <PublishCourseSplitButton
            courseSlug={props.courseDetails.slug}
            courseStatus={props.courseDetails.status}
            permissions={props.permissions}
            className="absolute top-2 right-2 text-white"
          />
        )}
      </div>
    </header>
  );
}

CourseHeader.displayName = 'CourseHeader';
