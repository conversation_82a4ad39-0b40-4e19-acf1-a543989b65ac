'use client';

import { useCallback, useEffect, useMemo, useState } from 'react';

import { useRouter, useSearchParams } from 'next/navigation';

import { BookOpen } from 'lucide-react';

import { NotFound } from '@kit/ui/dojo/molecules/not-found';
import { SidebarProvider } from '@kit/ui/shadcn-sidebar';

import type {
  CourseChapters,
  CourseDetails,
  CourseLesson,
} from '~/lib/communities/courses/types';
import type { PermissionsEnum } from '~/lib/communities/members/types';
import { hasCommunityPermission } from '~/lib/communities/permissions.utils';

import { DeleteCourseButton } from '../../_components/delete-course-button';
import { CreateChapterButton } from './chapter/create-chapter-button';
import { CreateLessonButton } from './lesson/create-lesson-button';
import { LessonContent } from './lesson/lesson-content';
import { CourseSidebar } from './sidebar/course-sidebar';

type CourseContainerProps = {
  courseDetails: CourseDetails;
  courseChapters: CourseChapters[];
  courseLessons: CourseLesson[];
  permissions: PermissionsEnum[];
};

export function CourseContainer({
  courseDetails,
  courseChapters: initialChapters,
  courseLessons: initialLessons,
  permissions,
}: CourseContainerProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const lessonId = searchParams.get('lessonId');
  const [chapters, setChapters] = useState(initialChapters);
  const [lessons, setLessons] = useState(initialLessons);
  const [isEditing, setIsEditing] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [showUnsavedDialog, setShowUnsavedDialog] = useState(false);
  const [pendingLessonId, setPendingLessonId] = useState<string | null>(null);

  const [currentLessonId, setCurrentLessonId] = useState<string | undefined>(
    lessonId ?? initialLessons?.[0]?.id,
  );

  const handleLessonSelect = useCallback(
    (lessonId: string) => {
      if (isEditing && hasUnsavedChanges) {
        setPendingLessonId(lessonId);
        setShowUnsavedDialog(true);
        return;
      }

      setIsEditing(false);
      setHasUnsavedChanges(false);
      setCurrentLessonId(lessonId);
      const params = new URLSearchParams(searchParams);
      params.set('lessonId', lessonId);
      router.push(`?${params.toString()}`);
    },
    [isEditing, hasUnsavedChanges, searchParams, router],
  );

  const handleProceedToNewLesson = useCallback(() => {
    if (pendingLessonId) {
      setIsEditing(false);
      setHasUnsavedChanges(false);
      setCurrentLessonId(pendingLessonId);
      const params = new URLSearchParams(searchParams);
      params.set('lessonId', pendingLessonId);
      router.push(`?${params.toString()}`);
      setPendingLessonId(null);
      setShowUnsavedDialog(false);
    }
  }, [pendingLessonId, searchParams, router]);

  const handleStayOnCurrentLesson = useCallback(() => {
    setPendingLessonId(null);
    setShowUnsavedDialog(false);
  }, []);

  useEffect(() => {
    if (lessonId) {
      setCurrentLessonId(lessonId);
    } else if (lessons?.[0]?.id) {
      handleLessonSelect(lessons[0].id);
    }
  }, [lessonId, lessons, handleLessonSelect]);

  useEffect(() => {
    setChapters(initialChapters);
    setLessons(initialLessons);
  }, [initialChapters, initialLessons]);

  const courseStructure = useMemo(() => {
    return (
      chapters?.map((chapter) => ({
        id: chapter.id,
        courseId: chapter.courseId,
        title: chapter.title,
        description: chapter.description,
        icon: chapter.icon,
        sequenceOrder: chapter.sequenceOrder,
        createdAt: chapter.createdAt,
        updatedAt: chapter.updatedAt,
        isChapter: true,
        lessonCount: chapter.lessonCount,
      })) ?? []
    );
  }, [chapters]);

  const currentLesson = useMemo(() => {
    const lesson =
      lessons?.find((lesson) => lesson.id === currentLessonId) ?? lessons?.[0];

    if (!lesson) return undefined;

    return {
      ...lesson,
      contentData: (lesson.contentData ?? {}) as CourseLesson['contentData'],
      lessonText: (lesson.lessonText ?? {}) as CourseLesson['lessonText'],
    } as CourseLesson;
  }, [currentLessonId, lessons]);

  const hasCreateChapterPermission = hasCommunityPermission(
    permissions,
    'community.courses.chapters.create',
  );

  const hasCreateLessonPermission = hasCommunityPermission(
    permissions,
    'community.courses.lessons.create',
  );

  const hasDeleteCoursePermission = hasCommunityPermission(
    permissions,
    'community.courses.delete',
  );

  const hasEditLessonPermission = hasCommunityPermission(
    permissions,
    'community.courses.lessons.update',
  );

  return (
    <div className="mx-auto w-full max-w-7xl p-4">
      {chapters?.length === 0 && lessons?.length === 0 ? (
        <NotFound
          title="No chapters or lessons found"
          message="Please create a chapter or lesson to get started."
          data-test="chapter-lesson-not-found"
          icon={BookOpen}
          actions={
            <div className="flex items-center justify-center gap-4">
              {hasCreateChapterPermission &&
                courseDetails.status !== 'archived' && (
                  <CreateChapterButton
                    courseSlug={courseDetails.slug}
                    variant="default"
                  />
                )}
              {hasCreateLessonPermission &&
                courseDetails.status !== 'archived' && (
                  <CreateLessonButton
                    courseSlug={courseDetails.slug}
                    variant="default"
                  />
                )}
              {hasDeleteCoursePermission && (
                <DeleteCourseButton
                  courseSlug={courseDetails.slug}
                  variant="default"
                />
              )}
            </div>
          }
        />
      ) : (
        <div className="bg-background flex h-[calc(100vh-2rem)] w-full overflow-hidden shadow-xs">
          <SidebarProvider defaultOpen={true}>
            <div className="h-full">
              <CourseSidebar
                chapters={chapters}
                lessons={lessons}
                currentLessonId={currentLessonId}
                onLessonSelect={handleLessonSelect}
                courseSlug={courseDetails.slug}
                permissions={permissions}
              />
            </div>
            <div className="flex-1 overflow-auto">
              <main className="h-full pt-1 pl-4">
                {lessons?.length === 0 ? (
                  <NotFound
                    title="No lessons found"
                    message="Please create a lesson to get started."
                    data-test="lesson-not-found"
                    icon={BookOpen}
                    actions={
                      hasCreateLessonPermission &&
                      courseDetails.status !== 'archived' && (
                        <CreateLessonButton
                          courseSlug={courseDetails.slug}
                          variant="default"
                        />
                      )
                    }
                  />
                ) : (
                  <LessonContent
                    lesson={currentLesson}
                    lessons={lessons}
                    chapters={courseStructure}
                    currentLessonId={currentLessonId}
                    onLessonSelect={handleLessonSelect}
                    canEditLesson={hasEditLessonPermission}
                    isEditing={isEditing}
                    setIsEditing={setIsEditing}
                    setHasUnsavedChanges={setHasUnsavedChanges}
                    showUnsavedDialog={showUnsavedDialog}
                    setShowUnsavedDialog={setShowUnsavedDialog}
                    onProceedToNewLesson={handleProceedToNewLesson}
                    onStayOnCurrentLesson={handleStayOnCurrentLesson}
                  />
                )}
              </main>
            </div>
          </SidebarProvider>
        </div>
      )}
    </div>
  );
}

CourseContainer.displayName = 'CourseContainer';
