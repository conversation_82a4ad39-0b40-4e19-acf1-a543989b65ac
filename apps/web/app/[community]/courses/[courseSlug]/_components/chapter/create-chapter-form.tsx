'use client';

import React, { useState, useTransition } from 'react';

import { isRedirectError } from 'next/dist/client/components/redirect-error';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';

import { Alert, AlertDescription, AlertTitle } from '@kit/ui/alert';
import { Button } from '@kit/ui/button';
import { EmojiPicker } from '@kit/ui/dojo/molecules/emoji-picker';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { If } from '@kit/ui/if';
import { Input } from '@kit/ui/input';
import { Textarea } from '@kit/ui/textarea';
import { Trans } from '@kit/ui/trans';

import {
  ChapterData,
  CreateCourseChapterData,
  CreateCourseChapterSchema,
} from '../../_lib/schema/course-chapter.schema';
import { createCourseChapterAction } from '../../_lib/server/actions/chapters/create-course-chapter';

export function CreateChapterForm(props: {
  courseSlug: string;
  onClose: () => void;
  onSuccess?: (chapter: ChapterData) => void;
}): React.ReactElement {
  const [error, setError] = useState<boolean>();
  const [isPending, startTransition] = useTransition();

  const form = useForm<CreateCourseChapterData>({
    defaultValues: {
      courseSlug: props.courseSlug,
      title: '',
      description: '',
      icon: '',
      sequenceOrder: 0,
    },
    resolver: zodResolver(CreateCourseChapterSchema),
  });

  async function onSubmit(data: CreateCourseChapterData) {
    setError(false);

    startTransition(async () => {
      try {
        const response = await createCourseChapterAction(data);
        if (response.data) {
          props.onSuccess?.({
            ...response.data,
            sequenceOrder: data.sequenceOrder ?? 0,
          });
          props.onClose();
        } else {
          setError(true);
        }
      } catch (e) {
        if (isRedirectError(e)) throw e;
        setError(true);
      }
    });
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className={'space-y-4'}>
        <FormField
          control={form.control}
          name={'icon'}
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                <Trans i18nKey={'courses:chapterIconLabel'} />
              </FormLabel>

              <FormControl>
                <EmojiPicker
                  currentIcon={field.value ?? ''}
                  onChange={field.onChange}
                  data-test="chapter-icon-picker"
                  aria-label="Choose chapter icon"
                />
              </FormControl>

              <FormDescription>
                <Trans i18nKey={'courses:chapterIconDescription'} />
              </FormDescription>

              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name={'title'}
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                <Trans i18nKey={'courses:chapterTitleLabel'} />
              </FormLabel>

              <FormControl>
                <Input
                  {...field}
                  placeholder={'Chapter Title'}
                  data-test={'chapter-title-input'}
                  aria-label="Chapter title"
                />
              </FormControl>

              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name={'description'}
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                <Trans i18nKey={'courses:chapterDescriptionLabel'} />
              </FormLabel>

              <FormControl>
                <Textarea
                  {...field}
                  placeholder={'Chapter Description'}
                  data-test={'chapter-description-input'}
                  aria-label="Chapter description"
                />
              </FormControl>

              <FormMessage />
            </FormItem>
          )}
        />

        <If condition={error}>
          <Alert variant={'destructive'}>
            <AlertTitle>
              <Trans i18nKey={'common:genericError'} />
            </AlertTitle>

            <AlertDescription>
              <Trans i18nKey={'common:genericErrorDescription'} />
            </AlertDescription>
          </Alert>
        </If>

        <div className={'flex flex-row justify-end space-x-2'}>
          <Button
            type={'button'}
            variant={'ghost'}
            onClick={props.onClose}
            data-test={'cancel-chapter-button'}
            aria-label="Cancel creating chapter"
          >
            <Trans i18nKey={'common:cancel'} />
          </Button>

          <Button
            type={'submit'}
            disabled={isPending}
            data-test={'create-chapter-button'}
            aria-label="Create chapter"
          >
            <Trans i18nKey={'courses:createChapterSubmitLabel'} />
          </Button>
        </div>
      </form>
    </Form>
  );
}

CreateChapterForm.displayName = 'CreateChapterForm';
