'use client';

import React, { ReactNode, useState } from 'react';

import { BookPlus } from 'lucide-react';

import { Button } from '@kit/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@kit/ui/tooltip';

import { CreateUpdateChapterDialog } from './create-update-chapter-dialog';

export type CreateChapterButtonProps = {
  courseSlug: string;
  variant?: 'default' | 'icon';
  className?: string;
  icon?: ReactNode;
  containerClassName?: string;
};

export const CreateChapterButton: React.FC<CreateChapterButtonProps> = ({
  courseSlug,
  variant = 'icon',
  className = '',
  icon = (
    <BookPlus className={variant === 'default' ? 'mr-2 h-5 w-5' : 'h-4 w-4'} />
  ),
  containerClassName = '',
}) => {
  const [isChapterCreateOpen, setIsChapterCreateOpen] = useState(false);

  const handleDialogChange = (open: boolean) => {
    setIsChapterCreateOpen(open);
  };

  // Icon variant (small rounded button with tooltip)
  if (variant === 'icon') {
    return (
      <>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                size="icon"
                className={`h-8 w-8 cursor-pointer rounded-full ${className}`}
                onClick={() => handleDialogChange(true)}
                data-test="create-chapter-icon-button"
                aria-label="Create chapter"
              >
                {icon}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Create Chapter</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <CreateUpdateChapterDialog
          courseSlug={courseSlug}
          isOpen={isChapterCreateOpen}
          onClose={handleDialogChange}
        />
      </>
    );
  }

  // Default variant (regular button)
  return (
    <div
      className={`flex h-full w-full items-center justify-center ${containerClassName}`}
    >
      <Button
        onClick={() => handleDialogChange(true)}
        className={`px-8 py-4 ${className}`}
        data-test="create-chapter-text-button"
        aria-label="Create a chapter"
      >
        {icon}
        Create a Chapter
      </Button>
      <CreateUpdateChapterDialog
        courseSlug={courseSlug}
        isOpen={isChapterCreateOpen}
        onClose={handleDialogChange}
      />
    </div>
  );
};

CreateChapterButton.displayName = 'CreateChapterButton';
