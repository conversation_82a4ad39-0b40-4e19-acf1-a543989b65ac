'use client';

import React from 'react';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@kit/ui/dialog';
import { Trans } from '@kit/ui/trans';

import { ChapterData } from '../../_lib/schema/course-chapter.schema';
import { CreateChapterForm } from './create-chapter-form';
import { UpdateChapterForm } from './update-chapter-form';

export function CreateUpdateChapterDialog(
  props: React.PropsWithChildren<{
    courseSlug: string;
    chapter?: ChapterData;
    isOpen: boolean;
    onClose: (isOpen: boolean) => void;
    onSuccess?: (chapter: ChapterData) => void;
  }>,
): React.ReactElement {
  return (
    <Dialog open={props.isOpen} onOpenChange={props.onClose}>
      <DialogContent data-test="create-update-chapter-dialog">
        <DialogHeader>
          <DialogTitle>
            <Trans
              i18nKey={
                props.chapter
                  ? 'courses:updateChapterModalHeading'
                  : 'courses:createChapterModalHeading'
              }
            />
          </DialogTitle>

          <DialogDescription>
            <Trans
              i18nKey={
                props.chapter
                  ? 'courses:updateChapterModalDescription'
                  : 'courses:createChapterModalDescription'
              }
            />
          </DialogDescription>
        </DialogHeader>

        {props.chapter ? (
          <UpdateChapterForm
            chapter={props.chapter}
            onClose={() => props.onClose(false)}
            onSuccess={props.onSuccess}
          />
        ) : (
          <CreateChapterForm
            courseSlug={props.courseSlug}
            onClose={() => props.onClose(false)}
            onSuccess={props.onSuccess}
          />
        )}
      </DialogContent>
    </Dialog>
  );
}

CreateUpdateChapterDialog.displayName = 'CreateUpdateChapterDialog';
