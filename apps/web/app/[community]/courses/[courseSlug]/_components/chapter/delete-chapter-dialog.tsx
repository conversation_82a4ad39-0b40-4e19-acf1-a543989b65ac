'use client';

import { deleteCourseChapterAction } from '../../_lib/server/actions/chapters/delete-course-chapter';
import { SharedDeleteDialog } from '../shared/shared-delete-dialog';

/**
 * Props for the DeleteChapterDialog component
 * @property {Object} chapter - The chapter object containing all chapter details
 * @property {boolean} isOpen - Controls the visibility of the dialog
 * @property {Function} setIsOpen - Callback to update the dialog's visibility
 * @property {Function} onDeleted - Callback triggered after successful deletion
 */

type DeleteChapterDialogProps = {
  chapter: {
    id: string;
    title: string;
    description: string | null;
    icon: string | null;
    sequenceOrder: number;
    courseId: string;
  };
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  onDeleted: (chapterId: string) => void;
};

export function DeleteChapterDialog(props: DeleteChapterDialogProps) {
  return (
    <SharedDeleteDialog
      type="chapter"
      item={props.chapter}
      deleteAction={async ({ chapterId }) => {
        const result = await deleteCourseChapterAction({ chapterId });
        return { error: result.error ? new Error(result.message) : undefined };
      }}
      {...props}
    />
  );
}

DeleteChapterDialog.displayName = 'DeleteChapterDialog';
