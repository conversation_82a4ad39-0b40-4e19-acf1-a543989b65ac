'use client';

import { toast } from 'sonner';

import { useUser } from '@kit/supabase/hooks/use-user';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@kit/ui/alert-dialog';
import { LoadingOverlay } from '@kit/ui/loading-overlay';
import { Trans } from '@kit/ui/trans';

import { DeleteConfirmationForm } from './delete-confirmation-form';

type BaseDeleteDialogProps<T extends 'chapter' | 'lesson'> = {
  type: T;
  item: {
    id: string;
    title: string;
  };
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  onDeleted: (id: string) => void;
};

type DeleteChapterDialogProps = BaseDeleteDialogProps<'chapter'> & {
  deleteAction: (params: { chapterId: string }) => Promise<{ error?: Error }>;
};

type LessonDeleteDialogProps = BaseDeleteDialogProps<'lesson'> & {
  deleteAction: (params: { lessonId: string }) => Promise<{ error?: Error }>;
};

type SharedDeleteDialogProps =
  | DeleteChapterDialogProps
  | LessonDeleteDialogProps;

export function SharedDeleteDialog({
  type,
  item,
  isOpen,
  setIsOpen,
  onDeleted,
  deleteAction,
}: SharedDeleteDialogProps) {
  const { data: user } = useUser();

  if (!user) {
    return <LoadingOverlay fullPage={false} />;
  }

  return (
    <div className={'flex flex-col space-y-4'}>
      <div>
        <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
          <AlertDialogContent data-test={`shared-delete-${type}-dialog`}>
            <AlertDialogHeader>
              <AlertDialogTitle>
                <Trans i18nKey={`courses:delete${type}ModalHeading`} />
              </AlertDialogTitle>

              <AlertDialogDescription>
                <Trans
                  i18nKey={`courses:delete${type}ModalDescription`}
                  values={{
                    [`${type}Title`]: item.title,
                  }}
                />
              </AlertDialogDescription>
            </AlertDialogHeader>

            <DeleteConfirmationForm
              type={type}
              title={item.title}
              id={item.id}
              deleteAction={deleteAction}
              onSuccess={() => {
                setIsOpen(false);
                onDeleted(item.id);
                toast.success(
                  `${type.charAt(0).toUpperCase() + type.slice(1)} deleted successfully`,
                );
              }}
            />
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  );
}

SharedDeleteDialog.displayName = 'SharedDeleteDialog';
