'use client';

import { useState, useTransition } from 'react';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';
import { z } from 'zod';

import { ErrorBoundary } from '@kit/monitoring/components';
import { AlertDialogCancel, AlertDialogFooter } from '@kit/ui/alert-dialog';
import { Button } from '@kit/ui/button';
import { StatusAlert } from '@kit/ui/dojo/molecules/status-alert';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { If } from '@kit/ui/if';
import { Input } from '@kit/ui/input';
import { Trans } from '@kit/ui/trans';

function isChapterAction(
  action:
    | ((params: { chapterId: string }) => Promise<{ error?: Error }>)
    | ((params: { lessonId: string }) => Promise<{ error?: Error }>),
  type: 'chapter' | 'lesson',
): action is (params: { chapterId: string }) => Promise<{ error?: Error }> {
  return type === 'chapter';
}

export function DeleteConfirmationForm<T extends 'chapter' | 'lesson'>({
  type,
  title,
  id,
  deleteAction,
  onSuccess,
}: {
  type: T;
  title: string;
  id: string;
  deleteAction: T extends 'chapter'
    ? (params: { chapterId: string }) => Promise<{ error?: Error }>
    : (params: { lessonId: string }) => Promise<{ error?: Error }>;
  onSuccess: () => void;
}) {
  const { t } = useTranslation('courses');
  const [error, setError] = useState<boolean>();
  const [isPending, startTransition] = useTransition();

  const form = useForm({
    mode: 'onChange',
    reValidateMode: 'onChange',
    resolver: zodResolver(
      z.object({
        confirm: z
          .string()
          .min(1, {
            message: t(`courses:delete${type}NameDoesNotMatch`),
          })
          .refine((value) => value === 'DELETE', {
            message: t(`courses:delete${type}NameDoesNotMatch`),
          }),
      }) as z.ZodType<{ confirm: string }>,
    ),
    defaultValues: {
      confirm: '',
    },
  });

  return (
    <ErrorBoundary
      fallback={
        <StatusAlert
          variant="destructive"
          titleKey={`courses:delete${type}ErrorHeading`}
          descriptionKey="common:genericError"
        />
      }
    >
      <Form {...form}>
        <form
          data-test={`delete-course-${type}-form`}
          className={'flex flex-col space-y-4'}
          onSubmit={form.handleSubmit(() => {
            startTransition(async () => {
              try {
                type ActionType =
                  | ((params: {
                      chapterId: string;
                    }) => Promise<{ error?: Error }>)
                  | ((params: {
                      lessonId: string;
                    }) => Promise<{ error?: Error }>);

                const response = isChapterAction(
                  deleteAction as ActionType,
                  type,
                )
                  ? await (
                      deleteAction as (params: {
                        chapterId: string;
                      }) => Promise<{ error?: Error }>
                    )({ chapterId: id })
                  : await (
                      deleteAction as (params: {
                        lessonId: string;
                      }) => Promise<{ error?: Error }>
                    )({ lessonId: id });

                if ('error' in response && response.error) {
                  setError(true);
                  toast.error(`Failed to delete ${type}. Please try again.`);
                } else {
                  onSuccess();
                }
              } catch (error) {
                console.error('Error deleting', error);
                setError(true);
                toast.error(`An unexpected error occurred. Please try again.`);
              }
            });
          })}
        >
          <div className={'flex flex-col space-y-2'}>
            <If condition={error}>
              <StatusAlert
                variant="destructive"
                titleKey={`courses:delete${type}ErrorHeading`}
                descriptionKey="common:genericError"
              />
            </If>

            <div
              className={
                'border-2 border-red-500 p-4 text-sm text-red-500' +
                ' my-4 flex flex-col space-y-2'
              }
            >
              <div>
                <Trans
                  i18nKey={`courses:delete${type}Disclaimer`}
                  values={{
                    [`${type}Title`]: title,
                  }}
                />
              </div>

              <div className={'text-sm'}>
                <Trans i18nKey={`courses:delete${type}WillBeDeleted`} />
              </div>
            </div>

            <FormField
              control={form.control}
              name="confirm"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <Trans
                      i18nKey={`courses:delete${type}ConfirmDeleteDisclaimer`}
                      values={{
                        [`${type}Title`]: title,
                      }}
                    />
                  </FormLabel>
                  <FormControl>
                    <Input
                      data-test={`delete-course-${type}-form-confirm-input`}
                      required
                      type={'text'}
                      autoComplete={'off'}
                      className={'w-full'}
                      placeholder={''}
                      aria-label={t(`confirmDeletionInput`)}
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    <Trans i18nKey={`courses:delete${type}InputField`} />
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <AlertDialogFooter>
            <AlertDialogCancel
              data-test={`cancel-delete-${type}-button`}
              aria-label={t('cancelDeletion')}
            >
              <Trans i18nKey={'common:cancel'} />
            </AlertDialogCancel>

            <Button
              data-test={`delete-course-${type}-form-confirm-button`}
              disabled={isPending}
              variant={'destructive'}
              aria-label={t(
                `confirmDelete${type.charAt(0).toUpperCase() + type.slice(1)}`,
              )}
            >
              <Trans i18nKey={`courses:delete${type}`} />
            </Button>
          </AlertDialogFooter>
        </form>
      </Form>
    </ErrorBoundary>
  );
}

DeleteConfirmationForm.displayName = 'DeleteConfirmationForm';
