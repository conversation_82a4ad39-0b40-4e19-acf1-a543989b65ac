import Link from 'next/link';

import { BookOpen } from 'lucide-react';

import { Button } from '@kit/ui/button';
import { NotFound } from '@kit/ui/dojo/molecules/not-found';
import { PageBody } from '@kit/ui/page';

import pathsConfig from '~/config/paths.config';

import { CourseContainer } from './_components/course-container';
import { CourseHeader } from './_components/course-header';
import { loadCoursePageData } from './_lib/server/course-page.loader';

type CourseDetailPageProps = {
  params: Promise<{
    courseSlug: string;
    community: string;
  }>;
};

export default async function CoursePage({ params }: CourseDetailPageProps) {
  const { community: communitySlug, courseSlug } = await params;

  const { courseDetails, courseChapters, courseLessons, communityWorkspace } =
    await loadCoursePageData(communitySlug, courseSlug);

  if (!courseDetails) {
    const coursesPath = pathsConfig.app.courses.replace(
      '[community]',
      communitySlug,
    );
    return (
      <PageBody>
        <NotFound
          title="Course not found"
          message="The course you are looking for has been archived or deleted."
          data-test="course-not-found"
          icon={BookOpen}
          actions={
            <Button asChild>
              <Link
                href={coursesPath}
                data-test="return-to-courses-link"
                aria-label="Return to courses"
              >
                Return to Courses
              </Link>
            </Button>
          }
        />
      </PageBody>
    );
  }

  // Map courseChapters to include required properties
  const mappedChapters = courseChapters.map((chapter) => ({
    ...chapter,
    description: chapter.description || '',
    icon: chapter.icon || '',
    lessonCount: courseLessons.filter(
      (lesson) => lesson.chapterId === chapter.id,
    ).length,
  }));

  return (
    <PageBody>
      {courseDetails && (
        <>
          <CourseHeader
            communitySlug={communitySlug}
            courseDetails={courseDetails}
            permissions={communityWorkspace.community.permissions}
          />

          <CourseContainer
            courseDetails={courseDetails}
            courseChapters={mappedChapters}
            courseLessons={courseLessons}
            permissions={communityWorkspace.community.permissions}
          />
        </>
      )}
    </PageBody>
  );
}

CoursePage.displayName = 'CoursePage';
