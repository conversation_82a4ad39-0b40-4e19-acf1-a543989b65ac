import { But<PERSON> } from '@kit/ui/button';
import { PageBody } from '@kit/ui/page';

export default function CourseNotFound() {
  return (
    <PageBody>
      <div
        className="flex h-full flex-col items-center justify-center"
        data-test="course-not-found-page"
      >
        <h1 className="mb-4 text-3xl font-bold">Course Not Found</h1>
        <p className="text-muted-foreground mb-8">
          The requested course could not be found
        </p>
        <Button
          onClick={() => window.history.back()}
          data-test="go-back-button"
          aria-label="Go back to previous page"
        >
          Go Back
        </Button>
      </div>
    </PageBody>
  );
}

CourseNotFound.displayName = 'CourseNotFound';
