import { Book<PERSON><PERSON>, LogIn, MessageCircleIcon, Users } from 'lucide-react';

import { NotFound } from '@kit/ui/dojo/molecules/not-found';
import { JoinCommunityCTA } from '@kit/ui/dojo/organisms/join-community-cta';
import { PageBody } from '@kit/ui/page';

import pathsConfig from '~/config/paths.config';
import { loadCommunityWorkspace } from '~/lib/communities/community-workspace.loader';
import type { CourseStatusEnum } from '~/lib/communities/courses/types';
import type { PermissionsEnum } from '~/lib/communities/members/types';
import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { withI18n } from '~/lib/i18n/with-i18n';

import { CommunityNotFound } from '../_components/community-not-found';
import { CoursesHeader } from './_components/courses-header';
import { CoursesLazyLoad } from './_components/courses-lazy-load';
import { CreateCourseButton } from './_components/create-course-button';
import { loadCoursesPageData } from './_lib/server/courses-page.loader';

export const generateMetadata = async () => {
  const i18n = await createI18nServerInstance();
  const title = i18n.t('courses:home.pageTitle');

  return {
    title,
  };
};

type CoursesPageProps = {
  params: Promise<{
    community: string;
  }>;
  searchParams: Promise<{
    page?: string;
    status?: CourseStatusEnum;
  }>;
};

async function CoursesPage({ params, searchParams }: CoursesPageProps) {
  const i18n = await createI18nServerInstance();
  const communitySlug = (await params).community;
  const pageParam = (await searchParams).page ?? '1';
  const statusParam = (await searchParams).status as
    | CourseStatusEnum
    | undefined;
  const page = Number(pageParam);
  const limit = 9;

  const workspace = await loadCommunityWorkspace(communitySlug);

  if (!workspace || !workspace.community?.id) {
    return (
      <CommunityNotFound
        icon={BookOpen}
        title="Courses not found"
        message="The courses you are looking for may not exist or you may not have access."
      />
    );
  }

  const courses = await loadCoursesPageData(
    workspace.community.id,
    page,
    limit,
    statusParam,
  );

  const isMember = !!workspace.community.memberId;
  const permissions = (workspace.community.permissions ??
    []) as PermissionsEnum[];

  const publicView = !isMember;

  const communityAboutPath = pathsConfig.app.communityAbout.replace(
    '[community]',
    communitySlug,
  );

  const communitySignInPath = `${pathsConfig.auth.signIn}?next=/${communitySlug}/forums`;

  return (
    <PageBody>
      {!isMember && !workspace.user && (
        <div className="mb-2">
          <JoinCommunityCTA
            title="courses:joinCommunityCTA.loginTitle"
            titleIcon={<LogIn className="h-5 w-5 text-white" />}
            buttonLink={communitySignInPath}
            buttonLabel="courses:joinCommunityCTA.loginButtonLabel"
            buttonIcon={<LogIn className="ml-1 h-4 w-4" />}
            descriptionText="courses:joinCommunityCTA.loginDescription"
            dataTestButton="cta-login-button"
            ariaLabelButton={i18n.t(
              'courses:joinCommunityCTA.loginButtonLabel',
            )}
          />
        </div>
      )}

      {!isMember && workspace.user && (
        <div className="mb-2">
          <JoinCommunityCTA
            title="courses:joinCommunityCTA.joinTitle"
            titleIcon={<Users className="h-5 w-5 text-white" />}
            buttonLink={communityAboutPath}
            buttonLabel="courses:joinCommunityCTA.joinButtonLabel"
            buttonIcon={<Users className="ml-1 h-4 w-4" />}
            descriptionText="courses:joinCommunityCTA.joinDescription"
            dataTestButton="cta-join-community-button"
            ariaLabelButton={i18n.t('courses:joinCommunityCTA.joinButtonLabel')}
          />
        </div>
      )}

      {!publicView && permissions && (
        <CoursesHeader
          communityId={workspace.community.id}
          communitySlug={communitySlug}
          permissions={permissions}
        />
      )}
      {courses.data.length > 0 ? (
        <CoursesLazyLoad
          communitySlug={communitySlug}
          initialCourses={courses.data}
          currentPage={page}
          pageCount={courses.pageCount}
          permissions={permissions}
          statusFilter={statusParam as CourseStatusEnum}
          publicView={publicView}
        />
      ) : (
        <NotFound
          title="courses:create.noCourses"
          message="courses:create.noCoursesDescription"
          icon={MessageCircleIcon}
          data-test="no-courses-found"
          actions={
            permissions?.includes('community.courses.create') &&
            !publicView && (
              <CreateCourseButton
                communityId={workspace.community.id}
                communitySlug={communitySlug}
              />
            )
          }
        />
      )}
    </PageBody>
  );
}

export default withI18n(CoursesPage);

CoursesPage.displayName = 'CoursesPage';
