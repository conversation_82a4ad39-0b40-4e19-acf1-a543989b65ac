import 'server-only';

import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createCommunityCoursesService } from '~/lib/communities/courses/community-courses.service';
import type { CourseStatusEnum } from '~/lib/communities/courses/types';

/**
 * Load paginated courses for a given community.
 * This loader focuses *only* on fetching the course data.
 * Community ID and permissions should be determined beforehand.
 *
 * @param communityId The UUID of the community.
 * @param page The page number to load.
 * @param limit Number of items per page.
 * @param status Optional filter by course status.
 */
export async function loadCoursesPageData(
  communityId: string,
  page: number,
  limit: number,
  status?: CourseStatusEnum,
) {
  const logger = await getLogger();
  const client = getSupabaseServerClient();

  const ctx = {
    name: 'courses.page.load',
    communityId,
    page,
    limit,
    status,
  };

  logger.info(ctx, 'Loading courses page data...');

  try {
    const courseService = createCommunityCoursesService(client);

    const {
      data,
      count,
      pageCount,
      page: pageNumber,
      pageSize,
    } = await courseService.getPaginatedCourses({
      communityId,
      page,
      limit,
      status,
    });

    const coursesCount = data?.length ?? 0;

    logger.info(
      {
        ...ctx,
        coursesCount,
        pageCount,
        retrievedPage: pageNumber,
        retrievedPageSize: pageSize,
        totalCount: count,
      },
      'Loaded courses page data successfully.',
    );

    return {
      data: data ?? [],
      count,
      pageCount,
      pageNumber,
      pageSize,
    };
  } catch (error) {
    logger.error({ ...ctx, error }, 'Failed to load courses page data');
    return {
      data: [],
      count: 0,
      pageCount: 0,
      pageNumber: 0,
      pageSize: 0,
    };
  }
}
