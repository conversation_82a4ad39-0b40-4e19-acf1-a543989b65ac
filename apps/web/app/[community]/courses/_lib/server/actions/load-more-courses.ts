'use server';

import type { CourseStatusEnum } from '~/lib/communities/courses/types';

import { loadCoursesPageData } from '../courses-page.loader';

export async function loadMoreCoursesAction(params: {
  communitySlug: string;
  page: number;
  limit: number;
  status: CourseStatusEnum;
}) {
  try {
    const { data } = await loadCoursesPageData(
      params.communitySlug,
      params.page,
      params.limit,
      params.status,
    );
    return data;
  } catch (error) {
    console.error('Failed to load more courses:', error);
    return { error: true, message: 'Failed to load more courses' };
  }
}
