'use server';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createCommunityCoursesService } from '~/lib/communities/courses/community-courses.service';

type ToggleCoursePublishedParams = {
  courseSlug: string;
};

export const toggleCoursePublishedAction = enhanceAction(
  async ({ courseSlug }: ToggleCoursePublishedParams, user) => {
    const client = getSupabaseServerClient();
    const logger = await getLogger();

    const ctx = {
      name: 'courses.toggle_published',
      userId: user.id,
      courseSlug,
    };

    logger.info(ctx, `Toggling published course...`);

    try {
      const service = createCommunityCoursesService(client);

      const data = await service.toggleCoursePublished({ courseSlug });

      logger.info(ctx, `Published course toggled successfully via service`);

      return {
        success: true,
        data: {
          added: data, // boolean indicating if post was pinned (true) or unpinned (false)
          courseSlug,
        },
      };
    } catch (error) {
      logger.error({ ...ctx, error }, `Failed to toggle published course`);
      const message = error instanceof Error ? error.message : 'Unknown error';
      return {
        success: false,
        error: message,
      };
    }
  },
  {
    auth: true,
  },
);
