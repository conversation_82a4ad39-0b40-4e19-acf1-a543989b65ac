'use server';

import { v4 as uuidv4 } from 'uuid';
import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createImageStorageService } from '~/lib/images/services/image-storage.service';
import type { UploadImageContext } from '~/lib/images/types';

const UploadCourseImageSchema = z.object({
  file: z.instanceof(File),
  communityId: z.string(),
  courseId: z.string().optional(),
});

export const uploadCourseImageAction = enhanceAction(
  async ({
    file,
    communityId,
    courseId,
  }: {
    file: File;
    communityId: string;
    courseId?: string;
  }) => {
    const client = getSupabaseServerClient();
    const logger = await getLogger();

    const imageService = createImageStorageService(client);

    const newCourseId = courseId ?? uuidv4();

    const ctx = {
      name: 'course.upload-image',
      courseId: newCourseId,
      communityId,
    };

    try {
      const ALLOWED_TYPES = [
        'image/jpeg',
        'image/png',
        'image/webp',
        'image/svg+xml',
      ];
      if (!ALLOWED_TYPES.includes(file.type)) {
        throw new Error(
          'Invalid file type. Only JPEG, PNG, SVG and WebP are allowed',
        );
      }

      const extension = file.name.split('.').pop()?.toLowerCase();
      if (
        !extension ||
        !['jpg', 'jpeg', 'png', 'webp', 'svg'].includes(extension)
      ) {
        throw new Error('Invalid file extension');
      }

      const targetFilename = `${newCourseId}-cover.${extension}`;
      const uploadContext: UploadImageContext = {
        communityId: communityId,
        featureDir: 'courses',
        storageDirPath: newCourseId,
        targetFilename: targetFilename,
      };

      logger.info(
        ctx,
        `Uploading image using ImageStorageService with context: ${JSON.stringify(uploadContext)}...`,
      );

      const result = await imageService.uploadImage(file, uploadContext);

      logger.info(ctx, 'Image uploaded successfully via ImageStorageService');

      return result.url;
    } catch (error) {
      logger.error({ ...ctx, error }, 'Failed to upload image');
      throw new Error('Failed to upload image');
    }
  },
  {
    auth: true,
    schema: UploadCourseImageSchema,
  },
);
