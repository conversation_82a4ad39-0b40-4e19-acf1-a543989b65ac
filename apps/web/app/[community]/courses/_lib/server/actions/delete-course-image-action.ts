'use server';

import { revalidatePath } from 'next/cache';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import pathsConfig from '~/config/paths.config';
// Import the image storage service
import { createImageStorageService } from '~/lib/images/services/image-storage.service';

const STORAGE_BUCKET = 'community_storage';

const DeleteCourseImageSchema = z.object({
  courseId: z.string(),
  url: z.string(),
});

export const deleteCourseImageAction = enhanceAction(
  async ({
    courseId,
    url,
  }: {
    courseId: string;
    url: string;
  }): Promise<boolean> => {
    const client = getSupabaseServerClient();
    const logger = await getLogger();
    // Instantiate the image storage service
    const imageService = createImageStorageService(client);
    const ctx = {
      name: 'course.delete-image',
      url: url,
    };

    try {
      // Extract the relative path from the full URL
      // The URL format should be: https://xxx.supabase.co/storage/v1/object/public/community_storage/communityId/courses/courseId/courseId-cover.jpg
      const urlParts = url.split(`${STORAGE_BUCKET}/`);
      if (urlParts.length < 2) {
        logger.error({ ...ctx, url }, 'Invalid file URL format');
        throw new Error('Invalid file URL format');
      }

      // Get the path relative to the bucket (everything after bucket name, before any query params)
      const relativePath = urlParts[1]?.split('?')[0];

      if (!relativePath) {
        logger.error(ctx, 'Could not extract file path from URL');
        throw new Error('Could not extract file path from URL');
      }

      logger.info(
        { ...ctx, relativePath },
        'Attempting to delete file from storage using ImageStorageService',
      );

      // Use the ImageStorageService to delete the image
      await imageService.deleteImage(relativePath, STORAGE_BUCKET);

      logger.info({ ...ctx }, 'Successfully deleted file from storage');

      logger.info({ ...ctx }, 'Attempting to delete url from database');

      const { error: dbError } = await client
        .from('community_courses')
        .update({
          cover_url: null,
        })
        .eq('id', courseId);

      if (dbError) {
        logger.error({ ...ctx, dbError }, 'Failed to delete url from database');
        throw dbError;
      }

      logger.info({ ...ctx }, 'Successfully deleted url from database');

      revalidatePath(pathsConfig.app.courses, 'page');

      return true;
    } catch (error) {
      logger.error({ ...ctx, error }, 'Failed to delete image');
      throw new Error('Failed to delete image');
    }
  },
  {
    auth: true,
    schema: DeleteCourseImageSchema,
  },
);
