'use server';

import { revalidatePath } from 'next/cache';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createCommunityCoursesService } from '~/lib/communities/courses/community-courses.service';
import { CourseAccessEnum, CourseData } from '~/lib/communities/courses/types';

import { uploadCourseImageAction } from './upload-course-image-action';

type CourseActionResponse = {
  error?: boolean;
  success?: boolean;
  message?: string;
  data?: CourseData;
};

export const createCourseAction = enhanceAction(
  async (params: {
    communityId: string;
    title: string;
    description?: string;
    coverImage?: File;
    coverUrl?: string;
    prerequisiteCourseIds?: string[];
    access: CourseAccessEnum;
  }): Promise<CourseActionResponse> => {
    const client = getSupabaseServerClient(); // Keep client for image upload and service instantiation
    const logger = await getLogger();
    const ctx = {
      name: 'course.create',
      communityId: params.communityId,
      title: params.title,
    };

    logger.info(ctx, 'Creating course...');

    const { coverImage, communityId } = params;
    let finalCoverUrl = params.coverUrl;

    // Handle image upload if provided
    if (coverImage) {
      try {
        finalCoverUrl = await uploadCourseImageAction({
          file: coverImage,
          communityId,
          courseId: undefined, // No courseId for create
        });
      } catch (error) {
        logger.error({ ...ctx, error }, 'Failed to upload course cover image');
        return {
          error: true,
          message: 'Failed to upload course cover image',
        };
      }
    }

    try {
      const communityCourseService = createCommunityCoursesService(client);

      const data = await communityCourseService.createCourse({
        communityId: params.communityId,
        title: params.title,
        description: params.description ?? '',
        coverUrl: finalCoverUrl,
        prerequisiteCourseIds: params.prerequisiteCourseIds,
        access: params.access,
      });

      logger.info(ctx, 'Course created via service');

      // Revalidate paths
      revalidatePath('/[community]/courses', 'page');
      revalidatePath('/[community]/courses', 'layout');

      return {
        success: true,
        data: {
          id: data.id,
          slug: data.slug,
          title: data.title,
          description: data.description,
          coverUrl: data.coverUrl ?? undefined,
          prerequisiteCourseIds: data.prerequisiteCourseIds ?? [],
          access: data.access,
        },
      };
    } catch (error) {
      logger.error({ ...ctx, error }, 'Failed to create course via service');
      const message = error instanceof Error ? error.message : 'Unknown error';
      return {
        error: true,
        message: message,
      };
    }
  },
  {
    auth: true,
  },
);
