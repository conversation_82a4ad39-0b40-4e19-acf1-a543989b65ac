'use server';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createCommunityCoursesService } from '~/lib/communities/courses/community-courses.service';
import type { CourseStatusEnum } from '~/lib/communities/courses/types';

type UpdateCourseStatusParams = {
  courseSlug: string;
  targetStatus: CourseStatusEnum;
};

export const updateCourseStatusAction = enhanceAction(
  async ({ courseSlug, targetStatus }: UpdateCourseStatusParams, user) => {
    const client = getSupabaseServerClient();
    const logger = await getLogger();

    const ctx = {
      name: 'courses.toggle_status',
      userId: user.id,
      courseSlug,
      targetStatus,
    };

    logger.info(ctx, `Updating course status...`);

    try {
      const communityCourseService = createCommunityCoursesService(client);

      const data = await communityCourseService.updateCourseStatus({
        courseSlug,
        targetStatus,
      });

      logger.info(ctx, `Course status updated successfully via service`);

      return {
        success: true,
        data: {
          courseStatus: data,
          courseSlug,
        },
      };
    } catch (error) {
      logger.error({ ...ctx, error }, `Failed to toggle course status`);
      const message = error instanceof Error ? error.message : 'Unknown error';
      return {
        success: false,
        error: message,
      };
    }
  },
  {
    auth: true,
  },
);
