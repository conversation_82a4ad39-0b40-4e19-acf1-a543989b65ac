'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createCommunityCoursesService } from '~/lib/communities/courses/community-courses.service';

/**
 * Schema for reordering a course
 */
const ReorderCourseSchema = z.object({
  courseId: z.string().uuid(),
  direction: z.enum(['left', 'right']),
});

export type ReorderCourseInput = z.infer<typeof ReorderCourseSchema>;

/**
 * Action to reorder a course by changing its sequence order
 * This swaps the sequence order with the adjacent course in the specified direction
 */
export const reorderCourseAction = enhanceAction(
  async function (data: ReorderCourseInput, user) {
    const logger = await getLogger();
    const ctx = {
      name: 'reorder-course-action',
      userId: user.id,
      courseId: data.courseId,
      direction: data.direction,
    };

    try {
      logger.info(ctx, 'Reordering course');

      const supabase = getSupabaseServerClient();

      const service = createCommunityCoursesService(supabase);

      const result = await service.reorderCourse({
        courseId: data.courseId,
        direction: data.direction,
      });

      logger.info(ctx, 'Successfully reordered course via service');

      return {
        success: true,
        courses: result, // Assuming service returns the reordered data
      };
    } catch (error) {
      logger.error({ ...ctx, error }, 'Unexpected error reordering course');
      const message = error instanceof Error ? error.message : 'Unknown error';
      return {
        success: false,
        error: message,
      };
    }
  },
  {
    schema: ReorderCourseSchema,
    auth: true,
  },
);
