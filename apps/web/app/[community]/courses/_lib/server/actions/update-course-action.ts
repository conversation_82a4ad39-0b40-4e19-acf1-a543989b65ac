'use server';

import { revalidatePath } from 'next/cache';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import pathsConfig from '~/config/paths.config';
import { createCommunityCoursesService } from '~/lib/communities/courses/community-courses.service';
import { CourseAccessEnum, CourseData } from '~/lib/communities/courses/types';

import { uploadCourseImageAction } from './upload-course-image-action';

type CourseActionResponse = {
  error?: boolean;
  success?: boolean;
  message?: string;
  data?: CourseData;
};

export const updateCourseAction = enhanceAction(
  async (params: {
    courseId: string;
    title: string;
    description?: string;
    coverImage?: File;
    coverUrl?: string;
    prerequisiteCourseIds?: string[];
    access: CourseAccessEnum;
  }): Promise<CourseActionResponse> => {
    const client = getSupabaseServerClient(); // Keep client for image upload, community lookup, and service instantiation
    const logger = await getLogger();
    const ctx = {
      name: 'course.update',
      courseId: params.courseId,
    };
    const { coverImage, courseId } = params;

    logger.info(ctx, 'Updating course...');

    const communityCourseService = createCommunityCoursesService(client);

    let communityId: string;
    let communitySlug: string | null | undefined;

    // Get the community ID and slug for the course using the service
    try {
      const communityData =
        await communityCourseService.getCommunityIdForCourse({
          courseId: params.courseId,
        });
      communityId = communityData.communityId;
      communitySlug = communityData.communitySlug;
    } catch (error) {
      logger.error(
        { ...ctx, error },
        'Failed to fetch course community data via service',
      );
      return {
        error: true,
        message:
          error instanceof Error
            ? error.message
            : 'Course not found or failed to fetch community data',
      };
    }

    let finalCoverUrl = params.coverUrl;

    // Handle image upload if provided
    if (coverImage) {
      try {
        // Upload the new image. The service now handles cache-busting.
        finalCoverUrl = await uploadCourseImageAction({
          file: coverImage,
          communityId,
          courseId,
        });
        // Timestamp is now added by the service, no need to add it here.
      } catch (error) {
        logger.error({ ...ctx, error }, 'Failed to upload course cover image');
        return {
          error: true,
          message: 'Failed to upload course cover image',
        };
      }
    } else {
      // If no new image, but a URL exists, add timestamp if missing
      // This busts cache even if only metadata changed
      if (finalCoverUrl && !finalCoverUrl.includes('?t=')) {
        finalCoverUrl = `${finalCoverUrl}?t=${Date.now()}`;
      }
    }

    try {
      // Call the update service method (service already instantiated)
      const data = await communityCourseService.updateCourse({
        courseId: params.courseId,
        title: params.title,
        description: params.description ?? '',
        coverUrl: finalCoverUrl,
        prerequisiteCourseIds: params.prerequisiteCourseIds,
        access: params.access,
      });

      logger.info(ctx, 'Course updated via service');

      // Revalidate paths
      // FIX: It should only be one of these
      revalidatePath(`${pathsConfig.app.courses}`, 'page');
      if (communitySlug) {
        revalidatePath(`/${communitySlug}/courses`, 'page');
      }
      revalidatePath('/[community]/courses', 'page');
      revalidatePath('/[community]/courses', 'layout');

      return {
        success: true,
        data: {
          id: data.id,
          slug: data.slug,
          title: data.title,
          description: data.description,
          coverUrl: data.coverUrl ?? undefined,
          prerequisiteCourseIds: data.prerequisiteCourseIds ?? [],
          access: data.access,
        },
      };
    } catch (error) {
      logger.error({ ...ctx, error }, 'Failed to update course via service');
      const message = error instanceof Error ? error.message : 'Unknown error';
      return {
        error: true,
        message: message,
      };
    }
  },
  {
    auth: true,
  },
);
