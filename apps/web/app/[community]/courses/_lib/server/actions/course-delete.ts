'use server';

import { revalidatePath } from 'next/cache';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import pathsConfig from '~/config/paths.config';
import { createCommunityCoursesService } from '~/lib/communities/courses/community-courses.service';

type DeleteCourseParams = {
  courseSlug: string;
};

export const deleteCourseAction = enhanceAction(
  async ({ courseSlug }: DeleteCourseParams, user) => {
    const client = getSupabaseServerClient();
    const logger = await getLogger();

    const ctx = {
      name: 'community.courses.delete',
      userId: user.id,
      courseSlug,
    };

    logger.info(ctx, `Deleting course...`);

    try {
      const communityCourseService = createCommunityCoursesService(client);

      await communityCourseService.deleteCourse({ courseSlug });

      logger.info(ctx, `Course deleted successfully via service`);

      // Revalidate paths after successful deletion
      revalidatePath(`${pathsConfig.app.courses}`, 'page');

      return {
        success: true,
        data: {
          courseSlug,
        },
      };
    } catch (error) {
      logger.error({ ...ctx, error }, `Failed to delete course`);
      const message = error instanceof Error ? error.message : 'Unknown error';
      return {
        success: false,
        error: message,
      };
    }
  },
  {
    auth: true,
  },
);
