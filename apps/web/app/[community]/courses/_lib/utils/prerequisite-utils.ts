import { PrerequisiteCourse, PrerequisiteId } from '../types';

/**
 * Utility functions for working with course prerequisites
 * Provides methods to convert between PrerequisiteCourse[] and PrerequisiteId[] formats
 */
export const prerequisiteUtils = {
  /**
   * Converts from PrerequisiteCourse[] to PrerequisiteId[]
   * Use when sending prerequisite data to the API or saving to the database
   *
   * @param prerequisites - Array of prerequisite course objects
   * @returns Array of prerequisite course IDs
   */
  toIds: (
    prerequisites: PrerequisiteCourse[] | undefined,
  ): PrerequisiteId[] => {
    return prerequisites?.map((p) => p.id) || [];
  },

  /**
   * Converts from PrerequisiteId[] to PrerequisiteCourse[]
   * Use when displaying prerequisite data in the UI
   *
   * @param ids - Array of prerequisite course IDs
   * @param coursesMap - Lookup map of course IDs to course data (must include title property)
   * @returns Array of prerequisite course objects with id, title, and completed status
   */
  toCourses: (
    ids: PrerequisiteId[] | undefined,
    coursesMap: Record<string, { title: string }>,
  ): PrerequisiteCourse[] => {
    return (
      ids?.map((id) => ({
        id,
        title: coursesMap[id]?.title || '',
        completed: false,
      })) || []
    );
  },
};
