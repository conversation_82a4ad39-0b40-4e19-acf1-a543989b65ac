/**
 * Formats a plural string based on the value
 * @param value - The numeric value
 * @param singular - The singular form of the word
 * @param plural - The plural form of the word
 * @returns Formatted string with the appropriate form
 * @example
 * pluralize(1, 'hour', 'hours') // "1 hour"
 * pluralize(2, 'min', 'mins') // "2 mins"
 */
function pluralize(value: number, singular: string, plural: string): string {
  return `${value} ${value === 1 ? singular : plural}`;
}

/**
 * Formats a duration in seconds into a human-readable string
 * @param durationSeconds - The duration in seconds
 * @throws {Error} If duration is not a finite number or is negative
 * @returns Formatted duration string (e.g., "5 mins", "2 hours 30 mins", "3 days 2 hours")
 * @example
 * formatDuration(150) // "2 mins"
 * formatDuration(3600) // "1 hour"
 * formatDuration(3900) // "1 hour 5 mins"
 * formatDuration(90000) // "1 day 1 hour"
 */

const SECONDS_IN_MINUTE = 60;
const MINUTES_IN_HOUR = 60;
const MINUTES_IN_DAY = 1440;

export function formatDuration(durationSeconds: number): string {
  if (!Number.isFinite(durationSeconds)) {
    throw new Error('Duration must be a finite number');
  }

  if (durationSeconds < 0) {
    throw new Error('Duration cannot be negative');
  }

  if (durationSeconds === 0) {
    return '0 mins';
  }

  const minutes = Math.round(durationSeconds / SECONDS_IN_MINUTE);

  if (minutes < MINUTES_IN_HOUR) {
    return pluralize(minutes, 'min', 'mins');
  } else if (minutes < MINUTES_IN_DAY) {
    const hours = Math.floor(minutes / MINUTES_IN_HOUR);
    const remainingMinutes = minutes % MINUTES_IN_HOUR;
    return `${pluralize(hours, 'hour', 'hours')}${
      remainingMinutes > 0
        ? ` ${pluralize(remainingMinutes, 'min', 'mins')}`
        : ''
    }`;
  } else {
    const days = Math.floor(minutes / MINUTES_IN_DAY);
    const hours = Math.floor((minutes % MINUTES_IN_DAY) / MINUTES_IN_HOUR);
    return `${pluralize(days, 'day', 'days')}${
      hours > 0 ? ` ${pluralize(hours, 'hour', 'hours')}` : ''
    }`;
  }
}
