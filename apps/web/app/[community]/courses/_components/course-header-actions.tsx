import { useTransition } from 'react';

import { useRouter } from 'next/navigation';

import {
  ArchiveIcon,
  ArchiveRestoreIcon,
  ChevronLeft,
  ChevronRight,
  Edit,
  EyeIcon,
  EyeOffIcon,
  MoreVertical,
  Trash2,
} from 'lucide-react';
import { toast } from 'sonner';

import { Button } from '@kit/ui/button';
import type { Course as UICourse } from '@kit/ui/dojo/types';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@kit/ui/dropdown-menu';
import { cn } from '@kit/ui/utils';

import type {
  CoursePaginated,
  PrerequisiteCourse,
} from '~/lib/communities/courses/types';
import type { PermissionsEnum } from '~/lib/communities/members/types';

import { deleteCourseAction } from '../_lib/server/actions/course-delete';
import { reorderCourseAction } from '../_lib/server/actions/reorder-course';
import { updateCourseStatusAction } from '../_lib/server/actions/update-course-status-action';
import { DeleteCourseAlert } from './delete-course-alert';
import { EditCourseDialog } from './edit-course-dialog';

type CourseHeaderActionsProps = {
  course: CoursePaginated;
  isFirst: boolean;
  isLast: boolean;
  minimalCourseData: Array<{
    id: string;
    title: string;
    prerequisites: string[];
  }>;
  permissions?: PermissionsEnum[];
  onCourseUpdate: (isPending: boolean) => void;
  onCourseReordered: (courseId: string | null) => void;
};

export function CourseHeaderActions({
  course,
  isFirst,
  isLast,
  minimalCourseData,
  permissions,
  onCourseUpdate,
  onCourseReordered,
}: CourseHeaderActionsProps) {
  const [isPending, startTransition] = useTransition();
  const router = useRouter();

  const canPublish = permissions?.includes('community.courses.publish');
  const canArchive = permissions?.includes('community.courses.archive');

  const canDelete = permissions?.includes('community.courses.delete');

  const handleMove = (direction: 'left' | 'right') => {
    onCourseUpdate(true);
    onCourseReordered(course.id);
    startTransition(async () => {
      try {
        const result = await reorderCourseAction({
          courseId: course.id,
          direction,
        });
        toast[result.success ? 'success' : 'error'](
          result.success
            ? 'Course moved successfully'
            : 'Failed to move course',
        );
        router.refresh();
      } catch (err) {
        console.error('An error occurred while reordering', err);
        toast.error('An error occurred while reordering');
      } finally {
        onCourseUpdate(false);
        onCourseReordered(null);
      }
    });
  };

  const handleUpdateStatus = (
    courseSlug: string,
    targetStatus: UICourse['status'],
  ) => {
    onCourseUpdate(true);
    startTransition(async () => {
      try {
        const result = await updateCourseStatusAction({
          courseSlug,
          targetStatus,
        });
        if (result.success) {
          toast.success('Course status updated successfully');
          router.refresh();
        } else {
          toast.error('Failed to update course status');
        }
      } catch (error) {
        console.error(`An error occurred while updating course status`, error);
        toast.error('An error occurred while updating course status');
      } finally {
        onCourseUpdate(false);
      }
    });
  };

  const handleDeleteCourse = (courseSlug: string) => {
    onCourseUpdate(true);
    startTransition(async () => {
      try {
        const result = await deleteCourseAction({
          courseSlug,
        });

        if (result.success) {
          toast.success('Course deleted successfully');
          router.refresh();
        } else {
          toast.error('Failed to delete course');
        }
      } catch (error) {
        console.error(`An error occurred while deleting course`, error);
        toast.error('An error occurred while deleting course');
      } finally {
        onCourseUpdate(false);
      }
    });
  };

  return (
    <div className="flex items-center gap-1">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon" data-test="course-options-button">
            <MoreVertical className="h-4 w-4" />
            <span className="sr-only">Course options</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          {course.status === 'published' && (
            <>
              <DropdownMenuItem
                onClick={() => handleMove('left')}
                disabled={isFirst || isPending}
                className={cn(
                  isFirst ? 'text-muted-foreground' : '',
                  'cursor-pointer',
                )}
                data-test="move-left-button"
                aria-label="Move course left"
              >
                <ChevronLeft className="mr-2 h-4 w-4" />
                Move Left
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => handleMove('right')}
                disabled={isLast || isPending}
                className={cn(
                  isLast ? 'text-muted-foreground' : '',
                  'cursor-pointer',
                )}
                data-test="move-right-button"
                aria-label="Move course right"
              >
                <ChevronRight className="mr-2 h-4 w-4" />
                Move Right
              </DropdownMenuItem>
            </>
          )}

          <EditCourseDialog
            course={{
              id: course.id,
              title: course.title,
              description: course.description,
              coverImage: course.coverUrl,
              prerequisites:
                (course.prerequisites as unknown as PrerequisiteCourse[]) ?? [],
              status: course.status,
            }}
            allCourses={minimalCourseData}
          >
            <DropdownMenuItem
              onSelect={(e) => {
                e.preventDefault();
              }}
              data-test="edit-course-button"
              className="cursor-pointer"
              disabled={isPending}
              aria-label="Edit course"
            >
              <Edit className="mr-2 h-4 w-4" />
              Edit Course
            </DropdownMenuItem>
          </EditCourseDialog>
          {course.status === 'published' && canPublish && (
            <DropdownMenuItem
              onClick={() => handleUpdateStatus(course.slug, 'draft')}
              data-test="unpublish-button"
              className="cursor-pointer"
              disabled={isPending}
              aria-label="Unpublish course"
            >
              <EyeOffIcon className="mr-2 h-4 w-4" />
              Unpublish
            </DropdownMenuItem>
          )}
          {course.status === 'draft' && (
            <>
              {canPublish && (
                <DropdownMenuItem
                  onClick={() => handleUpdateStatus(course.slug, 'published')}
                  data-test="publish-button"
                  className="cursor-pointer"
                  disabled={isPending}
                  aria-label="Publish course"
                >
                  <EyeIcon className="mr-2 h-4 w-4" />
                  Publish
                </DropdownMenuItem>
              )}
              {canArchive && (
                <DropdownMenuItem
                  onClick={() => handleUpdateStatus(course.slug, 'archived')}
                  data-test="archive-button"
                  className="cursor-pointer"
                  disabled={isPending}
                  aria-label="Archive course"
                >
                  <ArchiveIcon className="mr-2 h-4 w-4" />
                  Archive
                </DropdownMenuItem>
              )}
            </>
          )}
          {course.status === 'archived' && canArchive && (
            <DropdownMenuItem
              onClick={() => handleUpdateStatus(course.slug, 'draft')}
              data-test="unarchive-button"
              className="cursor-pointer"
              disabled={isPending}
              aria-label="Unarchive course"
            >
              <ArchiveRestoreIcon className="mr-2 h-4 w-4" />
              Unarchive
            </DropdownMenuItem>
          )}
          {canDelete && course.status !== 'published' && (
            <DeleteCourseAlert
              isDeleting={isPending}
              onDelete={async () => await handleDeleteCourse(course.slug)}
            >
              <DropdownMenuItem
                onSelect={(e) => {
                  e.preventDefault();
                }}
                data-test="delete-button"
                className="cursor-pointer"
                disabled={isPending}
                aria-label="Delete course"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DeleteCourseAlert>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
