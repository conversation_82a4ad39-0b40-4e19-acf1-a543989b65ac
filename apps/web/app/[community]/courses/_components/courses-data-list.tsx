'use client';

import { Fragment, useState } from 'react';

import { useRouter } from 'next/navigation';

import { Clock4, DollarSign, Lock, Star } from 'lucide-react';

import { CourseCard } from '@kit/ui/dojo/organisms/course-card';
import { CourseVisibilityDialog } from '@kit/ui/dojo/organisms/course-visibility-dialog';
import type { Course as UICourse } from '@kit/ui/dojo/types';

import pathsConfig from '~/config/paths.config';
import type {
  CourseAccessEnum,
  CoursePaginated,
  PrerequisiteCourse,
} from '~/lib/communities/courses/types';
import type { PermissionsEnum } from '~/lib/communities/members/types';

import { prerequisiteUtils } from '../_lib/utils';
import { CourseHeaderActions } from './course-header-actions';

/** Represents a duration in seconds */
export type Seconds = number & { __brand: 'Seconds' };

/**
 * Creates a Seconds value from a number.
 * @throws {Error} if the number is negative
 */
export function createSeconds(n: number): Seconds {
  if (n < 0) throw new Error('Seconds cannot be negative');
  return n as Seconds;
}

const OVERLAY_CONFIG = {
  private: {
    icon: Lock,
    title: 'Private',
    description: 'This course is private',
  },
  level: {
    icon: Star,
    title: 'Level',
    description: 'This course is a level course',
  },
  paid: {
    icon: DollarSign,
    title: 'Paid',
    description: 'This course is a paid course',
  },
  time: {
    icon: Clock4,
    title: 'Time',
    description: 'This course is a time course',
  },
} as const;

function transformToUICourse(course: CoursePaginated): UICourse {
  return {
    id: course.id,
    title: course.title,
    description: course.description,
    coverUrl: course.coverUrl,
    slug: course.slug,
    status: course.status,
    prerequisites:
      (course.prerequisites as unknown as PrerequisiteCourse[]) ?? [],
    prerequisiteParents: (
      (course.prerequisiteParents as unknown as { id: string }[]) ?? []
    ).map((p) => ({ id: p.id })),
    lessons: course.lessons || 0,
    durationSeconds: createSeconds(course.durationSeconds ?? 0),
    studentStatus: 'not_enrolled',
    progressSeconds: createSeconds(0),
  };
}

function getOverlayConfig(access: CourseAccessEnum) {
  if (access === 'standard') return undefined;

  const config = OVERLAY_CONFIG[access];
  if (!config) return undefined;

  const Icon = config.icon;
  return {
    type: access,
    title: config.title,
    description: config.description,
    icon: <Icon className="mb-2 h-8 w-8" />,
  };
}

type CoursesDataListProps = {
  communitySlug: string;
  courses: CoursePaginated[];
  pageSize: number;
  pageIndex: number;
  pageCount: number;
  permissions?: PermissionsEnum[];
  publicView?: boolean;
};

export function CoursesDataList({
  communitySlug,
  courses,
  permissions,
  publicView,
}: CoursesDataListProps) {
  const [isPending, setIsPending] = useState(false);
  /**
   * Track which course is currently being reordered to show loading state
   * only on that specific card rather than all cards simultaneously.
   * This provides more targeted feedback to the user about which action
   * is in progress.
   */
  const [movingCourseId, setMovingCourseId] = useState<string | null>(null);
  const router = useRouter();
  const [openVisibility, setOpenVisibility] = useState<string | null>(null);

  const canEdit = permissions?.includes('community.courses.update');

  const showBadge = permissions?.includes('community.courses.update');

  const minimalCourseData = courses.map((course) => ({
    id: course.id,
    title: course.title,
    prerequisites: prerequisiteUtils.toIds(
      course.prerequisites as unknown as PrerequisiteCourse[],
    ),
  }));

  return (
    <div className="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
      {courses.map((course, index) => {
        const overlay = getOverlayConfig(course.access);
        const uiCourse = transformToUICourse(course);
        const isFirst = index === 0;
        const isLast = index === courses.length - 1;

        return (
          <Fragment key={course.id}>
            <div
              data-test={`course-card-${uiCourse.id}`}
              aria-label={`View course ${uiCourse.title}`}
              onClick={() => {
                if (overlay) {
                  setOpenVisibility(course.id);
                } else if (!publicView) {
                  const path = pathsConfig.app.course
                    .replace('[community]', communitySlug)
                    .replace('[courseSlug]', course.slug);
                  router.push(path);
                }
              }}
              // Adding a cursor pointer to indicate it's clickable, if not already handled by CourseCard
              // Depending on CourseCard's own styling, this might need adjustment or removal.
              // For now, assuming the wrapper needs to signify clickability.
              className="cursor-pointer" 
            >
              <CourseCard
                course={uiCourse}
                publicView={publicView}
                showBadge={showBadge}
                headerActions={
                  canEdit ? (
                    <CourseHeaderActions
                      course={course}
                      isFirst={isFirst}
                      isLast={isLast}
                      minimalCourseData={minimalCourseData}
                      permissions={permissions}
                      onCourseUpdate={setIsPending}
                      onCourseReordered={setMovingCourseId}
                    />
                  ) : undefined
                }
                overlay={overlay}
                // onCardClick is now handled by the wrapper div
                formatDuration={(seconds) => `${Math.floor(seconds / 60)} min`}
                isLoading={isPending && movingCourseId === course.id}
              />
            </div>
            {overlay && (
              <CourseVisibilityDialog
                course={uiCourse}
                text={overlay.title}
                description={overlay.description}
                icon={overlay.icon}
                isOpen={openVisibility === course.id}
                onOpenChange={(open: boolean) =>
                  setOpenVisibility(open ? course.id : null)
                }
              />
            )}
          </Fragment>
        );
      })}
    </div>
  );
}

CoursesDataList.displayName = 'CoursesDataList';
