'use client';

import { useTranslation } from 'react-i18next';

import { Button } from '@kit/ui/button';

type LoadMoreCoursesProps = {
  hasMore: boolean;
  isLoading?: boolean;
  onLoadMore: () => void;
};

export function LoadMoreCourses({
  hasMore,
  isLoading = false,
  onLoadMore,
}: LoadMoreCoursesProps) {
  const { t } = useTranslation('courses');

  if (!hasMore) {
    return null;
  }

  return (
    <div className="flex justify-center">
      <Button
        variant="outline"
        onClick={onLoadMore}
        disabled={isLoading}
        className="min-w-[200px]"
        data-test="load-more-courses-button"
      >
        {isLoading ? t('home.loadMore.loading') : t('home.loadMore.showMore')}
      </Button>
    </div>
  );
}

LoadMoreCourses.displayName = 'LoadMoreCourses';
