'use client';

import React, { ReactNode, useTransition } from 'react';

import { useRouter } from 'next/navigation';

import { Trash2 } from 'lucide-react';
import { toast } from 'sonner';

import { Button } from '@kit/ui/button';
import { Spinner } from '@kit/ui/spinner';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@kit/ui/tooltip';

import pathsConfig from '~/config/paths.config';
import { useCommunityWorkspace } from '~/lib/communities/community/hooks/use-community-workspace';

import { deleteCourseAction } from '../_lib/server/actions/course-delete';
import { DeleteCourseAlert } from './delete-course-alert';

export type DeleteCourseButtonProps = {
  courseSlug: string;
  variant?: 'default' | 'icon';
  className?: string;
  icon?: ReactNode;
  containerClassName?: string;
};

export const DeleteCourseButton: React.FC<DeleteCourseButtonProps> = ({
  courseSlug,
  variant = 'icon',
  className = '',
  icon = (
    <Trash2 className={variant === 'default' ? 'mr-2 h-5 w-5' : 'h-4 w-4'} />
  ),
  containerClassName = '',
}) => {
  const [isDeleting, startTransition] = useTransition();
  const { community } = useCommunityWorkspace();
  const router = useRouter();

  const handleDelete = async (): Promise<void> => {
    return new Promise((resolve, reject) => {
      startTransition(async () => {
        try {
          const result = await deleteCourseAction({ courseSlug });

          if (result.error) {
            toast.error(
              result.error || 'Failed to delete course. Please try again.',
            );
            reject(new Error(result.error || 'Failed to delete course.'));
          } else {
            toast.success('Course deleted successfully');
            router.push(
              pathsConfig.app.courses.replace('[community]', community.slug),
            );
            resolve();
          }
        } catch (error) {
          console.error('Failed to delete course:', error);
          toast.error('An unexpected error occurred. Please try again.');
          reject(error);
        }
      });
    });
  };

  // Icon variant (small rounded button with tooltip)
  if (variant === 'icon') {
    return (
      <DeleteCourseAlert isDeleting={isDeleting} onDelete={handleDelete}>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                size="icon"
                className={`h-8 w-8 rounded-full ${className}`}
                disabled={isDeleting}
                data-test="delete-course-icon-button"
                aria-label="Delete course"
              >
                {icon}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Delete Course</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </DeleteCourseAlert>
    );
  }

  // Default variant (regular button)
  return (
    <div
      className={`flex h-full w-full items-center justify-center ${containerClassName}`}
    >
      <DeleteCourseAlert isDeleting={isDeleting} onDelete={handleDelete}>
        <Button
          disabled={isDeleting}
          className={`px-8 py-4 ${className}`}
          data-test="delete-course-text-button"
          aria-label="Delete course"
        >
          {isDeleting ? (
            <>
              <Spinner className="mr-2 h-4 w-4" />
              Deleting course...
            </>
          ) : (
            <>
              {icon}
              Delete Course
            </>
          )}
        </Button>
      </DeleteCourseAlert>
    </div>
  );
};

DeleteCourseButton.displayName = 'DeleteCourseButton';
