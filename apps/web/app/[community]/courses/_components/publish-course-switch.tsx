'use client';

import { startTransition } from 'react';

import { useRouter } from 'next/navigation';

import { toast } from 'sonner';

import { StatusBadge } from '@kit/ui/dojo/atoms/status-badge';
import { Switch } from '@kit/ui/switch';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@kit/ui/tooltip';
import { cn } from '@kit/ui/utils';

import type { CourseStatusEnum } from '~/lib/communities/courses/types';

import { toggleCoursePublishedAction } from '../_lib/server/actions/toggle-course-publised';

const CourseStatus = {
  published: 'published',
  draft: 'draft',
  archived: 'archived',
} as const satisfies Record<string, CourseStatusEnum>;

type PublishCourseSwitchProps = {
  courseSlug: string;
  status: CourseStatusEnum;
  className?: string;
};

export function PublishCourseSwitch({
  courseSlug,
  status,
  className,
}: PublishCourseSwitchProps) {
  const router = useRouter();

  const handlePublishToggle = () => {
    startTransition(async () => {
      try {
        await toggleCoursePublishedAction({
          courseSlug: courseSlug,
        });
        router.refresh();

        toast.success(
          status === CourseStatus.published
            ? 'Course published successfully'
            : 'Course unpublished successfully',
        );
      } catch (error) {
        console.error('Failed to toggle publish status:', error);
        toast.error('Failed to update publish status. Please try again.');
      }
    });
  };

  return (
    <div className={cn('inline-flex items-center gap-2', className)}>
      <StatusBadge
        status={status}
        studentStatus="not_enrolled"
        overlay={false}
        className="items-center"
      />
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="items-center">
              <Switch
                checked={status === CourseStatus.published}
                onCheckedChange={handlePublishToggle}
                data-test="publish-course-switch"
                aria-label="Toggle course publish status"
              />
            </div>
          </TooltipTrigger>
          <TooltipContent>
            {status === CourseStatus.published ? (
              <p>
                Unpublish the course, no lessons will be visible to members.
              </p>
            ) : (
              <p>
                Publish the course, this will also publish all draft lessons
                within the course.
              </p>
            )}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
}

PublishCourseSwitch.displayName = 'PublishCourseSwitch';
