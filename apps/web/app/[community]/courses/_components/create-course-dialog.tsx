'use client';

import { startTransition, useEffect, useState } from 'react';

import { isRedirectError } from 'next/dist/client/components/redirect-error';
import { useRouter } from 'next/navigation';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { Button } from '@kit/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@kit/ui/dialog';
import { StatusAlert } from '@kit/ui/dojo/molecules/status-alert';
import { If } from '@kit/ui/if';

import pathsConfig from '~/config/paths.config';
import { courseSchema } from '~/lib/communities/courses/schema/course.schema';

import { createCourseAction } from '../_lib/server/actions/create-course-action';
import { CourseForm } from './course-form';
import type { MinimalCourseData } from './course-form';

type CreateCourseDialogProps = {
  communityId: string;
  communitySlug: string;
  allCourses: MinimalCourseData[] | [];
  open: boolean;
  onOpenChange: (open: boolean) => void;
};

export function CreateCourseDialog({
  communityId,
  communitySlug,
  allCourses,
  open,
  onOpenChange,
}: CreateCourseDialogProps) {
  const router = useRouter();
  const [error, setError] = useState<boolean>();
  const [pending, setPending] = useState(false);
  const form = useForm<z.infer<typeof courseSchema>>({
    resolver: zodResolver(courseSchema),
    defaultValues: {
      title: '',
      description: '',
      coverImage: null,
      prerequisites: [],
      status: 'draft',
    },
  });

  useEffect(() => {
    if (!open) {
      form.reset({
        title: '',
        description: '',
        coverImage: null,
        prerequisites: [],
        status: 'draft',
      });
      setError(false);
    }
  }, [open, form]);

  const handleSubmit = (data: z.infer<typeof courseSchema>) => {
    setPending(true);
    startTransition(async () => {
      try {
        const baseParams = {
          title: data.title,
          description: data.description,
          coverImage:
            data.coverImage instanceof File ? data.coverImage : undefined,
          coverUrl:
            typeof data.coverImage === 'string' ? data.coverImage : undefined,
          prerequisiteCourseIds: data.prerequisites ?? undefined,
          access: 'standard' as const,
        };

        const response = await createCourseAction({
          ...baseParams,
          communityId,
        });

        if (response.error) {
          setError(true);
          setPending(false);
          return;
        }

        onOpenChange(false);
        form.reset();
        toast.success('Course created successfully');
        if (response.data?.slug) {
          const path = pathsConfig.app.course
            .replace('[community]', communitySlug)
            .replace('[courseSlug]', response.data.slug);
          router.push(path);
        }
      } catch (err) {
        if (!isRedirectError(err)) {
          setError(true);
          toast.error('An error occurred while creating the course');
        }
      } finally {
        setPending(false);
      }
    });
  };

  // TODO: delete handler - parameters removed as they are unused
  const handleImageDelete = async () => {
    // In create mode, deleting just clears the form field via ImageUploadContainer's internal
    // call to onDelete, which triggers handleClear, which calls our field.onChange(null) via the
    // main handleDelete passed down from CourseImageUpload (which we didn't implement fully here,
    // but ImageUploadContainer's handleClear does the job for UI state).
    // No backend action is needed since the image hasn't been saved yet.
    console.warn(
      'handleImageDelete called in CreateCourseDialog - primarily clears UI state',
    );
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(newOpen) => {
        onOpenChange(newOpen);
      }}
    >
      <DialogContent className="sm:max-w-2xl" data-test="create-course-dialog">
        <DialogHeader>
          <DialogTitle>Create New Course</DialogTitle>
          <DialogDescription>
            Fill in the details to create a new course.
          </DialogDescription>
        </DialogHeader>
        <If condition={error}>
          <StatusAlert
            variant="destructive"
            titleKey="common:error"
            descriptionKey="common:genericError"
          />
        </If>
        <CourseForm
          form={form}
          onSubmit={handleSubmit}
          availableCourses={allCourses}
          onImageDelete={handleImageDelete}
          pending={pending}
          submitButtonText="Create Course"
        />
        <Button
          type="button"
          variant="outline"
          onClick={() => {
            onOpenChange(false);
          }}
          className="mt-4 w-full"
          data-test="course-cancel-button"
          aria-label="Cancel creating course"
        >
          Cancel
        </Button>
      </DialogContent>
    </Dialog>
  );
}
