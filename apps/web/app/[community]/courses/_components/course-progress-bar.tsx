import { Progress } from '@kit/ui/progress';

import { formatDuration } from '../_lib/utils/formatDuration';

/**
 * Props for the CourseProgressBar component
 * @property {number} totalLessons - Total number of lessons in the course
 * @property {number} durationSeconds - Total duration of the course in seconds
 * @property {number} [completedLessons] - Number of completed lessons
 * @property {number} [progressSeconds] - Time spent on the course in seconds
 * @property {'lessons' | 'time'} displayMode - Display mode for progress
 */
type CourseProgressBarProps = {
  totalLessons: number;
  durationSeconds: number;
  completedLessons?: number;
  progressSeconds?: number;
  displayMode: 'lessons' | 'time';
};

export function CourseProgressBar({
  totalLessons,
  completedLessons,
  durationSeconds,
  progressSeconds,
  displayMode,
}: CourseProgressBarProps) {
  const progress = completedLessons
    ? totalLessons > 0
      ? Math.round((completedLessons / totalLessons) * 100)
      : 0
    : 0;

  const remainingSeconds = progressSeconds
    ? Math.max(0, durationSeconds - progressSeconds)
    : 0;

  const paddingClass =
    displayMode === 'time'
      ? 'flex justify-between text-xs text-gray-600 dark:text-gray-400 mt-2'
      : 'flex justify-between text-xs text-gray-600 dark:text-gray-400';

  return (
    <div className="w-full space-y-1" data-test="course-progress-bar">
      <div className={paddingClass}>
        <span>{progress}% complete</span>
        {displayMode === 'lessons' ? (
          <span>
            {completedLessons} of {totalLessons} lessons
          </span>
        ) : (
          <span>{formatDuration(remainingSeconds)} remaining</span>
        )}
      </div>
      <Progress
        value={progress}
        className="w-full bg-gray-300 [&>div]:bg-green-500"
      />
    </div>
  );
}

CourseProgressBar.displayName = 'CourseProgressBar';
