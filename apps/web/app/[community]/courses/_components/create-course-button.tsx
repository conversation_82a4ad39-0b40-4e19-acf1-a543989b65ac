'use client';

import { useState } from 'react';

import { PlusCircle } from 'lucide-react';

import { Button } from '@kit/ui/button';

import { CreateCourseDialog } from './create-course-dialog';

type CreateCourseActionProps = {
  communityId: string;
  communitySlug: string;
};

export function CreateCourseButton({
  communityId,
  communitySlug,
}: CreateCourseActionProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  return (
    <>
      <Button
        onClick={() => setIsDialogOpen(true)}
        className="gap-2"
        data-test="create-course-button"
        aria-label="Create new course"
      >
        <PlusCircle className="h-4 w-4" />
        Create Course
      </Button>

      <CreateCourseDialog
        communityId={communityId}
        communitySlug={communitySlug}
        allCourses={[]}
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
      />
    </>
  );
}

CreateCourseButton.displayName = 'CreateCourseButton';
