'use client';

import { useTransition } from 'react';

import { useRouter } from 'next/navigation';

import {
  Archive,
  CheckCircle,
  ChevronDown,
  RotateCcw,
  Trash2,
  XCircle,
} from 'lucide-react';
import { toast } from 'sonner';

import { But<PERSON> } from '@kit/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@kit/ui/dropdown-menu';
import { Spinner } from '@kit/ui/spinner';
import { cn } from '@kit/ui/utils';

import pathsConfig from '~/config/paths.config';
import { useCommunityWorkspace } from '~/lib/communities/community/hooks/use-community-workspace';
import type { CourseStatusEnum } from '~/lib/communities/courses/types';
import type { PermissionsEnum } from '~/lib/communities/members/types';
import { hasCommunityPermission } from '~/lib/communities/permissions.utils';

import { deleteCourseAction } from '../_lib/server/actions/course-delete';
import { updateCourseStatusAction } from '../_lib/server/actions/update-course-status-action';
import { DeleteCourseAlert } from './delete-course-alert';

type PublishCourseSplitButtonProps = {
  courseSlug: string;
  courseStatus: CourseStatusEnum;
  permissions: PermissionsEnum[];
  className?: string;
};

export function PublishCourseSplitButton({
  courseSlug,
  courseStatus,
  permissions,
  className,
}: PublishCourseSplitButtonProps) {
  const [isDeleting, startDeleteTransition] = useTransition();
  const { community } = useCommunityWorkspace();
  const router = useRouter();
  const [isPending, startStatusTransition] = useTransition();

  const handlePrimaryAction = async () => {
    switch (courseStatus) {
      case 'published':
        handleStatusChange('draft');
        break;
      case 'archived':
        handleStatusChange('draft');
        break;
      default:
        handleStatusChange('published');
    }
  };

  const handleStatusChange = async (targetStatus: string) => {
    try {
      startStatusTransition(async () => {
        await updateCourseStatusAction({
          courseSlug,
          targetStatus: targetStatus as CourseStatusEnum,
        });
        router.refresh();
      });
      let message;

      switch (targetStatus) {
        case 'published':
          message = 'Course published successfully';
          break;
        case 'draft':
          message = 'Course set to draft successfully';
          break;
        case 'archived':
          message = 'Course archived successfully';
          break;
        default:
          message = 'Course status updated successfully';
      }
      toast.success(message);
    } catch (error) {
      console.error('🔴 Error updating course status:', error);
      toast.error('Failed to update course status');
    }
  };

  const handleDelete = async (): Promise<void> => {
    return new Promise((resolve, reject) => {
      startDeleteTransition(async () => {
        try {
          const result = await deleteCourseAction({ courseSlug });

          if (result.error) {
            toast.error(
              result.error || 'Failed to delete course. Please try again.',
            );
            reject(new Error(result.error || 'Failed to delete course.'));
          } else {
            toast.success('Course deleted successfully');
            router.push(
              pathsConfig.app.courses.replace('[community]', community.slug),
            );
            resolve();
          }
        } catch (error) {
          console.error('Failed to delete course:', error);
          toast.error('An unexpected error occurred. Please try again.');
          reject(error);
        }
      });
    });
  };

  const getPrimaryAction = () => {
    switch (courseStatus) {
      case 'published':
        return {
          label: 'Unpublish',
          icon: <XCircle className="mr-2 h-4 w-4 text-white" />,
        };
      case 'archived':
        return {
          label: 'Unarchive',
          icon: <RotateCcw className="mr-2 h-4 w-4 text-white" />,
        };
      default:
        return {
          label: 'Publish',
          icon: <CheckCircle className="mr-2 h-4 w-4 text-white" />,
        };
    }
  };

  const { label: primaryLabel, icon: primaryIcon } = getPrimaryAction();

  const hasDeletePermission = hasCommunityPermission(
    permissions,
    'community.courses.delete',
  );
  // const hasPublishPermission = permissions.includes('community.courses.publish');
  const hasArchivePermission = hasCommunityPermission(
    permissions,
    'community.courses.archive',
  );

  return (
    <div className={cn('flex overflow-hidden', className)}>
      <Button
        variant="default"
        onClick={handlePrimaryAction}
        disabled={isPending || isDeleting}
        className="cursor-pointer rounded-r-none border-r-[1px] text-white"
        data-test="publish-course-primary-action"
        aria-label={primaryLabel}
      >
        {isPending ? (
          <>
            <Spinner className="mr-2 h-4 w-4" />
            {primaryLabel}
          </>
        ) : (
          <>
            {primaryIcon}
            {primaryLabel}
          </>
        )}
      </Button>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="default"
            disabled={isPending || isDeleting}
            className="cursor-pointer rounded-l-none text-white"
            data-test="publish-course-options-dropdown"
            aria-label="More course publishing options"
          >
            <ChevronDown className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          align="end"
          className="mt-1 w-[200px] rounded-xl p-2"
        >
          {courseStatus !== 'archived' && hasArchivePermission && (
            <DropdownMenuItem
              onClick={() => handleStatusChange('archived')}
              className="flex cursor-pointer items-center gap-2 rounded-lg py-2"
              data-test="archive-course-menu-item"
              aria-label="Archive course"
            >
              <Archive className="h-4 w-4 text-blue-500" />
              <span>Archive</span>
            </DropdownMenuItem>
          )}
          {hasDeletePermission && (
            <DeleteCourseAlert isDeleting={isDeleting} onDelete={handleDelete}>
              <DropdownMenuItem
                className="flex cursor-pointer items-center gap-2 rounded-lg py-2"
                onSelect={(e) => {
                  e.preventDefault();
                }}
                disabled={isDeleting}
                data-test="delete-course-menu-item"
                aria-label="Delete course"
              >
                {isDeleting ? (
                  <>
                    <Spinner className="mr-2 h-4 w-4" />
                    Deleting...
                  </>
                ) : (
                  <>
                    <Trash2 className="h-4 w-4 text-red-500" />
                    <span>Delete</span>
                  </>
                )}
              </DropdownMenuItem>
            </DeleteCourseAlert>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}

PublishCourseSplitButton.displayName = 'PublishCourseSplitButton';
