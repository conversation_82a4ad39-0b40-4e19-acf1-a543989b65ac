'use client';

import { useState } from 'react';

import { PlusCircle } from 'lucide-react';

import { Button } from '@kit/ui/button';
import { StatusFilter } from '@kit/ui/dojo/atoms/status-filter';

import type { CourseStatusEnum } from '~/lib/communities/courses/types';
import type { PermissionsEnum } from '~/lib/communities/members/types';
import { hasCommunityPermission } from '~/lib/communities/permissions.utils';

import { CreateCourseDialog } from './create-course-dialog';

type CourseStatusOption = {
  value: CourseStatusEnum;
  label: string;
};

type CoursesHeaderProps = {
  communityId: string;
  communitySlug: string;
  permissions: PermissionsEnum[];
};

/**
 * Available course status options for filtering.
 * Uses the CourseStatus type to ensure type safety and consistency across the application.
 */
const COURSE_STATUS_OPTIONS: CourseStatusOption[] = [
  { value: 'published', label: 'Published' },
  { value: 'draft', label: 'Draft' },
  { value: 'archived', label: 'Archived' },
];

export function CoursesHeader({
  communityId,
  communitySlug,
  permissions,
}: CoursesHeaderProps) {
  const hasCreatePermission = hasCommunityPermission(
    permissions,
    'community.courses.create',
  );
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-4">
        {hasCreatePermission && (
          <>
            <Button
              onClick={() => setIsCreateDialogOpen(true)}
              className="gap-2"
              data-test="header-create-course-button"
              aria-label="Create new course"
            >
              <PlusCircle className="h-4 w-4" />
              Create Course
            </Button>

            <CreateCourseDialog
              communityId={communityId}
              communitySlug={communitySlug}
              allCourses={[]}
              open={isCreateDialogOpen}
              onOpenChange={setIsCreateDialogOpen}
            />

            <StatusFilter
              options={COURSE_STATUS_OPTIONS}
              paramName="status"
              defaultValue="published"
              placeholder="Filter by status"
            />
          </>
        )}
      </div>
    </div>
  );
}

CoursesHeader.displayName = 'CoursesHeader';
