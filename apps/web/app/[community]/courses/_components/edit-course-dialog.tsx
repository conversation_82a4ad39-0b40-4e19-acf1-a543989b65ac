'use client';

import { ReactNode, startTransition, useEffect, useState } from 'react';

import { isRedirectError } from 'next/dist/client/components/redirect-error';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';
import { z } from 'zod';

import { Button } from '@kit/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@kit/ui/dialog';
import { StatusAlert } from '@kit/ui/dojo/molecules/status-alert';
import { If } from '@kit/ui/if';

import { courseSchema } from '~/lib/communities/courses/schema/course.schema';
import {
  CourseStatusEnum,
  PrerequisiteCourse,
} from '~/lib/communities/courses/types';

import { deleteCourseImageAction } from '../_lib/server/actions/delete-course-image-action';
import { updateCourseAction } from '../_lib/server/actions/update-course-action';
import { prerequisiteUtils } from '../_lib/utils';
import { CourseForm } from './course-form';
import type { MinimalCourseData } from './course-form';

/**
 * Props for the EditCourseDialog component
 *
 * @type EditCourseDialogProps
 * Course object containing all details of the course being edited
 * @property {string} id - Unique identifier for the course
 * @property {string} title - Title of the course
 * @property {string} description - Description of the course content
 * @property {string} [coverImage] - Optional URL for the course cover image
 * @property {PrerequisiteCourse[]} [prerequisites] - Optional array of prerequisite course objects
 *   NOTE: This prerequisite format (objects with id, title, completed) differs from the format in allCourses (string IDs).
 *   When passing data between components, ensure proper conversion between these formats:
 *   - To convert from PrerequisiteCourse[] to string[]: prerequisites.map(p => p.id)
 *   - To convert from string[] to PrerequisiteCourse[]: ids.map(id => ({ id, title: courseById[id]?.title || '', completed: false }))
 * @property {'draft' | 'published' | 'archived'} status - Current publication status of the course
 */
type EditCourseDialogProps = {
  course: {
    id: string;
    title: string;
    description: string;
    coverImage?: string;
    prerequisites?: PrerequisiteCourse[];
    prerequisiteParents?: { id: string }[];
    status: CourseStatusEnum;
  };
  /**
   * List of all available courses for prerequisite selection
   * @note The prerequisites property here uses string[] (PrerequisiteIds) while the course object uses PrerequisiteCourse[] format.
   * This inconsistency requires mapping between formats when handling prerequisites.
   */
  allCourses: MinimalCourseData[] | [];
  /**
   * React node to use as the trigger for opening the edit dialog
   * Typically a button or menu item that will open the edit course form when clicked
   */
  children: ReactNode;
};

/**
 * Helper function to get all dependent courses to prevent circular dependencies
 */
const getAllDependentCourses = (
  courseId: string,
  allCourses: MinimalCourseData[],
): Set<string> => {
  const dependentCourses = new Set<string>();

  const addDependents = (id: string) => {
    allCourses.forEach((c) => {
      if (c.prerequisites?.includes(id) && !dependentCourses.has(c.id)) {
        dependentCourses.add(c.id);
        addDependents(c.id);
      }
    });
  };

  addDependents(courseId);
  return dependentCourses;
};

/**
 * EditCourseDialog - A component that provides a dialog for editing a course
 *
 * This implementation handles both the dialog trigger and the form for editing a course
 * - Uses DialogTrigger with asChild to properly wrap the trigger element
 * - Stops propagation at the trigger level
 * - The children (typically a DropdownMenuItem) should handle preventing dropdown closing
 */
export function EditCourseDialog({
  course,
  allCourses,
  children,
}: EditCourseDialogProps) {
  const { t } = useTranslation('courses');
  const [open, setOpen] = useState(false);
  const [error, setError] = useState<boolean>();
  const [errorMessage, setErrorMessage] = useState<string>(
    'common:genericError',
  );
  const [pending, setPending] = useState(false);

  const form = useForm<z.infer<typeof courseSchema>>({
    resolver: zodResolver(courseSchema),
    defaultValues: {
      title: course.title,
      description: course.description,
      coverImage: course.coverImage,
      prerequisites: prerequisiteUtils.toIds(course.prerequisites),
      status: course.status,
    },
  });

  useEffect(() => {
    if (open && course) {
      form.reset({
        title: course.title,
        description: course.description,
        coverImage: course.coverImage,
        prerequisites: prerequisiteUtils.toIds(course.prerequisites),
        status: course.status,
      });
    } else if (!open) {
      setError(false);
    }
  }, [open, course, form]);

  // Calculate available courses for prerequisites based on the current course
  let availableCourses: MinimalCourseData[] = [];
  if (allCourses && allCourses.length > 0) {
    const dependentCourses = getAllDependentCourses(course.id, allCourses);
    availableCourses = allCourses.filter((c) => {
      if (c.id === course.id) return false;
      if (dependentCourses.has(c.id)) return false;
      if (c.prerequisites?.includes(course.id)) return false;
      if (course.prerequisiteParents?.some((parent) => parent.id === c.id))
        return false;
      return true;
    });
  }

  const handleImageDelete = async (courseId: string, imageUrl: string) => {
    try {
      await deleteCourseImageAction({ courseId, url: imageUrl });
      toast.success('Image deleted successfully');
      // Update form state to reflect image deletion
      form.setValue('coverImage', null, { shouldValidate: true });
    } catch (error) {
      console.error('Error deleting image', error);
      toast.error('Failed to delete image');
    }
  };

  const handleSubmit = (data: z.infer<typeof courseSchema>) => {
    setPending(true);
    startTransition(async () => {
      try {
        if (course.coverImage && !data.coverImage) {
          await handleImageDelete(course.id, course.coverImage);
        }

        const baseParams = {
          title: data.title,
          description: data.description,
          coverImage:
            data.coverImage instanceof File ? data.coverImage : undefined,
          coverUrl:
            typeof data.coverImage === 'string' ? data.coverImage : undefined,
          prerequisiteCourseIds: data.prerequisites ?? undefined,
          access: 'standard' as const, // Assuming standard access for now
        };

        const response = await updateCourseAction({
          ...baseParams,
          courseId: course.id,
        });

        if (response.error) {
          setError(true);
          setErrorMessage(response.message || 'common:genericError');
          setPending(false);
          return;
        }

        setOpen(false);
        toast.success('Course updated successfully');
      } catch (err) {
        if (!isRedirectError(err)) {
          setError(true);
          toast.error('An error occurred while updating the course');
        }
      } finally {
        setPending(false);
      }
    });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild onClick={(e) => e.stopPropagation()}>
        {children}
      </DialogTrigger>
      <DialogContent className="sm:max-w-2xl" data-test="edit-course-dialog">
        <DialogHeader>
          <DialogTitle>Edit Course</DialogTitle>
          <DialogDescription>
            Update the details of your existing course.
          </DialogDescription>
        </DialogHeader>
        <If condition={error}>
          <StatusAlert
            variant="destructive"
            titleKey="common:error"
            descriptionKey={errorMessage}
          />
        </If>
        <CourseForm
          form={form}
          onSubmit={handleSubmit}
          availableCourses={availableCourses}
          courseId={course.id}
          onImageDelete={handleImageDelete}
          pending={pending}
          submitButtonText="Update Course"
        />
        <Button
          type="button"
          variant="outline"
          onClick={() => setOpen(false)}
          className="mt-4 w-full cursor-pointer"
          data-test="course-cancel-button"
          aria-label={t('cancelEditingCourse')}
        >
          Cancel
        </Button>
      </DialogContent>
    </Dialog>
  );
}

EditCourseDialog.displayName = 'EditCourseDialog';
