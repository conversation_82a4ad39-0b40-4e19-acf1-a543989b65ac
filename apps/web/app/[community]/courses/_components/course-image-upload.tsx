import { useCallback } from 'react';

import { ControllerRenderProps } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { ImageUploadContainer } from '@kit/ui/dojo/organisms/image-upload-container';
import { FormControl, FormItem, FormLabel, FormMessage } from '@kit/ui/form';

import { courseSchema } from '~/lib/communities/courses/schema/course.schema';

const IMAGE_WIDTH = 1460;
const IMAGE_HEIGHT = 752;
const ASPECT_RATIO = IMAGE_WIDTH / IMAGE_HEIGHT;

type CourseFormValues = z.infer<typeof courseSchema>;

type CourseImageUploadProps = {
  field: ControllerRenderProps<CourseFormValues, 'coverImage'>;
  courseId?: string;
  onImageDelete?: (courseId: string, imageUrl: string) => Promise<void>;
};

export function CourseImageUpload({
  field,
  courseId,
  onImageDelete,
}: CourseImageUploadProps) {
  const handleDelete = useCallback(async () => {
    const currentValue = field.value;

    // Clear the RHF field value first
    field.onChange(null);

    // If the original value was a string URL and we have the necessary info, call the backend delete
    if (typeof currentValue === 'string' && courseId && onImageDelete) {
      try {
        await onImageDelete(courseId, currentValue);
        // No toast here, ImageUploadContainer might have its own feedback or parent handles it
      } catch (error) {
        console.error('Error deleting existing course image', error);
        toast.error('Failed to delete existing image. Reverting form change.');
        // Revert RHF change if backend delete failed
        field.onChange(currentValue);
      }
    }
    // If currentValue was a File, just clearing the field is enough (no backend action needed yet)
  }, [field, courseId, onImageDelete]);

  // Pass field.onChange directly as the onUpload callback
  const handleUpload = field.onChange;

  // Determine the initial imageUrl to pass (null if it's a File object)
  const initialImageUrl = typeof field.value === 'string' ? field.value : null;

  return (
    <FormItem data-test="course-image-upload-wrapper">
      <FormLabel>Cover Image</FormLabel>
      <FormControl>
        <ImageUploadContainer
          imageUrl={initialImageUrl} // Pass only the string URL or null
          headingKey="courses:courseImageHeading"
          uploadHeadingKey="courses:courseImageUploadHeading"
          uploadSubheadingKey="courses:courseImageUploadSubheading"
          aspectRatio={ASPECT_RATIO}
          imageRounded="rounded-lg"
          cropShape="rect"
          imageSize="w-full"
          onDelete={handleDelete} // Use the combined delete handler
          onUpload={handleUpload} // Pass RHF's onChange directly
        />
      </FormControl>
      <p className="text-muted-foreground pt-1 text-xs">
        Recommended size: {IMAGE_WIDTH}x{IMAGE_HEIGHT} pixels
      </p>
      <FormMessage />
    </FormItem>
  );
}

CourseImageUpload.displayName = 'CourseImageUpload';
