'use client';

import React from 'react';

import { UseFormReturn } from 'react-hook-form';
import { z } from 'zod';

import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { Textarea } from '@kit/ui/textarea';

import { courseSchema } from '~/lib/communities/courses/schema/course.schema';

import { CourseImageUpload } from './course-image-upload';

// Define MinimalCourseData locally and export it
export type MinimalCourseData = {
  id: string;
  title: string;
  prerequisites?: string[];
};

const MAX_TITLE_LENGTH = 60;
const MAX_DESCRIPTION_LENGTH = 160;

type CourseFormProps = {
  form: UseFormReturn<z.infer<typeof courseSchema>>;
  onSubmit: (data: z.infer<typeof courseSchema>) => void;
  availableCourses: MinimalCourseData[];
  courseId?: string;
  onImageDelete: (courseId: string, imageUrl: string) => Promise<void>;
  pending: boolean;
  submitButtonText: string;
};

export function CourseForm({
  form,
  onSubmit,
  availableCourses,
  courseId,
  onImageDelete,
  pending,
  submitButtonText,
}: CourseFormProps) {
  // Handler for title input with character limit
  const handleTitleChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    onChange: (value: string) => void,
  ) => {
    const value = e.target.value;
    if (value.length <= MAX_TITLE_LENGTH) {
      onChange(value);
    }
  };

  // Handler for description input with character limit
  const handleDescriptionChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>,
    onChange: (value: string) => void,
  ) => {
    const value = e.target.value;
    if (value.length <= MAX_DESCRIPTION_LENGTH) {
      onChange(value);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Title</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="Enter course title"
                  onChange={(e) => handleTitleChange(e, field.onChange)}
                  maxLength={MAX_TITLE_LENGTH}
                  data-test="course-title-input"
                  aria-label="Course title"
                />
              </FormControl>
              <FormDescription>
                {field.value.length}/{MAX_TITLE_LENGTH}
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  {...field}
                  value={field.value ?? ''}
                  placeholder="Enter course description"
                  onChange={(e) => handleDescriptionChange(e, field.onChange)}
                  maxLength={MAX_DESCRIPTION_LENGTH}
                  data-test="course-description-input"
                  aria-label="Course description"
                />
              </FormControl>
              <FormDescription>
                {field.value?.length ?? 0}/{MAX_DESCRIPTION_LENGTH}
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="coverImage"
          render={({ field }) => (
            <CourseImageUpload
              field={field}
              courseId={courseId}
              onImageDelete={onImageDelete}
              data-test="course-image-upload"
            />
          )}
        />
        <FormField
          control={form.control}
          name="prerequisites"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Prerequisite Courses</FormLabel>
              <FormControl>
                <div className="flex flex-wrap gap-2">
                  {availableCourses.map((availableCourse) => (
                    <Badge
                      key={availableCourse.id}
                      variant={
                        field.value?.includes(availableCourse.id)
                          ? 'default'
                          : 'outline'
                      }
                      className="cursor-pointer"
                      onClick={() => {
                        const newValue = field.value?.includes(
                          availableCourse.id,
                        )
                          ? field.value?.filter(
                              (id) => id !== availableCourse.id,
                            )
                          : [...(field.value ?? []), availableCourse.id];
                        field.onChange(newValue);
                      }}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          e.preventDefault();
                          const newValue = field.value?.includes(
                            availableCourse.id,
                          )
                            ? field.value?.filter(
                                (id) => id !== availableCourse.id,
                              )
                            : [...(field.value ?? []), availableCourse.id];
                          field.onChange(newValue);
                        }
                      }}
                      data-test={`prerequisite-badge-${availableCourse.id}`}
                      aria-label={`Toggle prerequisite ${availableCourse.title}`}
                      role="checkbox"
                      aria-checked={field.value?.includes(availableCourse.id)}
                      tabIndex={0}
                    >
                      {availableCourse.title}
                    </Badge>
                  ))}
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button
          disabled={pending}
          type="submit"
          className="w-full cursor-pointer"
          data-test="course-submit-button"
        >
          {submitButtonText}
        </Button>
      </form>
    </Form>
  );
}
