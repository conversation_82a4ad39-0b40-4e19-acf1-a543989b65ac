'use client';

import { useCallback, useEffect, useState } from 'react';

import type {
  CoursePaginated,
  CourseStatusEnum,
} from '~/lib/communities/courses/types';
import type { PermissionsEnum } from '~/lib/communities/members/types';

import { loadMoreCoursesAction } from '../_lib/server/actions/load-more-courses';
import { CoursesDataList } from './courses-data-list';
import { LoadMoreCourses } from './load-more-courses';

type CoursesLazyLoadProps = {
  communitySlug: string;
  initialCourses: CoursePaginated[];
  currentPage: number;
  pageCount: number;
  permissions?: PermissionsEnum[];
  statusFilter: CourseStatusEnum;
  publicView?: boolean;
};

export function CoursesLazyLoad({
  communitySlug,
  initialCourses,
  currentPage,
  pageCount,
  permissions,
  statusFilter,
  publicView,
}: CoursesLazyLoadProps) {
  const [courses, setCourses] = useState<CoursePaginated[]>(initialCourses);
  const [page, setPage] = useState(currentPage);
  const [isLoading, setIsLoading] = useState(false);
  const [hasMorePages, setHasMorePages] = useState(currentPage < pageCount);

  const resetState = useCallback(() => {
    setCourses(initialCourses);
    setPage(currentPage);
    setHasMorePages(currentPage < pageCount);
  }, [initialCourses, currentPage, pageCount]);

  useEffect(() => {
    resetState();
  }, [initialCourses, currentPage, pageCount, statusFilter, resetState]);

  const handleLoadMore = useCallback(async () => {
    if (isLoading) return;

    setIsLoading(true);
    try {
      const nextPage = page + 1;

      const newCourses = await loadMoreCoursesAction({
        communitySlug,
        page: nextPage,
        limit: 9,
        status: statusFilter,
      });

      if ('error' in newCourses) {
        console.error('Failed to load courses:', newCourses.message);
        setHasMorePages(false);
        return;
      }

      if (newCourses.length > 0) {
        setCourses((prevCourses) => [...prevCourses, ...newCourses]);
        setPage(nextPage);
        setHasMorePages(nextPage < pageCount);
      } else {
        setHasMorePages(false);
      }
    } catch (error) {
      console.error('Failed to load more courses:', error);
      setHasMorePages(false);
    } finally {
      setIsLoading(false);
    }
  }, [communitySlug, page, isLoading, statusFilter, pageCount]);

  return (
    <div className="mb-10 space-y-6">
      <CoursesDataList
        communitySlug={communitySlug}
        pageIndex={page - 1}
        pageCount={pageCount}
        pageSize={9}
        courses={courses}
        permissions={permissions}
        publicView={publicView}
      />

      {hasMorePages && (
        <LoadMoreCourses
          hasMore={hasMorePages}
          isLoading={isLoading}
          onLoadMore={handleLoadMore}
        />
      )}
    </div>
  );
}

CoursesLazyLoad.displayName = 'CoursesLazyLoad';
