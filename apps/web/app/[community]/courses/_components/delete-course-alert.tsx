import { ReactNode } from 'react';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@kit/ui/alert-dialog';

type CourseDeleteAlertProps = {
  isDeleting: boolean;
  onDelete: () => Promise<void>;
  children: ReactNode;
};

export function DeleteCourseAlert({
  isDeleting,
  onDelete,
  children,
}: CourseDeleteAlertProps) {
  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>{children}</AlertDialogTrigger>
      <AlertDialogContent data-test="delete-course-alert-dialog">
        <AlertDialogHeader>
          <AlertDialogTitle>Delete Course</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete this course? All course content will
            be deleted. This action cannot be undone.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel
            onClick={(e) => e.stopPropagation()}
            data-test="cancel-delete-course-button"
            aria-label="Cancel deleting course"
          >
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={(e) => {
              e.stopPropagation();
              void onDelete();
            }}
            disabled={isDeleting}
            data-test="confirm-delete-course-button"
            aria-label="Confirm delete course"
          >
            {isDeleting ? 'Deleting...' : 'Delete'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

DeleteCourseAlert.displayName = 'CourseDeleteAlert';
