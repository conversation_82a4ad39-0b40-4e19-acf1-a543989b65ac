import { useCallback } from 'react';

import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { ImageUploadContainer } from '@kit/ui/dojo/organisms/image-upload-container';
import { Trans } from '@kit/ui/trans';

import type { CommunityExpanded } from '~/lib/communities/community/types';
import type { PermissionsEnum } from '~/lib/communities/members/types';

import { deleteCommunityImageAction } from './_lib/server/actions/delete-community-image';
import { uploadCommunityImageAction } from './_lib/server/actions/upload-community-image';

export function CommunityImagesContainer(props: {
  community: CommunityExpanded;
  permissions: PermissionsEnum[];
}) {
  const { t } = useTranslation('communities');

  const createToaster = useCallback(
    (promise: () => Promise<unknown>) => {
      return toast.promise(promise, {
        success: t(`updateCommunitySuccessMessage`),
        error: t(`updateCommunityErrorMessage`),
        loading: t(`updateCommunityLoadingMessage`),
      });
    },
    [t],
  );

  const handleLogoDelete = useCallback(async () => {
    const promise = async () => {
      if (props.community.logoUrl) {
        await deleteCommunityImageAction({
          url: props.community.logoUrl,
          storageFolder: 'public_images',
          communityId: props.community.id,
          imageKey: 'logo_url',
        });
      }
    };

    await createToaster(promise);
  }, [createToaster, props.community.id, props.community.logoUrl]);

  const handleLogoUpload = useCallback(
    async (file: File) => {
      const promise = async () => {
        if (props.community.logoUrl) {
          try {
            await deleteCommunityImageAction({
              url: props.community.logoUrl,
              storageFolder: 'public_images',
              communityId: props.community.id,
              imageKey: 'logo_url',
            });
          } catch (error) {
            console.error('Failed to delete previous logo:', error);
          }
        }

        await uploadCommunityImageAction({
          photoFile: file,
          communityId: props.community.id,
          storageFolder: 'public_images',
          imageKey: 'logo_url',
        });
      };

      await createToaster(promise);
    },
    [createToaster, props.community.id, props.community.logoUrl],
  );

  const handleCoverDelete = useCallback(async () => {
    const promise = async () => {
      if (props.community.coverUrl) {
        await deleteCommunityImageAction({
          url: props.community.coverUrl,
          storageFolder: 'public_images',
          communityId: props.community.id,
          imageKey: 'cover_url',
        });
      }
    };

    await createToaster(promise);
  }, [createToaster, props.community.id, props.community.coverUrl]);

  const handleCoverUpload = useCallback(
    async (file: File) => {
      const promise = async () => {
        if (props.community.coverUrl) {
          try {
            await deleteCommunityImageAction({
              url: props.community.coverUrl,
              storageFolder: 'public_images',
              communityId: props.community.id,
              imageKey: 'cover_url',
            });
          } catch (error) {
            console.error('Failed to delete previous cover:', error);
          }
        }

        await uploadCommunityImageAction({
          photoFile: file,
          communityId: props.community.id,
          storageFolder: 'public_images',
          imageKey: 'cover_url',
        });
      };

      await createToaster(promise);
    },
    [createToaster, props.community.id, props.community.coverUrl],
  );

  return (
    <Card data-test="community-images-container">
      <CardHeader>
        <CardTitle>
          <Trans i18nKey={'communities:settings.communityImages'} />
        </CardTitle>
        <CardDescription>
          <Trans i18nKey={'communities:settings.communityImagesDescription'} />
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ImageUploadContainer
          imageUrl={props.community.logoUrl}
          headingKey="communities:settings.logoPictureHeading"
          uploadHeadingKey="communities:settings.logoPictureUploadHeading"
          uploadSubheadingKey="communities:settings.logoPictureUploadSubheading"
          imageRounded="rounded-xl"
          imageSize="w-20 h-20"
          aspectRatio={1}
          onDelete={handleLogoDelete}
          onUpload={handleLogoUpload}
          data-test="logo-image-upload"
          aria-label="Upload or manage community logo"
        />
      </CardContent>
      <CardContent>
        <ImageUploadContainer
          imageUrl={props.community.coverUrl}
          headingKey="communities:settings.coverImageHeading"
          uploadHeadingKey="communities:settings.coverImageUploadHeading"
          uploadSubheadingKey="communities:settings.coverImageUploadSubheading"
          imageRounded="rounded-xl"
          imageSize="w-80 h-48"
          aspectRatio={80 / 48}
          onDelete={handleCoverDelete}
          onUpload={handleCoverUpload}
          data-test="cover-image-upload"
          aria-label="Upload or manage community cover image"
        />
      </CardContent>
    </Card>
  );
}

CommunityImagesContainer.displayName = 'CommunityImagesContainer';
