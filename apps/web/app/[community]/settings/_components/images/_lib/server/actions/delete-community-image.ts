'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { STORAGE_CONFIG } from '~/lib/images/constants';
import { createImageStorageService } from '~/lib/images/services/image-storage.service';

const DeleteImageActionSchema = z.object({
  url: z.string(),
  storageFolder: z.string(),
  communityId: z.string(),
  imageKey: z.enum(['logo_url', 'cover_url']),
});

export const deleteCommunityImageAction = enhanceAction(
  async ({
    url,
    storageFolder,
    communityId,
    imageKey,
  }: z.infer<typeof DeleteImageActionSchema>): Promise<boolean> => {
    const client = getSupabaseServerClient();
    const logger = await getLogger();

    const imageService = createImageStorageService(client);
    const ctx = {
      functionName: 'deleteCommunityImage',
      bucket: 'community_storage',
      storageFolder,
      url,
      communityId,
      imageKey,
    };

    logger.info(ctx, 'Attempting to delete photo and update DB via service');

    try {
      // Extract the community ID and image type from the URL
      // Expected format contains: community_storage/{communityId}/public_images/{communityId}-{type}.{ext}
      const regex = /community_storage\/([a-f0-9-]+)\/([^/]+)\/([^?]+)/i;
      const match = url.match(regex);

      if (!match || !match[1] || !match[2] || !match[3]) {
        logger.warn(ctx, 'File path could not be determined from URL', { url });
        return false;
      }

      const communityId = match[1];
      const folder = match[2];
      const fileName = match[3].split('?')[0];

      const filePath = `${communityId}/${folder}/${fileName}`;
      logger.info(ctx, 'Removing file via ImageStorageService', {
        filePath,
        communityId,
        folder,
        fileName,
      });

      try {
        await imageService.deleteImage(filePath, STORAGE_CONFIG.bucket);
        logger.info(
          { ...ctx, filePath },
          'Photo deleted successfully from storage via service',
        );
      } catch (storageError) {
        logger.error(
          ctx,
          'Failed to delete photo from storage via service',
          storageError,
        );
        return false;
      }

      try {
        const { error: updateError } = await client
          .from('communities')
          .update({ [imageKey]: null })
          .eq('id', communityId)
          .throwOnError();

        if (updateError) {
          logger.error(
            ctx,
            'Storage deleted, but failed to update DB',
            updateError,
          );
          return false;
        }

        logger.info(ctx, 'DB updated successfully, image key set to null');
        return true;
      } catch (dbError) {
        logger.error(
          ctx,
          'Storage deleted, but encountered error updating DB',
          dbError,
        );
        return false;
      }
    } catch (error) {
      logger.error({ ...ctx, error }, 'Error during delete/update process');
      throw new Error('Failed to delete community image');
    }
  },
  {
    auth: true,
    schema: DeleteImageActionSchema,
  },
);
