'use server';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

// Import the image service, config, and context type
import { STORAGE_CONFIG } from '~/lib/images/constants';
import { createImageStorageService } from '~/lib/images/services/image-storage.service';
import type { UploadImageContext } from '~/lib/images/types';

export const uploadCommunityImageAction = enhanceAction(
  async ({
    photoFile,
    communityId,
    storageFolder, // Should typically be 'public_images' for this action
    imageKey, // Required: 'logo_url' or 'cover_url'
  }: {
    photoFile: File;
    communityId: string;
    storageFolder: string;
    imageKey: 'logo_url' | 'cover_url';
  }): Promise<string> => {
    const logger = await getLogger();
    const ctx = {
      functionName: 'uploadCommunityImage',
      bucket: STORAGE_CONFIG.bucket, // Use config bucket
      communityId,
      storageFolder,
      imageKey,
    };
    const client = getSupabaseServerClient();

    const imageService = createImageStorageService(client);
    logger.info(ctx, 'Uploading community photo via service');

    // Basic check for required parameters
    if (!imageKey || !communityId) {
      logger.error(ctx, 'Missing required parameters communityId or imageKey');
      throw new Error('Missing required parameters for image upload.');
    }

    // Sanitize file extension - still needed for filename generation
    const extension = photoFile.name.split('.').pop()?.toLowerCase();
    if (
      !extension ||
      !['jpg', 'jpeg', 'png', 'webp', 'svg'].includes(extension)
    ) {
      logger.error(ctx, 'Invalid file extension');
      throw new Error('Invalid file extension');
    }

    try {
      // Determine the target filename based on imageKey
      const targetFilename = `${communityId}-${imageKey === 'logo_url' ? 'logo' : 'cover'}.${extension}`;

      // Prepare context for the image service
      const uploadContext: UploadImageContext = {
        communityId: communityId,
        featureDir: storageFolder, // Use storageFolder (e.g., 'public_images') as featureDir
        targetFilename: targetFilename,
        // subPath is not needed here
      };

      logger.info(ctx, 'Calling ImageStorageService.uploadImage', {
        context: uploadContext,
      });

      // Step 1: Upload image using the service
      // The service handles validation, path construction, upload, and cache-busting URL
      const uploadResult = await imageService.uploadImage(
        photoFile,
        uploadContext,
      );

      const finalUrl = uploadResult.url; // URL from service already has timestamp
      logger.info(ctx, 'Upload via service successful', {
        publicUrl: finalUrl,
      });

      // Step 2: Update the communities table with the new URL
      try {
        const { error: updateError } = await client
          .from('communities')
          .update({ [imageKey]: finalUrl })
          .eq('id', communityId)
          .throwOnError();

        if (updateError) {
          logger.error(
            ctx,
            'Upload successful, but failed to update DB',
            updateError,
          );
          // Consider cleanup? For now, throw to indicate failure.
          throw new Error('Failed to update database after image upload.');
        }

        logger.info(ctx, 'DB updated successfully');
        return finalUrl; // Return the final URL
      } catch (dbError) {
        logger.error(
          ctx,
          'Upload successful, but encountered error updating DB',
          dbError,
        );
        throw new Error('Database error after image upload.');
      }
    } catch (error) {
      // Catch errors from service call (validation, upload) or DB update
      logger.error({ ...ctx, error }, 'Error during upload/DB update process');
      throw error; // Let enhanceAction handle it
    }
  },
  {
    auth: true,
  },
);
