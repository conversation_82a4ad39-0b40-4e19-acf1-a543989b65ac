'use client';

import { Suspense, useEffect, useState } from 'react';

import { usePathname, useRouter, useSearchParams } from 'next/navigation';

import { User } from '@supabase/supabase-js';

import { useTranslation } from 'react-i18next';

import { If } from '@kit/ui/if';
import { Spinner } from '@kit/ui/spinner';
import { Tabs, TabsContent, TabsList } from '@kit/ui/tabs';
import { TabsTriggersList } from '@kit/ui/tabs-triggers-list';

import type {
  CommunityConnectStatusData,
  CommunityExpanded,
} from '~/lib/communities/community/types';
import type { PermissionsEnum } from '~/lib/communities/members/types';
import { hasCommunityPermission } from '~/lib/communities/permissions.utils';

import { SettingsTabProvider } from '../_lib/_context/settings-tab-context';
import { CommunityBillingContainer } from './billing/community-billing-container';
import { CommunityDeleteContainer } from './delete/community-delete-container';
import { CommunityDetailsContainer } from './details/community-details-container';
import { ExplorePageRequestContainer } from './explore-page-request/explore-page-request';
import { ForumCategoriesContainer } from './forum-categories/forum-categories-container';
import { CommunityImagesContainer } from './images/community-images-container';
import { DisplayLinkedAccountStatus } from './payouts/display-linked-account-status';
import { MemberPlansContainer } from './plans/plans-container';
import { CommunitySocialsContainer } from './socials/community-socials-container';

export function CommunitySettingsContainer(props: {
  user: User;
  memberId: string;
  community: CommunityExpanded;
  permissions: PermissionsEnum[];
  connectStatusData: CommunityConnectStatusData | null;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [activeTab, setActiveTab] = useState(
    searchParams.get('tab') ?? 'details',
  );
  const { t } = useTranslation();

  useEffect(() => {
    const tab = searchParams.get('tab') ?? 'details';
    setActiveTab(tab);
  }, [searchParams]);

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    const params = new URLSearchParams(searchParams.toString());
    if (value === 'details') {
      params.delete('tab');
    } else {
      params.set('tab', value);
    }
    router.push(`${pathname}?${params.toString()}`, { scroll: false });
  };

  const hasStripeAccountSettingsPermission = hasCommunityPermission(
    props.permissions,
    'community.stripe_account.settings',
  );

  const hasStripeAccountsPayoutsPermission = hasCommunityPermission(
    props.permissions,
    'community.stripe_account.payouts.update',
  );

  const hasProductsCreatePermission = hasCommunityPermission(
    props.permissions,
    'community.products.create',
  );

  const hasBillingManagePermission = hasCommunityPermission(
    props.permissions,
    'billing.manage',
  );

  const isOwner = props.community.primaryOwnerUserId === props.user.id;

  let tabs = [
    { value: 'details', label: t('communities:settings.detailsTabLabel') },
    { value: 'socials', label: t('communities:settings.socialsTabLabel') },
    { value: 'images', label: t('communities:settings.imagesTabLabel') },

    {
      value: 'forum-categories',
      label: t('communities:settings.forumCategoriesTabLabel'),
    },
    {
      value: 'plans',
      label: t('communities:settings.plansTabLabel'),
    },
    {
      value: 'payouts',
      label: t('communities:settings.payoutsTabLabel'),
    },

    {
      value: 'explore_page_request',
      label: t('communities:settings.explorePageRequestTabLabel'),
    },
    { value: 'billing', label: t('communities:settings.billingTabLabel') },
    { value: 'delete', label: t('communities:settings.deleteTabLabel') },
  ];

  if (
    !hasStripeAccountSettingsPermission ||
    !hasStripeAccountsPayoutsPermission
  ) {
    tabs = tabs.filter((tab) => tab.value !== 'payouts');
  }

  if (!hasProductsCreatePermission) {
    tabs = tabs.filter((tab) => tab.value !== 'subscriptions');
  }

  if (!isOwner) {
    tabs = tabs.filter((tab) => tab.value !== 'explore_page_request');
  }

  if (!hasBillingManagePermission) {
    tabs = tabs.filter((tab) => tab.value !== 'billing');
  }

  if (!isOwner) {
    tabs = tabs.filter((tab) => tab.value !== 'delete');
  }

  return (
    <SettingsTabProvider setActiveTab={(tab) => handleTabChange(tab)}>
      <Tabs
        value={activeTab}
        className="flex h-full"
        orientation="vertical"
        onValueChange={handleTabChange}
      >
        <TabsList className="mr-4 flex h-min w-[200px] shrink-0 flex-col justify-start space-y-2 rounded-xl border py-4">
          <TabsTriggersList
            values={tabs.map((tab) => tab.value)}
            labels={tabs.map((tab) => tab.label)}
            className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground h-10 w-full justify-start rounded pl-2 text-left hover:bg-slate-200"
          />
        </TabsList>
        <div className="flex-1 overflow-auto">
          <TabsContent value="details">
            <CommunityDetailsContainer
              community={props.community}
              permissions={props.permissions}
            />
          </TabsContent>
          <TabsContent value="socials">
            <CommunitySocialsContainer
              community={props.community}
              permissions={props.permissions}
            />
          </TabsContent>
          <TabsContent value="images">
            <CommunityImagesContainer
              community={props.community}
              permissions={props.permissions}
            />
          </TabsContent>
          <TabsContent value="forum-categories">
            <ForumCategoriesContainer
              communityId={props.community.id}
              communitySlug={props.community.slug}
              permissions={props.permissions}
            />
          </TabsContent>
          <If condition={hasProductsCreatePermission}>
            <TabsContent value="plans">
              <MemberPlansContainer
                community={props.community}
                permissions={props.permissions}
              />
            </TabsContent>
          </If>
          <If
            condition={
              hasStripeAccountSettingsPermission &&
              hasStripeAccountsPayoutsPermission
            }
          >
            <TabsContent value="payouts">
              <Suspense key={props.community.id} fallback={<Spinner />}>
                <div className="space-y-4">
                  <DisplayLinkedAccountStatus
                    statusData={props.connectStatusData}
                  />
                </div>
              </Suspense>
            </TabsContent>
          </If>

          <If condition={isOwner}>
            <TabsContent value="explore_page_request">
              <ExplorePageRequestContainer
                community={props.community}
                memberId={props.memberId}
                permissions={props.permissions}
              />
            </TabsContent>
          </If>
          <If condition={hasBillingManagePermission}>
            <TabsContent value="billing">
              <CommunityBillingContainer
                community={props.community}
                permissions={props.permissions}
              />
            </TabsContent>
          </If>
          <If condition={isOwner}>
            <TabsContent value="delete">
              <CommunityDeleteContainer
                community={props.community}
                permissions={props.permissions}
              />
            </TabsContent>
          </If>
        </div>
      </Tabs>
    </SettingsTabProvider>
  );
}
