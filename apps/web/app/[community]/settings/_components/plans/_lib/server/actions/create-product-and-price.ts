'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { getStripeProductsService } from '~/lib/stripe/services';

import { formatProductName } from '../../utils/format-product-name';

const CreateProductPriceSchema = z.object({
  communityId: z.string(),
  type: z.enum(['one_time', 'recurring']),
  interval: z.enum(['month', 'year', 'month_year']).optional(),
  amount: z.number().min(1).max(999),
  yearlyAmount: z.number().min(1).max(999).optional(),
  nickname: z.string().max(50).optional(),
  trialDays: z.number().min(0).max(90).optional(),
});

export const createProductAndPriceAction = enhanceAction(
  async function (props: {
    communityId: string;
    type: 'one_time' | 'recurring';
    interval?: 'month' | 'year' | 'month_year';
    amount: number;
    yearlyAmount?: number;
    nickname?: string;
    trialDays?: number;
  }) {
    const client = getSupabaseServerClient();
    const productsService = getStripeProductsService(client);

    // Format the product name based on pricing details
    const formattedName = await formatProductName({
      nickname: props.nickname,
      type: props.type,
      amount: props.amount,
      interval: props.interval,
      yearlyAmount: props.yearlyAmount,
    });

    // Create the new product (which will be active by default)
    const product = await productsService.createProduct({
      name: formattedName,
      communityId: props.communityId,
      seller: 'community',
      metadata: {
        community_id: props.communityId,
        seller_type: 'community',
      },
      trialDays: props.trialDays,
    });

    try {
      // Helper function to create price with common parameters
      const createPriceWithCommonParams = async (
        priceType: 'one_time' | 'recurring',
        priceInterval?: 'month' | 'year' | 'one_time',
        amount: number = props.amount,
      ) => {
        await productsService.createPrice({
          productId: product.id,
          unitAmount: amount * 100, // Convert to cents
          currency: 'usd',
          type: priceType,
          interval: priceInterval,
          nickname: props.nickname,
        });
      };

      // Create price based on type
      if (props.type === 'one_time') {
        await createPriceWithCommonParams('one_time', 'one_time');
      } else if (props.type === 'recurring' && props.interval) {
        if (props.interval === 'month' || props.interval === 'month_year') {
          await createPriceWithCommonParams('recurring', 'month');
        }

        // Create yearly price if needed
        if (
          props.interval === 'year' ||
          (props.interval === 'month_year' && props.yearlyAmount)
        ) {
          const yearlyAmount = props.yearlyAmount || props.amount;
          await createPriceWithCommonParams('recurring', 'year', yearlyAmount);
        }
      }

      // Activate this product and deactivate others
      await productsService.toggleCurrentProductActive({
        productId: product.id,
      });

      return product;
    } catch (error) {
      console.error('Error creating prices:', error);
      throw error;
    }
  },
  {
    auth: true,
    schema: CreateProductPriceSchema,
  },
);
