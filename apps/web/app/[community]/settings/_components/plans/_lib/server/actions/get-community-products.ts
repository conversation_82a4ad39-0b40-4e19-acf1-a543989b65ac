'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { getStripeProductsService } from '~/lib/stripe/services';

export const getCommunityProductsAction = enhanceAction(
  async function (props: { communityId: string }) {
    const logger = await getLogger();
    const client = getSupabaseServerClient();
    const productsService = getStripeProductsService(client);

    try {
      const { products, prices } =
        await productsService.getCommunityProductsAndPrices(props.communityId);

      // Transform the data to match the expected format
      const productsWithPrices = products.map((product) => ({
        ...product,
        prices: prices[product.id] || [],
      }));
      logger.info(
        {
          communityId: props.communityId,
          productCount: productsWithPrices.length,
        },
        'Successfully retrieved community products and prices',
      );
      return { products: productsWithPrices };
    } catch (error) {
      logger.error({ error }, 'Error loading community plan');
      return { products: [] };
    }
  },
  {
    auth: true,
    schema: z.object({
      communityId: z.string(),
    }),
  },
);
