'use server';

/**
 * Formats a product name based on price details and optional nickname
 */
export async function formatProductName(params: {
  nickname?: string;
  type: 'one_time' | 'recurring';
  amount: number;
  interval?: 'month' | 'year' | 'month_year';
  yearlyAmount?: number;
}): Promise<string> {
  const { nickname, type, amount, interval, yearlyAmount } = params;

  // Build the price part of the name
  let pricePart = '';
  if (type === 'one_time') {
    pricePart = `$${Math.round(amount)} one-time`;
  } else if (interval === 'month_year' && yearlyAmount) {
    pricePart = `$${Math.round(amount)} per month / $${Math.round(yearlyAmount)} per year`;
  } else if (interval === 'month') {
    pricePart = `$${Math.round(amount)} per month`;
  } else if (interval === 'year') {
    pricePart = `$${Math.round(amount)} per year`;
  }

  // Add nickname if it exists
  if (nickname) {
    return `${nickname} - ${pricePart}`;
  }
  return pricePart;
}
