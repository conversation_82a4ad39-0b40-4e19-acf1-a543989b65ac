'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { getStripeProductsService } from '~/lib/stripe/services';

const DeleteProductSchema = z.object({
  communityId: z.string(),
  productId: z.string(),
});

export const deleteProductAction = enhanceAction(
  async function (props: { communityId: string; productId: string }) {
    const client = getSupabaseServerClient();
    const productsService = getStripeProductsService(client);
    const product = await productsService.deleteProduct({
      productId: props.productId,
    });
    return product;
  },
  {
    auth: true,
    schema: DeleteProductSchema,
  },
);
