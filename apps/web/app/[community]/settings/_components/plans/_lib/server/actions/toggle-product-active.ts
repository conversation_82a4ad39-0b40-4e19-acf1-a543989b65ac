'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { getStripeProductsService } from '~/lib/stripe/services';

const ToggleProductActiveSchema = z.object({
  communityId: z.string(),
  productId: z.string(),
  active: z.boolean(),
});

export const toggleProductActiveAction = enhanceAction(
  async function (props: {
    communityId: string;
    productId: string;
    active: boolean;
  }) {
    const logger = await getLogger();
    const ctx = {
      name: 'toggleProductActiveAction',
      communityId: props.communityId,
      productId: props.productId,
      active: props.active,
    };

    logger.info(ctx, 'Toggling product active status...');

    try {
      const client = getSupabaseServerClient();
      const productsService = getStripeProductsService(client);

      if (props.active) {
        await productsService.toggleCurrentProductActive({
          productId: props.productId,
        });
      }
      logger.info(ctx, 'Product active status toggled successfully');
      return { success: true };
    } catch (error) {
      logger.error({ ...ctx, error }, 'Error toggling product active status');
      throw error;
    }
  },
  {
    auth: true,
    schema: ToggleProductActiveSchema,
  },
);
