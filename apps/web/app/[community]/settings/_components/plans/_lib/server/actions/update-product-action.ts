'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { getStripeProductsService } from '~/lib/stripe/services';

const UpdateProductSchema = z.object({
  communityId: z.string(),
  productId: z.string(),
  name: z.string().max(50).optional(),
  description: z.string().max(500).optional(),
  trialDays: z.number().optional(),
});

export const updateProductAction = enhanceAction(
  async function (props: {
    communityId: string;
    productId: string;
    name?: string;
    description?: string;
    trialDays?: number;
  }) {
    const client = getSupabaseServerClient();
    const productsService = getStripeProductsService(client);

    try {
      const product = await productsService.updateProduct({
        productId: props.productId,
        name: props.name,
        description: props.description,
        trialDays: props.trialDays,
        metadata: {
          community_id: props.communityId,
        },
      });
      return product;
    } catch (error) {
      console.error('Error updating product name', error);
      throw error;
    }
  },
  {
    auth: true,
    schema: UpdateProductSchema,
  },
);
