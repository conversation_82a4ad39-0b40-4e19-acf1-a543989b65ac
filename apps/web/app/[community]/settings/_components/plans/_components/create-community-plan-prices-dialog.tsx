'use client';

import { useEffect, useState, useTransition } from 'react';

import { isRedirectError } from 'next/dist/client/components/redirect-error';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@kit/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { If } from '@kit/ui/if';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import { RadioGroup, RadioGroupItem } from '@kit/ui/radio-group';
import { Trans } from '@kit/ui/trans';

import type { PlatformProduct, ProductPrice } from '~/lib/stripe/types';

import { createProductAndPriceAction } from '../_lib/server/actions/create-product-and-price';
import { TrialDaysSelect } from './trial-days-select';

// Reusable discount percentage selector component
function DiscountPercentageSelector({
  selectedDiscount,
  onDiscountChange,
}: {
  selectedDiscount: string;
  onDiscountChange: (value: string) => void;
}) {
  const discountOptions = [
    { value: '0', label: 'discountNone', text: 'None' },
    { value: '10', label: 'discount10', text: '10%' },
    { value: '20', label: 'discount20', text: '20%', badge: 'discountBest' },
    { value: '25', label: 'discount25', text: '25%' },
    { value: '50', label: 'discount50', text: '50%' },
  ];

  return (
    <RadioGroup
      value={selectedDiscount}
      onValueChange={onDiscountChange}
      className="grid grid-cols-5 gap-2"
    >
      {discountOptions.map((option) => (
        <div key={option.value}>
          <RadioGroupItem
            value={option.value}
            id={`discount-${option.value}`}
            className="sr-only"
            data-test={`discount-${option.value === '0' ? 'none' : option.value}`}
            aria-label={`${option.text} discount`}
          />
          <Label
            htmlFor={`discount-${option.value}`}
            className={`hover:bg-accent hover:text-accent-foreground flex h-full min-h-[40px] cursor-pointer flex-col items-center justify-center rounded-lg border p-2 transition-colors ${
              selectedDiscount === option.value
                ? 'border-primary bg-primary/5'
                : 'border-border'
            }`}
          >
            <span className="text-sm">
              <Trans i18nKey={`communities:${option.label}`}>
                {option.text}
              </Trans>
            </span>
            {option.badge && (
              <Badge className="mt-1 text-xs">
                <Trans i18nKey={`communities:${option.badge}`}>Best</Trans>
              </Badge>
            )}
          </Label>
        </div>
      ))}
    </RadioGroup>
  );
}

type CreateCommunityPlanPricesDialogProps = {
  communityId: string;
  productId?: string;
  product?: PlatformProduct;
  price?: ProductPrice;
  onCreated?: () => void;
  children?: React.ReactNode;
};

export function CreateCommunityPlanPricesDialog(
  props: CreateCommunityPlanPricesDialogProps,
) {
  const [open, setOpen] = useState(false);
  const [error, setError] = useState<boolean>();
  const [isPending, startTransition] = useTransition();
  const [selectedDiscount, setSelectedDiscount] = useState<string>('');
  const { t } = useTranslation('communities');

  const priceFormSchema = z
    .object({
      type: z.enum(['one_time', 'recurring']),
      interval: z.enum(['month', 'year', 'month_year']),
      amount: z.string().refine(
        (val) => {
          const num = Number.parseFloat(val);
          return !isNaN(num) && num >= 1 && num <= 999 && Number.isInteger(num);
        },
        { message: t('priceValidation.wholeNumber') },
      ),
      yearlyAmount: z.union([
        z.literal(''),
        z.string().refine(
          (val) => {
            const num = Number.parseFloat(val);
            return (
              !isNaN(num) && num >= 1 && num <= 999 && Number.isInteger(num)
            );
          },
          { message: t('priceValidation.wholeNumber') },
        ),
      ]),
      nickname: z.string().optional(),
      trialDays: z.number().optional(),
    })
    .refine(
      (data) => {
        if (data.type === 'recurring' && data.interval === 'month_year') {
          return data.yearlyAmount !== '';
        }
        return true;
      },
      {
        message: t('priceValidation.yearlyRequired'),
        path: ['yearlyAmount'],
      },
    );

  type PriceFormData = z.infer<typeof priceFormSchema>;

  const form = useForm<PriceFormData>({
    resolver: zodResolver(priceFormSchema),
    mode: 'onChange',
    defaultValues: {
      type: props.price?.type || 'recurring',
      interval: 'month',
      amount: props.price?.unitAmount
        ? (props.price.unitAmount / 100).toString()
        : '',
      yearlyAmount: '',
      nickname: props.price?.nickname || '',
      trialDays: 0,
    },
  });

  const type = form.watch('type');
  const interval = form.watch('interval');
  const amount = form.watch('amount');
  const yearlyAmount = form.watch('yearlyAmount');

  // Function to check if the form is valid based on price fields
  const isPriceValid = () => {
    const amountNum = Number.parseFloat(amount);

    if (type === 'one_time') {
      return (
        !isNaN(amountNum) &&
        amountNum >= 1 &&
        amountNum <= 999 &&
        Number.isInteger(amountNum)
      );
    }

    const yearlyAmountNum = Number.parseFloat(yearlyAmount);

    if (interval === 'month_year') {
      return (
        !isNaN(amountNum) &&
        amountNum >= 1 &&
        amountNum <= 999 &&
        Number.isInteger(amountNum) &&
        !isNaN(yearlyAmountNum) &&
        yearlyAmountNum >= 1 &&
        yearlyAmountNum <= 999 &&
        Number.isInteger(yearlyAmountNum)
      );
    } else {
      return (
        !isNaN(amountNum) &&
        amountNum >= 1 &&
        amountNum <= 999 &&
        Number.isInteger(amountNum)
      );
    }
  };

  // Calculate what percentage a customer saves by choosing yearly over monthly
  const calculateYearlySavingsPercent = (
    monthlyPrice: number,
    yearlyPrice: number,
  ) => {
    if (
      isNaN(monthlyPrice) ||
      isNaN(yearlyPrice) ||
      monthlyPrice <= 0 ||
      yearlyPrice <= 0
    ) {
      return null;
    }
    return Math.round(
      ((monthlyPrice * 12 - yearlyPrice) / (monthlyPrice * 12)) * 100,
    );
  };
  // Calculate discount percentage when both monthly and yearly amounts are provided
  const monthlyString = form.watch('amount');
  const yearlyString = form.watch('yearlyAmount');
  const monthly = Number.parseFloat(monthlyString ?? '');
  const yearly = Number.parseFloat(yearlyString ?? '');
  const discountPercent = calculateYearlySavingsPercent(monthly, yearly);

  // Reset form values when type changes
  useEffect(() => {
    if (type === 'one_time') {
      form.setValue('interval', 'month'); // Default value, won't be used
      form.setValue('yearlyAmount', '');
      setSelectedDiscount('');
    }
  }, [type, form]);

  useEffect(() => {
    if (!open) {
      form.reset();
      setError(false);
      setSelectedDiscount('');
    }
  }, [open, form]);

  const onSubmit = async (data: PriceFormData) => {
    startTransition(async () => {
      try {
        await createProductAndPriceAction({
          communityId: props.communityId,
          type: data.type,
          interval: data.type === 'recurring' ? data.interval : undefined,
          amount: Math.round(Number.parseFloat(data.amount)),
          yearlyAmount: data.yearlyAmount
            ? Math.round(Number.parseFloat(data.yearlyAmount))
            : undefined,
          nickname: data.nickname,
          trialDays: data.type === 'recurring' ? data.trialDays : undefined,
        });

        props.onCreated?.();

        setOpen(false);
      } catch (error) {
        console.error('Error caught in main try-catch:', error);
        if (!isRedirectError(error)) {
          setError(true);
        }
      }
    });
  };

  return (
    <>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>{props.children}</DialogTrigger>

        <DialogContent
          className="sm:top-[15%] sm:max-w-[500px] sm:translate-y-0"
          data-test="create-price-dialog"
        >
          <DialogHeader>
            <DialogTitle>
              <Trans i18nKey={'communities:createPriceTitle'} />
            </DialogTitle>
            <DialogDescription>
              <Trans i18nKey={'communities:createPriceDescription'} />
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <If condition={!props.price}>
                <div className="space-y-4">
                  <div>
                    <Label>
                      <Trans i18nKey={'communities:priceType'} />
                    </Label>

                    <RadioGroup
                      value={type}
                      onValueChange={(value) =>
                        form.setValue('type', value as 'one_time' | 'recurring')
                      }
                      className="mt-2 grid grid-cols-2 gap-4"
                    >
                      <div className="h-full">
                        <RadioGroupItem
                          value="recurring"
                          id="recurring"
                          className="peer sr-only"
                          data-test="price-type-recurring"
                          aria-label="Recurring price"
                        />
                        <Label
                          htmlFor="recurring"
                          className={`hover:bg-accent hover:text-accent-foreground flex h-full min-h-[120px] cursor-pointer flex-col rounded-lg border p-4 transition-colors ${
                            type === 'recurring'
                              ? 'border-primary bg-primary/5'
                              : 'border-border'
                          }`}
                        >
                          <span className="text-base">
                            <Trans i18nKey={'communities:recurringPrice'} />
                          </span>
                          <span className="text-muted-foreground text-sm">
                            <Trans
                              i18nKey={'communities:recurringPriceDescription'}
                            />
                          </span>
                        </Label>
                      </div>
                      <div className="h-full">
                        <RadioGroupItem
                          value="one_time"
                          id="one_time"
                          className="peer sr-only"
                          data-test="price-type-one-time"
                          aria-label="One-time price"
                        />
                        <Label
                          htmlFor="one_time"
                          className={`hover:bg-accent hover:text-accent-foreground flex h-full min-h-[120px] cursor-pointer flex-col rounded-lg border p-4 transition-colors ${
                            type === 'one_time'
                              ? 'border-primary bg-primary/5'
                              : 'border-border'
                          }`}
                        >
                          <span className="text-base">
                            <Trans i18nKey={'communities:oneTimePrice'} />
                          </span>
                          <span className="text-muted-foreground text-sm">
                            <Trans
                              i18nKey={'communities:oneTimePriceDescription'}
                            />
                          </span>
                        </Label>
                      </div>
                    </RadioGroup>
                  </div>

                  <If condition={type === 'recurring'}>
                    <div>
                      <Label>
                        <Trans i18nKey={'communities:priceInterval'} />
                      </Label>

                      <RadioGroup
                        value={interval}
                        onValueChange={(value) =>
                          form.setValue(
                            'interval',
                            value as 'month' | 'year' | 'month_year',
                          )
                        }
                        className="mt-2 grid grid-cols-3 gap-4"
                      >
                        <div className="h-full">
                          <RadioGroupItem
                            value="month"
                            id="month"
                            className="peer sr-only"
                            data-test="interval-month"
                            aria-label="Monthly interval"
                          />
                          <Label
                            htmlFor="month"
                            className={`hover:bg-accent hover:text-accent-foreground flex h-full min-h-[120px] cursor-pointer flex-col rounded-lg border p-4 transition-colors ${
                              interval === 'month'
                                ? 'border-primary bg-primary/5'
                                : 'border-border'
                            }`}
                          >
                            <span className="text-base">
                              <Trans i18nKey={'communities:monthlyInterval'} />
                            </span>
                            <span className="text-muted-foreground text-sm">
                              <Trans
                                i18nKey={
                                  'communities:monthlyIntervalDescription'
                                }
                              />
                            </span>
                          </Label>
                        </div>

                        <div className="h-full">
                          <RadioGroupItem
                            value="year"
                            id="year"
                            className="peer sr-only"
                            data-test="interval-year"
                            aria-label="Yearly interval"
                          />
                          <Label
                            htmlFor="year"
                            className={`hover:bg-accent hover:text-accent-foreground flex h-full min-h-[120px] cursor-pointer flex-col rounded-lg border p-4 transition-colors ${
                              interval === 'year'
                                ? 'border-primary bg-primary/5'
                                : 'border-border'
                            }`}
                          >
                            <span className="text-base">
                              <Trans i18nKey={'communities:yearlyInterval'} />
                            </span>
                            <span className="text-muted-foreground text-sm">
                              <Trans
                                i18nKey={
                                  'communities:yearlyIntervalDescription'
                                }
                              />
                            </span>
                          </Label>
                        </div>

                        <div className="h-full">
                          <RadioGroupItem
                            value="month_year"
                            id="month_year"
                            className="peer sr-only"
                            data-test="interval-month-year"
                            aria-label="Monthly and yearly interval"
                          />
                          <Label
                            htmlFor="month_year"
                            className={`hover:bg-accent hover:text-accent-foreground flex h-full min-h-[120px] cursor-pointer flex-col rounded-lg border p-4 transition-colors ${
                              interval === 'month_year'
                                ? 'border-primary bg-primary/5'
                                : 'border-border'
                            }`}
                          >
                            <span className="text-base">
                              <Trans
                                i18nKey={'communities:monthYearInterval'}
                              />
                            </span>
                            <span className="text-muted-foreground text-sm">
                              <Trans
                                i18nKey={
                                  'communities:monthYearIntervalDescription'
                                }
                              />
                            </span>
                          </Label>
                        </div>
                      </RadioGroup>
                    </div>
                  </If>
                </div>
              </If>

              <div className="space-y-4">
                <If
                  condition={type === 'recurring' && interval === 'month_year'}
                >
                  <FormField
                    control={form.control}
                    name="amount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          <Trans i18nKey={'communities:monthlyPriceAmount'} />
                        </FormLabel>
                        <FormControl>
                          <div className="relative">
                            <span className="text-muted-foreground pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                              $
                            </span>
                            <Input
                              type="number"
                              step="1"
                              min="1"
                              placeholder="0"
                              className="pr-12 pl-7"
                              {...field}
                              data-test="price-amount-input"
                              aria-label="Price amount"
                            />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="space-y-2">
                    <Label>
                      <Trans i18nKey={'communities:discountPercentage'}>
                        Yearly Discount Percentage
                      </Trans>
                    </Label>
                    <DiscountPercentageSelector
                      selectedDiscount={selectedDiscount}
                      onDiscountChange={(value) => {
                        setSelectedDiscount(value);
                        const monthly = Number.parseFloat(form.watch('amount'));
                        if (!isNaN(monthly) && monthly > 0) {
                          const discount = Number.parseFloat(value) / 100;
                          const yearly = Math.round(
                            monthly * 12 * (1 - discount),
                          );
                          form.setValue('yearlyAmount', yearly.toString());
                        }
                      }}
                    />
                    <p className="text-muted-foreground text-xs">
                      <Trans i18nKey={'communities:discountDescription'}>
                        Select a discount to automatically calculate yearly
                        price
                      </Trans>
                    </p>
                  </div>

                  <FormField
                    control={form.control}
                    name="yearlyAmount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          <Trans
                            i18nKey={'communities:yearlyPriceWithDiscount'}
                          />
                        </FormLabel>
                        <FormControl>
                          <div className="relative">
                            <span className="text-muted-foreground pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                              $
                            </span>
                            <Input
                              type="number"
                              step="1"
                              min="1"
                              placeholder="0"
                              className="pr-12 pl-7"
                              {...field}
                              data-test="yearly-amount-input"
                              aria-label="Yearly price amount"
                              onChange={(e) => {
                                field.onChange(e);
                                // Unselect discount when manually changing yearly price
                                setSelectedDiscount('');
                              }}
                            />
                            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                              {interval === 'month_year' &&
                                discountPercent !== null &&
                                discountPercent > 0 && (
                                  <Badge>
                                    <span className="text-sm font-semibold text-white">
                                      {discountPercent}%{' '}
                                      <Trans
                                        i18nKey={
                                          'communities:communityPlans.discount'
                                        }
                                      />
                                    </span>
                                  </Badge>
                                )}
                            </div>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </If>

                <If
                  condition={type === 'recurring' && interval !== 'month_year'}
                >
                  <FormField
                    control={form.control}
                    name="amount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          <Trans i18nKey={'communities:priceAmount'} />
                        </FormLabel>
                        <FormControl>
                          <div className="relative">
                            <span className="text-muted-foreground pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                              $
                            </span>
                            <Input
                              type="number"
                              step="1"
                              min="1"
                              placeholder="0"
                              className="pr-12 pl-7"
                              {...field}
                              data-test="price-amount-input"
                              aria-label="Price amount"
                            />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </If>

                <If condition={type === 'one_time'}>
                  <FormField
                    control={form.control}
                    name="amount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          <Trans i18nKey={'communities:priceAmount'} />
                        </FormLabel>
                        <FormControl>
                          <div className="relative">
                            <span className="text-muted-foreground pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                              $
                            </span>
                            <Input
                              type="number"
                              step="1"
                              min="1"
                              placeholder="0"
                              className="pr-12 pl-7"
                              {...field}
                              data-test="price-amount-input"
                              aria-label="Price amount"
                            />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </If>

                <FormField
                  control={form.control}
                  name="nickname"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        <Trans i18nKey={'communities:priceNickname'} />
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          data-test="price-nickname-input"
                          aria-label="Price nickname (optional)"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <If condition={type === 'recurring'}>
                  <FormField
                    control={form.control}
                    name="trialDays"
                    render={({ field }) => (
                      <FormItem data-test="trial-days-select">
                        <FormLabel>
                          <Trans i18nKey="communities:updateTrialPeriod" />
                        </FormLabel>
                        <FormControl>
                          <TrialDaysSelect
                            value={field.value ?? 0}
                            onValueChange={(value) =>
                              field.onChange(Number.parseInt(value))
                            }
                            defaultValue={0}
                          />
                        </FormControl>
                        <p className="text-muted-foreground text-sm">
                          <Trans i18nKey="communities:trialPeriodExplanation" />
                        </p>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </If>
              </div>

              <DialogFooter>
                <If condition={error}>
                  <p className="text-destructive text-sm">
                    <Trans i18nKey={'communities:createPriceError'} />
                  </p>
                </If>

                <Button
                  type="submit"
                  disabled={isPending || !isPriceValid()}
                  data-test="confirm-create-price-button"
                  aria-label="Create price"
                >
                  {isPending ? (
                    <Trans i18nKey={'communities:creatingPrice'} />
                  ) : (
                    <Trans i18nKey={'communities:createPrice'} />
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
}

CreateCommunityPlanPricesDialog.displayName = 'CreateCommunityPlanPricesDialog';
