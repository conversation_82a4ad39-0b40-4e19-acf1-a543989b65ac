'use client';

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { PowerIcon } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';

import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Trans } from '@kit/ui/trans';

import { getCommunityProductsAction } from '../_lib/server/actions/get-community-products';
import { toggleProductActiveAction } from '../_lib/server/actions/toggle-product-active';
import { ProductDangerZone } from './product-danger-zone';
import { UpdateProductDialog } from './update-product-dialog';

type CommunityPlanPricesProps = {
  communityId: string;
  permissions: {
    canCreatePrice: boolean;
    canUpdatePrice: boolean;
    canDeletePrice: boolean;
    canTogglePrice: boolean;
  };
};

export function CommunityPlanPrices(props: CommunityPlanPricesProps) {
  const { t } = useTranslation('communities');
  const queryClient = useQueryClient();

  const { data: communityPlans, isLoading } = useQuery({
    queryKey: ['community-plan', props.communityId],
    queryFn: async () => {
      try {
        return await getCommunityProductsAction({
          communityId: props.communityId,
        });
      } catch (error) {
        console.error('Failed to load community plan:', error);
        toast.error('Failed to load community plan');
        return null;
      }
    },
  });

  const handleProductToggled = async (productId: string, active: boolean) => {
    try {
      await toggleProductActiveAction({
        communityId: props.communityId,
        productId,
        active,
      });

      // Invalidate the query to refresh the data
      queryClient.invalidateQueries({
        queryKey: ['community-plan', props.communityId],
      });
    } catch (error) {
      console.error('Failed to toggle product:', error);
      toast.error('Failed to toggle product');
    }
  };

  if (isLoading) {
    return (
      <div
        className="flex items-center justify-center p-8"
        role="status"
        aria-label="Loading community plans"
      >
        <div className="border-primary h-6 w-6 animate-spin rounded-full border-2 border-t-transparent" />
      </div>
    );
  }

  if (!communityPlans) {
    return (
      <div
        className="bg-destructive/15 text-destructive rounded-lg p-3 text-sm"
        role="alert"
      >
        <Trans i18nKey={'communities:loadCommunityPlanError'} />
      </div>
    );
  }

  if (communityPlans.products.length === 0) {
    return (
      <div
        className="bg-muted text-muted-foreground rounded-lg p-3 text-sm"
        data-test="no-community-plans-found"
      >
        <Trans i18nKey={'communities:noCommunityPlan'} />
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {communityPlans.products.map((product) => (
        <div
          key={product.id}
          className="border-border bg-primary/10 space-y-4 rounded-lg border p-2"
          data-test={`product-display-${product.id}`}
        >
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center space-x-2">
                <div>{product.name}</div>
                {product.active && (
                  <span
                    className="rounded-full bg-green-100 px-2 py-1 text-xs text-green-800"
                    data-test={`product-status-${product.id}`}
                  >
                    <Trans i18nKey={'communities:active'} />
                  </span>
                )}
              </div>
              {product.trialDays != null && product.trialDays > 0 && (
                <Badge variant={'secondary'}>
                  <Trans
                    i18nKey={'communities:settings.trialDaysBadge'}
                    values={{ trialDays: product.trialDays }}
                  />
                </Badge>
              )}
              {product.description && (
                <p className="text-muted-foreground text-sm">
                  {product.description}
                </p>
              )}
            </div>

            <div className="flex space-x-2">
              {props.permissions.canUpdatePrice && (
                <>
                  {!product.active && (
                    <Button
                      variant={'secondary'}
                      size="icon"
                      onClick={() =>
                        handleProductToggled(product.id, !product.active)
                      }
                      data-test={`toggle-product-active-${product.id}`}
                      aria-label={t('ariaLabels.activateProduct')}
                    >
                      <PowerIcon className={`text-muted-foreground h-4 w-4`} />
                    </Button>
                  )}
                  <UpdateProductDialog
                    communityId={props.communityId}
                    product={product}
                    onUpdated={() => {
                      queryClient.invalidateQueries({
                        queryKey: ['community-plan', props.communityId],
                      });
                    }}
                  />
                  <ProductDangerZone
                    communityId={props.communityId}
                    product={{
                      id: product.id,
                      name: product.name || 'Unnamed Product',
                    }}
                    onDeleted={() => {
                      queryClient.invalidateQueries({
                        queryKey: ['community-plan', props.communityId],
                      });
                    }}
                  />
                </>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

CommunityPlanPrices.displayName = 'CommunityPlanPrices';
