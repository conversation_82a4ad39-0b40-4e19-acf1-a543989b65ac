import { useTranslation } from 'react-i18next';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';

type TrialDaysSelectProps = {
  value: number;
  onValueChange: (value: string) => void;
  defaultValue?: number;
};

export function TrialDaysSelect({
  value,
  onValueChange,
  defaultValue = 0,
}: TrialDaysSelectProps) {
  const { t } = useTranslation('communities');

  return (
    <Select
      onValueChange={onValueChange}
      defaultValue={defaultValue.toString()}
      value={value.toString()}
    >
      <SelectTrigger aria-label="Select trial days">
        <SelectValue placeholder="Select trial period" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem
          value="0"
          data-test="trial-days-option-0"
          aria-label={t('communities:communityPlans.noTrial')}
        >
          {t('communities:communityPlans.noTrial')}
        </SelectItem>
        <SelectItem
          value="3"
          data-test="trial-days-option-3"
          aria-label={t('communities:communityPlans.threeDaysTrial')}
        >
          {t('communities:communityPlans.threeDaysTrial')}
        </SelectItem>
        <SelectItem
          value="7"
          data-test="trial-days-option-7"
          aria-label={t('communities:communityPlans.sevenDaysTrial')}
        >
          {t('communities:communityPlans.sevenDaysTrial')}
        </SelectItem>
        <SelectItem
          value="14"
          data-test="trial-days-option-14"
          aria-label={t('communities:communityPlans.fourteenDaysTrial')}
        >
          {t('communities:communityPlans.fourteenDaysTrial')}
        </SelectItem>
        <SelectItem
          value="30"
          data-test="trial-days-option-30"
          aria-label={t('communities:communityPlans.thirtyDaysTrial')}
        >
          {t('communities:communityPlans.thirtyDaysTrial')}
        </SelectItem>
      </SelectContent>
    </Select>
  );
}
