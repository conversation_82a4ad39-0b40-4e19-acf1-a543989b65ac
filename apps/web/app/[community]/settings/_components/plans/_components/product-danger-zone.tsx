'use client';

import { useState, useTransition } from 'react';

import { isRedirectError } from 'next/dist/client/components/redirect-error';

import { TrashIcon } from 'lucide-react';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@kit/ui/alert-dialog';
import { Button } from '@kit/ui/button';
import { If } from '@kit/ui/if';
import { Trans } from '@kit/ui/trans';

import { deleteProductAction } from '../_lib/server/actions/delete-product';

type ProductDangerZoneProps = {
  communityId: string;
  product: {
    name: string;
    id: string;
  };
  onDeleted: () => void;
};

export function ProductDangerZone(props: ProductDangerZoneProps) {
  const [error, setError] = useState<boolean>();
  const [isPending, startTransition] = useTransition();

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button
          variant="destructive"
          size="icon"
          data-test={`delete-product-trigger-${props.product.id}`}
          aria-label={`Delete product ${props.product.name}`}
        >
          <span className="sr-only">
            <Trans i18nKey={'communities:deleteProduct'} />
          </span>
          <TrashIcon className="h-4 w-4" />
        </Button>
      </AlertDialogTrigger>

      <AlertDialogContent data-test="delete-product-dialog">
        <AlertDialogHeader>
          <AlertDialogTitle>
            <Trans i18nKey={'communities:product.deleteProduct'} />
          </AlertDialogTitle>
          <AlertDialogDescription>
            <Trans
              i18nKey={'communities:product.deleteProductDescription'}
              values={{
                productName: props.product.name,
              }}
            />
          </AlertDialogDescription>
        </AlertDialogHeader>

        <If condition={error}>
          <div className="bg-destructive/15 text-destructive rounded-lg p-3 text-sm">
            <Trans i18nKey={'communities:product.deleteProductError'} />
          </div>
        </If>

        <AlertDialogFooter>
          <AlertDialogCancel
            disabled={isPending}
            data-test="cancel-delete-product-button"
            aria-label="Cancel deleting product"
          >
            <Trans i18nKey={'common:cancel'} />
          </AlertDialogCancel>

          <AlertDialogAction
            disabled={isPending}
            onClick={(event) => {
              event.preventDefault();

              startTransition(async () => {
                try {
                  await deleteProductAction({
                    communityId: props.communityId,
                    productId: props.product.id,
                  });

                  props.onDeleted();
                } catch (error) {
                  if (!isRedirectError(error)) {
                    setError(true);
                  }
                }
              });
            }}
            data-test="confirm-delete-product-button"
            aria-label="Confirm delete product"
          >
            {isPending ? (
              <Trans i18nKey={'communities:product.deletingProduct'} />
            ) : (
              <Trans i18nKey={'communities:product.confirmDeleteProduct'} />
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
