'use client';

import { useState, useTransition } from 'react';

import { isRedirectError } from 'next/dist/client/components/redirect-error';

import { zodResolver } from '@hookform/resolvers/zod';
import { PencilIcon } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import { Button } from '@kit/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@kit/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { If } from '@kit/ui/if';
import { Input } from '@kit/ui/input';
import { Trans } from '@kit/ui/trans';

import type { PlatformProduct } from '~/lib/stripe/types';

import { updateProductAction } from '../_lib/server/actions/update-product-action';
import { TrialDaysSelect } from './trial-days-select';

type UpdateProductDialogProps = {
  communityId: string;
  product: PlatformProduct;
  onUpdated?: () => void;
};

const productFormSchema = z.object({
  nickname: z.string().max(50).optional(),
  trialDays: z.number(),
});

type ProductFormData = z.infer<typeof productFormSchema>;

export function UpdateProductDialog(props: UpdateProductDialogProps) {
  const { t } = useTranslation('communities');
  const [open, setOpen] = useState(false);
  const [error, setError] = useState<boolean>();
  const [isPending, startTransition] = useTransition();

  const form = useForm<ProductFormData>({
    resolver: zodResolver(productFormSchema),
    defaultValues: {
      nickname: props.product.name || '',
      trialDays: props.product.trialDays ?? 0,
    },
  });

  const showTrialDays = props.product.productPrices?.some(
    (price) => price.type === 'recurring',
  );

  const onSubmit = async (data: ProductFormData) => {
    startTransition(async () => {
      try {
        await updateProductAction({
          communityId: props.communityId,
          productId: props.product.id,
          name: data.nickname,
          trialDays: data.trialDays,
        });

        props.onUpdated?.();
        setOpen(false);
      } catch (error) {
        if (!isRedirectError(error)) {
          setError(true);
        }
      }
    });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="secondary"
          size="icon"
          data-test={`update-product-trigger-${props.product.id}`}
          aria-label={t('ariaLabels.editProduct', {
            productName: props.product.name,
          })}
        >
          <PencilIcon className="h-4 w-4" />
        </Button>
      </DialogTrigger>

      <DialogContent data-test={`update-product-dialog-${props.product.id}`}>
        <DialogHeader>
          <DialogTitle>
            <Trans i18nKey="communities:updateProductSettings" />
          </DialogTitle>
          <DialogDescription>
            <Trans i18nKey="communities:updateProductSettingsDescription" />
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="nickname"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <Trans i18nKey="communities:priceNickname" />
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      data-test="product-nickname-input"
                      aria-label={t('ariaLabels.priceNickname')}
                    />
                  </FormControl>

                  <FormMessage />
                </FormItem>
              )}
            />
            {showTrialDays && (
              <FormField
                control={form.control}
                name="trialDays"
                render={({ field }) => (
                  <FormItem data-test="trial-days-select">
                    <FormLabel>
                      <Trans i18nKey="communities:updateTrialPeriod" />
                    </FormLabel>
                    <FormControl>
                      <TrialDaysSelect
                        value={field.value}
                        onValueChange={(value) =>
                          field.onChange(Number.parseInt(value))
                        }
                        defaultValue={props.product.trialDays ?? 0}
                      />
                    </FormControl>
                    <p className="text-muted-foreground text-sm">
                      <Trans i18nKey="communities:trialPeriodExplanation" />
                    </p>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <DialogFooter>
              <If condition={error}>
                <p className="text-destructive text-sm">
                  <Trans i18nKey="communities:updateProductError" />
                </p>
              </If>

              <Button
                type="submit"
                disabled={isPending}
                data-test="confirm-update-product-button"
                aria-label={t('ariaLabels.updateProduct')}
              >
                {isPending ? (
                  <Trans i18nKey="communities:updatingProduct" />
                ) : (
                  <Trans i18nKey="communities:updateProduct" />
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
