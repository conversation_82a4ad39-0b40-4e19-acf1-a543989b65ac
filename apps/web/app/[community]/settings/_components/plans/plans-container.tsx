'use client';

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { PlusIcon } from 'lucide-react';
import { toast } from 'sonner';

import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { If } from '@kit/ui/if';
import { Spinner } from '@kit/ui/spinner';
import { Trans } from '@kit/ui/trans';

import type { CommunityExpanded } from '~/lib/communities/community/types';
import type { PermissionsEnum } from '~/lib/communities/members/types';
import { hasCommunityPermission } from '~/lib/communities/permissions.utils';

import { CommunityPlanPrices } from './_components/community-plan-prices';
import { CreateCommunityPlanPricesDialog } from './_components/create-community-plan-prices-dialog';
import { getCommunityProductsAction } from './_lib/server/actions/get-community-products';

export function MemberPlansContainer(props: {
  community: CommunityExpanded;
  permissions: PermissionsEnum[];
}) {
  const queryClient = useQueryClient();

  const canCreatePrice = hasCommunityPermission(
    props.permissions,
    'community.prices.create',
  );
  const canUpdatePrice = hasCommunityPermission(
    props.permissions,
    'community.prices.update',
  );
  const canDeletePrice = hasCommunityPermission(
    props.permissions,
    'community.prices.delete',
  );
  const canTogglePrice = hasCommunityPermission(
    props.permissions,
    'community.prices.toggle',
  );

  const communityId = props.community.id;

  // Load the community plan to get the product ID
  const { data: communityProducts, isLoading: isLoadingCommunityProducts } =
    useQuery({
      queryKey: ['community-plan', communityId],
      queryFn: async () => {
        if (!communityId) return null;

        try {
          return await getCommunityProductsAction({ communityId });
        } catch (error) {
          console.error('Failed to load community plan:', error);
          toast.error('Failed to load community plan');
          return null;
        }
      },
      enabled: !!communityId,
    });

  if (isLoadingCommunityProducts) {
    return (
      <Card>
        <CardHeader className="flex flex-row justify-between">
          <div className="flex flex-col justify-start gap-2">
            <CardTitle className="mr-2">
              <Trans i18nKey={'communities:settings.plansTitle'} />
            </CardTitle>
            <CardDescription>
              <Trans i18nKey={'communities:settings.plansDescription'} />
            </CardDescription>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex min-h-[60vh] items-center justify-center">
            <Spinner />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row justify-between">
        <div className="flex flex-col justify-start gap-2">
          <CardTitle className="mr-2">
            <Trans i18nKey={'communities:settings.plansTitle'} />
          </CardTitle>
          <CardDescription>
            <Trans i18nKey={'communities:settings.plansDescription'} />
          </CardDescription>
        </div>
        <If condition={canCreatePrice}>
          <div className="flex gap-2">
            <CreateCommunityPlanPricesDialog
              communityId={props.community.id}
              productId={communityProducts?.products[0]?.id}
              onCreated={() => {
                // Invalidate the community plan query to trigger a re-fetch
                queryClient.invalidateQueries({
                  queryKey: ['community-plan', communityId],
                });
              }}
            >
              <Button>
                <PlusIcon className="mr-2 h-4 w-4" />
                <Trans i18nKey="communities:createPrice" />
              </Button>
            </CreateCommunityPlanPricesDialog>
          </div>
        </If>
      </CardHeader>

      <CardContent>
        <CommunityPlanPrices
          communityId={props.community.id}
          permissions={{
            canCreatePrice,
            canUpdatePrice,
            canDeletePrice,
            canTogglePrice,
          }}
        />
      </CardContent>
    </Card>
  );
}
