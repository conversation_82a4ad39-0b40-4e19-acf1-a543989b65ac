'use client';

import { useState } from 'react';

import { PencilIcon } from 'lucide-react';

import { Button } from '@kit/ui/button';
import { If } from '@kit/ui/if';

import type { ForumCategory } from '~/lib/communities/forums/types';
import type { PermissionsEnum } from '~/lib/communities/members/types';
import { hasCommunityPermission } from '~/lib/communities/permissions.utils';

import { CreateUpdateForumCategoryDialog } from './create-update-forum-category-dialog';
import { ForumCategoryDangerZone } from './forum-category-danger-zone';

type CategoryItemProps = {
  communityId: string;
  category: ForumCategory;
  permissions: PermissionsEnum[];
  onCategoryUpdated: (category: ForumCategory) => void;
  onCategoryDeleted: (categoryId: string) => void;
};

export function ForumCategoryItem(props: CategoryItemProps) {
  const [isForumCategoryDialogOpen, setIsForumCategoryDialogOpen] =
    useState(false);

  const { category, communityId, permissions } = props;

  const hasUpdatePermission = hasCommunityPermission(
    permissions,
    'community.forums.category.update',
  );
  const hasDeletePermission = hasCommunityPermission(
    permissions,
    'community.forums.category.delete',
  );

  return (
    <div
      className="mb-4 flex flex-row items-center justify-between"
      data-test={`forum-category-item-${category.id}`}
    >
      <div className="flex items-center">
        <div className="mr-2">{category.icon}</div>
        <div>
          <div>{category.name}</div>
          <div className="text-muted-foreground text-sm">
            {category.description}
          </div>
        </div>
      </div>
      <div className="flex space-x-2">
        <If condition={hasUpdatePermission}>
          <CreateUpdateForumCategoryDialog
            isOpen={isForumCategoryDialogOpen}
            setIsOpen={setIsForumCategoryDialogOpen}
            communityId={communityId}
            category={category}
            onSuccess={props.onCategoryUpdated}
          />
          <Button
            variant={'secondary'}
            size="icon"
            onClick={() => {
              setIsForumCategoryDialogOpen(true);
            }}
            data-test={`edit-forum-category-button-${category.id}`}
            aria-label={`Edit category ${category.name}`}
          >
            <PencilIcon className="h-4 w-4" />
          </Button>
        </If>
        <If condition={hasDeletePermission}>
          <ForumCategoryDangerZone
            category={category}
            onDeleted={() => props.onCategoryDeleted(category.id)}
          />
        </If>
      </div>
    </div>
  );
}

ForumCategoryItem.displayName = 'ForumCategoryItem';
