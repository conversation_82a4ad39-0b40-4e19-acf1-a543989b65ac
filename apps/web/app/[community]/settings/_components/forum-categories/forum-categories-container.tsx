'use client';

import { useEffect, useState } from 'react';

import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { If } from '@kit/ui/if';
import { Spinner } from '@kit/ui/spinner';
import { Trans } from '@kit/ui/trans';

import type { ForumCategory } from '~/lib/communities/forums/types';
import type { PermissionsEnum } from '~/lib/communities/members/types';
import { hasCommunityPermission } from '~/lib/communities/permissions.utils';

import { getForumCategoriesAction } from './_lib/server/get-forum-categories-action';
import { CreateUpdateForumCategoryDialog } from './create-update-forum-category-dialog';
import { ForumCategoryItem } from './forum-category-item';

export function ForumCategoriesContainer(props: {
  communityId: string;
  communitySlug: string;
  permissions: PermissionsEnum[];
}) {
  const [categories, setCategories] = useState<ForumCategory[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load categories using the server action
  useEffect(() => {
    const loadCategories = async () => {
      setIsLoading(true);
      try {
        const { forumCategories } = await getForumCategoriesAction({
          communityId: props.communityId,
        });
        setCategories(forumCategories);
      } catch (error) {
        console.error('Failed to load forum categories:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadCategories();
  }, [props.communityId]);

  const handleCategoryChange = (updatedCategory: ForumCategory) => {
    setCategories((prev) =>
      prev.map((cat) =>
        cat.id === updatedCategory.id ? updatedCategory : cat,
      ),
    );
  };

  const handleNewCategory = (newCategory: ForumCategory) => {
    setCategories((prev) => [...prev, newCategory]);
  };

  const handleCategoryDeleted = (deletedCategoryId: string) => {
    setCategories((prev) => prev.filter((cat) => cat.id !== deletedCategoryId));
  };

  const hasForumCategoryCreatePermission = hasCommunityPermission(
    props.permissions,
    'community.forums.category.settings',
  );

  const [isForumCategoryDialogOpen, setIsForumCategoryDialogOpen] =
    useState(false);

  return (
    <Card>
      <CardHeader className="flex flex-row justify-between">
        <div className="flex flex-col gap-2">
          <CardTitle className="mr-2">
            <Trans i18nKey={'forums:settings.forumCategories'} />
          </CardTitle>
          <CardDescription>
            <Trans i18nKey={'forums:settings.forumCategoriesDescription'} />
          </CardDescription>
        </div>
        <If condition={hasForumCategoryCreatePermission}>
          <div className="flex gap-2">
            <Button
              onClick={() => setIsForumCategoryDialogOpen(true)}
              data-test="create-forum-category-button"
              aria-label="Create new forum category"
              title="Create new forum category"
            >
              <Trans i18nKey="forums:settings.createCategoryButtonLabel" />
            </Button>
            <CreateUpdateForumCategoryDialog
              isOpen={isForumCategoryDialogOpen}
              setIsOpen={setIsForumCategoryDialogOpen}
              communityId={props.communityId}
              onSuccess={handleNewCategory}
            />
          </div>
        </If>
      </CardHeader>

      <CardContent>
        <If condition={isLoading}>
          <div className="flex justify-center py-4">
            <Spinner />
          </div>
        </If>
        <If condition={!isLoading && categories.length > 0}>
          {categories.map((category) => (
            <ForumCategoryItem
              key={category.id}
              communityId={props.communityId}
              category={category}
              permissions={props.permissions}
              onCategoryUpdated={handleCategoryChange}
              onCategoryDeleted={handleCategoryDeleted}
            />
          ))}
        </If>
        <If condition={!isLoading && categories.length === 0}>
          <p className="text-muted-foreground text-center">
            <Trans i18nKey="forums:settings.noCategoriesFound" />
          </p>
        </If>
      </CardContent>
    </Card>
  );
}

ForumCategoriesContainer.displayName = 'ForumCategoriesContainer';
