'use client';

import React from 'react';

import { useUser } from '@kit/supabase/hooks/use-user';
import { LoadingOverlay } from '@kit/ui/loading-overlay';

import { DeleteForumCategoryContainer } from './delete-forum-category-container';

export function ForumCategoryDangerZone({
  category,
  onDeleted,
}: React.PropsWithChildren<{
  category: {
    name: string;
    id: string;
    postsCount?: number;
  };
  onDeleted: () => void;
}>) {
  const { data: user } = useUser();

  if (!user) {
    return <LoadingOverlay fullPage={false} />;
  }

  return (
    <DeleteForumCategoryContainer category={category} onDeleted={onDeleted} />
  );
}

ForumCategoryDangerZone.displayName = 'ForumCategoryDangerZone';
