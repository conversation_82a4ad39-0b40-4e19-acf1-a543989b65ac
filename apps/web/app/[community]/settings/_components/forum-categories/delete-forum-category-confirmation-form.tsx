'use client';

import { useState, useTransition } from 'react';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import { ErrorBoundary } from '@kit/monitoring/components';
import { AlertDialogCancel, AlertDialogFooter } from '@kit/ui/alert-dialog';
import { Button } from '@kit/ui/button';
import { StatusAlert } from '@kit/ui/dojo/molecules/status-alert';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { If } from '@kit/ui/if';
import { Input } from '@kit/ui/input';
import { Trans } from '@kit/ui/trans';

import { deleteForumCategoryAction } from '~/lib/communities/forums/actions/forum-category-actions';

export function DeleteForumCategoryConfirmationForm({
  name,
  id,
  postsCount,
  onSuccess,
}: {
  name: string;
  id: string;
  postsCount?: number;
  onSuccess: () => void;
}) {
  const { t } = useTranslation('forums');

  const form = useForm({
    mode: 'onChange',
    reValidateMode: 'onChange',
    resolver: zodResolver(
      z.object({
        confirm: z
          .string()
          .min(1, {
            message: t('forum_category.deleteCategoryNameDoesNotMatch'),
          })
          .refine((value) => value === name, {
            message: t('forum_category.deleteCategoryNameDoesNotMatch'),
          }),
      }),
    ),
    defaultValues: {
      confirm: '',
    },
  });

  const [error, setError] = useState<boolean>();
  const [isPending, startTransition] = useTransition();

  return (
    <ErrorBoundary
      fallback={
        <StatusAlert
          variant="destructive"
          titleKey="forums:forum_category.deleteCategoryErrorHeading"
          descriptionKey="common:genericError"
        />
      }
    >
      <Form {...form}>
        <form
          data-test={'delete-forum-category-form'}
          className={'flex flex-col space-y-4'}
          onSubmit={form.handleSubmit(() => {
            startTransition(async () => {
              try {
                const response = await deleteForumCategoryAction({
                  categoryId: id,
                });

                if ('error' in response && response.error) {
                  setError(true);
                } else {
                  onSuccess();
                }
              } catch (error) {
                console.error('Error deleting forum category', error);
                setError(true);
              }
            });
          })}
        >
          <div className={'flex flex-col space-y-2'}>
            <If condition={error}>
              <StatusAlert
                variant="destructive"
                titleKey="forums:forum_category.deleteCategoryErrorHeading"
                descriptionKey="common:genericError"
              />
            </If>
            <div
              className={
                'border-2 border-red-500 p-4 text-sm text-red-500' +
                ' my-4 flex flex-col space-y-2'
              }
            >
              <div>
                <Trans
                  i18nKey={'forums:forum_category.deleteCategoryDisclaimer'}
                  values={{
                    categoryName: name,
                  }}
                />
              </div>

              <div className={'text-sm'}>
                <Trans
                  i18nKey={
                    'forums:forum_category.deleteCategoryPostWillBeDeleted'
                  }
                  values={{
                    postsCount: postsCount,
                  }}
                />
              </div>
            </div>

            <FormField
              control={form.control}
              name="confirm"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <Trans
                      i18nKey={
                        'forums:forum_category.confirmDeleteCategoryDisclaimer'
                      }
                      values={{
                        categoryName: name,
                      }}
                    />
                  </FormLabel>
                  <FormControl>
                    <Input
                      data-test={'delete-forum-category-form-confirm-input'}
                      required
                      type={'text'}
                      autoComplete={'off'}
                      className={'w-full'}
                      placeholder={''}
                      {...field}
                      aria-label={t(
                        'forum_category.ariaLabels.confirmDeletionInput',
                        { categoryName: name },
                      )}
                    />
                  </FormControl>
                  <FormDescription>
                    <Trans
                      i18nKey={'forums:forum_category.deleteCategoryInputField'}
                    />
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <AlertDialogFooter>
            <AlertDialogCancel
              data-test="cancel-delete-forum-category-button"
              aria-label={t('forum_category.ariaLabels.cancelDeletingCategory')}
            >
              <Trans i18nKey={'common:cancel'} />
            </AlertDialogCancel>

            <Button
              data-test={'delete-forum-category-form-confirm-button'}
              disabled={isPending}
              variant={'destructive'}
              aria-label={t('forum_category.ariaLabels.confirmDeleteCategory')}
            >
              <Trans i18nKey={'forums:forum_category.deleteCategory'} />
            </Button>
          </AlertDialogFooter>
        </form>
      </Form>
    </ErrorBoundary>
  );
}

DeleteForumCategoryConfirmationForm.displayName =
  'DeleteForumCategoryConfirmationForm';
