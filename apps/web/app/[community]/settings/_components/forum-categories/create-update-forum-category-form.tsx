'use client';

import { useState, useTransition } from 'react';

import { isRedirectError } from 'next/dist/client/components/redirect-error';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@kit/ui/button';
import { EmojiPicker } from '@kit/ui/dojo/molecules/emoji-picker';
import { StatusAlert } from '@kit/ui/dojo/molecules/status-alert';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { If } from '@kit/ui/if';
import { Input } from '@kit/ui/input';
import { Textarea } from '@kit/ui/textarea';
import { Trans } from '@kit/ui/trans';

import {
  createForumCategoryAction,
  updateForumCategoryAction,
} from '~/lib/communities/forums/actions/forum-category-actions';
import { CreateForumCategorySchema } from '~/lib/communities/forums/schema/create-forum-category.schema';
import type { ForumCategory } from '~/lib/communities/forums/types';

export function CreateForumCategoryForm(props: {
  communityId: string;
  category?: ForumCategory;
  onClose: () => void;
  onSuccess?: (category: ForumCategory) => void;
}) {
  const [error, setError] = useState<boolean>();
  const [isPending, startTransition] = useTransition();

  const form = useForm<z.infer<typeof CreateForumCategorySchema>>({
    defaultValues: {
      communityId: props.communityId,
      name: props.category?.name ?? '',
      description: props.category?.description ?? '',
      icon: props.category?.icon ?? '',
    },
    resolver: zodResolver(CreateForumCategorySchema),
  });

  const [forumCategoryIcon, setForumCategoryIcon] = useState<string>(
    props.category?.icon ?? '',
  );

  return (
    <Form {...form}>
      <form
        data-test={'create-forum-category-form'}
        onSubmit={form.handleSubmit((data) => {
          startTransition(async () => {
            try {
              const action = props.category
                ? updateForumCategoryAction
                : createForumCategoryAction;
              const response = await action({
                ...data,
                communityId: props.communityId,
                categoryId: props.category?.id ?? '',
                description: data.description ?? '',
                icon: data.icon ?? '',
              });

              if ('error' in response && response.error) {
                setError(true);
              } else if ('data' in response && response.data) {
                props.onSuccess?.({
                  id: response.data.id,
                  name: data.name,
                  description: data.description ?? '',
                  icon: data.icon ?? '',
                  slug: '',
                  postsCount: 0,
                });
                props.onClose();
              } else {
                props.onSuccess?.({
                  id: props.category?.id ?? '',
                  name: data.name,
                  description: data.description ?? '',
                  icon: data.icon ?? '',
                  slug: props.category?.slug ?? '',
                  postsCount: props.category?.postsCount ?? 0,
                });
                props.onClose();
              }
            } catch (error) {
              if (!isRedirectError(error)) {
                setError(true);
              }
            }
          });
        })}
      >
        <div className={'flex flex-col space-y-4'}>
          <If condition={error}>
            <StatusAlert
              variant="destructive"
              titleKey="forums:createCategoryErrorHeading"
              descriptionKey="forums:createCategoryErrorMessage"
            />
          </If>

          <FormField
            name={'name'}
            render={({ field }) => {
              return (
                <FormItem>
                  <FormLabel>
                    <Trans i18nKey={'forums:createForumCategoryNameLabel'} />
                  </FormLabel>

                  <FormControl>
                    <Input
                      data-test={'create-forum-category-name-input'}
                      required
                      minLength={2}
                      maxLength={50}
                      placeholder={''}
                      {...field}
                      aria-label="Category name"
                    />
                  </FormControl>

                  <FormDescription>
                    <Trans
                      i18nKey={'forums:createForumCategoryNameDescription'}
                    />
                  </FormDescription>

                  <FormMessage />
                </FormItem>
              );
            }}
          />

          <FormField
            name={'description'}
            render={({ field }) => {
              return (
                <FormItem>
                  <FormLabel>
                    <Trans
                      i18nKey={'forums:createForumCategoryDescriptionLabel'}
                    />
                  </FormLabel>

                  <FormControl>
                    <Textarea
                      data-test={'create-forum-category-description-input'}
                      maxLength={500}
                      placeholder={''}
                      {...field}
                      aria-label="Category description"
                    />
                  </FormControl>

                  <FormDescription>
                    <Trans
                      i18nKey={
                        'forums:createForumCategoryDescriptionDescription'
                      }
                    />
                  </FormDescription>

                  <FormMessage />
                </FormItem>
              );
            }}
          />
          <FormField
            name={'icon'}
            render={({ field }) => {
              return (
                <FormItem>
                  <FormLabel>
                    <Trans i18nKey={'forums:createForumCategoryIconLabel'} />
                  </FormLabel>

                  <div className={'flex items-center space-x-2'}>
                    <EmojiPicker
                      onChange={(emoji) => {
                        setForumCategoryIcon(emoji);
                        field.onChange(emoji);
                      }}
                      currentIcon={forumCategoryIcon}
                      data-test="forum-category-icon-picker"
                      aria-label="Choose category icon"
                    />
                  </div>
                  <FormDescription>
                    <Trans
                      i18nKey={'forums:createForumCategoryIconDescription'}
                    />
                  </FormDescription>

                  <FormMessage />
                </FormItem>
              );
            }}
          />

          <div className={'flex justify-end space-x-2'}>
            <Button
              variant={'outline'}
              type={'button'}
              disabled={isPending}
              onClick={() => {
                props.onClose();
              }}
              data-test="cancel-create-update-forum-category-button"
              aria-label="Cancel"
            >
              <Trans i18nKey={'common:cancel'} />
            </Button>

            <Button
              data-test={'confirm-create-forum-category-button'}
              disabled={isPending}
              aria-label={
                props.category ? 'Update category' : 'Create category'
              }
            >
              {isPending ? (
                <Trans
                  i18nKey={
                    props.category
                      ? 'forums:updatingForumCategory'
                      : 'forums:creatingForumCategory'
                  }
                />
              ) : (
                <Trans
                  i18nKey={
                    props.category
                      ? 'forums:updateForumCategorySubmitLabel'
                      : 'forums:createForumCategorySubmitLabel'
                  }
                />
              )}
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
}

CreateForumCategoryForm.displayName = 'CreateForumCategoryForm';
