'use client';

import React from 'react';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@kit/ui/dialog';
import { Trans } from '@kit/ui/trans';

import type { ForumCategory } from '~/lib/communities/forums/types';

import { CreateForumCategoryForm } from './create-update-forum-category-form';

export function CreateUpdateForumCategoryDialog(
  props: React.PropsWithChildren<{
    communityId: string;
    category?: ForumCategory;
    isOpen: boolean;
    setIsOpen: (isOpen: boolean) => void;
    onSuccess?: (category: ForumCategory) => void;
  }>,
) {
  return (
    <Dialog open={props.isOpen} onOpenChange={props.setIsOpen}>
      <DialogContent data-test="create-update-forum-category-dialog">
        <DialogHeader>
          <DialogTitle>
            <Trans
              i18nKey={
                props.category
                  ? 'forums:updateCategoryModalHeading'
                  : 'forums:createCategoryModalHeading'
              }
            />
          </DialogTitle>

          <DialogDescription>
            <Trans
              i18nKey={
                props.category
                  ? 'forums:updateCategoryModalDescription'
                  : 'forums:createCategoryModalDescription'
              }
            />
          </DialogDescription>
        </DialogHeader>

        <CreateForumCategoryForm
          communityId={props.communityId}
          category={props.category}
          onClose={() => props.setIsOpen(false)}
          onSuccess={props.onSuccess}
        />
      </DialogContent>
    </Dialog>
  );
}

CreateUpdateForumCategoryDialog.displayName = 'CreateUpdateForumCategoryDialog';
