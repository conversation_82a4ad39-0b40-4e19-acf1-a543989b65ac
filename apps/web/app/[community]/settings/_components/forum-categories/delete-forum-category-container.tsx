'use client';

import { useState } from 'react';

import { TrashIcon } from 'lucide-react';

import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@kit/ui/alert-dialog';
import { Button } from '@kit/ui/button';
import { Trans } from '@kit/ui/trans';

import { DeleteForumCategoryConfirmationForm } from './delete-forum-category-confirmation-form';

export function DeleteForumCategoryContainer(props: {
  category: {
    name: string;
    id: string;
    postsCount?: number;
  };
  onDeleted: () => void;
}) {
  const [open, setOpen] = useState(false);

  return (
    <div className={'flex flex-col space-y-4'}>
      <div>
        <AlertDialog open={open} onOpenChange={setOpen}>
          <AlertDialogTrigger asChild>
            <Button
              data-test={'delete-forum-category-trigger'}
              type={'button'}
              variant={'destructive'}
              size="icon"
              aria-label={`Delete category ${props.category.name}`}
            >
              <TrashIcon className="h-4 w-4" />
            </Button>
          </AlertDialogTrigger>

          <AlertDialogContent data-test="delete-forum-category-dialog">
            <AlertDialogHeader>
              <AlertDialogTitle>
                <Trans i18nKey={'forums:forum_category.deleteCategory'} />
              </AlertDialogTitle>

              <AlertDialogDescription>
                <Trans
                  i18nKey={'forums:forum_category.deleteCategoryDescription'}
                  values={{
                    categoryName: props.category.name,
                  }}
                />
              </AlertDialogDescription>
            </AlertDialogHeader>

            <DeleteForumCategoryConfirmationForm
              name={props.category.name}
              id={props.category.id}
              postsCount={props.category.postsCount}
              onSuccess={() => {
                setOpen(false);
                props.onDeleted();
              }}
            />
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  );
}

DeleteForumCategoryContainer.displayName = 'DeleteForumCategoryContainer';
