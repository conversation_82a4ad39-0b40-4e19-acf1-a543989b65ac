'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createCommunityForumsService } from '~/lib/communities/forums/community-forums.service';

const ForumCategoriesSchema = z.object({
  communityId: z.string(),
});

export const getForumCategoriesAction = enhanceAction(
  async function (data) {
    const { communityId } = data;
    const client = getSupabaseServerClient();

    const service = createCommunityForumsService(client);

    const { data: forumCategories } = await service.getForumCategories({
      communityId,
    });

    return {
      forumCategories: forumCategories ?? [],
    };
  },
  {
    auth: true,
    schema: ForumCategoriesSchema,
  },
);
