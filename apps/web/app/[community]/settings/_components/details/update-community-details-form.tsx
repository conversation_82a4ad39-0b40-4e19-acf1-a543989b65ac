'use client';

import { useEffect, useRef, useState, useTransition } from 'react';

import { useSearchParams } from 'next/navigation';
import { useRouter } from 'next/navigation';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';
import { z } from 'zod';

import { Button } from '@kit/ui/button';
import { Card, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { ColorPicker } from '@kit/ui/color-picker';
import { CommunityCategorySelector } from '@kit/ui/dojo/molecules/community-category-selector';
import { IconTextSelector } from '@kit/ui/dojo/molecules/icon-text-selector';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@kit/ui/tabs';
import { Textarea } from '@kit/ui/textarea';
import { Trans } from '@kit/ui/trans';

import { updateCommunityDetailsAction } from '~/lib/communities/community/actions/update-community-details-server-actions';
import { UpdateCommunityFormDetailsSchema } from '~/lib/communities/community/schema/update-community-details.schema';
import type { CommunityExpanded } from '~/lib/communities/community/types';

import { CommunitySlugField } from './community-slug-field';

const DEFAULT_PRIMARY_COLOR = '#FFFFFF';

export const UpdateCommunityDetailsForm = (props: {
  community: CommunityExpanded;
  path: string;
  categories: {
    id: string;
    name: string;
    icon: string;
  }[];
  languages: {
    id: string;
    name: string;
    icon: string;
  }[];
}) => {
  const [pending, startTransition] = useTransition();
  const { t } = useTranslation('communities');
  const searchParams = useSearchParams();
  const router = useRouter();

  const form = useForm<z.infer<typeof UpdateCommunityFormDetailsSchema>>({
    resolver: zodResolver(UpdateCommunityFormDetailsSchema),
    mode: 'onChange',
    defaultValues: {
      name: props.community.name ?? '',
      slug: props.community.slug ?? '',
      newSlug: props.community.slug ?? '',
      description: props.community.description || undefined,
      primaryColor: props.community.primaryColor ?? DEFAULT_PRIMARY_COLOR,
      isPrivate: props.community.isPrivate ?? false,
      category: props.community.categoryId,
      language: props.community.languageId,
      path: props.path,
    },
  });

  const [isCheckingSlug, setIsCheckingSlug] = useState(false);

  const hasShownSuccessToast = useRef(false);

  useEffect(() => {
    if (
      searchParams.get('slugUpdate') === 'success' &&
      !hasShownSuccessToast.current
    ) {
      toast.dismiss();
      toast.success(t('updateCommunitySuccessMessage'));
      hasShownSuccessToast.current = true;
    }
  }, [searchParams, t]);

  useEffect(() => {
    // Trigger initial validation
    form.trigger();
  }, [form]);

  return (
    <div className={'space-y-8'}>
      <Form {...form}>
        <form
          data-test={'update-community-details-form'}
          className={'flex flex-col space-y-4'}
          onSubmit={form.handleSubmit((data) => {
            startTransition(async () => {
              const toastId = toast.loading(t('updateCommunityLoadingMessage'));

              try {
                const result = await updateCommunityDetailsAction({
                  slug: props.community.slug ?? '',
                  newSlug: data.newSlug,
                  name: data.name,
                  path: props.path,
                  isPrivate: data.isPrivate,
                  category: data.category,
                  language: data.language,
                  description: data.description ?? '',
                  primaryColor: data.primaryColor ?? DEFAULT_PRIMARY_COLOR,
                });

                if (result.success) {
                  if (
                    data.newSlug !== props.community.slug &&
                    result.redirect
                  ) {
                    router.push(result.redirect);
                    return;
                  }

                  toast.dismiss(toastId);
                  toast.success(t('updateCommunitySuccessMessage'));
                } else {
                  toast.error(t('updateCommunityErrorMessage'), {
                    id: toastId,
                  });
                }
              } catch (error) {
                console.error('Error updating community', error);
                toast.error(t('updateCommunityErrorMessage'), {
                  id: toastId,
                });
              }
            });
          })}
        >
          <FormField
            name={'name'}
            render={({ field }) => {
              return (
                <FormItem>
                  <FormLabel>
                    <Trans
                      i18nKey={'communities:settings.communityNameInputLabel'}
                    />
                  </FormLabel>

                  <FormControl>
                    <Input
                      data-test={'community-name-input'}
                      required
                      minLength={2}
                      maxLength={50}
                      placeholder={''}
                      {...field}
                      aria-label="Community name"
                    />
                  </FormControl>
                  <FormDescription>
                    <Trans
                      i18nKey={
                        'communities:settings.communityNameInputDescription'
                      }
                    />
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              );
            }}
          />
          <CommunitySlugField
            form={form}
            communityId={props.community.id}
            originalSlug={props.community.slug ?? ''}
            onCheckingChange={setIsCheckingSlug}
          />

          <FormField
            name={'description'}
            render={({ field }) => {
              return (
                <FormItem>
                  <FormLabel>
                    <Trans
                      i18nKey={
                        'communities:settings.communityDescriptionInputLabel'
                      }
                    />
                  </FormLabel>

                  <FormControl>
                    <Textarea
                      data-test={'community-description-input'}
                      placeholder={''}
                      {...field}
                      aria-label="Community description"
                    />
                  </FormControl>
                  <FormDescription>
                    <Trans
                      i18nKey={
                        'communities:settings.communityDescriptionInputDescription'
                      }
                    />
                  </FormDescription>
                </FormItem>
              );
            }}
          />

          <FormField
            name={'primaryColor'}
            render={() => {
              return (
                <FormItem>
                  <FormLabel>
                    <Trans
                      i18nKey={
                        'communities:settings.communityPrimaryColorInputLabel'
                      }
                    />
                  </FormLabel>

                  <ColorPicker
                    pickedColor={form.watch('primaryColor')}
                    onColorChange={(color) =>
                      form.setValue('primaryColor', color)
                    }
                    data-test="community-primary-color-picker"
                    aria-label="Select primary color for community"
                  />
                  <FormDescription>
                    <Trans
                      i18nKey={
                        'communities:settings.communityPrimaryColorInputDescription'
                      }
                    />
                  </FormDescription>
                </FormItem>
              );
            }}
          />

          <FormField
            name="category"
            render={({ field }) => {
              return (
                <FormItem>
                  <FormLabel>
                    <Trans
                      i18nKey={
                        'communities:settings.communityCategoryInputLabel'
                      }
                    />
                  </FormLabel>
                  <FormControl>
                    <CommunityCategorySelector
                      currentCategoryId={props.community.categoryId ?? ''}
                      onChange={(selectedCategory: string) => {
                        field.onChange(selectedCategory);
                      }}
                      categories={props.categories.map((category) => ({
                        ...category,
                        name: t(`categories.${category.name}`),
                      }))}
                      data-test="community-category-selector"
                      aria-label="Select community category"
                    />
                  </FormControl>
                  <FormDescription>
                    <Trans
                      i18nKey={'settings.communityCategoryInputDescription'}
                    />
                  </FormDescription>
                </FormItem>
              );
            }}
          />

          <FormField
            name={'language'}
            render={({ field }) => {
              return (
                <FormItem>
                  <FormLabel>
                    <Trans
                      i18nKey={
                        'communities:settings.communityLanguageInputLabel'
                      }
                    />
                  </FormLabel>
                  <FormControl>
                    <IconTextSelector
                      placeholder={t('settings:selectLanguagePlaceholder')}
                      description={t(
                        'settings.communityLanguageInputDescription',
                      )}
                      currentId={props.community.languageId ?? ''}
                      onChange={(selectedLanguage: string) => {
                        field.onChange(selectedLanguage);
                      }}
                      items={props.languages.map((language) => ({
                        ...language,
                        name: t(`languages.${language.name}`),
                      }))}
                      data-test="community-language-selector"
                      aria-label="Select community language"
                    />
                  </FormControl>
                  <FormDescription>
                    <Trans
                      i18nKey={'settings.communityLanguageInputDescription'}
                    />
                  </FormDescription>
                </FormItem>
              );
            }}
          />

          <FormField
            name={'isPrivate'}
            render={({ field }) => {
              return (
                <FormItem>
                  <FormLabel>
                    <Trans
                      i18nKey={
                        'communities:settings.communityIsPrivateInputLabel'
                      }
                    />
                  </FormLabel>

                  <FormControl>
                    <Tabs
                      defaultValue={field.value ? 'private' : 'public'}
                      onValueChange={(value) => {
                        form.setValue('isPrivate', value === 'private');
                      }}
                    >
                      <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger
                          value="private"
                          data-test="privacy-private-tab"
                          aria-label="Set community to private"
                        >
                          <Trans
                            i18nKey={'communities:settings.privateTabLabel'}
                          />
                        </TabsTrigger>
                        <TabsTrigger
                          value="public"
                          data-test="privacy-public-tab"
                          aria-label="Set community to public"
                        >
                          <Trans
                            i18nKey={'communities:settings.publicTabLabel'}
                          />
                        </TabsTrigger>
                      </TabsList>
                      <TabsContent value="private">
                        <Card>
                          <CardHeader>
                            <CardTitle>
                              <Trans
                                i18nKey={'communities:settings.privateTabLabel'}
                              />
                            </CardTitle>
                            <CardDescription>
                              <Trans
                                i18nKey={
                                  'communities:settings.privateTabDescription'
                                }
                              />
                            </CardDescription>
                          </CardHeader>
                        </Card>
                      </TabsContent>
                      <TabsContent value="public">
                        <Card>
                          <CardHeader>
                            <CardTitle>
                              <Trans
                                i18nKey={'communities:settings.publicTabLabel'}
                              />
                            </CardTitle>
                            <CardDescription>
                              <Trans
                                i18nKey={
                                  'communities:settings.publicTabDescription'
                                }
                              />
                            </CardDescription>
                          </CardHeader>
                        </Card>
                      </TabsContent>
                    </Tabs>
                  </FormControl>
                </FormItem>
              );
            }}
          />

          <div>
            <Button
              className={'w-full md:w-auto'}
              data-test={'update-community-submit-button'}
              disabled={pending || isCheckingSlug || !form.formState.isValid}
              type="submit"
              aria-label="Save community details"
            >
              <Trans i18nKey={'communities:updateCommunitySubmitLabel'} />
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

UpdateCommunityDetailsForm.displayName = 'UpdateCommunityDetailsForm';
