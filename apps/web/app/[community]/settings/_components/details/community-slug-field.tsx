import { useCallback, useEffect, useState } from 'react';

import { useWatch } from 'react-hook-form';
import { UseFormReturn } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { Trans } from '@kit/ui/trans';

import { useCheckCommunitySlugAvailability } from '~/lib/communities/community/hooks/use-check-community-slug-availability';
import { UpdateCommunityFormDetailsSchema } from '~/lib/communities/community/schema/update-community-details.schema';

type CommunitySlugFieldProps = {
  form: UseFormReturn<z.infer<typeof UpdateCommunityFormDetailsSchema>>;
  communityId: string;
  originalSlug: string;
  onCheckingChange: (isChecking: boolean) => void;
};

export function CommunitySlugField({
  form,
  communityId,
  originalSlug,
  onCheckingChange,
}: CommunitySlugFieldProps) {
  const [slugAvailable, setSlugAvailable] = useState<boolean | null>(null);
  const [isChecking, setIsChecking] = useState(false);
  const { checkAvailability } = useCheckCommunitySlugAvailability();

  const { t } = useTranslation('communities');

  const slug = useWatch<z.infer<typeof UpdateCommunityFormDetailsSchema>>({
    name: 'newSlug',
    defaultValue: '',
    control: form.control,
  }) as string;

  useEffect(() => {
    if (!slug || slug === '' || slug === originalSlug) {
      setSlugAvailable(null);
      setIsChecking(false);
      return;
    }

    setIsChecking(true);
    // Only call the debounced function
    void checkAvailability?.(slug, communityId);
  }, [slug, originalSlug, communityId, checkAvailability]);

  // Add a callback to handle the result
  const handleAvailabilityResult = useCallback(
    (isAvailable: boolean | null) => {
      setSlugAvailable(isAvailable);
      setIsChecking(false);
    },
    [],
  );

  // Register the callback with the hook
  useEffect(() => {
    if (checkAvailability) {
      checkAvailability.subscribe(handleAvailabilityResult);
      return () => checkAvailability.unsubscribe(handleAvailabilityResult);
    }
  }, [checkAvailability, handleAvailabilityResult]);

  // Add effect to notify parent of checking state changes
  useEffect(() => {
    onCheckingChange(isChecking);
  }, [isChecking, onCheckingChange]);

  return (
    <FormField
      name="newSlug"
      render={({ field }) => (
        <FormItem>
          <FormLabel>
            <Trans i18nKey={'communities:settings.communitySlugInputLabel'} />
          </FormLabel>

          <FormControl>
            <Input
              data-test={'community-slug-input'}
              minLength={2}
              placeholder={t('slugPlaceholder')}
              maxLength={50}
              pattern="[a-z0-9\-]*"
              inputMode="text"
              autoCorrect="off"
              autoCapitalize="none"
              spellCheck={false}
              {...field}
              aria-label="Community slug"
              onChange={(e) => {
                // Remove any characters that aren't lowercase alphanumeric or dash
                const sanitized = e.target.value.replace(/[^a-z0-9-]/g, '');
                field.onChange(sanitized);
              }}
              onKeyDown={(e) => {
                // Only allow alphanumeric, dash, and control keys
                if (
                  !/^[a-z0-9-]$/.test(e.key) &&
                  ![
                    'Backspace',
                    'Delete',
                    'ArrowLeft',
                    'ArrowRight',
                    'Tab',
                  ].includes(e.key)
                ) {
                  e.preventDefault();
                }
              }}
            />
          </FormControl>

          <FormMessage>
            {field.value && (
              <>
                {isChecking ? (
                  <span className="text-gray-400">
                    <Trans i18nKey="user:slugCheckingMessage" />
                  </span>
                ) : slugAvailable === true ? (
                  <span className="text-green-500">
                    <Trans i18nKey="user:slugAvailableMessage" />
                  </span>
                ) : slugAvailable === false ? (
                  <span className="text-red-500">
                    <Trans i18nKey="user:slugNotAvailableMessage" />
                  </span>
                ) : null}
              </>
            )}
          </FormMessage>
          <FormDescription>
            <Trans
              i18nKey={'communities:settings.communitySlugInputDescription'}
            />
          </FormDescription>
        </FormItem>
      )}
    />
  );
}

CommunitySlugField.displayName = 'CommunitySlugField';
