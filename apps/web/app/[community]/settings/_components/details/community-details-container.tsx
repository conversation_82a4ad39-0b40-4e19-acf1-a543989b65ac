'use client';

import { useEffect, useState } from 'react';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { Spinner } from '@kit/ui/spinner';
import { Trans } from '@kit/ui/trans';

import pathsConfig from '~/config/paths.config';
import type {
  CommunityCategory,
  CommunityExpanded,
  Language,
} from '~/lib/communities/community/types';
import type { PermissionsEnum } from '~/lib/communities/members/types';

import { getCommunityDataAction } from './_lib/server/get-community-data-action';
import { UpdateCommunityDetailsForm } from './update-community-details-form';

const paths = {
  communitySettings: pathsConfig.app.communitySettings,
};

export function CommunityDetailsContainer(props: {
  community: CommunityExpanded;
  permissions: PermissionsEnum[];
}) {
  const [categories, setCategories] = useState<CommunityCategory[]>([]);
  const [languages, setLanguages] = useState<Language[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load categories and languages using the server action
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      try {
        const data = await getCommunityDataAction({});
        setCategories(data.categories);
        setLanguages(data.languages);
      } catch (error) {
        console.error('Failed to load community data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          <Trans i18nKey={'communities:settings.communityDetails'} />
        </CardTitle>
        <CardDescription>
          <Trans i18nKey={'communities:settings.communityDetailsDescription'} />
        </CardDescription>
      </CardHeader>
      <CardContent>
        {!isLoading && (
          <UpdateCommunityDetailsForm
            path={paths.communitySettings}
            community={props.community}
            categories={categories}
            languages={languages}
          />
        )}
        {isLoading && (
          <div className="flex justify-center py-4">
            <Spinner />
          </div>
        )}
      </CardContent>
    </Card>
  );
}

CommunityDetailsContainer.displayName = 'CommunityDetailsContainer';
