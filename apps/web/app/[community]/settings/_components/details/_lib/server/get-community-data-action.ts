'use server';

import { enhanceAction } from '@kit/next/actions';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createCommunityService } from '~/lib/communities/community/community.service';
import { createSystemService } from '~/lib/system/services/system.service';

export const getCommunityDataAction = enhanceAction(
  async function () {
    const client = getSupabaseServerClient();
    const communityService = createCommunityService(client);
    const coreService = createSystemService(client);

    const [categories, languages] = await Promise.all([
      communityService.getCommunityCategories(),
      coreService.getSystemLanguages(),
    ]);

    return {
      categories: categories.categories || [],
      languages: languages.languages || [],
    };
  },
  {
    auth: true,
  },
);
