'use client';

import { useState } from 'react';

import { useFormStatus } from 'react-dom';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { ErrorBoundary } from '@kit/monitoring/components';
import { useUser } from '@kit/supabase/hooks/use-user';
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@kit/ui/alert-dialog';
import { Button } from '@kit/ui/button';
import { StatusAlert } from '@kit/ui/dojo/molecules/status-alert';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { LoadingOverlay } from '@kit/ui/loading-overlay';
import { Trans } from '@kit/ui/trans';

import { deleteCommunityAction } from '~/lib/communities/community/actions/delete-community-server-actions';

// TODO: Break this down into smaller components
export function CommunityDangerZone({
  community,
  primaryOwnerId,
}: React.PropsWithChildren<{
  community: {
    name: string;
    id: string;
  };

  primaryOwnerId: string;
}>) {
  const { data: user } = useUser();

  if (!user) {
    return <LoadingOverlay fullPage={false} />;
  }

  // Only the primary owner can delete the community
  const userIsPrimaryOwner = user.id === primaryOwnerId;

  if (userIsPrimaryOwner) {
    return <DeleteCommunityContainer community={community} />;
  }

  return null;
}

function DeleteCommunityContainer(props: {
  community: {
    name: string;
    id: string;
  };
}) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  return (
    <div className={'flex flex-col space-y-4'}>
      <div className={'flex flex-col space-y-1'}>
        <span className={'font-medium'}>
          <Trans i18nKey={'communities:deleteCommunity'} />
        </span>

        <p className={'text-muted-foreground text-sm'}>
          <Trans
            i18nKey={'communities:deleteCommunityDescription'}
            values={{
              communityName: props.community.name,
            }}
          />
        </p>
      </div>

      <div>
        <AlertDialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <AlertDialogTrigger asChild>
            <Button
              data-test={'delete-community-trigger'}
              type={'button'}
              variant={'destructive'}
              aria-label="Delete community"
            >
              <Trans i18nKey={'communities:deleteCommunity'} />
            </Button>
          </AlertDialogTrigger>

          <AlertDialogContent
            onEscapeKeyDown={(e) => e.preventDefault()}
            data-test="delete-community-dialog"
          >
            <AlertDialogHeader>
              <AlertDialogTitle>
                <Trans i18nKey={'communities:deletingCommunity'} />
              </AlertDialogTitle>

              <AlertDialogDescription>
                <Trans
                  i18nKey={'communities:deletingCommunityDescription'}
                  values={{
                    communityName: props.community.name,
                  }}
                />
              </AlertDialogDescription>
            </AlertDialogHeader>

            <DeleteCommunityConfirmationForm
              name={props.community.name}
              id={props.community.id}
            />
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  );
}

function DeleteCommunityConfirmationForm({
  name,
  id,
}: {
  name: string;
  id: string;
}) {
  const form = useForm({
    mode: 'onChange',
    reValidateMode: 'onChange',
    resolver: zodResolver(
      z.object({
        name: z.string().refine((value) => value === name, {
          message: 'Name does not match',
          path: ['name'],
        }),
      }),
    ),
    defaultValues: {
      name: '',
    },
  });

  return (
    <ErrorBoundary
      fallback={
        <StatusAlert
          variant="destructive"
          titleKey="communities:deleteCommunityErrorHeading"
          descriptionKey="common:genericError"
        />
      }
    >
      <Form {...form}>
        <form
          data-test={'delete-community-form'}
          className={'flex flex-col space-y-4'}
          action={deleteCommunityAction}
        >
          <div className={'flex flex-col space-y-2'}>
            <div
              className={
                'border-2 border-red-500 p-4 text-sm text-red-500' +
                ' my-4 flex flex-col space-y-2'
              }
            >
              <div>
                <Trans
                  i18nKey={'communities:deleteCommunityDisclaimer'}
                  values={{
                    communityName: name,
                  }}
                />
              </div>

              <div className={'text-sm'}>
                <Trans i18nKey={'common:modalConfirmationQuestion'} />
              </div>
            </div>

            <input type="hidden" value={id} name={'communityId'} />

            <FormField
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <Trans
                      i18nKey={
                        'communities:deleteCommunityDisclaimerInputField'
                      }
                      values={{
                        communityName: name,
                      }}
                    />
                  </FormLabel>

                  <FormControl>
                    <Input
                      data-test={'delete-community-form-confirm-input'}
                      required
                      type={'text'}
                      autoComplete={'off'}
                      className={'w-full'}
                      placeholder={''}
                      pattern={name}
                      {...field}
                      aria-label={`Type community name ${name} to confirm deletion`}
                    />
                  </FormControl>

                  <FormDescription>
                    <Trans i18nKey={'communities:deleteCommunityInputField'} />
                  </FormDescription>

                  <FormMessage />
                </FormItem>
              )}
              name={'confirm'}
            />
          </div>

          <AlertDialogFooter>
            <AlertDialogCancel
              data-test="cancel-delete-community-button"
              aria-label="Cancel deleting community"
            >
              <Trans i18nKey={'common:cancel'} />
            </AlertDialogCancel>

            <DeleteCommunitySubmitButton />
          </AlertDialogFooter>
        </form>
      </Form>
    </ErrorBoundary>
  );
}

function DeleteCommunitySubmitButton() {
  const { pending } = useFormStatus();

  return (
    <Button
      data-test={'delete-community-form-confirm-button'}
      disabled={pending}
      variant={'destructive'}
      aria-label="Confirm delete community"
    >
      <Trans i18nKey={'communities:deleteCommunity'} />
    </Button>
  );
}

DeleteCommunityContainer.displayName = 'DeleteCommunityContainer';
DeleteCommunityConfirmationForm.displayName = 'DeleteCommunityConfirmationForm';
DeleteCommunitySubmitButton.displayName = 'DeleteCommunitySubmitButton';
