import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON><PERSON>er,
  CardTitle,
} from '@kit/ui/card';
import { Trans } from '@kit/ui/trans';

import type { CommunityExpanded } from '~/lib/communities/community/types';
import type { PermissionsEnum } from '~/lib/communities/members/types';

import { CommunityDangerZone } from './community-danger-zone';

export function CommunityDeleteContainer(props: {
  community: CommunityExpanded;
  permissions: PermissionsEnum[];
}) {
  return (
    <Card className={'border-destructive border'}>
      <CardHeader>
        <CardTitle>
          <Trans i18nKey={'communities:settings.dangerZone'} />
        </CardTitle>
        <CardDescription>
          <Trans i18nKey={'communities:settings.dangerZoneDescription'} />
        </CardDescription>
      </CardHeader>
      <CardContent>
        <CommunityDangerZone
          primaryOwnerId={props.community.primaryOwnerUserId}
          community={props.community}
        />
      </CardContent>
    </Card>
  );
}

CommunityDeleteContainer.displayName = 'CommunityDeleteContainer';
