import { toast } from 'sonner';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { Social, SocialsForm } from '@kit/ui/dojo/organisms/socials-form';
import { Trans } from '@kit/ui/trans';

import { updateCommunitySocialsAction } from '~/lib/communities/community/actions/update-community-socials-server-actions';
import type { CommunityExpanded } from '~/lib/communities/community/types';
import type { PermissionsEnum } from '~/lib/communities/members/types';

export function CommunitySocialsContainer(props: {
  community: CommunityExpanded;
  permissions: PermissionsEnum[];
}) {
  const handleUpdateCommunitySocials = async (socialsArray: Social[]) => {
    try {
      await updateCommunitySocialsAction({
        communitySlug: props.community.slug,
        socials: socialsArray,
      });
    } catch (error) {
      console.error('Error updating community socials:', error);
      toast.error(
        <Trans
          i18nKey={'communities:settings.communitySocialsError'}
          values={{
            errorMessage:
              error instanceof Error ? error.message : 'Unknown error',
          }}
        />,
      );
    }
  };

  return (
    <Card data-test="community-socials-container">
      <CardHeader>
        <CardTitle>
          <Trans i18nKey={'communities:settings.communitySocials'} />
        </CardTitle>
        <CardDescription>
          <Trans i18nKey={'communities:settings.communitySocialsDescription'} />
        </CardDescription>
      </CardHeader>
      <CardContent>
        <SocialsForm
          socials={props.community.socials || []}
          onSubmit={handleUpdateCommunitySocials}
          testId="community-socials-form"
        />
      </CardContent>
    </Card>
  );
}

CommunitySocialsContainer.displayName = 'CommunitySocialsContainer';
