'use client';

import { useState, useTransition } from 'react';

import dynamic from 'next/dynamic';
import { useParams } from 'next/navigation';

import { useAppEvents } from '@kit/shared/events';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { Trans } from '@kit/ui/trans';

import { PricePicker } from '~/lib/stripe/components/price-picker';
import { ProductWithPrices } from '~/lib/stripe/types';

import { createCommunityAccountCheckoutSession } from '../_lib/server/actions/create-community-account-checkout-session';

const EmbeddedCheckout = dynamic(
  async () => {
    const { EmbeddedCheckout } = await import(
      '~/lib/stripe/components/embedded-checkout'
    );

    return {
      default: EmbeddedCheckout,
    };
  },
  {
    ssr: false,
  },
);

type CommunityCheckoutFormProps = {
  communityId: string;
  customerId: string | null | undefined;
  productWithPrices: ProductWithPrices[];
};

export function CommunityCheckoutForm({
  communityId,
  customerId,
  productWithPrices,
}: CommunityCheckoutFormProps) {
  const routeParams = useParams();
  const [pending, startTransition] = useTransition();
  const appEvents = useAppEvents();

  const [checkoutToken, setCheckoutToken] = useState<string | undefined>(
    undefined,
  );

  // Extract the community slug from route params
  const slug =
    (routeParams.community as string) || (routeParams.account as string);

  // If the checkout token is set, render the embedded checkout component
  if (checkoutToken) {
    return (
      <EmbeddedCheckout
        checkoutToken={checkoutToken}
        onClose={() => setCheckoutToken(undefined)}
      />
    );
  }

  // only allow trial if the user is not already a customer
  const canStartTrial = !customerId;

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          <Trans i18nKey={'billing:manageCommunityPlan'} />
        </CardTitle>

        <CardDescription>
          <Trans i18nKey={'billing:manageCommunityPlanDescription'} />
        </CardDescription>
      </CardHeader>

      <CardContent>
        <PricePicker
          pending={pending}
          productWithPrices={productWithPrices}
          canStartTrial={canStartTrial}
          onSubmit={({ priceId, productId }) => {
            startTransition(async () => {
              appEvents.emit({
                type: 'checkout.started',
                payload: {
                  priceId,
                  communityId,
                },
              });

              const { checkoutToken } =
                await createCommunityAccountCheckoutSession({
                  priceId,
                  productId,
                  slug,
                  communityId,
                });

              setCheckoutToken(checkoutToken);
            });
          }}
        />
      </CardContent>
    </Card>
  );
}

CommunityCheckoutForm.displayName = 'CommunityCheckoutForm';
