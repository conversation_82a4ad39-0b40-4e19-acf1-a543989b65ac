'use client';

import { useState } from 'react';

import { CircleX } from 'lucide-react';
import { toast } from 'sonner';

import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@kit/ui/alert-dialog';
import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { Trans } from '@kit/ui/trans';

import type { Subscription } from '~/lib/communities/billing/types';

import { cancelCommunitySubscription } from '../_lib/server/actions/cancel-community-subscription';

/**
 * Card component for canceling a subscription
 */

type CancelSubscriptionCardProps = {
  subscription: Subscription;
};

export function CancelSubscriptionCard({
  subscription,
}: CancelSubscriptionCardProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleCancelSubscription = async () => {
    if (!subscription) return;

    try {
      setIsLoading(true);
      const result = await cancelCommunitySubscription({
        subscriptionId: subscription.id,
      });

      if (result.success) {
        toast.success('Subscription canceled successfully');
      } else {
        toast.error('Failed to cancel subscription');
      }
    } catch (error) {
      console.error('Error canceling subscription:', error);
      toast.error('Failed to cancel subscription');
    } finally {
      setIsLoading(false);
      setIsDialogOpen(false);
    }
  };

  return (
    <Card data-test="cancel-subscription-card">
      <CardHeader>
        <CardTitle>
          <Trans i18nKey="billing:cancelSubscriptionCardTitle" />
        </CardTitle>

        <CardDescription>
          <Trans i18nKey="billing:cancelSubscriptionCardDescription" />
        </CardDescription>
      </CardHeader>

      <CardContent className={'space-y-2'}>
        <div className="flex flex-col items-start gap-2">
          {subscription.status === 'trialing' && (
            <span className={'text-muted-foreground text-sm'}>
              (Trial Ends{' '}
              {subscription.trialEndsAt
                ? new Date(subscription.trialEndsAt).toLocaleDateString()
                : 'N/A'}
              )
            </span>
          )}
          <AlertDialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <AlertDialogTrigger asChild>
              <Button
                data-test={'cancel-subscription-card-trigger'}
                type={'button'}
                variant={'destructive'}
                aria-label="Cancel subscription"
              >
                <Trans i18nKey={'billing:cancelSubscriptionCardButton'} />
              </Button>
            </AlertDialogTrigger>

            <AlertDialogContent
              onEscapeKeyDown={(e) => e.preventDefault()}
              data-test="cancel-subscription-dialog"
            >
              <AlertDialogHeader>
                <AlertDialogTitle>
                  <Trans i18nKey={'billing:cancelSubscriptionCardTitle'} />
                </AlertDialogTitle>

                <AlertDialogDescription>
                  <Trans
                    i18nKey={'billing:cancelSubscriptionCardDescription'}
                  />
                </AlertDialogDescription>
              </AlertDialogHeader>

              <AlertDialogFooter>
                <AlertDialogCancel
                  data-test="keep-subscription-button"
                  aria-label="Keep subscription"
                >
                  <Trans i18nKey={'common:cancel'} />
                </AlertDialogCancel>

                <div className={'flex flex-row gap-2'}>
                  <Button
                    data-test={'cancel-subscription-card-button'}
                    onClick={handleCancelSubscription}
                    disabled={isLoading}
                    aria-label="Confirm cancel subscription"
                  >
                    <span>
                      <Trans i18nKey="billing:cancelSubscriptionCardButton" />
                    </span>

                    <CircleX className={'h-4'} />
                  </Button>
                </div>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </CardContent>
    </Card>
  );
}

CancelSubscriptionCard.displayName = 'CancelSubscriptionCard';
