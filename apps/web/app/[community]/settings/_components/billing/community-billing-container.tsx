'use client';

import { useEffect, useState } from 'react';

import { Badge } from '@kit/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { Spinner } from '@kit/ui/spinner';
import { Trans } from '@kit/ui/trans';
import { cn } from '@kit/ui/utils';

import type { Subscription } from '~/lib/communities/billing/types';
import type { CommunityExpanded } from '~/lib/communities/community/types';
import type { PermissionsEnum } from '~/lib/communities/members/types';
import { hasCommunityPermission } from '~/lib/communities/permissions.utils';
import { BillingPortalCard } from '~/lib/stripe/components/billing/billing-portal-card';

import { CancelSubscriptionCard } from './_components/cancel-subscription-card';
import { createBillingPortalSession } from './_lib/server/actions/create-billing-portal-session';
import { getBillingDataAction } from './_lib/server/actions/get-billing-data';

export function CommunityBillingContainer(props: {
  community: CommunityExpanded;
  permissions: PermissionsEnum[];
}) {
  const [stripeAccountId, setStripeAccountId] = useState<string | undefined>(
    undefined,
  );
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const canManageBilling = hasCommunityPermission(
    props.permissions,
    'billing.manage',
  );

  useEffect(() => {
    async function loadBillingData() {
      try {
        const result = await getBillingDataAction({
          communityId: props.community.id,
        });

        setSubscription(result.subscription as unknown as Subscription);
        setStripeAccountId(result.stripeAccountId);
      } catch (error) {
        console.error('Failed to load billing data:', error);
      } finally {
        setIsLoading(false);
      }
    }

    loadBillingData();
  }, [props.community.id]);

  if (isLoading) {
    return (
      <Card>
        <CardHeader className="flex flex-row justify-between">
          <div className="flex flex-col justify-start gap-2">
            <CardTitle className="mr-2">
              <Trans i18nKey={'billing:settings.billing'} />
            </CardTitle>
            <CardDescription>
              <Trans i18nKey={'billing:settings.billingDescription'} />
            </CardDescription>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex min-h-[60vh] items-center justify-center">
            <Spinner />
          </div>
        </CardContent>
      </Card>
    );
  }

  // Billing portal component
  const BillingPortal = () => {
    if (!canManageBilling || !stripeAccountId) {
      return null;
    }

    return (
      <>
        <form action={createBillingPortalSession}>
          <input
            type="hidden"
            name={'communityId'}
            value={props.community.id}
          />
          <input type="hidden" name={'slug'} value={props.community.slug} />

          <BillingPortalCard />
        </form>
        {subscription?.id && (
          <CancelSubscriptionCard subscription={subscription} />
        )}
      </>
    );
  };

  return (
    <Card>
      <CardHeader className="flex flex-row justify-between">
        <div className="flex flex-col justify-start gap-2">
          <CardTitle className="mr-2">
            <div className="flex flex-row items-center gap-2">
              <Trans i18nKey={'billing:settings.billing'} />
              <Badge variant={'outline'}>
                {subscription?.status.toLocaleUpperCase()}
              </Badge>
            </div>
          </CardTitle>
          <CardDescription>
            <Trans i18nKey={'billing:settings.billingDescription'} />
          </CardDescription>
        </div>
      </CardHeader>

      <CardContent>
        <div
          className={cn(`flex w-full flex-col space-y-4`, {
            'max-w-2xl': subscription,
          })}
        >
          <BillingPortal />
        </div>
      </CardContent>
    </Card>
  );
}

CommunityBillingContainer.displayName = 'CommunityBillingContainer';
