'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createCommunityBillingService } from '~/lib/communities/billing/community-billing.service';

const BillingDataSchema = z.object({
  communityId: z.string(),
});

export const getBillingDataAction = enhanceAction(
  async function (data) {
    const { communityId } = data;

    const client = getSupabaseServerClient();
    const communityBillingService = createCommunityBillingService(client);

    try {
      // Get the Stripe account id for the community
      const stripeAccountId =
        await communityBillingService.getUserStripeAccountId(communityId);

      // Get the subscription for the community
      const subscription =
        await communityBillingService.getSubscription(communityId);

      return {
        stripeAccountId: stripeAccountId ?? '',
        subscription,
      };
    } catch (error) {
      console.error(error);
      throw new Error('Failed to get billing data');
    }
  },
  {
    auth: true,
    schema: BillingDataSchema,
  },
);
