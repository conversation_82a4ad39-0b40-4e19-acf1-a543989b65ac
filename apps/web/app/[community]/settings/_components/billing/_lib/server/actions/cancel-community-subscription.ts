'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { getStripeSubscriptionService } from '~/lib/stripe/services';

/**
 * @name cancelCommunitySubscription
 * @description Cancels a community subscription
 */
export const cancelCommunitySubscription = enhanceAction(
  async (data) => {
    const client = getSupabaseServerClient();
    const stripeSubscriptionService =
      await getStripeSubscriptionService(client);

    return stripeSubscriptionService.cancelSubscription(data);
  },
  {
    auth: true,
    schema: z.object({
      subscriptionId: z.string(),
    }),
  },
);
