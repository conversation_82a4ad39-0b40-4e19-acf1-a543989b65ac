'use server';

import { redirect } from 'next/navigation';

import { enhanceAction } from '@kit/next/actions';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createCommunityBillingService } from '~/lib/communities/billing/community-billing.service';
import { CommunityBillingPortalSchema } from '~/lib/communities/billing/schema/community-billing.schema';

/**
 * @name createBillingPortalSession
 * @description Creates a Billing Session Portal and redirects the user to the
 * provider's hosted instance
 */
export const createBillingPortalSession = enhanceAction(
  async (formData: FormData) => {
    const params = CommunityBillingPortalSchema.parse(
      Object.fromEntries(formData),
    );

    const client = getSupabaseServerClient();
    const service = createCommunityBillingService(client);

    // get url to billing portal
    const url = await service.createBillingPortalSession(params);

    return redirect(url);
  },
  {},
);
