'use server';

import { enhanceAction } from '@kit/next/actions';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createCommunityBillingService } from '~/lib/communities/billing/community-billing.service';
import { CommunityCheckoutSchema } from '~/lib/communities/billing/schema/community-billing.schema';

/**
 * @name createCommunityAccountCheckoutSession
 * @description Creates a checkout session for a community
 */
export const createCommunityAccountCheckoutSession = enhanceAction(
  async (data) => {
    const client = getSupabaseServerClient();
    const billingService = createCommunityBillingService(client);

    return billingService.createCheckout(data);
  },
  {
    schema: CommunityCheckoutSchema,
  },
);
