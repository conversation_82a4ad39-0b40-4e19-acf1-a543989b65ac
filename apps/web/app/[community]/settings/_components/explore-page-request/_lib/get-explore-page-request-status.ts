'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

export const getExplorePageRequestAction = enhanceAction(
  async function (props: { communityId: string }) {
    const logger = await getLogger();
    const client = getSupabaseServerClient();

    try {
      const { data, error } = await client.rpc(
        'get_community_explore_page_request_status',
        {
          p_community_id: props.communityId,
        },
      );

      if (error) {
        throw new Error(error.message);
      }

      return objectToCamel(
        typeof data === 'object' && data !== null ? data : {},
      );
    } catch (error) {
      logger.error(
        { error },
        'Error loading community explore page request status',
      );
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      return {
        status: 'error',
        error: errorMessage,
        requirements: {
          aboutPageExists: false,
          aboutPageContentLength: 0,
          aboutPageMediaCount: 0,
          logoExists: false,
          coverExists: false,
          categoryChanged: false,
          descriptionLength: false,
          nameValid: false,
        },
        requestSubmitted: false,
        requestStatus: 'not_submitted',
      };
    }
  },
  {
    auth: true,
    schema: z.object({
      communityId: z.string(),
    }),
  },
);
