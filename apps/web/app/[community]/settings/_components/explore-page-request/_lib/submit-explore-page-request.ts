'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

export const submitExplorePageRequestAction = enhanceAction(
  async function (props: { communityId: string; memberId: string }, user) {
    const logger = await getLogger();
    const client = getSupabaseServerClient();

    try {
      // First check if the community can reapply
      const { data: canReapplyData, error: canReapplyError } = await client
        .from('community_explore_requests')
        .select('status, updated_at')
        .eq('community_id', props.communityId)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      // Check reapplication eligibility locally
      if (canReapplyData && !canReapplyError) {
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

        // If there's a pending request, cannot reapply
        if (canReapplyData.status === 'pending') {
          throw new Error(
            'You already have a pending request. Please wait for it to be processed.',
          );
        }

        // If approved, cannot reapply (already listed)
        if (canReapplyData.status === 'approved') {
          throw new Error('Your community is already approved and listed.');
        }

        // If rejected, check 7-day waiting period
        if (canReapplyData.status === 'rejected') {
          if (canReapplyData.updated_at) {
            const rejectedDate = new Date(canReapplyData.updated_at);
            if (rejectedDate > sevenDaysAgo) {
              const nextReapplyDate = new Date(rejectedDate);
              nextReapplyDate.setDate(nextReapplyDate.getDate() + 7);
              throw new Error(
                `You must wait 7 days after rejection before reapplying. You can reapply on ${nextReapplyDate.toLocaleDateString()}.`,
              );
            }
          }
        }
      }

      // If eligible, proceed with submission
      const { data, error } = await client
        .from('community_explore_requests')
        .insert({
          community_id: props.communityId,
          status: 'pending',
          created_by_user_id: user.id,
          created_by_member_id: props.memberId,
        });

      if (error) {
        throw new Error(error.message);
      }

      return { data, error: null };
    } catch (error) {
      logger.error(
        { error },
        'Error submitting community explore page request',
      );
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      return { data: null, error: errorMessage };
    }
  },
  {
    auth: true,
    schema: z.object({
      communityId: z.string(),
      memberId: z.string(),
    }),
  },
);
