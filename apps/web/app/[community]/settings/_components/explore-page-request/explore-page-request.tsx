import { useQuery, useQueryClient } from '@tanstack/react-query';
import { CheckCircle2, Circle, Clock, ListChecks } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';

import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { If } from '@kit/ui/if';
import { Spinner } from '@kit/ui/spinner';
import { Trans } from '@kit/ui/trans';
import { cn } from '@kit/ui/utils';

import type { CommunityExpanded } from '~/lib/communities/community/types';
import type { PermissionsEnum } from '~/lib/communities/members/types';

import { getExplorePageRequestAction } from './_lib/get-explore-page-request-status';
import { submitExplorePageRequestAction } from './_lib/submit-explore-page-request';

// Define the expected response type from the server
type ExplorePageRequestResponse = {
  status: 'listed' | 'ready' | 'not_ready' | 'error';
  error?: string;
  requirements: {
    aboutPageExists: boolean;
    aboutPageContentLength: number;
    aboutPageMediaCount: number;
    logoExists: boolean;
    coverExists: boolean;
    categoryChanged: boolean;
    descriptionLength: boolean;
    nameValid: boolean;
  };
  requestSubmitted: boolean;
  requestStatus: 'not_submitted' | 'pending' | 'approved' | 'rejected';
  requestReason?: string;
  canReapply: boolean;
  nextReapplyDate?: string;
};

// Default empty requirement structure
const defaultRequirements = {
  nameValid: false,
  descriptionLength: false,
  categoryChanged: false,
  logoExists: false,
  coverExists: false,
  aboutPageExists: false,
  aboutPageContentLength: 0,
  aboutPageMediaCount: 0,
  requestSubmitted: false,
  requestStatus: 'not_submitted',
};

// Constants for requirement thresholds
const MIN_DESCRIPTION_LENGTH = 100;
const MIN_ABOUT_CONTENT_LENGTH = 100;
const MIN_MEDIA_COUNT = 2;

export function ExplorePageRequestContainer(props: {
  memberId: string;
  community: CommunityExpanded;
  permissions: PermissionsEnum[];
}) {
  const { t } = useTranslation('communities');
  // Get query client for cache invalidation
  const queryClient = useQueryClient();

  // Load the explore page request details
  const { data: explorePageRequestDetails, isLoading } = useQuery({
    queryKey: ['community-explore-page-request', props.community.id],
    queryFn: async () => {
      if (!props.community.id) return null;

      try {
        return (await getExplorePageRequestAction({
          communityId: props.community.id,
        })) as ExplorePageRequestResponse;
      } catch (error) {
        console.error('Failed to load explore page request:', error);
        toast.error('Failed to load explore page request');
        return null;
      }
    },
    enabled: !!props.community.id,
  });

  // Check if requirements are met based on the API response or community data
  const requirements =
    explorePageRequestDetails?.requirements || defaultRequirements;
  const hasLogo = requirements.logoExists ?? !!props.community.logoUrl;
  const hasCover = requirements.coverExists ?? !!props.community.coverUrl;
  const hasDescription =
    requirements.descriptionLength ??
    (props.community.description &&
      props.community.description.length >= MIN_DESCRIPTION_LENGTH);
  const hasCategory =
    requirements.categoryChanged ??
    props.community.categoryName !== 'new_community';
  const hasValidName = requirements.nameValid ?? true;
  const hasAboutPage = requirements.aboutPageExists ?? false;
  const hasAboutPageContent =
    (requirements.aboutPageContentLength ?? 0) >= MIN_ABOUT_CONTENT_LENGTH;
  const hasAboutPageMedia =
    (requirements.aboutPageMediaCount ?? 0) >= MIN_MEDIA_COUNT;

  // Get actual values for progress indicators
  const descriptionLength = props.community.description?.length || 0;
  const contentLength = requirements.aboutPageContentLength || 0;
  const mediaCount = requirements.aboutPageMediaCount || 0;

  // Status can be 'listed', 'ready', 'not_ready', or 'error'
  const status = explorePageRequestDetails?.status || 'not_ready';
  const isListed = props.community.isListed || status === 'listed';
  const isReady = status === 'ready';
  const hasError = status === 'error';

  // Request submission status
  const requestSubmitted = explorePageRequestDetails?.requestSubmitted || false;
  const requestStatus =
    explorePageRequestDetails?.requestStatus || 'not_submitted';
  const requestReason = explorePageRequestDetails?.requestReason;
  const canReapply = explorePageRequestDetails?.canReapply ?? true;
  const nextReapplyDate = explorePageRequestDetails?.nextReapplyDate;
  const isPendingApproval = requestSubmitted && requestStatus === 'pending';
  const isRejected = requestSubmitted && requestStatus === 'rejected';

  const handleSubmitExplorePageRequest = async () => {
    try {
      const { error } = await submitExplorePageRequestAction({
        communityId: props.community.id,
        memberId: props.memberId,
      });

      if (error) {
        toast.error('Failed to submit explore page request');
      } else {
        toast.success('Explore page request submitted');
        queryClient.invalidateQueries({
          queryKey: ['community-explore-page-request', props.community.id],
        });
      }
    } catch (error) {
      console.error('Failed to submit explore page request:', error);
      toast.error('Failed to submit explore page request');
    }
  };

  if (isLoading) {
    return (
      <Card className="relative" data-test="explore-page-request-card">
        <CardHeader className="flex flex-row justify-between">
          <div className="flex flex-col justify-start gap-2">
            <CardTitle>
              <Trans
                i18nKey={'communities:settings.explorePageRequest.title'}
              />
            </CardTitle>
            <CardDescription>
              <Trans
                i18nKey={'communities:settings.explorePageRequest.description'}
              />
            </CardDescription>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex min-h-[40vh] items-center justify-center">
            <Spinner />
          </div>
        </CardContent>
      </Card>
    );
  }

  // Helper function to render a requirement item
  const renderRequirement = (
    isMet: boolean,
    transKey: string,
    values?: Record<string, number>,
  ) => (
    <div
      className="flex items-center"
      data-test={`requirement-item-${transKey.substring(transKey.lastIndexOf('.') + 1)}`}
    >
      <div className="mr-2 h-4 w-4 flex-shrink-0">
        {isMet ? (
          <CheckCircle2 className="text-success h-4 w-4" />
        ) : (
          <Circle className="text-muted-foreground stroke-dashed h-4 w-4 stroke-[1.5px]" />
        )}
      </div>
      <span className={cn(isMet ? 'text-foreground' : 'text-muted-foreground')}>
        <Trans i18nKey={transKey} values={values} />
      </span>
    </div>
  );

  return (
    <Card className="relative" data-test="explore-page-request-card">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>
            <Trans i18nKey={'communities:settings.explorePageRequest.title'} />
          </CardTitle>
          {isListed ? (
            <Badge
              variant="outline"
              className="bg-success/10 ml-2"
              data-test="explore-status-badge-listed"
            >
              <CheckCircle2 className="text-success mr-1 h-3.5 w-3.5" />
              <Trans
                i18nKey={
                  'communities:settings.explorePageRequest.communityIsListed'
                }
              />
            </Badge>
          ) : isRejected ? (
            <Badge
              variant="outline"
              className="bg-destructive/10 ml-2"
              data-test="explore-status-badge-rejected"
            >
              <Trans
                i18nKey={
                  'communities:settings.explorePageRequest.requestRejected'
                }
              />
            </Badge>
          ) : isPendingApproval ? (
            <Badge
              variant="outline"
              className="bg-primary/10 ml-2"
              data-test="explore-status-badge-pending"
            >
              <Trans
                i18nKey={
                  'communities:settings.explorePageRequest.pendingApproval'
                }
              />
            </Badge>
          ) : isReady ? (
            <Badge
              variant="outline"
              className="bg-success/10 ml-2"
              data-test="explore-status-badge-ready"
            >
              <CheckCircle2 className="text-success mr-1 h-3.5 w-3.5" />
              <Trans
                i18nKey={'communities:settings.explorePageRequest.readyToList'}
              />
            </Badge>
          ) : (
            <Badge
              variant="outline"
              className="ml-2"
              data-test="explore-status-badge-not-listed"
            >
              <Trans
                i18nKey={'communities:settings.explorePageRequest.notListed'}
              />
            </Badge>
          )}
        </div>
        <CardDescription>
          <Trans
            i18nKey={'communities:settings.explorePageRequest.description'}
          />
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isListed ? (
          <div className="bg-muted/50 flex items-center space-x-3 rounded-md p-4">
            <CheckCircle2 className="text-success h-6 w-6" />
            <div>
              <p className="text-foreground font-medium">
                <Trans
                  i18nKey={
                    'communities:settings.explorePageRequest.successTitle'
                  }
                />{' '}
                <Trans
                  i18nKey={
                    'communities:settings.explorePageRequest.successMessage'
                  }
                />
              </p>
              <p className="text-muted-foreground text-sm">
                <Trans
                  i18nKey={
                    'communities:settings.explorePageRequest.successDescription'
                  }
                />
              </p>
            </div>
          </div>
        ) : isPendingApproval ? (
          <div className="bg-muted/50 flex items-center space-x-3 rounded-md p-4">
            <Clock className="text-primary h-6 w-6" />
            <div>
              <p className="text-foreground font-medium">
                <Trans
                  i18nKey={
                    'communities:settings.explorePageRequest.pendingTitle'
                  }
                />
              </p>
              <p className="text-muted-foreground text-sm">
                <Trans
                  i18nKey={
                    'communities:settings.explorePageRequest.pendingDescription'
                  }
                />
              </p>
            </div>
          </div>
        ) : isRejected ? (
          <div className="bg-destructive/10 flex items-center space-x-3 rounded-md p-4">
            <div className="text-destructive bg-destructive/20 flex h-6 w-6 items-center justify-center rounded-full text-sm font-bold">
              ✕
            </div>
            <div className="flex-1">
              <p className="text-foreground font-medium">
                <Trans
                  i18nKey={
                    'communities:settings.explorePageRequest.rejectedTitle'
                  }
                />
              </p>
              <p className="text-muted-foreground text-sm">
                <Trans
                  i18nKey={
                    'communities:settings.explorePageRequest.rejectedDescription'
                  }
                />
              </p>
              {requestReason && (
                <div className="bg-destructive/5 border-destructive/20 mt-2 rounded border-l-2 p-2">
                  <p className="text-destructive/80 text-sm font-medium">
                    <Trans
                      i18nKey={
                        'communities:settings.explorePageRequest.rejectionReason'
                      }
                    />
                  </p>
                  <p className="text-muted-foreground mt-1 text-sm">
                    {requestReason}
                  </p>
                </div>
              )}
              {!canReapply && nextReapplyDate && (
                <div className="bg-primary/5 border-primary/20 mt-2 rounded border-l-2 p-2">
                  <p className="text-primary/80 text-sm font-medium">
                    <Trans
                      i18nKey={
                        'communities:settings.explorePageRequest.waitingPeriod'
                      }
                    />
                  </p>
                  <p className="text-muted-foreground mt-1 text-sm">
                    <Trans
                      i18nKey={
                        'communities:settings.explorePageRequest.canReapplyOn'
                      }
                      values={{
                        date: new Date(nextReapplyDate).toLocaleDateString(),
                      }}
                    />
                  </p>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="space-y-5">
            <p className="text-muted-foreground">
              <Trans
                i18nKey={'communities:settings.explorePageRequest.requestInfo'}
              />
            </p>

            <div className="rounded-md border p-4">
              <div className="mb-3 flex items-center gap-2">
                <ListChecks className="text-primary h-5 w-5" />
                <h4 className="text-foreground font-medium">
                  <Trans
                    i18nKey={
                      'communities:settings.explorePageRequest.requirementsTitle'
                    }
                  />
                </h4>
              </div>
              <div className="grid gap-x-4 gap-y-3 sm:grid-cols-2">
                {/* Always dashed indicators */}
                {renderRequirement(
                  false,
                  'communities:settings.explorePageRequest.requirements.qualityContent',
                )}
                {renderRequirement(
                  false,
                  'communities:settings.explorePageRequest.requirements.activeCommunity',
                )}

                {/* Dynamic indicators */}
                {renderRequirement(
                  hasLogo,
                  'communities:settings.explorePageRequest.requirements.logo',
                )}
                {renderRequirement(
                  hasCover,
                  'communities:settings.explorePageRequest.requirements.cover',
                )}
                {renderRequirement(
                  hasDescription,
                  'communities:settings.explorePageRequest.requirements.communityDescription',
                  {
                    current: descriptionLength,
                    required: MIN_DESCRIPTION_LENGTH,
                  },
                )}
                {renderRequirement(
                  hasCategory,
                  'communities:settings.explorePageRequest.requirements.communityCategory',
                )}
                {renderRequirement(
                  hasValidName,
                  'communities:settings.explorePageRequest.requirements.communityName',
                )}
                {renderRequirement(
                  hasAboutPage,
                  'communities:settings.explorePageRequest.requirements.aboutPage',
                )}
                {renderRequirement(
                  hasAboutPageContent,
                  'communities:settings.explorePageRequest.requirements.aboutPageContent',
                  {
                    current: contentLength,
                    required: MIN_ABOUT_CONTENT_LENGTH,
                  },
                )}
                {renderRequirement(
                  hasAboutPageMedia,
                  'communities:settings.explorePageRequest.requirements.aboutPageMedia',
                  {
                    current: mediaCount,
                    required: MIN_MEDIA_COUNT,
                  },
                )}
              </div>
            </div>

            {hasError && explorePageRequestDetails && (
              <div className="bg-destructive/10 text-destructive rounded-md p-3 text-sm">
                <p>
                  <Trans
                    i18nKey={
                      'communities:settings.explorePageRequest.errorLoadingStatus'
                    }
                  />
                  {explorePageRequestDetails.error && (
                    <>: {explorePageRequestDetails.error}</>
                  )}
                </p>
              </div>
            )}
          </div>
        )}
      </CardContent>
      <If
        condition={
          !isListed &&
          !hasError &&
          (!isPendingApproval || (isRejected && canReapply))
        }
      >
        <CardFooter className="flex justify-end pt-2">
          <Button
            onClick={handleSubmitExplorePageRequest}
            disabled={!isReady || (requestSubmitted && !canReapply)}
            data-test="request-explore-page-button"
            aria-label={t('ariaLabels.requestExplorePageAriaLabel')}
          >
            <Trans
              i18nKey={
                isRejected && canReapply
                  ? 'communities:settings.explorePageRequest.reapplyButton'
                  : 'communities:settings.explorePageRequest.requestButton'
              }
            />
          </Button>
        </CardFooter>
      </If>
    </Card>
  );
}

ExplorePageRequestContainer.displayName = 'ExplorePageRequestContainer';
