'use client';

import React from 'react';
// For refresh
import { useTransition } from 'react';

import { useRouter } from 'next/navigation';

import { toast } from 'sonner';

import { Button } from '@kit/ui/button';
import { Trans } from '@kit/ui/trans';

import { linkCommunityAccountAction } from './_lib/server/actions/link-community-account-action';

interface LinkAccountButtonProps {
  communityId: string;
  isOwner: boolean;
  communityConnectedAccountId: string | null;
  userConnectedAccountId: string | null;
}

export function LinkAccountButton({
  communityId,
  isOwner,
  communityConnectedAccountId,
  userConnectedAccountId,
}: LinkAccountButtonProps) {
  const [isPending, startTransition] = useTransition();
  const router = useRouter();

  const shouldShowLinkButton =
    isOwner && !communityConnectedAccountId && !!userConnectedAccountId;

  const handleLinkAccount = () => {
    startTransition(async () => {
      try {
        const result = await linkCommunityAccountAction({ communityId });

        if (result.success) {
          toast.success('Account linked successfully! Refreshing...');
          router.refresh();
        }
      } catch (error) {
        console.error('Error linking account:', error);
        toast.error(
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred.',
        );
      }
    });
  };

  if (!shouldShowLinkButton) {
    return null;
  }

  return (
    <Button
      data-test="link-connect-account-button"
      onClick={handleLinkAccount}
      disabled={isPending}
      aria-label="Link my Connect account"
    >
      {isPending ? (
        <Trans i18nKey="common:loading.default" />
      ) : (
        <Trans i18nKey="community:settings.linkMyAccountButton" />
      )}
    </Button>
  );
}
