'use client';

import React from 'react';

import { Badge } from '@kit/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { Trans } from '@kit/ui/trans';

import type { CommunityConnectStatusData } from '~/lib/communities/community/types';

interface DisplayLinkedAccountStatusProps {
  statusData: CommunityConnectStatusData | null;
}

/**
 * @name DisplayLinkedAccountStatus
 * @description Client component to display the fetched linked account status.
 */
export function DisplayLinkedAccountStatus({
  statusData: accountStatusData,
}: DisplayLinkedAccountStatusProps) {
  // Logic to determine text and variant based on props (copied from original)
  const getStatusTextAndVariant = () => {
    if (!accountStatusData || !accountStatusData.id) {
      return { text: 'Not Linked', variant: 'outline' as const };
    }
    if (!accountStatusData.detailsSubmitted) {
      return { text: 'Incomplete', variant: 'destructive' as const };
    }
    if (accountStatusData.chargesEnabled && accountStatusData.payoutsEnabled) {
      return { text: 'Active', variant: 'default' as const };
    }
    return { text: 'Restricted', variant: 'secondary' as const };
  };

  const { text: statusText, variant: statusVariant } =
    getStatusTextAndVariant();

  // Fetching errors handled by parent server component, here we just display data
  return (
    <Card data-test="linked-account-status-card">
      <CardHeader>
        <CardTitle>
          <Trans i18nKey="community:settings.linkedAccountStatusTitle" />
        </CardTitle>
        <CardDescription>
          <Trans i18nKey="community:settings.linkedAccountStatusDescription" />
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center space-x-2">
          <span className="text-muted-foreground text-sm">Account Status:</span>
          <div role="status" aria-live="polite">
            <Badge variant={statusVariant} data-test="payouts-status-badge">
              {statusText}
            </Badge>
          </div>
          {accountStatusData?.id && (
            <span className="text-muted-foreground text-xs">
              ({accountStatusData.id})
            </span>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

DisplayLinkedAccountStatus.displayName = 'DisplayLinkedAccountStatus';
