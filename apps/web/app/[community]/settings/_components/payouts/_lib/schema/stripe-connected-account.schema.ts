import { z } from 'zod';

/**
 * Schema for Stripe Connect payout schedule configuration
 */
export const PayoutScheduleSchema = z.object({
  interval: z.enum(['manual', 'daily', 'weekly', 'monthly']),
  weeklyAnchor: z.number().min(1).max(7).optional(),
  monthlyAnchor: z.number().min(1).max(31).optional(),
});

/**
 * Schema for Stripe Connect payout settings
 */
export const PayoutSettingsSchema = z.object({
  schedule: PayoutScheduleSchema.optional(),
});

/**
 * Schema for Stripe Connect account settings
 */
export const SettingsSchema = z.object({
  payouts: PayoutSettingsSchema.optional(),
});

/**
 * Schema for Stripe Connect business profile
 */
export const BusinessProfileSchema = z.object({
  mcc: z.string().optional(),
  url: z.string().url().optional(),
  name: z.string().optional(),
  productDescription: z.string().optional(),
});

/**
 * Schema for creating a Stripe Connect account with strict validation
 */
export const StrictConnectedAccountParamsSchema = z.object({
  communityId: z.string().uuid(),
  communityName: z.string(),
  countryISOCode1: z.string().length(2),
  email: z.string().email(),
  accountType: z.enum(['express', 'standard', 'custom']),
  businessType: z.enum(['individual', 'company']).optional(),
  businessProfile: BusinessProfileSchema.optional(),
  settings: SettingsSchema.optional(),
  returnUrl: z.string().url().optional(),
});
