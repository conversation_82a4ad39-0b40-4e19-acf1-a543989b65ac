'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { RetrieveStripeAccountParamsSchema } from '~/lib/stripe/schema';
import { getStripeAccountService } from '~/lib/stripe/services';

/**
 * Retrieve a stripe account for the given parameters
 */
export const getStripeAccountDetailsAction = enhanceAction(
  async (params: z.infer<typeof RetrieveStripeAccountParamsSchema>) => {
    const logger = await getLogger();

    const ctx = {
      name: 'getStripeAccountDetailsAction',
      stripeAccountId: params.stripeAccountId,
    };

    logger.info(ctx, 'Retrieving stripe account');

    const supabase = getSupabaseServerClient();

    try {
      // Retrieve the stripe account using the service
      const stripeAccountService = getStripeAccountService(supabase);
      const response = await stripeAccountService.retrieveStripeAccount(params);

      logger.info(ctx, 'Stripe account retrieved successfully');

      return response;
    } catch (error) {
      logger.error({ ...ctx, error }, 'Error retrieving stripe account');

      throw error;
    }
  },
  {
    auth: true,
    schema: RetrieveStripeAccountParamsSchema,
  },
);
