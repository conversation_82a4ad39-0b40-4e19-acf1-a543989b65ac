'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

const LinkCommunityAccountSchema = z.object({
  communityId: z.string().uuid(),
});

export const linkCommunityAccountAction = enhanceAction(
  async ({ communityId }: z.infer<typeof LinkCommunityAccountSchema>, user) => {
    const client = getSupabaseServerClient();
    const logger = await getLogger();
    const ctx = {
      name: 'linkCommunityAccountAction',
      communityId,
      userId: user.id,
    };

    logger.info(ctx, 'Attempting to link community to user stripe account...');

    try {
      // We use the DB function directly which handles permission checks (owner)
      // and fetching the user's account ID.
      const { error } = await client.rpc(
        'link_community_to_connected_account',
        {
          p_community_id: communityId,
          p_user_id: user.id,
        },
      );

      if (error) {
        logger.error(
          ctx,
          'Error calling link_community_to_connected_account RPC',
          {
            error,
          },
        );
        throw new Error(
          error.message ||
            'Failed to link account. Ensure you are the owner and have a stripe account set up in your user settings.',
        );
      }

      logger.info(ctx, 'Successfully linked community to user account.');
      return { success: true };
    } catch (error) {
      logger.error(
        { ...ctx, error },
        'Unexpected error linking community account.',
      );
      // Re-throw or return a structured error
      throw error instanceof Error
        ? error
        : new Error('An unexpected error occurred.');
    }
  },
  {
    auth: true,
    schema: LinkCommunityAccountSchema,
  },
);
