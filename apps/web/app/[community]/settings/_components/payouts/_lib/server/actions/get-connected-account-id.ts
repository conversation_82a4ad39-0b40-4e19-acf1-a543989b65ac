'use server';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { getStripeAccountService } from '~/lib/stripe/services';

export const getConnectedAccountIdAction = enhanceAction(
  async function (communityId: string) {
    const logger = await getLogger();
    const ctx = {
      name: 'get-connected-account',
      communityId,
    };

    logger.info(ctx, 'Fetching stripe account...');

    try {
      const client = getSupabaseServerClient();
      const stripeAccountService = getStripeAccountService(client);

      const stripeAccount =
        await stripeAccountService.getCommunityStripeAccountStatus(communityId);

      if (stripeAccount?.id === null) {
        logger.info(ctx, 'No stripe account found');
        return null;
      }

      logger.info(ctx, 'Stripe account retrieved');

      return stripeAccount;
    } catch (error) {
      logger.error({ ...ctx, error }, 'Failed to fetch stripe account');
      throw new Error('Failed to check your Connect account status.');
    }
  },
  {
    auth: true,
  },
);
