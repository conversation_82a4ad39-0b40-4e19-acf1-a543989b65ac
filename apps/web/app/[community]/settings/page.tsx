import { redirect } from 'next/navigation';

import { PageBody } from '@kit/ui/page';

import pathsConfig from '~/config/paths.config';
import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { withI18n } from '~/lib/i18n/with-i18n';

import { CommunityNotFound } from '../_components/community-not-found';
import { CommunitySettingsContainer } from './_components/community-settings-container';
import { loadSettingsPageData } from './_lib/server/settings-page.loader';

export const generateMetadata = async () => {
  const i18n = await createI18nServerInstance();
  const title = i18n.t('communities:settings:pageTitle');

  return {
    title,
  };
};

type CommunitySettingsPageProps = {
  params: Promise<{ community: string }>;
};

async function CommunitySettingsPage(props: CommunitySettingsPageProps) {
  const communitySlug = (await props.params).community;

  const { communityWorkspace, community, permissions, connectStatusData } =
    await loadSettingsPageData(communitySlug);

  if (!communityWorkspace || !communityWorkspace.user) {
    redirect(pathsConfig.auth.signIn);
  }

  if (!community) {
    return <CommunityNotFound />;
  }

  return (
    <>
      <PageBody>
        <div className="flex w-full flex-1 flex-col">
          <CommunitySettingsContainer
            user={communityWorkspace.user}
            memberId={communityWorkspace.community.memberId}
            community={community}
            permissions={permissions}
            connectStatusData={connectStatusData}
          />
        </div>
      </PageBody>
    </>
  );
}

export default withI18n(CommunitySettingsPage);
