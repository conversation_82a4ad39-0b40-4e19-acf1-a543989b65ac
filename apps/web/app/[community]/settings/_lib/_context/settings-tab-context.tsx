'use client';

import { ReactNode, createContext, useContext } from 'react';

type TabValue =
  | 'details'
  | 'socials'
  | 'images'
  | 'forum-categories'
  | 'payments'
  | 'subscriptions'
  | 'billing'
  | 'delete';

type SettingsTabContextType = {
  setActiveTab: (tab: TabValue) => void;
};

const SettingsTabContext = createContext<SettingsTabContextType | undefined>(
  undefined,
);

export function SettingsTabProvider({
  children,
  setActiveTab,
}: {
  children: ReactNode;
  setActiveTab: (tab: TabValue) => void;
}) {
  return (
    <SettingsTabContext.Provider value={{ setActiveTab }}>
      {children}
    </SettingsTabContext.Provider>
  );
}

export function useSettingsTab() {
  const context = useContext(SettingsTabContext);

  if (context === undefined) {
    throw new Error('useSettingsTab must be used within a SettingsTabProvider');
  }

  return context;
}
