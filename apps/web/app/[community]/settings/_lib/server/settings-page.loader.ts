import 'server-only';

import { redirect } from 'next/navigation';

import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import pathsConfig from '~/config/paths.config';
import { loadCommunityWorkspace } from '~/lib/communities/community-workspace.loader';
import { createCommunityService } from '~/lib/communities/community/community.service';
import type { CommunityExpanded } from '~/lib/communities/community/types';
import { getStripeAccountService } from '~/lib/stripe/services';

/**
 * Load data specific to the Settings page.
 * @param communitySlug
 */
export async function loadSettingsPageData(communitySlug: string) {
  const logger = await getLogger();
  const client = getSupabaseServerClient();

  const ctx = {
    name: 'settings.page.load',
    communitySlug,
  };

  logger.info(ctx, 'Loading Settings page specific data...');

  const communityService = createCommunityService(client);

  const communityWorkspace = await loadCommunityWorkspace(communitySlug);

  if (!communityWorkspace.user) {
    logger.warn(ctx, 'User not authenticated, redirecting to sign in.');
    redirect(pathsConfig.auth.signIn);
  }

  if (!communityWorkspace.community.memberId) {
    logger.warn(ctx, 'Community member not found.');
    const path = pathsConfig.app.communityAbout.replace(
      '[community]',
      communitySlug,
    );
    redirect(path);
  }

  const permissions = communityWorkspace.community.permissions;

  let community;
  try {
    community = await communityService.getCommunityBySlug(communitySlug);
  } catch (error) {
    logger.error({ ...ctx, error }, 'Failed to load community by slug');
    return {
      communityWorkspace: null,
      community: null,
      permissions: [],
      connectStatusData: null,
    };
  }

  if (!community) {
    logger.warn(ctx, 'Community not found by slug.');
    return {
      communityWorkspace,
      community: null,
      permissions,
      connectStatusData: null,
    };
  }

  const stripeAccountService = getStripeAccountService(client);

  const connectStatusData =
    await stripeAccountService.getCommunityStripeAccountStatus(community.id);

  logger.info(ctx, 'Successfully loaded Settings page specific data');

  return {
    communityWorkspace,
    community: community as CommunityExpanded,
    permissions,
    connectStatusData,
  };
}
