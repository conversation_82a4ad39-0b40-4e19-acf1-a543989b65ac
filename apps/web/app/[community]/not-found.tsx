import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { withI18n } from '~/lib/i18n/with-i18n';

import { CommunityNotFound } from './_components/community-not-found';

export const generateMetadata = async () => {
  const i18n = await createI18nServerInstance();
  const title = i18n.t('communities:notFound.pageTitle');

  return {
    title,
  };
};

function CommunityNotFoundPage() {
  return <CommunityNotFound />;
}

export default withI18n(CommunityNotFoundPage);

CommunityNotFoundPage.displayName = 'CommunityNotFoundPage';
