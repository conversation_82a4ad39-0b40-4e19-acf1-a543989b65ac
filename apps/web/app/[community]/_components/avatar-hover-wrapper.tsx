'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@kit/ui/avatar';
import { AvatarHoverCard } from '@kit/ui/dojo/organisms/avatar-hover-card';
import { PublicProfile } from '@kit/ui/dojo/organisms/user-profile-card';
import { cn } from '@kit/ui/utils';

import { getUserByIdAction } from '~/lib/users/actions/get-user-by-id-action';

type AvatarHoverWrapperProps = {
  userId: string;
  pictureUrl: string;
  firstName: string;
  lastName: string;
  showName?: boolean;
  avatarClassName?: string;
  className?: string;
};

export function AvatarHoverWrapper({
  userId,
  pictureUrl,
  firstName,
  lastName,
  showName = true,
  className,
  avatarClassName,
}: AvatarHoverWrapperProps) {
  // Handler for fetching author profile on hover
  const handleAuthorHover = async (
    userId: string,
  ): Promise<PublicProfile | null> => {
    try {
      const user = await getUserByIdAction({ id: userId });

      if (user) {
        return user as PublicProfile;
      }
      return null;
    } catch (error) {
      console.error('Error fetching author profile:', error);
      return null;
    }
  };

  return (
    <AvatarHoverCard
      userId={userId}
      onHover={handleAuthorHover}
      data-test="avatar-hover-card"
    >
      <div className={cn('flex flex-row items-center gap-2', className)}>
        <Avatar className={cn('h-12 w-12', avatarClassName)}>
          <AvatarImage
            src={pictureUrl}
            alt={firstName}
            data-test="avatar-image"
          />
          <AvatarFallback>
            {firstName.charAt(0)}
            {lastName.charAt(0)}
          </AvatarFallback>
        </Avatar>

        {showName && (
          <span className="cursor-pointer text-sm hover:underline">
            {firstName} {lastName}
          </span>
        )}
      </div>
    </AvatarHoverCard>
  );
}

AvatarHoverWrapper.displayName = 'AvatarHoverWrapper';
