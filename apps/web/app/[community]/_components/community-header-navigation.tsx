import { CommunityNavigationSelector } from '~/[community]/_components/community-navigation-selector';
import { UserAccountDropdownContainer } from '~/_components/user-account-dropdown-container';
import { CommunityWorkspace } from '~/lib/communities/community-workspace.loader';

import { CommunityNotifications } from './community-notifications';

export function CommunityHeaderNavigation(props: {
  workspace: CommunityWorkspace;
}) {
  const { community, user, communities } = props.workspace;

  if (!user || !community || !communities) {
    return null;
  }

  return (
    <>
      <CommunityNotifications userId={user.id} communityId={community.id} />

      <CommunityNavigationSelector
        userId={user.id}
        selectedCommunity={community.slug}
        communities={communities}
      />

      <div>
        <UserAccountDropdownContainer user={user} showProfileName={false} />
      </div>
    </>
  );
}

CommunityHeaderNavigation.displayName = 'CommunityHeaderNavigation';
