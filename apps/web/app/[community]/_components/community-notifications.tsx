import { NotificationsPopover } from '@kit/notifications/components';

import featuresFlagConfig from '~/config/feature-flags.config';

export function CommunityNotifications(params: {
  userId: string;
  communityId: string;
}) {
  if (!featuresFlagConfig.enableNotifications) {
    return null;
  }

  return (
    <NotificationsPopover
      communityIds={[params.userId, params.communityId]}
      realtime={featuresFlagConfig.realtimeNotifications}
      data-test="notifications-popover-container"
    />
  );
}

CommunityNotifications.displayName = 'CommunityNotifications';
