'use client';

import { useTransition } from 'react';

import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@kit/ui/alert-dialog';
import { Button } from '@kit/ui/button';
import { Trans } from '@kit/ui/trans';

import { leaveCommunityAccountAction } from '~/lib/communities/members/actions/leave-community-server-actions';

export function LeaveCommunityContainer(props: {
  communityName: string;
  communityId: string;
}) {
  const [isLeaving, startTransition] = useTransition();
  const handleLeaveCommunity = async () => {
    startTransition(async () => {
      await leaveCommunityAccountAction({ communityId: props.communityId });
    });
  };

  return (
    <div className={'flex flex-col space-y-4'}>
      <p className={'text-muted-foreground text-sm'}>
        <Trans
          i18nKey={'communities:leaveCommunityDescription'}
          values={{
            communityName: props.communityName,
          }}
        />
      </p>

      <AlertDialog>
        <AlertDialogTrigger asChild>
          <div>
            <Button
              data-test={'leave-community-button'}
              type={'button'}
              variant={'destructive'}
            >
              <Trans i18nKey={'communities:leaveCommunity'} />
            </Button>
          </div>
        </AlertDialogTrigger>

        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              <Trans i18nKey={'communities:leavingCommunityModalHeading'} />
            </AlertDialogTitle>

            <AlertDialogDescription>
              <Trans i18nKey={'communities:leavingCommunityModalDescription'} />
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>
              <Trans i18nKey={'communities:cancel'} />
            </AlertDialogCancel>

            <Button
              onClick={handleLeaveCommunity}
              data-test={'confirm-leave-community-button'}
              disabled={isLeaving}
              variant={'destructive'}
            >
              <Trans
                i18nKey={
                  isLeaving
                    ? 'communities:leaving'
                    : 'communities:leaveCommunity'
                }
              />
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

LeaveCommunityContainer.displayName = 'LeaveCommunityContainer';
