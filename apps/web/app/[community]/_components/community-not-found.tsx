import Link from 'next/link';

import { Building2, LucideIcon } from 'lucide-react';

import { Button } from '@kit/ui/button';
import { NotFound } from '@kit/ui/dojo/molecules/not-found';
import { PageBody } from '@kit/ui/page';

import pathsConfig from '~/config/paths.config';
import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { withI18n } from '~/lib/i18n/with-i18n';

type CommunityNotFoundProps = {
  icon?: LucideIcon;
  title?: string;
  message?: string;
  actions?: React.ReactNode;
};

export async function CommunityNotFound({
  icon = Building2,
  title,
  message,
  actions,
}: CommunityNotFoundProps) {
  const i18n = await createI18nServerInstance();
  const homePath = pathsConfig.app.home;

  if (!title) {
    title = i18n.t('communities:notFound.title');
  }

  if (!message) {
    message = i18n.t('communities:notFound.message');
  }

  if (!actions) {
    actions = (
      <Button
        asChild
        data-test="return-home-button"
        aria-label={i18n.t('communities:notFound.returnHome')}
      >
        <Link href={homePath}>{i18n.t('communities:notFound.returnHome')}</Link>
      </Button>
    );
  }

  return (
    <PageBody>
      <NotFound
        title={title}
        message={message}
        icon={icon}
        actions={actions}
        data-test="not-found-container"
      />
    </PageBody>
  );
}

export default withI18n(CommunityNotFound);

CommunityNotFound.displayName = 'CommunityNotFound';
