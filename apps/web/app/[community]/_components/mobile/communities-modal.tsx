'use client';

import { useRouter } from 'next/navigation';

import { Home } from 'lucide-react';

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@kit/ui/dialog';
import { DropdownMenuItem } from '@kit/ui/dropdown-menu';
import { Trans } from '@kit/ui/trans';

import pathsConfig from '~/config/paths.config';
import type { CommunitiesSlim } from '~/lib/communities/community/types';
import { CommunitySelector } from '~/user/_components/community-selector';

export function CommunitiesModal(props: {
  communities: CommunitiesSlim;
  userId: string;
}) {
  const router = useRouter();

  return (
    <Dialog>
      <DialogTrigger asChild>
        <DropdownMenuItem
          className={'flex h-12 w-full items-center space-x-2'}
          onSelect={(e) => e.preventDefault()}
          data-test="communities-modal-trigger"
          aria-label="Open communities modal"
        >
          <Home className={'h-4'} />

          <span>
            <Trans i18nKey={'common:yourCommunities'} />
          </span>
        </DropdownMenuItem>
      </DialogTrigger>

      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            <Trans i18nKey={'common:yourCommunities'} />
          </DialogTitle>
        </DialogHeader>

        <div className={'py-16'}>
          <CommunitySelector
            className={'w-full max-w-full'}
            collisionPadding={0}
            userId={props.userId}
            onCommunityChange={(value) => {
              const path = value
                ? pathsConfig.app.communityHome.replace('[community]', value)
                : pathsConfig.app.home;

              router.replace(path);
            }}
            communities={props.communities}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
}

CommunitiesModal.displayName = 'CommunitiesModal';
