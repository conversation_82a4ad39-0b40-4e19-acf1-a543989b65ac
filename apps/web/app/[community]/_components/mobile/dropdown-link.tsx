'use client';

import Link from 'next/link';

import { DropdownMenuItem } from '@kit/ui/dropdown-menu';
import { Trans } from '@kit/ui/trans';

export function DropdownLink(
  props: React.PropsWithChildren<{
    path: string;
    label: string;
    icon: React.ReactNode;
  }>,
) {
  return (
    <DropdownMenuItem asChild aria-label={props.label}>
      <Link
        href={props.path}
        className={'flex h-12 w-full items-center space-x-2 px-3'}
        data-test={`dropdown-link-${props.label}`}
      >
        {props.icon}

        <span>
          <Trans i18nKey={props.label} defaults={props.label} />
        </span>
      </Link>
    </DropdownMenuItem>
  );
}

DropdownLink.displayName = 'DropdownLink';
