'use client';

import React from 'react';

import { LogOut } from 'lucide-react';

import { DropdownMenuItem } from '@kit/ui/dropdown-menu';
import { Trans } from '@kit/ui/trans';

export function SignOutDropdownItem(
  props: React.PropsWithChildren<{
    onSignOut: () => unknown;
  }>,
) {
  return (
    <DropdownMenuItem
      className={'flex h-12 w-full items-center space-x-2'}
      onClick={props.onSignOut}
      data-test="sign-out-dropdown-item"
    >
      <LogOut className={'h-4'} />

      <span>
        <Trans i18nKey={'common:signOut'} />
      </span>
    </DropdownMenuItem>
  );
}

SignOutDropdownItem.displayName = 'SignOutDropdownItem';
