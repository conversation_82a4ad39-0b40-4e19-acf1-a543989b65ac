'use client';

import React from 'react';

import { Menu } from 'lucide-react';

import { useSignOut } from '@kit/supabase/hooks/use-sign-out';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@kit/ui/dropdown-menu';

import { getCommunitiesRoutesConfig } from '~/[community]/_lib/config/communities-navigation.config';
import type { CommunitiesSlim } from '~/lib/communities/community/types';
import type { PermissionsEnum } from '~/lib/communities/members/types';

import { CommunitiesModal } from './communities-modal';
import { DropdownLink } from './dropdown-link';
import { SignOutDropdownItem } from './sign-out-dropdown-item';

export const CommunitiesLayoutMobileNavigation = (
  props: React.PropsWithChildren<{
    communitySlug: string;
    userId: string | null;
    communities: CommunitiesSlim;
    permissions?: PermissionsEnum[];
    isMember: boolean;
    isPrivate: boolean;
  }>,
) => {
  const signOut = useSignOut();

  const Links = getCommunitiesRoutesConfig(
    props.communitySlug,
    props.permissions ?? [],
    props.isMember,
    props.isPrivate,
  ).routes.map((item, index) => {
    // Handle RouteGroup (has children)
    if (
      'children' in item &&
      !('path' in item) &&
      Array.isArray(item.children)
    ) {
      return item.children.map((child) => (
        <DropdownLink
          key={child.path}
          icon={child.Icon}
          path={child.path}
          label={child.label}
        />
      ));
    }
    // Handle RouteChild (single link)
    else if ('path' in item && 'label' in item) {
      return (
        <DropdownLink
          key={item.path}
          icon={item.Icon}
          path={item.path}
          label={item.label}
        />
      );
    }
    // Handle Divider
    else if ('divider' in item) {
      return <DropdownMenuSeparator key={`divider-${index}`} />;
    }

    // Return null for any other cases or if children wasn't an array
    return null;
  });

  return (
    <DropdownMenu>
      <DropdownMenuTrigger
        data-test="mobile-navigation-trigger"
        aria-label="Open mobile navigation"
      >
        <Menu className={'h-9'} />
      </DropdownMenuTrigger>
      {props.userId && (
        <DropdownMenuContent
          sideOffset={10}
          className={'w-screen rounded-none'}
        >
          <CommunitiesModal
            userId={props.userId}
            communities={props.communities}
          />

          {Links}

          <DropdownMenuSeparator />

          <SignOutDropdownItem onSignOut={() => signOut.mutateAsync()} />
        </DropdownMenuContent>
      )}
    </DropdownMenu>
  );
};

CommunitiesLayoutMobileNavigation.displayName =
  'CommunitiesLayoutMobileNavigation';
