'use client';

import { useTransition } from 'react';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
} from '@kit/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Trans } from '@kit/ui/trans';

import { TicketPriority } from '~/lib/communities/tickets/types';

import { UpdateTicketPrioritySchema } from '../_lib/schema/update-ticket-priority.schema';
import { updateTicketPriorityAction } from '../_lib/server/actions/update-ticket-priority-action';

export function PrioritySelect(props: {
  priority: TicketPriority;
  ticketId: string;
  disabled: boolean;
}) {
  const { t } = useTranslation('common');
  const form = useForm({
    resolver: zodResolver(UpdateTicketPrioritySchema),
    defaultValues: {
      priority: props.priority,
      ticketId: props.ticketId,
    },
  });

  const [isPending, startTransition] = useTransition();

  return (
    <Form {...form}>
      <FormField
        render={({ field }) => {
          return (
            <FormItem>
              <FormLabel>
                <Trans i18nKey={'common:table.priority'} />
              </FormLabel>

              <FormControl>
                <Select
                  value={form.getValues('priority')}
                  disabled={isPending || props.disabled}
                  onValueChange={(value) => {
                    form.setValue(field.name, value as TicketPriority, {
                      shouldValidate: true,
                    });

                    void form.handleSubmit(
                      (value: z.infer<typeof UpdateTicketPrioritySchema>) => {
                        startTransition(async () => {
                          await updateTicketPriorityAction(value);
                        });
                      },
                    )();
                  }}
                >
                  <SelectTrigger
                    data-test="priority-select-trigger"
                    aria-label={t('table.priority')}
                  >
                    <SelectValue
                      placeholder={
                        <Trans
                          i18nKey={'tickets:ticketDetails.choosePriority'}
                        />
                      }
                    />
                  </SelectTrigger>

                  <SelectContent>
                    <SelectItem
                      value={'low'}
                      data-test="priority-option-low"
                      aria-label={t('priority.low')}
                    >
                      <Trans i18nKey={'common:priority.low'} />
                    </SelectItem>
                    <SelectItem
                      value={'medium'}
                      data-test="priority-option-medium"
                      aria-label={t('priority.medium')}
                    >
                      <Trans i18nKey={'common:priority.medium'} />
                    </SelectItem>
                    <SelectItem
                      value={'high'}
                      data-test="priority-option-high"
                      aria-label={t('priority.high')}
                    >
                      <Trans i18nKey={'common:priority.high'} />
                    </SelectItem>
                  </SelectContent>
                </Select>
              </FormControl>

              <FormDescription>
                <Trans i18nKey={'tickets:ticketDetails.priorityDescription'} />
              </FormDescription>
            </FormItem>
          );
        }}
        name={'priority'}
      />
    </Form>
  );
}
