'use client';

import { useTransition } from 'react';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
} from '@kit/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Trans } from '@kit/ui/trans';

import { useFetchTeamMembers } from '~/lib/communities/members/hooks/use-fetch-team-members';
import { Ticket } from '~/lib/communities/tickets/types';

import { UpdateTicketAssigneeSchema } from '../_lib/schema/update-ticket-assignee.schema';
import { updateTicketAssigneeAction } from '../_lib/server/actions/update-ticket-assignee-action';

export function AssigneeSelect(props: {
  assignee: Ticket['assignedToUserId'];
  ticketId: string;
  communityId: string;
  disabled: boolean;
}) {
  const [isPending, startTransition] = useTransition();

  const form = useForm({
    resolver: zodResolver(UpdateTicketAssigneeSchema),
    defaultValues: {
      assigneeId: props.assignee!,
      ticketId: props.ticketId,
    },
  });

  const membersQuery = useFetchTeamMembers(props.communityId);
  const members = membersQuery.data ?? [];

  return (
    <Form {...form}>
      <FormField
        render={({ field }) => {
          return (
            <FormItem>
              <FormLabel>
                <Trans i18nKey={'common:table.assignee'} />
              </FormLabel>

              <FormControl>
                <Select
                  value={form.getValues('assigneeId')}
                  disabled={
                    isPending || props.disabled || membersQuery.isPending
                  }
                  onValueChange={(value) => {
                    form.setValue(field.name, value, {
                      shouldValidate: true,
                    });

                    void form.handleSubmit(
                      (value: z.infer<typeof UpdateTicketAssigneeSchema>) => {
                        startTransition(async () => {
                          await updateTicketAssigneeAction(value);
                        });
                      },
                    )();
                  }}
                >
                  <SelectTrigger
                    data-test="assignee-select-trigger"
                    aria-label="Select assignee"
                  >
                    <SelectValue
                      placeholder={
                        <Trans
                          i18nKey={'tickets:ticketDetails.chooseAssignee'}
                        />
                      }
                    />
                  </SelectTrigger>

                  <SelectContent>
                    {members.map((member) => {
                      return (
                        <SelectItem
                          key={member.user_id}
                          value={member.user_id}
                          data-test={`assignee-option-${member.user_id}`}
                          aria-label={`${member.first_name} ${member.last_name}`}
                        >
                          {member.first_name} {member.last_name}
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </FormControl>

              <FormDescription>
                <Trans i18nKey={'tickets:ticketDetails.assigneeDescription'} />
              </FormDescription>
            </FormItem>
          );
        }}
        name={'assigneeId'}
      />
    </Form>
  );
}
