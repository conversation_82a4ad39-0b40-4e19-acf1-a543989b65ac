'use client';

import { useTransition } from 'react';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { Button } from '@kit/ui/button';
import { Input } from '@kit/ui/input';
import { Trans } from '@kit/ui/trans';

import type { TicketMessages } from '~/lib/communities/tickets/types';

import { MessageFormSchema } from '../_lib/schema/message-form.schema';
import { insertTicketMessageAction } from '../_lib/server/actions/insert-ticket-message-action';

export function SendMessageInput({
  onMessageSent,
  ticketId,
}: {
  ticketId: string;
  onMessageSent: (message: TicketMessages) => void;
}) {
  const { t } = useTranslation();
  const [isPending, startTransition] = useTransition();

  const form = useForm({
    resolver: zodResolver(MessageFormSchema),
    defaultValues: {
      message: '',
      ticketId,
    },
  });

  return (
    <form
      className={'sticky bottom-8 z-10 mt-auto'}
      onSubmit={form.handleSubmit((data) => {
        startTransition(async () => {
          const message = await insertTicketMessageAction(data);

          onMessageSent(message);

          form.reset();
        });
      })}
      data-test="send-message-form"
    >
      <Input
        {...form.register('message')}
        disabled={isPending}
        placeholder={t('tickets:messages.sendMessagePlaceholder')}
        type="text"
        className={'bg-background h-16 border pr-36 focus:shadow-xl'}
        data-test="message-input"
        aria-label="Type your message"
      />

      <Button
        disabled={isPending}
        className={'absolute top-3.5 right-4'}
        data-test="send-message-button"
        aria-label="Send message"
      >
        <Trans i18nKey={'tickets:messages.sendMessageButton'} />
      </Button>
    </form>
  );
}
