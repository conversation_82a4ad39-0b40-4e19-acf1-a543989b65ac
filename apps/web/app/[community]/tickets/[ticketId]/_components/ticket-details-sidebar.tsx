'use client';

import { Heading } from '@kit/ui/heading';
import { Trans } from '@kit/ui/trans';

import { useCommunityWorkspace } from '~/lib/communities/community/hooks/use-community-workspace';
import type { PermissionsEnum } from '~/lib/communities/members/types';
import { hasCommunityPermission } from '~/lib/communities/permissions.utils';
import { Ticket } from '~/lib/communities/tickets/types';

// Import the new individual components
import { AssigneeSelect } from './assignee-select';
import { PrioritySelect } from './priority-select';
import { StatusSelect } from './status-select';

export function TicketDetailsSidebar({
  ticket,
}: {
  ticket: Ticket & {
    communityId: {
      id: string;
      slug: string;
    };
  };
}) {
  const { community } = useCommunityWorkspace();
  const permissions = (community.permissions ?? []) as PermissionsEnum[];

  const canUpdateTicket = hasCommunityPermission(
    permissions,
    'community.tickets.update',
  );

  return (
    <div
      className={'flex h-screen flex-1 flex-col space-y-8'}
      data-test="ticket-details-sidebar"
    >
      <div>
        <Heading level={4}>
          <Trans i18nKey={'tickets:ticketDetails.pageTitle'} />
        </Heading>

        <Heading level={6} className={'text-muted-foreground'}>
          <Trans i18nKey={'tickets:ticketDetails.pageDescription'} />
        </Heading>
      </div>

      <div className={'flex flex-1 flex-col space-y-4'}>
        {/* Use the imported components */}
        <StatusSelect
          disabled={!canUpdateTicket}
          status={ticket.status}
          ticketId={ticket.id}
        />

        <AssigneeSelect
          disabled={!canUpdateTicket}
          assignee={ticket.assignedToUserId}
          ticketId={ticket.id}
          communitySlug={ticket.communityId.slug}
        />

        <PrioritySelect
          disabled={!canUpdateTicket}
          priority={ticket.priority}
          ticketId={ticket.id}
        />
      </div>
    </div>
  );
}
