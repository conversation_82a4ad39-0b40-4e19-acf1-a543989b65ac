'use client';

import { useEffect, useRef, useState } from 'react';

import { Alert, AlertTitle } from '@kit/ui/alert';
import { LoadingOverlay } from '@kit/ui/loading-overlay';
import { Trans } from '@kit/ui/trans';

import { useAppendNewMessage } from '../_lib/hooks/use-append-new-message';
import { useFetchTicketMessages } from '../_lib/hooks/use-fetch-ticket-messages';
import { SendMessageInput } from './send-message-input';
import { TicketMessage } from './ticket-message';

export function TicketMessagesContainer(props: {
  ticketId: string;
  page: number;
}) {
  const [state] = useState<{
    page: number;
  }>({
    page: props.page,
  });

  const scrollingDiv = useRef<HTMLDivElement | null>(null);
  const queryKey = ['ticket-messages', props.ticketId, props.page.toString()];
  const appendMessage = useAppendNewMessage({ queryKey });

  const { status, data } = useFetchTicketMessages({
    ticketId: props.ticketId,
    page: state.page,
    queryKey,
  });

  useEffect(() => {
    if (scrollingDiv.current) {
      scrollingDiv.current.scrollTo({
        top: scrollingDiv.current.scrollHeight,
      });
    }
  }, [data]);

  if (status === 'pending') {
    return (
      <LoadingOverlay fullPage={false}>
        <Trans i18nKey={'tickets:messages.loadingMessages'} />
      </LoadingOverlay>
    );
  }

  if (status === 'error') {
    return (
      <Alert variant={'destructive'}>
        <AlertTitle>
          <Trans i18nKey={'tickets:messages.errorLoadingMessages'} />
        </AlertTitle>
      </Alert>
    );
  }

  return (
    <div className={'relative flex w-full flex-1 flex-col'}>
      <div
        ref={(ref) => {
          scrollingDiv.current = ref;
        }}
        className={
          'absolute flex h-full w-full flex-col gap-4 overflow-y-auto pb-24'
        }
        data-test="ticket-messages-scrolling-container"
        role="region"
        aria-label="Ticket messages"
      >
        {data.pages.map((page) => {
          return page.map((message) => (
            <TicketMessage key={message.id} message={message} />
          ));
        })}
      </div>

      <SendMessageInput
        ticketId={props.ticketId}
        onMessageSent={appendMessage}
      />
    </div>
  );
}
