'use client';

import { Trans } from '@kit/ui/trans';
import { cn } from '@kit/ui/utils';

import type { TicketMessages } from '~/lib/communities/tickets/types';

// Move the Message type definition here
type Message = TicketMessages & {
  user: {
    email: string;
    first_name: string;
    last_name: string;
    picture_url: string;
  };
};

export function TicketMessage(props: { message: Message }) {
  const author = props.message.author;
  const content = props.message.content;
  const user = props.message.user;

  const alignClassname = cn('flex w-full lg:w-6/12', {
    'justify-end self-end': author === 'support',
    'self-start': author === 'customer',
  });

  const className = cn('flex w-full gap-4 rounded-lg border p-2.5', {
    'text-primary-900 bg-primary/5 dark:bg-primary/90': author === 'support',
  });

  const authorName =
    author === 'customer' ? (
      <Trans i18nKey={'tickets:messages.customer'} />
    ) : user?.first_name && user?.last_name ? (
      `${user.first_name} ${user.last_name}`
    ) : (
      <Trans i18nKey={'tickets:messages.support'} />
    );

  const date = new Date(props.message.createdAt);

  return (
    <div
      className={alignClassname}
      data-test={`ticket-message-${props.message.id}`}
    >
      <div className={'flex w-auto max-w-full flex-col gap-2'}>
        <div className={'flex flex-col'}>
          <div className={'text-sm font-medium capitalize'}>{authorName}</div>

          <div>
            <span className={'text-muted-foreground text-xs'}>
              <Trans i18nKey={'tickets:messages.sentOn'} />{' '}
              {date.toLocaleString('en-US')}
            </span>
          </div>
        </div>

        <div className={className} data-test="ticket-message-content-bubble">
          <p className={'inline-block text-sm break-words'}>{content}</p>
        </div>
      </div>
    </div>
  );
}
