'use client';

import { useTransition } from 'react';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
} from '@kit/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Trans } from '@kit/ui/trans';

import { TicketStatus } from '~/lib/communities/tickets/types';

import { UpdateTicketStatusSchema } from '../_lib/schema/update-ticket-status.schema';
import { updateTicketStatusAction } from '../_lib/server/actions/update-ticket-status-action';

export function StatusSelect(props: {
  status: TicketStatus;
  ticketId: string;
  disabled: boolean;
}) {
  const { t } = useTranslation(['common', 'tickets']);
  const form = useForm({
    resolver: zodResolver(UpdateTicketStatusSchema),
    defaultValues: {
      status: props.status,
      ticketId: props.ticketId,
    },
  });

  const [isPending, startTransition] = useTransition();

  return (
    <Form {...form}>
      <FormField
        render={({ field }) => {
          return (
            <FormItem>
              <FormLabel>
                <Trans i18nKey={'common:table.status'} />
              </FormLabel>

              <FormControl>
                <Select
                  value={form.getValues('status')}
                  disabled={isPending || props.disabled}
                  onValueChange={(value) => {
                    form.setValue(field.name, value as TicketStatus, {
                      shouldValidate: true,
                    });

                    void form.handleSubmit(
                      (value: z.infer<typeof UpdateTicketStatusSchema>) => {
                        startTransition(async () => {
                          await updateTicketStatusAction(value);
                        });
                      },
                    )();
                  }}
                >
                  <SelectTrigger
                    data-test="status-select-trigger"
                    aria-label={t('common:table.status')}
                  >
                    <SelectValue placeholder={'Status'} />
                  </SelectTrigger>

                  <SelectContent>
                    <SelectItem
                      value={'open'}
                      data-test="status-option-open"
                      aria-label={t('tickets:status.open')}
                    >
                      <Trans i18nKey={'tickets:status.open'} />
                    </SelectItem>
                    <SelectItem
                      value={'closed'}
                      data-test="status-option-closed"
                      aria-label={t('tickets:status.closed')}
                    >
                      <Trans i18nKey={'tickets:status.closed'} />
                    </SelectItem>
                    <SelectItem
                      value={'in_progress'}
                      data-test="status-option-in-progress"
                      aria-label={t('tickets:status.in-progress')}
                    >
                      <Trans i18nKey={'tickets:status.in_progress'} />
                    </SelectItem>
                    <SelectItem
                      value={'resolved'}
                      data-test="status-option-resolved"
                      aria-label={t('tickets:status.resolved')}
                    >
                      <Trans i18nKey={'tickets:status.resolved'} />
                    </SelectItem>
                  </SelectContent>
                </Select>
              </FormControl>

              <FormDescription>
                <Trans i18nKey={'tickets:status.description'} />
              </FormDescription>
            </FormItem>
          );
        }}
        name={'status'}
      />
    </Form>
  );
}
