'use server';

import { revalidatePath } from 'next/cache';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import pathsConfig from '~/config/paths.config';
import { createCommunityTicketsService } from '~/lib/communities/tickets/community-tickets.service';

import { MessageFormSchema } from '../../schema/message-form.schema';

export const insertTicketMessageAction = enhanceAction(
  async (data, user) => {
    const logger = await getLogger();
    const client = getSupabaseServerClient();
    const ctx = {
      name: 'tickets.message.insert',
      userId: user.id,
      ticketId: data.ticketId,
    };

    logger.info(ctx, 'Inserting ticket message...');

    try {
      const service = createCommunityTicketsService(client);

      const newMessage = await service.insertTicketMessage({
        content: data.message,
        ticketId: data.ticketId,
        userId: user.id,
        author: 'support', // Assuming 'support' for messages inserted via this action
      });

      logger.info(ctx, 'Ticket message inserted successfully via service');

      // Revalidate the ticket page path
      revalidatePath(`${pathsConfig.app.tickets}/${data.ticketId}`, 'page');

      return newMessage;
    } catch (error) {
      logger.error(
        { ...ctx, error },
        'Error inserting ticket message via service',
      );
      const message =
        error instanceof Error
          ? error.message
          : 'Unknown error inserting message';
      // Throwing error to be caught by enhanceAction/client
      throw new Error(message);
    }
  },
  {
    auth: true,
    schema: MessageFormSchema,
  },
);
