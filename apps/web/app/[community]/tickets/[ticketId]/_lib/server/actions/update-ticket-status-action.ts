'use server';

import { revalidatePath } from 'next/cache';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import pathsConfig from '~/config/paths.config';
import { createCommunityTicketsService } from '~/lib/communities/tickets/community-tickets.service';

import { UpdateTicketStatusSchema } from '../../schema/update-ticket-status.schema';

export const updateTicketStatusAction = enhanceAction(
  async (data, user) => {
    const logger = await getLogger();
    const client = getSupabaseServerClient();
    const ctx = {
      name: 'tickets.status.update',
      userId: user.id,
      ticketId: data.ticketId,
      status: data.status,
    };

    logger.info(ctx, 'Updating ticket status...');

    try {
      const service = createCommunityTicketsService(client);

      await service.updateTicketStatus(data);

      logger.info(ctx, 'Ticket status updated successfully via service');

      // Revalidate path
      revalidatePath(`${pathsConfig.app.tickets}/${data.ticketId}`, 'page');

      return { success: true };
    } catch (error) {
      logger.error(
        { ...ctx, error },
        'Error updating ticket status via service',
      );
      const message =
        error instanceof Error
          ? error.message
          : 'Unknown error updating status';
      throw new Error(message);
    }
  },
  {
    auth: true,
    schema: UpdateTicketStatusSchema,
  },
);
