'use server';

import { revalidatePath } from 'next/cache';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import pathsConfig from '~/config/paths.config';
import { createCommunityTicketsService } from '~/lib/communities/tickets/community-tickets.service';

import { UpdateTicketAssigneeSchema } from '../../schema/update-ticket-assignee.schema';

export const updateTicketAssigneeAction = enhanceAction(
  async (data, user) => {
    const client = getSupabaseServerClient();
    const logger = await getLogger();
    const ctx = {
      name: 'tickets.assignee.update',
      userId: user.id,
      ticketId: data.ticketId,
      assigneeId: data.assigneeId,
    };

    logger.info(ctx, 'Updating ticket assignee...');

    try {
      const service = createCommunityTicketsService(client);

      await service.updateTicketAssignee(data);

      logger.info(ctx, 'Ticket assignee updated successfully via service');

      // Revalidate path
      revalidatePath(`${pathsConfig.app.tickets}/${data.ticketId}`, 'page');

      return { success: true };
    } catch (error) {
      logger.error(
        { ...ctx, error },
        'Error updating ticket assignee via service',
      );
      const message =
        error instanceof Error
          ? error.message
          : 'Unknown error updating assignee';
      throw new Error(message);
    }
  },
  {
    auth: true,
    schema: UpdateTicketAssigneeSchema,
  },
);
