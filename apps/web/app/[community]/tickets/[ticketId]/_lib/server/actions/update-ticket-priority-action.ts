'use server';

import { revalidatePath } from 'next/cache';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import pathsConfig from '~/config/paths.config';
import { createCommunityTicketsService } from '~/lib/communities/tickets/community-tickets.service';

import { UpdateTicketPrioritySchema } from '../../schema/update-ticket-priority.schema';

export const updateTicketPriorityAction = enhanceAction(
  async (data, user) => {
    const logger = await getLogger();
    const client = getSupabaseServerClient();
    const ctx = {
      name: 'tickets.priority.update',
      userId: user.id,
      ticketId: data.ticketId,
      priority: data.priority,
    };

    logger.info(ctx, 'Updating ticket priority...');

    try {
      const service = createCommunityTicketsService(client);

      await service.updateTicketPriority(data);

      logger.info(ctx, 'Ticket priority updated successfully via service');

      // Revalidate path
      revalidatePath(`${pathsConfig.app.tickets}/${data.ticketId}`, 'page');

      return { success: true };
    } catch (error) {
      logger.error(
        { ...ctx, error },
        'Error updating ticket priority via service',
      );
      const message =
        error instanceof Error
          ? error.message
          : 'Unknown error updating priority';
      throw new Error(message);
    }
  },
  {
    auth: true,
    schema: UpdateTicketPrioritySchema,
  },
);
