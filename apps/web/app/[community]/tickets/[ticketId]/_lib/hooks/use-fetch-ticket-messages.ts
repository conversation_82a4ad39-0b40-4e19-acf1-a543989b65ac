'use client';

import { useEffect } from 'react';

import { useInfiniteQuery } from '@tanstack/react-query';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';

import type { TicketMessages } from '~/lib/communities/tickets/types';

import { useAppendNewMessage } from './use-append-new-message';

type Message = TicketMessages & {
  user: {
    email: string;
    first_name: string;
    last_name: string;
    picture_url: string;
  };
};

export function useFetchTicketMessages(params: {
  ticketId: string;
  page: number;
  queryKey: string[];
}) {
  const appendMessage = useAppendNewMessage({ queryKey: params.queryKey });
  const client = useSupabase();

  const { ticketId, page } = params;
  const messagesPerPage = 25;

  const queryFn = async () => {
    const startOffset = (page - 1) * messagesPerPage;
    const endOffset = startOffset + messagesPerPage;

    const { data: messages, error } = await client
      .from('community_ticket_messages')

      .select<string, Message>(
        '*, user: created_by_user_id (email, first_name, last_name, picture_url)',
      )
      .eq('ticket_id', ticketId)
      .order('created_at', { ascending: true })
      .range(startOffset, endOffset);

    if (error) {
      throw error;
    }

    return messages;
  };

  useEffect(() => {
    const channel = client.channel(`messages-channel-${ticketId}`);

    const subscription = channel
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          filter: `ticket_id=eq.${ticketId}`,
          table: 'community_ticket_messages',
        },
        (payload) => {
          const message = payload.new as TicketMessages;

          if (message.author === 'customer') {
            appendMessage(message);
          }
        },
      )
      .subscribe();

    return () => {
      void subscription.unsubscribe();
    };
  }, [client, ticketId, appendMessage]);

  return useInfiniteQuery({
    queryKey: params.queryKey,
    queryFn,
    initialPageParam: page,
    getNextPageParam: (lastPage, _, lastPageParam) => {
      if (lastPage.length === 0) {
        return;
      }

      return lastPageParam + 1;
    },
    getPreviousPageParam: (_, __, firstPageParam) => {
      if (firstPageParam <= 1) {
        return;
      }

      return firstPageParam - 1;
    },
  });
}
