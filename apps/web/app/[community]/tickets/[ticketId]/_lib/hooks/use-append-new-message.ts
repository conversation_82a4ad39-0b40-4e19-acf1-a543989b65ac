'use client';

import { useCallback } from 'react';

import { useQueryClient } from '@tanstack/react-query';

import type { TicketMessages } from '~/lib/communities/tickets/types';

export function useAppendNewMessage(params: { queryKey: string[] }) {
  const queryClient = useQueryClient();
  const { queryKey } = params;

  return useCallback(
    (message: TicketMessages) => {
      queryClient.setQueryData(
        queryKey,
        (data: { pages: Array<TicketMessages[]> }) => {
          // append message to the last page
          const lastPage = [...data.pages[data.pages.length - 1]!, message];

          return {
            ...data,
            // replace the last page
            pages: [...data.pages.slice(0, -1), lastPage],
          };
        },
      );
    },
    [queryClient, queryKey],
  );
}
