import { use } from 'react';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { PageBody } from '@kit/ui/page';

import { createCommunityTicketsService } from '~/lib/communities/tickets/community-tickets.service';
import { createI18nServerInstance } from '~/lib/i18n/i18n.server';

import { TicketsDataTable } from './_components/tickets-data-table';

export const generateMetadata = async () => {
  const i18n = await createI18nServerInstance();
  const title = i18n.t('tickets:home.pageTitle');

  return {
    title,
  };
};

type TicketsPageProps = {
  params: Promise<{
    community: string;
  }>;

  searchParams: Promise<{
    page?: string;
    query?: string;
  }>;
};

export default function TicketsPage(props: TicketsPageProps) {
  const client = getSupabaseServerClient();
  const service = createCommunityTicketsService(client);

  const { community } = use(props.params);
  const { page: pageParam, query = '' } = use(props.searchParams);

  const page = Number(pageParam ?? '1');

  const { data, pageSize, pageCount } = use(
    service.getTickets({
      communitySlug: community,
      page,
      query,
    }),
  );

  // Map data to ensure communityId is a string as expected by the DataTable
  const tableData = data.map((ticket) => ({
    ...ticket,
    // Assuming the service returns an object for communityId and the table expects the ID string
    // Adjust '.id' if a different property (like '.slug') is needed or the structure is different.
    communityId:
      typeof ticket.communityId === 'object' && ticket.communityId !== null
        ? ticket.communityId.slug
        : ticket.communityId,
    // You might need similar transformations for other fields like assignedTo, closedBy if they also return objects
  }));

  return (
    <>
      <PageBody>
        <TicketsDataTable
          pageIndex={page - 1}
          pageCount={pageCount}
          pageSize={pageSize}
          data={tableData}
        />
      </PageBody>
    </>
  );
}
