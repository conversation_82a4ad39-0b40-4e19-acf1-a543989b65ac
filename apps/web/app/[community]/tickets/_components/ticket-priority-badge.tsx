import { Badge } from '@kit/ui/badge';
import { Trans } from '@kit/ui/trans';

import { TicketPriority } from '~/lib/communities/tickets/types';

export function TicketPriorityBadge({
  priority,
}: {
  priority: TicketPriority;
}) {
  switch (priority) {
    case 'low':
      return (
        <Badge variant={'outline'} data-test={`ticket-priority-badge-${priority}`}>
          <Trans i18nKey={'tickets:priority.low'} />
        </Badge>
      );

    case 'medium':
      return (
        <Badge variant={'warning'} data-test={`ticket-priority-badge-${priority}`}>
          <Trans i18nKey={'tickets:priority.medium'} />
        </Badge>
      );

    case 'high':
      return (
        <Badge variant={'destructive'} data-test={`ticket-priority-badge-${priority}`}>
          <Trans i18nKey={'tickets:priority.high'} />
        </Badge>
      );
  }
}
