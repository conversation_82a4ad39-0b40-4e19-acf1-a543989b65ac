'use client';

import Link from 'next/link';

import { ColumnDef } from '@tanstack/react-table';

import { Button } from '@kit/ui/button';
import { EnhancedDataTable } from '@kit/ui/enhanced-data-table';
import { Trans } from '@kit/ui/trans';

import type { Ticket } from '~/lib/communities/tickets/types';

import { TicketPriorityBadge } from './ticket-priority-badge';
import { TicketStatusBadge } from './ticket-status-badge';

export function TicketsDataTable(props: {
  data: Ticket[];
  pageSize: number;
  pageIndex: number;
  pageCount: number;
}) {
  return <EnhancedDataTable {...props} columns={getColumns()} />;
}

function getColumns(): ColumnDef<Ticket>[] {
  return [
    {
      id: 'title',
      accessorKey: 'title',
      header: () => <Trans i18nKey={'common:table.title'} />,
      cell({ row }) {
        const ticket = row.original;

        return (
          <Link
            className={'hover:underline'}
            href={`tickets/${ticket.id}`}
            data-test={`ticket-link-${ticket.id}`}
            aria-label={`View ticket ${ticket.title}`}
          >
            {ticket.title}
          </Link>
        );
      },
    },
    {
      id: 'status',
      accessorKey: 'status',
      header: () => <Trans i18nKey={'common:table.status'} />,
      cell({ row }) {
        const ticket = row.original;

        return <TicketStatusBadge status={ticket.status} />;
      },
    },
    {
      id: 'priority',
      accessorKey: 'priority',
      header: () => <Trans i18nKey={'common:table.priority'} />,
      cell({ row }) {
        const ticket = row.original;

        return <TicketPriorityBadge priority={ticket.priority} />;
      },
    },
    {
      id: 'created_at',
      accessorKey: 'created_at',
      header: () => <Trans i18nKey={'common:table.createdAt'} />,
      cell({ row }) {
        const ticket = row.original;
        const date = new Date(ticket.createdAt);

        return getDateString(date);
      },
    },
    {
      id: 'updated_at',
      accessorKey: 'updated_at',
      header: () => <Trans i18nKey={'common:table.updatedAt'} />,
      cell({ row }) {
        const ticket = row.original;
        const date = new Date(ticket.updatedAt);

        return getDateString(date);
      },
    },
    {
      id: 'actions',
      cell({ row }) {
        return (
          <div className={'flex justify-end'}>
            <Button
              asChild
              variant={'outline'}
              data-test={`view-ticket-button-${row.original.id}`}
            >
              <Link
                href={`tickets/${row.original.id}`}
                aria-label="View issue details"
              >
                <Trans i18nKey={'tickets:actions.view_issue'} />
              </Link>
            </Button>
          </div>
        );
      },
    },
  ];
}

function getDateString(date: Date) {
  const day = date.toLocaleDateString();
  const time = date.toLocaleTimeString();

  return `${day} at ${time}`;
}
