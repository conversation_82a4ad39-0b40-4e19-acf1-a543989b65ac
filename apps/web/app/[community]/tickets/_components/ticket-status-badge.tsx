import { Badge } from '@kit/ui/badge';
import { Trans } from '@kit/ui/trans';

import { TicketStatus } from '~/lib/communities/tickets/types';

export function TicketStatusBadge({ status }: { status: TicketStatus }) {
  switch (status) {
    case 'open':
      return (
        <Badge variant={'warning'} data-test={`ticket-status-badge-${status}`}>
          <Trans i18nKey={'tickets:status.open'} />
        </Badge>
      );

    case 'closed':
      return (
        <Badge
          variant={'secondary'}
          data-test={`ticket-status-badge-${status}`}
        >
          <Trans i18nKey={'tickets:status.closed'} />
        </Badge>
      );

    case 'resolved':
      return (
        <Badge variant={'success'} data-test={`ticket-status-badge-${status}`}>
          <Trans i18nKey={'tickets:status.resolved'} />
        </Badge>
      );

    case 'in_progress':
      return (
        <Badge variant={'info'} data-test={`ticket-status-badge-${status}`}>
          <Trans i18nKey={'tickets:status.in-progress'} />
        </Badge>
      );
  }
}
