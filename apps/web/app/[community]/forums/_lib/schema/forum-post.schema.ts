import { object, string, z } from 'zod';

import type { JSONContent } from '@kit/ui/dojo/organisms/tiptap-editor';

import { validateTipTapContent } from '~/lib/utils/validate-tiptap-content';

// Define the post status enum to match database
export const ForumPostStatus = z.enum(['draft', 'published']);
export type ForumPostStatus = z.infer<typeof ForumPostStatus>;

// Base schema for forum posts
export const ForumPostSchema = object({
  title: string().min(1).max(255),
  content: z.custom<JSONContent>(validateTipTapContent, 'Content is required'),
  contentDraft: z.custom<JSONContent>(validateTipTapContent).nullable(),
  status: ForumPostStatus.default('draft'),
  lastEditedAt: z.date().nullable(),
  autoSavedAt: z.date().nullable(),
});

export const ForumPostUpdateSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  content: z.custom<JSONContent>(validateTipTapContent, 'Content is required'),
  contentDraft: z.custom<JSONContent>(validateTipTapContent).nullable(),
  status: ForumPostStatus,
});

// Schema for form submissions
export const ForumPostFormSchema = ForumPostSchema.merge(
  object({
    communityId: string().uuid(),
    categoryId: string().uuid(),
  }),
);

// Schema for creating new posts
export const CreateForumPostSchema = z.object({
  communityId: z.string(),
  status: ForumPostStatus.default('draft'),
  title: z.string().min(1, 'Title is required'),
  content: z.custom<JSONContent>(validateTipTapContent, 'Content is required'),
  categoryId: z.string().min(1, 'Category is required'),
});

// Schema for creating new draft posts
export const CreateDraftForumPostSchema = z.object({
  status: ForumPostStatus.default('draft'),
  draftTitle: z.string().min(1, 'Title is required'),
  draftContent: z.custom<JSONContent>(
    validateTipTapContent,
    'Content is required',
  ),
  draftCharacterCount: z.number().min(0),
  draftWordCount: z.number().min(0),
  draftCategoryId: z.string().min(1, 'Category is required'),
});

export type CreateDraftForumPost = z.infer<typeof CreateDraftForumPostSchema>;

// Schema for auto-saving drafts
export const AutoSaveForumPostSchema = z.object({
  content: z.custom<JSONContent>(validateTipTapContent, 'Content is required'),
  postId: z.string().uuid(),
});

// Schema for updating draft content
export const UpdateDraftContentSchema = z.object({
  postId: z.string().uuid(),
  content: z.custom<JSONContent>(validateTipTapContent, 'Content is required'),
  contentDraft: z.custom<JSONContent>(validateTipTapContent).nullable(),
  title: z.string().min(1, 'Title is required').optional(),
  categoryId: z.string().uuid().optional(),
});

// Schema for publishing drafts
export const PublishForumPostSchema = z.object({
  postId: z.string().uuid(),
});

// Schema for discarding drafts
export const DiscardDraftSchema = z.object({
  postId: z.string().uuid(),
});

// Type exports
export type CreateForumPost = z.infer<typeof CreateForumPostSchema>;
export type AutoSaveForumPost = z.infer<typeof AutoSaveForumPostSchema>;
export type UpdateDraftContent = z.infer<typeof UpdateDraftContentSchema>;
export type PublishForumPost = z.infer<typeof PublishForumPostSchema>;
export type DiscardDraft = z.infer<typeof DiscardDraftSchema>;
export type ForumPost = z.infer<typeof ForumPostSchema>;

// Response types
export type ForumPostResponse = ForumPost & {
  id: string;
  authorAccountId: string;
  categoryName: string;
  commentCount: number;
  isPinned: boolean;
  createdAt: Date;
  updatedAt: Date;
};
