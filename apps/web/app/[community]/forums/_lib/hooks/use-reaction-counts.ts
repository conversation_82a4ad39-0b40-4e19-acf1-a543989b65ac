'use client';

import { useQuery } from '@tanstack/react-query';

import { getReactionCountsAction } from '../server/actions/get-reaction-counts';

type UseReactionCountsProps = {
  postId?: string;
  commentId?: string;
};

export function useReactionCounts({
  postId,
  commentId,
}: UseReactionCountsProps) {
  const queryKey = postId
    ? ['reaction-counts', postId]
    : ['reaction-counts', commentId];

  const { data, ...rest } = useQuery({
    queryKey,
    queryFn: async () => {
      const result = await getReactionCountsAction({
        postId: postId ?? undefined,
        commentId: commentId ?? undefined,
      });

      if (result.error || !result.success || !result.data) {
        console.error('Error fetching reaction counts:', result.error);
        return {};
      }

      return result.data;
    },
  });

  return { data, ...rest };
}
