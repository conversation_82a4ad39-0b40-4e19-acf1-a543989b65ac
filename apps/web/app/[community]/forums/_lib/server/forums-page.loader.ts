import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createCommunityForumsService } from '~/lib/communities/forums/community-forums.service';
import type { Database } from '~/lib/database.types';

/**
 * Load data specific to the forums page (categories and posts).
 * Workspace context (auth, permissions) is loaded separately.
 *
 * @param communityId The UUID of the community.
 * @param page The page number for posts pagination.
 * @param limit Number of posts per page.
 * @param categoryId Optional category ID to filter posts.
 * @param status Optional status to filter posts ('draft' | 'published').
 */
export async function loadForumsPageData(
  communityId: string,
  page = 1,
  limit = 10,
  categoryId?: string,
  status?: 'draft' | 'published',
) {
  const logger = await getLogger();
  const client = getSupabaseServerClient();

  const ctx = {
    name: 'forums.page.load',
    communityId,
    page,
    limit,
    categoryId,
    status,
  };

  logger.info(ctx, 'Loading forums page specific data...');

  try {
    // Load categories and posts in parallel
    const [categoriesResult, postsResult] = await Promise.all([
      loadForumCategories(client, communityId),
      loadForumPosts(client, communityId, page, limit, categoryId, status),
    ]);

    logger.info(ctx, 'Loaded forums page specific data successfully', {
      categoryCount: categoriesResult.data.length,
      postCount: postsResult.data.length,
    });

    return {
      forumPosts: postsResult,
      forumCategories: categoriesResult.data,
    };
  } catch (error) {
    logger.error({ ...ctx, error }, 'Failed to load forums page specific data');
    // Return empty data structure on error to allow the page to render gracefully
    return {
      forumPosts: {
        data: [],
        count: 0,
        pageCount: 0,
        pageNumber: 1,
        pageSize: limit,
      },
      forumCategories: [],
    };
  }
}

/**
 * Load forum categories
 */
async function loadForumCategories(
  client: SupabaseClient<Database>,
  communityId: string,
) {
  const logger = await getLogger();
  const ctx = {
    name: 'forums.categories.load',
    communityId,
  };
  const service = createCommunityForumsService(client);

  try {
    const { data } = await service.getForumCategories({
      communityId: communityId,
    });
    return {
      data: data ?? [],
    };
  } catch (error) {
    logger.error({ ...ctx, error }, 'Failed to load forum categories');
    return {
      data: [],
    };
  }
}

/**
 * Load forum posts
 */
async function loadForumPosts(
  client: SupabaseClient<Database>,
  communityId: string,
  page: number,
  limit: number,
  categoryId?: string,
  status?: 'draft' | 'published',
) {
  const logger = await getLogger();
  const ctx = {
    name: 'forums.posts.load',
    communityId,
    page,
    limit,
    categoryId,
    status,
  };
  const service = createCommunityForumsService(client);
  try {
    const response = await service.getPaginatedForumPosts({
      communityId,
      page,
      limit,
      categoryId,
      status,
    });
    return {
      data: response.data ?? [],
      count: response.count ?? 0,
      pageCount: response.pageCount ?? 0,
      pageNumber: response.page ?? 1,
      pageSize: response.pageSize ?? limit,
    };
  } catch (error) {
    logger.error({ ...ctx, error }, 'Failed to load forum posts');
    return {
      data: [],
      count: 0,
      pageCount: 0,
      pageNumber: 1,
      pageSize: limit,
    };
  }
}
