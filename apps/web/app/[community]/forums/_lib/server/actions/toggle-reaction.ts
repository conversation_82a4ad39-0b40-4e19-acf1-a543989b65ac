'use server';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createCommunityForumsService } from '~/lib/communities/forums/community-forums.service';
import { ForumReactionType } from '~/lib/communities/forums/types';

type ToggleReactionParams = {
  postId: string | null;
  commentId: string | null;
  reactionType: ForumReactionType;
};

export const toggleReactionAction = enhanceAction(
  async ({ postId, commentId, reactionType }: ToggleReactionParams, user) => {
    const client = getSupabaseServerClient();
    const logger = await getLogger();

    const ctx = {
      name: 'forums_reaction.toggle',
      userId: user.id,
      postId,
      commentId,
      reactionType,
    };

    logger.info(ctx, `Toggling reaction...`);

    try {
      const service = createCommunityForumsService(client);

      const data = await service.toggleReaction({
        postId,
        commentId,
        reactionType,
      });

      logger.info(ctx, `Reaction toggled successfully via service`);

      return {
        success: true,
        data: {
          added: data,
          postId,
          commentId,
          reactionType,
        },
      };
    } catch (error) {
      logger.error({ ...ctx, error }, `Failed to toggle reaction`);
      const message = error instanceof Error ? error.message : 'Unknown error';
      return {
        error: true,
        message: message,
      };
    }
  },
  {
    auth: true,
  },
);
