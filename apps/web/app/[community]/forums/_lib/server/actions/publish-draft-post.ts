'use server';

import { revalidatePath } from 'next/cache';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import pathsConfig from '~/config/paths.config';
import { createCommunityForumsService } from '~/lib/communities/forums/community-forums.service';

/**
 * Schema for publishing a draft post
 */
const PublishDraftPostSchema = z.object({
  postId: z.string().uuid(),
});

/**
 * Server action for publishing a draft forum post
 */
export const publishDraftPostAction = enhanceAction(
  async function (data, user) {
    const logger = await getLogger();
    const ctx = {
      name: 'publish-draft-post-action',
      userId: user.id,
      postId: data.postId,
    };

    logger.info(ctx, 'Publishing draft post...');

    try {
      const client = getSupabaseServerClient();
      const forumsService = createCommunityForumsService(client);

      const publishedPost = await forumsService.publishDraftPost({
        postId: data.postId,
      });

      logger.info(ctx, 'Successfully published draft post');
      revalidatePath(`${pathsConfig.app.forums}`, 'page');

      return {
        success: true,
        data: {
          post: publishedPost,
        },
      };
    } catch (error) {
      logger.error(
        {
          ...ctx,
          error,
        },
        'Failed to publish draft post',
      );

      return {
        success: false,
        error: 'Failed to publish draft post. Please try again.',
      };
    }
  },
  {
    auth: true,
    schema: PublishDraftPostSchema,
  },
);
