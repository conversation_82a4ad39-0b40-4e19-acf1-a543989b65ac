'use server';

import { revalidatePath } from 'next/cache';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { CreateDraftForumPostSchema } from '~/[community]/forums/_lib/schema/forum-post.schema';
import pathsConfig from '~/config/paths.config';
import { createCommunityForumsService } from '~/lib/communities/forums/community-forums.service';

/**
 * Server action for creating a draft forum post
 * Returns the created draft post ID for immediate use
 */
export const createDraftPostAction = enhanceAction(
  async function (data, user) {
    const logger = await getLogger();
    const ctx = {
      name: 'create-draft-post-action',
      userId: user.id,
    };

    logger.info(ctx, 'Creating draft post...');

    try {
      const client = getSupabaseServerClient();
      const forumsService = createCommunityForumsService(client);

      const post = await forumsService.createDraftPost({
        draftTitle: data.draftTitle,
        draftContent: data.draftContent,
        draftCategoryId: data.draftCategoryId,
        draftCharacterCount: data.draftCharacterCount,
        draftWordCount: data.draftWordCount,
        status: 'draft',
      });

      logger.info(
        { ...ctx, postId: post.id },
        'Successfully created draft post',
      );

      revalidatePath(`${pathsConfig.app.forums}`, 'page');

      return {
        success: true,
        data: {
          postId: post.id,
        },
      };
    } catch (error) {
      logger.error(
        {
          ...ctx,
          error,
        },
        'Failed to create draft post',
      );

      return {
        success: false,
        error: 'Failed to create draft post. Please try again.',
      };
    }
  },
  {
    auth: true,
    schema: CreateDraftForumPostSchema,
  },
);
