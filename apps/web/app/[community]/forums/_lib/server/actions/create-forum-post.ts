'use server';

import { revalidatePath } from 'next/cache';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import type { JSONContent } from '@kit/ui/dojo/organisms/tiptap-editor';

import pathsConfig from '~/config/paths.config';
import { createCommunityForumsService } from '~/lib/communities/forums/community-forums.service';

// TODO: Add zod validation

export const createForumPostAction = enhanceAction(
  async (
    params: {
      title: string;
      content: string; // Content is stringified JSON
      categoryId: string;
    },
    user, // Add user parameter
  ): Promise<{
    error?: boolean;
    success?: boolean;
    message?: string;
    data?: {
      id: string;
      title: string;
      content: JSONContent;
      categoryId: string;
    };
  }> => {
    const client = getSupabaseServerClient();
    const logger = await getLogger();

    const { title, content, categoryId } = params;

    const ctx = {
      name: 'forums_post.create',
      userId: user.id, // Add user ID
    };

    logger.info(ctx, `Creating forum post...`);

    try {
      // Parse content before passing to service
      const parsedContent = JSON.parse(content) as JSONContent;

      const service = createCommunityForumsService(client);

      const newPostId = await service.createForumPost({
        title,
        content: parsedContent,
        categoryId,
      });

      logger.info(ctx, `Forum post created via service`);
      revalidatePath(`${pathsConfig.app.forums}`, 'page');

      return {
        success: true,
        data: {
          id: newPostId,
          title,
          content: parsedContent, // Return parsed content (already JSONContent)
          categoryId,
        },
      };
    } catch (error) {
      logger.error({ ...ctx, error }, `Failed to create forum post`);
      const message = error instanceof Error ? error.message : 'Unknown error';
      return {
        error: true,
        message: message,
      };
    }
  },
  {
    auth: true,
  },
);
