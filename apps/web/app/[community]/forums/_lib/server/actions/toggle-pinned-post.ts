'use server';

import { revalidatePath } from 'next/cache';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import pathsConfig from '~/config/paths.config';
import { createCommunityForumsService } from '~/lib/communities/forums/community-forums.service';

type TogglePinnedPostParams = {
  postId: string;
};

export const togglePinnedPostAction = enhanceAction(
  async ({ postId }: TogglePinnedPostParams, user) => {
    const client = getSupabaseServerClient();
    const logger = await getLogger();

    const ctx = {
      name: 'forums_post.toggle_pinned',
      userId: user.id, // Add user ID
      postId,
    };

    logger.info(ctx, `Toggling pinned post...`);

    try {
      const service = createCommunityForumsService(client);

      const data = await service.togglePinnedPost({ postId });

      logger.info(ctx, `Pinned post toggled successfully via service`);
      revalidatePath(`${pathsConfig.app.forums}`, 'page');

      return {
        success: true,
        data: {
          added: data, // boolean indicating if post was pinned (true) or unpinned (false)
          postId,
        },
      };
    } catch (error) {
      logger.error({ ...ctx, error }, `Failed to toggle pinned post`);
      const message = error instanceof Error ? error.message : 'Unknown error';
      return {
        success: false,
        error: message,
      };
    }
  },
  {
    auth: true,
  },
);
