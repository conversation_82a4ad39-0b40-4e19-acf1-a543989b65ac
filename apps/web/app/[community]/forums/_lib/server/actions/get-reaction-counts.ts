'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createCommunityForumsService } from '~/lib/communities/forums/community-forums.service';

// Define Zod schema for input validation
const GetReactionCountsSchema = z.object({
  postId: z.string().uuid().optional(),
  commentId: z.string().uuid().optional(),
});

// Define the expected return type (matches the map returned by the service)
type ReactionCountsMap = Record<
  string,
  { count: number; user_reacted: boolean }
>;

/**
 * Server action for getting reaction counts for a post or comment
 */
export const getReactionCountsAction = enhanceAction(
  async function (data: z.infer<typeof GetReactionCountsSchema>): Promise<{
    success: boolean;
    data?: ReactionCountsMap;
    error?: string;
  }> {
    const logger = await getLogger();
    const ctx = {
      name: 'get-reaction-counts-action',
      postId: data.postId,
      commentId: data.commentId,
    };

    logger.info(ctx, 'Fetching reaction counts via action...');

    // Basic validation: ensure at least one ID is provided
    if (!data.postId && !data.commentId) {
      logger.warn(ctx, 'Either postId or commentId must be provided');
      return {
        success: false,
        error: 'Either postId or commentId must be provided',
      };
    }

    try {
      const client = getSupabaseServerClient();
      const forumsService = createCommunityForumsService(client);

      const reactionCounts = await forumsService.getReactionCounts({
        postId: data.postId,
        commentId: data.commentId,
      });

      logger.info(ctx, 'Successfully fetched reaction counts via action');

      return {
        success: true,
        data: reactionCounts,
      };
    } catch (error) {
      logger.error(
        { ...ctx, error },
        'Failed to fetch reaction counts via action',
      );
      const message =
        error instanceof Error
          ? error.message
          : 'Unknown error fetching counts';
      return {
        success: false,
        error: message,
      };
    }
  },
  {
    auth: false,
    schema: GetReactionCountsSchema,
  },
);
