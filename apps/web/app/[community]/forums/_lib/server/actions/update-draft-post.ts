'use server';

import { revalidatePath } from 'next/cache';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import type { JSONContent } from '@kit/ui/dojo/organisms/tiptap-editor';

import pathsConfig from '~/config/paths.config';
import { createCommunityForumsService } from '~/lib/communities/forums/community-forums.service';
import { transformTiptapContent } from '~/lib/utils/transform-tiptap-content';

/**
 * Schema for updating a draft post
 * Handles both content updates and status changes (draft to published)
 */
const UpdateDraftPostSchema = z.object({
  postId: z.string().uuid(),
  draftContent: z.custom<JSONContent>(),
  draftTitle: z.string().min(1).optional(),
  draftCategoryId: z.string().uuid().optional(),
  draftCharacterCount: z.number().min(0).optional(),
  draftWordCount: z.number().min(0).optional(),
  status: z.enum(['draft', 'published']).optional(),
});

/**
 * Server action for updating a draft forum post
 * Supports both manual updates and auto-save functionality
 * Very important to parse the content before passing to the server action
 * or NextJS removes extra attrs from the content
 * example: transformTiptapContent(data.content)
 */
export const updateDraftPostAction = enhanceAction(
  async function (data) {
    const logger = await getLogger();
    const ctx = {
      name: 'update-draft-post-action',
      postId: data.postId,
    };

    logger.info(ctx, 'Starting draft post update...');

    try {
      const client = getSupabaseServerClient();
      const forumsService = createCommunityForumsService(client);

      // If status is being updated to published, use the publish method
      if (data.status === 'published') {
        logger.info(ctx, 'Publishing draft post...');

        const publishedPost = await forumsService.publishDraftPost({
          postId: data.postId,
        });

        logger.info(
          { ...ctx, status: 'published' },
          'Successfully published draft post',
        );

        // Revalidate the forums page
        revalidatePath(`${pathsConfig.app.forums}`, 'page');

        return {
          success: true,
          data: {
            post: publishedPost,
          },
        };
      }

      // Otherwise, update the draft content (auto-save)

      const updatedPost = await forumsService.updateDraftPost({
        postId: data.postId,
        draftContent: transformTiptapContent(data.draftContent),
        draftTitle: data.draftTitle,
        draftCategoryId: data.draftCategoryId,
        draftCharacterCount: data.draftCharacterCount,
        draftWordCount: data.draftWordCount,
      });

      logger.info(ctx, 'Successfully updated draft post');

      // Revalidate the forums page
      revalidatePath(`${pathsConfig.app.forums}`, 'page');

      return {
        success: true,
        data: {
          post: updatedPost,
        },
      };
    } catch (error) {
      logger.error(
        {
          ...ctx,
          error,
        },
        'Failed to update draft post',
      );

      return {
        success: false,
        error: 'Failed to update draft post. Please try again.',
      };
    }
  },
  {
    auth: true,
    schema: UpdateDraftPostSchema,
  },
);
