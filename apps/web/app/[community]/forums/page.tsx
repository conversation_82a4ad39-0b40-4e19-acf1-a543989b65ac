import Link from 'next/link';

import { <PERSON><PERSON><PERSON>, Building2, LogIn, Users } from 'lucide-react';

import { But<PERSON> } from '@kit/ui/button';
import { JoinCommunityCTA } from '@kit/ui/dojo/organisms/join-community-cta';
import { PageBody } from '@kit/ui/page';

import pathsConfig from '~/config/paths.config';
import { loadCommunityWorkspace } from '~/lib/communities/community-workspace.loader';
import type { PermissionsEnum } from '~/lib/communities/members/types';
import { hasCommunityPermission } from '~/lib/communities/permissions.utils';
import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { withI18n } from '~/lib/i18n/with-i18n';

import { CommunityNotFound } from '../_components/community-not-found';
import { ForumCreatePost } from './_components/forum-create-post';
import { ForumHeader } from './_components/forum-header';
import { ForumPostsContainer } from './_components/forum-posts-container';
import { loadForumsPageData } from './_lib/server/forums-page.loader';

export const generateMetadata = async () => {
  const i18n = await createI18nServerInstance();
  const title = i18n.t('forums:home.pageTitle');

  return {
    title,
  };
};

type ForumsPageProps = {
  params: { community: string };
  searchParams: {
    page?: string;
    category?: string;
    status?: string;
  };
};

async function ForumsPage({ params, searchParams }: ForumsPageProps) {
  const communitySlug = (await params).community;

  const awaitedSearchParams = await searchParams;

  const pageParam = awaitedSearchParams.page ?? '1';
  const categoryParam = awaitedSearchParams.category;
  const statusParam = awaitedSearchParams.status as
    | 'draft'
    | 'published'
    | undefined;
  const page = Number(pageParam);
  const limit = 10;

  const workspace = await loadCommunityWorkspace(communitySlug);

  if (!workspace || !workspace.community?.id) {
    const homePath = pathsConfig.app.home;
    return (
      <CommunityNotFound
        icon={Building2}
        title="Community not found"
        message="The community you are looking for may not exist or you may not have access."
        actions={
          <Button asChild>
            <Link href={homePath}>Return to Home</Link>
          </Button>
        }
      />
    );
  }

  const communityId = workspace.community.id;
  const user = workspace.user;
  const isMember = !!workspace.community.memberId;
  const permissions = (workspace.community.permissions ??
    []) as PermissionsEnum[];

  const publicView = !isMember;
  const effectiveStatus = publicView ? 'published' : statusParam;

  const { forumPosts, forumCategories } = await loadForumsPageData(
    communityId,
    page,
    limit,
    categoryParam,
    effectiveStatus,
  );

  const canManageForumCategories = hasCommunityPermission(
    permissions,
    'community.forums.category.settings',
  );
  if (!forumCategories || forumCategories.length === 0) {
    const communitySettingsPath = pathsConfig.app.communitySettings.replace(
      '[community]',
      communitySlug,
    );
    return (
      <CommunityNotFound
        icon={BookOpen}
        title="Forum categories not found"
        message="No forum categories have been set up for this community yet."
        actions={
          !publicView &&
          canManageForumCategories && (
            <Button asChild>
              <Link href={`${communitySettingsPath}?tab=forum-categories`}>
                Create Forum Category
              </Link>
            </Button>
          )
        }
      />
    );
  }

  const communityAboutPath = pathsConfig.app.communityAbout.replace(
    '[community]',
    communitySlug,
  );

  const communitySignInPath = `${pathsConfig.auth.signIn}?next=/${communitySlug}/forums`;

  return (
    <PageBody>
      {!isMember && !user && (
        <div className="mb-2">
          <JoinCommunityCTA
            title="forums:joinCommunityCTA.loginTitle"
            titleIcon={<LogIn className="h-5 w-5 text-white" />}
            buttonLink={communitySignInPath}
            buttonLabel="forums:joinCommunityCTA.loginButtonLabel"
            buttonIcon={<LogIn className="ml-1 h-4 w-4" />}
            descriptionText="forums:joinCommunityCTA.loginDescription"
          />
        </div>
      )}

      {!isMember && user && (
        <div className="mb-2">
          <JoinCommunityCTA
            title="forums:joinCommunityCTA.joinTitle"
            titleIcon={<Users className="h-5 w-5 text-white" />}
            buttonLink={communityAboutPath}
            buttonLabel="forums:joinCommunityCTA.joinButtonLabel"
            buttonIcon={<Users className="ml-1 h-4 w-4" />}
            descriptionText="forums:joinCommunityCTA.joinDescription"
          />
        </div>
      )}

      {!publicView && user && (
        <ForumCreatePost
          communityId={communityId}
          categories={forumCategories}
        />
      )}

      <ForumHeader categories={forumCategories} />
      <ForumPostsContainer
        communitySlug={communitySlug}
        posts={forumPosts}
        categories={forumCategories}
        status={effectiveStatus ?? 'published'}
        permissions={permissions}
        userId={user?.id ?? ''}
        isPublicView={publicView}
      />
    </PageBody>
  );
}

export default withI18n(ForumsPage);

ForumsPage.displayName = 'ForumsPage';
