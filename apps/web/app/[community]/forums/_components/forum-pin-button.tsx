'use client';

import { startTransition } from 'react';

import { useRouter } from 'next/navigation';

import { Pin } from 'lucide-react';
import { toast } from 'sonner';

import { Button } from '@kit/ui/button';
import { cn } from '@kit/ui/utils';

import { togglePinnedPostAction } from '~/community/forums/_lib/server/actions/toggle-pinned-post';

type ForumPinButtonProps = {
  postId: string;
  isPinned: boolean;
};

export function ForumPinButton({ postId, isPinned }: ForumPinButtonProps) {
  const router = useRouter();

  const handlePinClick = () => {
    startTransition(async () => {
      try {
        const result = await togglePinnedPostAction({
          postId,
        });

        if (result.success) {
          router.refresh();
          toast.success(
            isPinned
              ? 'Post unpinned successfully'
              : 'Post pinned successfully',
          );
        }
      } catch (error) {
        // Check for the specific error message
        const errorMessage = error instanceof Error ? error.message : '';
        if (errorMessage.includes('Maximum number of pinned posts')) {
          toast.error('You can only pin up to 3 posts at a time');
        } else {
          toast.error('Failed to update pin status. Please try again.');
        }
      }
    });
  };

  return (
    <Button
      key={postId}
      variant="ghost"
      onClick={() => handlePinClick()}
      type="button"
      className={cn(
        'flex items-center gap-1 rounded p-1 transition-colors',
        'hover:bg-muted',
        isPinned && 'bg-muted',
      )}
      data-test={`forum-pin-button-${postId}`}
      aria-label={isPinned ? 'Unpin post' : 'Pin post'}
    >
      <Pin size={16} className="text-muted-foreground" />
    </Button>
  );
}

ForumPinButton.displayName = 'ForumPinButton';
