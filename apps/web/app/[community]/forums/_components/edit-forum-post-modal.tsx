'use client';

import { z } from 'zod';

import { ObjectToCamel } from '@kit/shared/utils';

import { ForumCategorySchema } from '~/community/forums/_lib/schema/forum-category.schema';
import { useCommunityWorkspace } from '~/lib/communities/community/hooks/use-community-workspace';
import type { ForumPost } from '~/lib/communities/forums/types';

import { ForumPostForm } from './forum-post-form';

type EditForumPostModalProps = {
  post: ObjectToCamel<ForumPost>;
  isOpen: boolean;
  onClose: () => void;
  categories: z.infer<typeof ForumCategorySchema>[];
};

export function EditForumPostModal({
  post,
  isOpen,
  onClose,
  categories,
}: EditForumPostModalProps) {
  const { community } = useCommunityWorkspace();

  // Prepare initial data based on draft or published content
  const initialData = {
    id: post.id,
    title: post.title,
    content: JSON.stringify(post.content),
    categoryId: post.categoryId,
    status: post.status,
  };

  return (
    <ForumPostForm
      communityId={community.id}
      categories={categories}
      existingPost={post}
      onSuccess={() => onClose()}
      onCancel={onClose}
      mode="edit"
      isOpen={isOpen}
      onOpenChange={(open) => !open && onClose()}
      initialData={initialData}
    />
  );
}

EditForumPostModal.displayName = 'EditForumPostModal';
