import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@kit/ui/alert-dialog';
import { cn } from '@kit/ui/utils';

type DraftWarningDialogProps = {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  onCancel: () => void;
  draftTitle?: string;
};

export function DraftWarningDialog({
  isOpen,
  onClose,
  onConfirm,
  onCancel,
  draftTitle,
}: DraftWarningDialogProps) {
  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent data-test="draft-warning-dialog">
        <AlertDialogHeader>
          <AlertDialogTitle>Unsaved Draft Changes</AlertDialogTitle>
          <AlertDialogDescription>
            {draftTitle ? (
              <>
                Your draft &quot;{draftTitle}&quot; has unsaved changes. Are you
                sure you want to leave? Your changes will be lost.
              </>
            ) : (
              'You have unsaved changes in your draft. Are you sure you want to leave? Your changes will be lost.'
            )}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel
            onClick={onCancel}
            data-test="draft-warning-stay"
            aria-label="Stay on page and keep editing"
          >
            Stay
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={onConfirm}
            className={cn(
              'bg-destructive text-destructive-foreground hover:bg-destructive/90',
            )}
            data-test="draft-warning-leave"
            aria-label="Leave page and discard changes"
          >
            Leave
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

DraftWarningDialog.displayName = 'DraftWarningDialog';
