import { MessageCircleIcon } from 'lucide-react';
import { z } from 'zod';

import { NotFound } from '@kit/ui/dojo/molecules/not-found';

import { ForumCategorySchema } from '~/community/forums/_lib/schema/forum-category.schema';
import type { ForumPost } from '~/lib/communities/forums/types';
import type { PermissionsEnum } from '~/lib/communities/members/types';

import { ForumPostContainer } from './forum-post-container';

type ForumPostsResponse = {
  data: ForumPost[];
  count: number;
  pageCount: number;
  pageNumber: number;
  pageSize: number;
};

type ForumPostsContainerProps = {
  communitySlug: string;
  posts: ForumPostsResponse;
  categories: z.infer<typeof ForumCategorySchema>[];
  status: string;
  permissions: PermissionsEnum[];
  userId: string;
  isPublicView?: boolean;
};

export function ForumPostsContainer({
  communitySlug,
  posts,
  categories,
  status,
  permissions,
  userId,
  isPublicView = false,
}: ForumPostsContainerProps) {
  if (!posts?.data?.length) {
    return (
      <NotFound
        title={status === 'draft' ? 'No draft posts yet' : 'No forum posts yet'}
        message={
          status === 'draft'
            ? 'You have no draft posts, you can create a post by clicking "Create a new post" above'
            : 'No forum posts yet, you can create a post by clicking "Create a new post" above'
        }
        icon={MessageCircleIcon}
        data-test="no-forum-posts-found"
      />
    );
  }

  return (
    <div className="space-y-4">
      <div className="mt-4">
        {posts.data.map((post) => (
          <ForumPostContainer
            communitySlug={communitySlug}
            key={post.id}
            post={post}
            permissions={permissions}
            userId={userId}
            categories={categories}
            isPublicView={isPublicView}
          />
        ))}
      </div>
    </div>
  );
}

ForumPostsContainer.displayName = 'ForumPostsContainer';
