'use client';

import { useCallback, useEffect, useRef, useState, useTransition } from 'react';

import { useRouter } from 'next/navigation';

import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';
import { z } from 'zod';

import { getLogger } from '@kit/shared/logger';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@kit/ui/dialog';
import { CharacterCountDisplay } from '@kit/ui/dojo/molecules/character-count-display';
import { ForumCategorySelector } from '@kit/ui/dojo/molecules/forum-category-selector';
import {
  type Content,
  type Editor,
  type JSONContent,
  TipTapEditor,
  useTipTapCharacterCount,
} from '@kit/ui/dojo/organisms/tiptap-editor';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { TooltipProvider } from '@kit/ui/tooltip';

import { ForumCategorySchema } from '~/community/forums/_lib/schema/forum-category.schema';
import { deleteForumPostAction } from '~/community/forums/_lib/server/actions/delete-forum-post';
import { publishDraftPostAction } from '~/community/forums/_lib/server/actions/publish-draft-post';
import { updateDraftPostAction } from '~/community/forums/_lib/server/actions/update-draft-post';
import { AutosaveProvider } from '~/lib/autosave/context';
import { forumAutosaveStrategy } from '~/lib/autosave/handlers/forum';
import { useAutosave } from '~/lib/autosave/hooks';
import { AutosaveData } from '~/lib/autosave/types';
import type { ForumPost } from '~/lib/communities/forums/types';
import {
  createImageDeleteHandler,
  createImageUploadHandler,
} from '~/lib/images/handler/image-handlers';
import { transformTiptapContent } from '~/lib/utils/transform-tiptap-content';

import { DeletePostCommentAlert } from './delete-post-comment-alert';
import { DraftWarningDialog } from './draft-warning-dialog';

const FORUM_POST_CHARACTER_LIMIT = 5000;

const ForumPostFormSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  content: z.custom<JSONContent>((data) => {
    try {
      return typeof data === 'object' && data !== null;
    } catch {
      return false;
    }
  }, 'Invalid JSON content'),
  categoryId: z.string().uuid('Category is required'),
  status: z.enum(['draft', 'published']),
});

type FormData = z.infer<typeof ForumPostFormSchema>;

type ForumPostFormProps = {
  communityId: string;
  categories: z.infer<typeof ForumCategorySchema>[];
  existingPost?: ForumPost;
  onSuccess?: (postId: string) => void;
  onCancel?: () => void;
  mode: 'create' | 'edit';
  isOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
  onLoadingChange?: (loading: boolean) => void;
  initialData?: {
    id: string;
    title: string;
    content: string;
    categoryId: string;
    status: 'draft' | 'published';
  };
  initialDraftId?: string;
};

// TODO: Break this down into smaller components, its donig too much work in a single compoenent, can you move some to server-actions?
export const ForumPostForm = ({
  communityId,
  categories,
  existingPost,
  initialData,
  initialDraftId,
  mode,
  isOpen,
  onOpenChange,
  onLoadingChange,
  onSuccess,
  onCancel,
}: ForumPostFormProps) => {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [isDraft] = useState<boolean>(
    existingPost?.status === 'draft' || mode === 'create',
  );

  // State to store the editor instance
  const [editor, setEditor] = useState<Editor | null>(null);

  // Use the character count hook to get counts from the editor
  const { characterCount: rawCharacterCount, wordCount: rawWordCount } =
    useTipTapCharacterCount(editor);
  // Initialize to 0 when editor is null to prevent undefined→number transition causing re-renders
  const characterCount = rawCharacterCount ?? 0;
  const wordCount = rawWordCount ?? 0;

  // Add a ref to store the latest counts
  const countsRef = useRef<{ characterCount: number; wordCount: number }>({
    characterCount: 0,
    wordCount: 0,
  });

  // Update the ref whenever counts change
  useEffect(() => {
    countsRef.current = { characterCount, wordCount };
  }, [characterCount, wordCount]);

  // Use a ref for draftId to maintain stable reference
  const draftIdRef = useRef<string | null>(
    initialDraftId ?? existingPost?.id ?? null,
  );

  const draftId = draftIdRef.current;

  const [showWarning, setShowWarning] = useState(false);
  const [pendingNavigation, setPendingNavigation] = useState<string | null>(
    null,
  );
  const hasUnsavedChanges = useRef(false);
  const [isAutoSaving, setIsAutoSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const { t } = useTranslation('forums');

  // Initialize form with initial data only once
  const form = useForm<FormData>({
    resolver: zodResolver(ForumPostFormSchema),
    defaultValues: {
      title: initialData?.title ?? existingPost?.title ?? 'Untitled Post',
      content: initialData?.content
        ? typeof initialData.content === 'string'
          ? JSON.parse(initialData.content)
          : initialData.content
        : (existingPost?.content ?? { type: 'doc', content: [] }),
      categoryId:
        initialData?.categoryId ??
        existingPost?.categoryId ??
        categories[0]?.id ??
        '',
      status: initialData?.status ?? existingPost?.status ?? 'draft',
    },
  });

  // Track form content with ref to avoid re-renders
  const formContentRef = useRef<{
    title: string;
    content: JSONContent;
    categoryId: string;
  }>({
    title: initialData?.title ?? existingPost?.title ?? 'Untitled Post',
    content: initialData?.content
      ? typeof initialData.content === 'string'
        ? JSON.parse(initialData.content)
        : initialData.content
      : (existingPost?.content ?? { type: 'doc', content: [] }),
    categoryId:
      initialData?.categoryId ??
      existingPost?.categoryId ??
      categories[0]?.id ??
      '',
  });

  const { enqueue, start, stop } = useAutosave<AutosaveData>(
    {
      async saveBatch(items: AutosaveData[]) {
        setIsAutoSaving(true);
        try {
          await Promise.all(
            items.map(async (item) => {
              // Bypass validation completely - just use transform directly
              try {
                const transformed = forumAutosaveStrategy.transform(item);
                await forumAutosaveStrategy.save(transformed);
                setLastSaved(new Date());
              } catch (error) {
                toast.error('Failed to save draft', {
                  description:
                    error instanceof Error ? error.message : String(error),
                });
              }
            }),
          );
        } catch (error) {
          toast.error('Failed to save draft', {
            description: error instanceof Error ? error.message : String(error),
          });
        } finally {
          setIsAutoSaving(false);
        }
      },
    },
    {
      debounceMs: 3000,
      forceInitialSave: true,
    },
  );

  const enqueueChange = useCallback(
    (
      content: JSONContent,
      title: string,
      categoryId: string,
      charCount: number,
      wordCount: number,
    ) => {
      const timestamp = Date.now();

      if (!draftId) {
        return;
      }

      // Always transform content, even if it might be empty
      const transformedContent = transformTiptapContent(content);

      // Add timestamp to force uniqueness and ensure saves happen
      const autosaveData = {
        id: draftId,
        type: 'forumPost' as const,
        content: transformedContent,
        metadata: {
          communityId,
          title,
          categoryId,
          characterCount: charCount,
          wordCount: wordCount,
          timestamp,
        },
        lastModified: new Date(),
      };

      enqueue(autosaveData);
    },
    [draftId, communityId, enqueue],
  );

  const updateFormContent = useCallback(
    (
      update: Partial<{
        content: JSONContent;
        title: string;
        categoryId: string;
      }>,
    ) => {
      // Update ref with only the changed fields
      formContentRef.current = {
        ...formContentRef.current,
        ...update,
      };

      // Mark as having unsaved changes
      hasUnsavedChanges.current = true;

      // Trigger autosave with the new state
      enqueueChange(
        formContentRef.current.content,
        formContentRef.current.title,
        formContentRef.current.categoryId,
        characterCount,
        wordCount,
      );
    },
    [enqueueChange, characterCount, wordCount],
  );

  const handleFormChange = useCallback(
    (content: Content) => {
      // Ensure content is a valid JSONContent object
      if (!content || typeof content !== 'object') {
        return;
      }

      // Check if the content is empty or has only empty paragraphs
      const jsonContent = content as JSONContent;

      // Always activate autosave regardless of whether content is empty
      // This ensures changes are tracked even when deleting content
      start();

      // Update formContentRef for autosave
      updateFormContent({ content: jsonContent });

      // Also update the React Hook Form state
      form.setValue('content', jsonContent, {
        shouldDirty: true,
        shouldTouch: true,
        shouldValidate: true,
      });
    },
    [updateFormContent, form, start],
  );

  const handleTitleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      // Make sure autosave is active
      start();

      updateFormContent({ title: e.target.value });
    },
    [updateFormContent, start],
  );

  const handleCategoryChange = useCallback(
    (categoryId: string) => {
      // Make sure autosave is active
      start();

      updateFormContent({ categoryId });
    },
    [updateFormContent, start],
  );

  // Initialize autosave once when component mounts
  useEffect(() => {
    if (!draftId) {
      return;
    }

    // Start autosave
    start();

    // Only stop autosave when component is unmounted completely
    return () => {
      // Don't enqueue content during unmount as this can cause infinite loops
      // Just stop the autosave
      stop();
    };
  }, [draftId, start, stop]);

  // Handle form reset
  const resetForm = useCallback(() => {
    if (!categories.length) {
      throw new Error('No categories available for forum post');
    }

    const defaultCategory = categories[0];
    if (!defaultCategory) {
      throw new Error('Failed to get default category');
    }

    form.reset({
      title: '',
      content: { type: 'doc', content: [] },
      categoryId: defaultCategory.id,
      status: 'draft',
    });
    draftIdRef.current = null;
    hasUnsavedChanges.current = false;
  }, [categories, form]);

  // Handle warning dialog actions
  const handleWarningClose = useCallback(() => {
    setShowWarning(false);
  }, []);

  const handleWarningConfirm = useCallback(() => {
    setShowWarning(false);
    if (pendingNavigation) {
      router.push(pendingNavigation);
    } else {
      resetForm();
      onCancel?.();
    }
  }, [pendingNavigation, router, resetForm, onCancel]);

  // Handle beforeunload event
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges.current) {
        // Standard way to show a confirmation dialog before unload
        // The empty string is ignored, browsers show their own message
        const confirmationMessage =
          'You have unsaved changes. Are you sure you want to leave?';
        e.preventDefault();
        // For modern browsers
        return confirmationMessage;
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, []);

  // Handle navigation interceptor
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handlePopState = () => {
      if (hasUnsavedChanges.current) {
        setShowWarning(true);
        setPendingNavigation(window.location.pathname);
        window.history.pushState(null, '', window.location.href);
      }
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, []);

  useEffect(() => {
    onLoadingChange?.(isPending);
  }, [isPending, onLoadingChange]);

  const onSubmit = useCallback(
    async (formData: FormData): Promise<void> => {
      const logger = await getLogger();
      try {
        logger.info(
          {
            component: 'ForumPostForm',
            communityId,
            mode,
            isPending,
            draftId,
          },
          'Starting form submission...',
        );

        if (!draftId) {
          logger.error(
            {
              component: 'ForumPostForm',
              communityId,
              mode,
            },
            'No draft ID available for submission',
          );
          toast.error('Unable to submit post. Please try again.');
          return;
        }

        // Stop autosave before submission
        stop();

        if (formData.status === 'published') {
          // Call publish draft function
          logger.info(
            {
              component: 'ForumPostForm',
              communityId,
              draftId,
            },
            'Publishing draft post',
          );
          const response = await publishDraftPostAction({
            postId: draftId,
          });

          if (response?.success && onSuccess) {
            // Clear unsaved changes flag before success callback
            hasUnsavedChanges.current = false;
            onSuccess(draftId);
          } else if (!response?.success) {
            // Restart autosave if publishing fails
            start();
            toast.error(response?.error ?? 'Failed to publish post');
          }
        } else {
          // Update draft using our strategy
          logger.info(
            {
              component: 'ForumPostForm',
              communityId,
              draftId,
            },
            'Updating draft post',
          );

          try {
            // Make sure content is properly transformed
            const transformedContent = transformTiptapContent(formData.content);

            const autosaveData: AutosaveData = {
              id: draftId,
              type: 'forumPost' as const,
              content: transformedContent,
              metadata: {
                communityId,
                title: formData.title,
                categoryId: formData.categoryId,
                characterCount: characterCount,
                wordCount: wordCount,
              },
              lastModified: new Date(),
            };

            if (forumAutosaveStrategy.validate(autosaveData)) {
              const transformed = forumAutosaveStrategy.transform(autosaveData);
              await forumAutosaveStrategy.save(transformed);

              if (onSuccess) {
                hasUnsavedChanges.current = false;
                onSuccess(draftId);
              }
            } else {
              throw new Error('Invalid form data');
            }
          } catch {
            // Restart autosave if update fails
            start();
            toast.error('Failed to update draft');
          }
        }
      } catch (err: unknown) {
        // Restart autosave if there's an error
        start();
        logger.error(
          {
            component: 'ForumPostForm',
            communityId,
            mode,
            error: err instanceof Error ? err.message : String(err),
          },
          'Failed to submit post',
        );
        toast.error(t('errors.createPost'));
      }
    },
    [
      communityId,
      draftId,
      isPending,
      mode,
      onSuccess,
      t,
      start,
      stop,
      characterCount,
      wordCount,
    ],
  );

  // Define storage context parts
  const featureDir = 'forums';
  const storageDirPath = draftId ? draftId : null;

  const handleImageUpload = createImageUploadHandler({
    featureDir,
    storageDirPath: storageDirPath ?? undefined,
    communityId,
  });

  const handleImageDelete = createImageDeleteHandler({
    featureDir,
    storageDirPath: storageDirPath ?? undefined,
    communityId,
  });

  const handleDelete = useCallback(async () => {
    startTransition(async () => {
      const logger = await getLogger();
      try {
        logger.info(
          {
            component: 'ForumPostForm',
            communityId,
            mode,
          },
          'Starting delete draft post',
        );

        if (!draftId) {
          logger.error(
            {
              component: 'ForumPostForm',
              communityId,
              mode,
            },
            'No draft ID available for deletion',
          );
          toast.error('Unable to delete post. Please try again.');
          return;
        }

        const result = await deleteForumPostAction({
          postId: draftId,
          confirmed: true,
        });

        if (result.success) {
          resetForm();
          toast.success(mode === 'create' ? 'Draft deleted' : 'Post deleted');
          if (onCancel) {
            onCancel();
          }
        } else {
          toast.error(result.message || 'Failed to delete post');
        }
      } catch (error: unknown) {
        logger.error(
          {
            error: error instanceof Error ? error.message : String(error),
            name: 'delete-draft-post',
          },
          'Failed to delete post',
        );
        toast.error('Failed to delete post');
      }
    });
  }, [communityId, draftId, mode, onCancel, resetForm]);

  // Add this new function to handle the publish action
  const handlePublish = useCallback(async () => {
    startTransition(async () => {
      // Stop autosave immediately
      stop();

      // Set form status to published and submit
      form.setValue('status', 'published');
      await form.handleSubmit(onSubmit)();
    });
  }, [form, onSubmit, stop]);

  // Add handleSave function before the return statement
  const handleSave = useCallback(async () => {
    if (!draftId) {
      toast.error('Cannot save: No draft ID available');
      return;
    }

    try {
      startTransition(async () => {
        // Stop autosave before manual save
        stop();

        // Get current form values
        const currentTitle = form.getValues('title');
        const currentContent = form.getValues('content');
        const currentCategoryId = form.getValues('categoryId');

        // Make sure content is properly transformed
        const transformedContent = transformTiptapContent(currentContent);

        // Call the action directly with the form data
        const result = await updateDraftPostAction({
          postId: draftId,
          draftTitle: currentTitle,
          draftContent: transformedContent,
          draftCategoryId: currentCategoryId,
          draftCharacterCount: characterCount,
          draftWordCount: wordCount,
          status: 'draft',
        });

        if (result.success) {
          // Update the form content ref to match what was saved
          formContentRef.current = {
            title: currentTitle,
            content: currentContent,
            categoryId: currentCategoryId,
          };

          // Reset unsaved changes flag
          hasUnsavedChanges.current = false;
          toast.success('Draft saved');
          onCancel?.();
        } else {
          toast.error(result.error || 'Failed to save draft');

          // Restart autosave if save fails
          start();
        }
      });
    } catch (error) {
      toast.error('Failed to save draft', {
        description: error instanceof Error ? error.message : String(error),
      });

      // Restart autosave if save fails
      start();
    }
  }, [draftId, form, onCancel, stop, start, characterCount, wordCount]);

  return (
    <AutosaveProvider>
      <Dialog open={isOpen} onOpenChange={onOpenChange}>
        <DialogContent
          className="max-h-[80vh] w-full max-w-4xl overflow-y-auto rounded-xl border bg-white p-6 shadow-md dark:bg-gray-900"
          data-test="forum-post-form-dialog"
        >
          <DialogHeader className="pb-4">
            <DialogTitle>
              {mode === 'create' ? 'Create New Post' : 'Edit Post'}
            </DialogTitle>
          </DialogHeader>

          <Form {...form}>
            <form className="space-y-6" data-test="forum-post-form">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold">
                  {mode === 'create' ? 'Create New Post' : 'Edit Post'}
                </h2>
                <div className="flex items-center gap-2">
                  {isAutoSaving && (
                    <div className="text-muted-foreground flex items-center gap-1 text-sm">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span>Saving...</span>
                    </div>
                  )}
                  {lastSaved && !isAutoSaving && (
                    <div className="text-muted-foreground text-sm">
                      Last saved: {lastSaved.toLocaleTimeString()}
                    </div>
                  )}
                  <Badge
                    variant={isDraft ? 'secondary' : 'default'}
                    className="text-xs font-medium"
                    data-test="draft-status"
                  >
                    {isDraft ? 'Draft' : 'Published'}
                  </Badge>
                </div>
              </div>

              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Title</FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t('enterTitle')}
                        {...field}
                        onChange={(e) => {
                          field.onChange(e);
                          handleTitleChange(e);
                        }}
                        data-test="forum-post-title-input"
                        aria-label="Post title"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="content"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Content</FormLabel>
                    <FormControl>
                      <TooltipProvider>
                        <TipTapEditor
                          value={field.value}
                          onChange={(content) => {
                            // Call the handleFormChange function which will handle autosave
                            // and check for empty content
                            handleFormChange(content);
                          }}
                          onBlur={() => {
                            // Sync on blur as well
                            form.setValue('content', field.value, {
                              shouldValidate: true,
                            });

                            // Don't trigger autosave on blur as it can cause issues
                            // The handleFormChange function will handle autosave
                          }}
                          onImageUpload={handleImageUpload}
                          onImageDelete={handleImageDelete}
                          className="border-input focus-within:border-primary shadow-xs grow rounded-xl border"
                          editorContentClassName="p-5 grow"
                          editorClassName="focus:outline-hidden h-full min-h-[200px]"
                          output="json"
                          autofocus={mode === 'create'}
                          editable={true}
                          contextType="forumPost"
                          characterLimit={FORUM_POST_CHARACTER_LIMIT}
                          onReady={(editor) => {
                            setEditor(editor);
                            // Don't trigger autosave when the editor is ready
                            // The user will trigger autosave when they make changes
                          }}
                          onDestroy={() => setEditor(null)}
                          data-test="forum-post-content-editor"
                        />
                      </TooltipProvider>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <ForumCategorySelector
                    categories={categories.map((category) => ({
                      id: category.id,
                      name: category.name,
                      icon: category.icon ?? '',
                    }))}
                    onSelect={handleCategoryChange}
                    initialValue={form.getValues('categoryId')}
                    className="bg-white dark:bg-gray-900"
                  />
                </div>

                <div className="flex items-center gap-4">
                  <CharacterCountDisplay
                    characterCount={characterCount}
                    wordCount={wordCount}
                    characterLimit={FORUM_POST_CHARACTER_LIMIT}
                  />

                  <div className="flex items-center gap-2">
                    {isDraft && (
                      <DeletePostCommentAlert
                        type="post"
                        isDeleting={isPending}
                        onDelete={handleDelete}
                      >
                        <Button
                          type="button"
                          variant="secondary"
                          data-test="forum-post-delete-button"
                          aria-label="Delete draft post"
                        >
                          Delete
                        </Button>
                      </DeletePostCommentAlert>
                    )}
                    {isDraft && (
                      <Button
                        type="button"
                        variant="secondary"
                        onClick={handleSave}
                        disabled={isPending || !draftId}
                        data-test="forum-post-save-draft-button"
                        aria-label="Save draft and close editor"
                      >
                        {isPending ? (
                          <>
                            <Loader2 className="h-4 w-4 animate-spin" />
                            Saving...
                          </>
                        ) : (
                          'Save & Close'
                        )}
                      </Button>
                    )}
                    <Button
                      type="button"
                      onClick={handlePublish}
                      disabled={isPending}
                      data-test="forum-post-submit-button"
                      aria-label={isDraft ? 'Publish post' : 'Update post'}
                    >
                      {isPending ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : isDraft ? (
                        'Publish'
                      ) : (
                        'Update'
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </form>
          </Form>
          <div className="border-border mt-4 rounded-lg border p-4">
            <h3 className="text-muted-foreground mb-2 text-sm font-medium">
              Editor Output Preview
            </h3>
            <div className="wra overflow-x-auto overflow-y-auto">
              <pre className="text-xs">
                {JSON.stringify(formContentRef.current.content, null, 2)}
              </pre>
            </div>
          </div>

          <DraftWarningDialog
            isOpen={showWarning}
            onClose={handleWarningClose}
            onConfirm={handleWarningConfirm}
            onCancel={handleWarningClose}
            draftTitle={form.getValues('title')}
          />
        </DialogContent>
      </Dialog>
    </AutosaveProvider>
  );
};

ForumPostForm.displayName = 'ForumPostForm';
