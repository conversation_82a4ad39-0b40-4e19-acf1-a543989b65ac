'use client';

import { use<PERSON><PERSON>back, useState, useTransition } from 'react';

import { useRouter } from 'next/navigation';

import { useQueryClient } from '@tanstack/react-query';
import { Pencil, Trash } from 'lucide-react';
import { toast } from 'sonner';
import { z } from 'zod';

import { But<PERSON> } from '@kit/ui/button';
import { ForumPostCard } from '@kit/ui/dojo/organisms/forum-post-card';
import { Spinner } from '@kit/ui/spinner';

import { AvatarHoverWrapper } from '~/[community]/_components/avatar-hover-wrapper';
import { DeletePostCommentAlert } from '~/community/forums/_components/delete-post-comment-alert';
import { EditForumPostModal } from '~/community/forums/_components/edit-forum-post-modal';
import { ForumPinButton } from '~/community/forums/_components/forum-pin-button';
import { useReactionCounts } from '~/community/forums/_lib/hooks/use-reaction-counts';
import { ForumCategorySchema } from '~/community/forums/_lib/schema/forum-category.schema';
import { deleteForumPostAction } from '~/community/forums/_lib/server/actions/delete-forum-post';
import { toggleReactionAction } from '~/community/forums/_lib/server/actions/toggle-reaction';
import { forumReactionValues } from '~/lib/communities/forums/reaction.types';
import { ForumPost, ForumReactionType } from '~/lib/communities/forums/types';
import type { PermissionsEnum } from '~/lib/communities/members/types';
import { hasCommunityPermission } from '~/lib/communities/permissions.utils';

type ForumPostContainerProps = {
  communitySlug: string;
  post: ForumPost;
  className?: string;
  permissions: PermissionsEnum[];
  userId: string;
  categories: z.infer<typeof ForumCategorySchema>[];
  redirectTo?: string;
  isPublicView?: boolean;
  'data-test'?: string;
};

export function ForumPostContainer({
  communitySlug,
  post,
  className,
  permissions,
  userId,
  categories,
  redirectTo,
  isPublicView = false,
  'data-test': dataTest,
}: ForumPostContainerProps) {
  const [isPending, startTransition] = useTransition();
  const queryClient = useQueryClient();
  const router = useRouter();

  const [isEditing, setIsEditing] = useState(false);

  const { data: reactionCounts, isLoading: isLoadingReactions } =
    useReactionCounts({ postId: post.id });

  const handleReaction = (reactionType: ForumReactionType) => {
    if (isPublicView) {
      return;
    }

    startTransition(() => {
      void toggleReactionAction({
        postId: post.id,
        commentId: null,
        reactionType: reactionType,
      }).then(() => {
        void queryClient.invalidateQueries({
          queryKey: ['reaction-counts', post.id],
        });
      });
    });
  };

  const handleDelete = useCallback(async () => {
    startTransition(async () => {
      try {
        if (!post.id) {
          toast.error('Unable to delete post. Please try again.');
          return;
        }

        await deleteForumPostAction({
          postId: post.id,
          confirmed: true,
        });

        // Invalidate any relevant queries and wait for it to complete
        await queryClient.invalidateQueries({ queryKey: ['forum-posts'] });

        if (redirectTo) {
          router.push(redirectTo.replace('[community]', communitySlug));
        }
      } catch {
        toast.error('Failed to delete post');
      }
    });
  }, [communitySlug, post.id, redirectTo, router, queryClient]);

  const hasPinPermission = hasCommunityPermission(
    permissions,
    'community.forums.post.pin_post',
  );
  const hasDeletePermission = hasCommunityPermission(
    permissions,
    'community.forums.post.delete',
  );
  const isDraftPost = post.status === 'draft';
  const isAuthor = post.createdByUserId === userId;

  // Disable certain actions in public view
  const canEditPost =
    !isPublicView &&
    (isAuthor ||
      hasCommunityPermission(permissions, 'community.forums.post.update'));
  const canDeletePost = !isPublicView && (isAuthor || hasDeletePermission);
  const canPinPost = !isPublicView && hasPinPermission && !isDraftPost;

  const avatarChildren = (
    <AvatarHoverWrapper
      userId={post.createdByUserId}
      pictureUrl={post.authorPictureUrl}
      firstName={post.authorFirstName}
      lastName={post.authorLastName}
    />
  );
  // Determine whether to show edit functionality
  const showEditModal = !isPublicView && isEditing;

  // Prepare header actions - only show for authenticated users
  const headerActions = isPublicView ? null : (
    <>
      {canEditPost && (
        <Button
          variant="ghost"
          size="icon"
          onClick={(e) => {
            e.stopPropagation();
            setIsEditing(true);
          }}
          data-test="forum-post-edit-button"
        >
          <Pencil className="h-4 w-4" />
          <span className="sr-only">Edit post</span>
        </Button>
      )}
      {canDeletePost && (
        <DeletePostCommentAlert
          type="post"
          isDeleting={isPending}
          onDelete={handleDelete}
        >
          <Button
            variant="ghost"
            size="icon"
            disabled={isPending}
            data-test="forum-post-delete-button"
          >
            {isPending ? (
              <Spinner className="h-4 w-4" />
            ) : (
              <Trash className="h-4 w-4" />
            )}
            <span className="sr-only">Delete post</span>
          </Button>
        </DeletePostCommentAlert>
      )}
      {canPinPost && (
        <div onClick={(e) => e.stopPropagation()}>
          <ForumPinButton postId={post.id} isPinned={post.isPinned} />
        </div>
      )}
    </>
  );

  return (
    <>
      <ForumPostCard
        post={post}
        postDetailsLink={`/${communitySlug}/forums/${post.slug}`}
        reactionCounts={reactionCounts ?? {}}
        isLoadingReactions={isLoadingReactions}
        isPending={isPending}
        reactionTypes={[...forumReactionValues]}
        onReaction={(reaction) => handleReaction(reaction as ForumReactionType)}
        avatarChildren={avatarChildren}
        className={className}
        data-test={dataTest}
        headerActions={headerActions}
        disableInteractions={isPublicView}
      />
      {showEditModal && (
        <EditForumPostModal
          post={post}
          isOpen={isEditing}
          onClose={() => setIsEditing(false)}
          categories={categories}
        />
      )}
    </>
  );
}

ForumPostContainer.displayName = 'ForumPostContainer';
