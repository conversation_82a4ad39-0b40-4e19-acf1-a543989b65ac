import { z } from 'zod';

import { StatusFilter } from '@kit/ui/dojo/atoms/status-filter';
import { CategoryFilter } from '@kit/ui/dojo/organisms/category-filter';

import { ForumCategorySchema } from '~/community/forums/_lib/schema/forum-category.schema';

const forumStatusOptions = [
  { value: 'all', label: 'All Posts' },
  { value: 'published', label: 'Published Only' },
  { value: 'draft', label: 'My Drafts' },
];

export function ForumHeader({
  categories,
}: {
  categories: z.infer<typeof ForumCategorySchema>[];
}) {
  return (
    <div className="flex justify-between">
      <CategoryFilter categories={categories ?? []} />
      <StatusFilter
        options={forumStatusOptions}
        paramName="status"
        defaultValue="all"
        placeholder="Filter by status"
      />
    </div>
  );
}

ForumHeader.displayName = 'ForumHeader';
