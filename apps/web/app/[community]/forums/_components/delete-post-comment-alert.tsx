import { ReactNode } from 'react';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@kit/ui/alert-dialog';

type DeletePostCommentAlertProps = {
  type: 'post' | 'comment';
  isDeleting: boolean;
  onDelete: () => Promise<void>;
  children: ReactNode;
};

export function DeletePostCommentAlert({
  type,
  isDeleting,
  onDelete,
  children,
}: DeletePostCommentAlertProps) {
  return (
    <AlertDialog>
      <AlertDialogTrigger asChild onClick={(e) => e.stopPropagation()}>
        {children}
      </AlertDialogTrigger>
      <AlertDialogContent data-test={`delete-${type}-alert-dialog`}>
        <AlertDialogHeader>
          <AlertDialogTitle>
            Delete {type === 'post' ? 'Post' : 'Comment'}
          </AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete this {type}? This action cannot be
            undone.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel
            onClick={(e) => e.stopPropagation()}
            data-test={`cancel-delete-${type}-button`}
            aria-label="Cancel deletion"
          >
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={(e) => {
              e.stopPropagation();
              void onDelete();
            }}
            disabled={isDeleting}
            data-test={`confirm-delete-${type}-button`}
            aria-label={`Confirm delete ${type}`}
          >
            {isDeleting ? 'Deleting...' : 'Delete'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

DeletePostCommentAlert.displayName = 'DeletePostCommentAlert';
