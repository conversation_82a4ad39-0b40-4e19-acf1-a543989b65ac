'use client';

import { useState } from 'react';

import { toast } from 'sonner';
import { z } from 'zod';

import { getLogger } from '@kit/shared/logger';

import { ForumCategorySchema } from '~/community/forums/_lib/schema/forum-category.schema';
import { createDraftPostAction } from '~/community/forums/_lib/server/actions/create-draft-post';

import { ForumPostForm } from './forum-post-form';

type ForumCreatePostProps = {
  communityId: string;
  categories: z.infer<typeof ForumCategorySchema>[];
  onSuccess?: (postId: string) => void;
};

export function ForumCreatePost({
  communityId,
  categories,
  onSuccess,
}: ForumCreatePostProps) {
  const [showingEditor, setShowingEditor] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [draftId, setDraftId] = useState<string | null>(null);

  const handleEditorOpen = async () => {
    if (isLoading) return;

    const logger = await getLogger();
    try {
      setIsLoading(true);
      logger.info(
        { component: 'ForumCreatePost', communityId },
        'Creating initial draft...',
      );

      const response = await createDraftPostAction({
        draftTitle: 'Untitled Post',
        draftContent: { type: 'doc', content: [] },
        draftCategoryId: categories[0]?.id ?? '',
        draftCharacterCount: 0,
        draftWordCount: 0,
        status: 'draft',
      });

      if (response?.success && response.data?.postId) {
        logger.info(
          {
            component: 'ForumCreatePost',
            communityId,
            postId: response.data.postId,
          },
          'Successfully created initial draft, opening editor...',
        );
        setDraftId(response.data.postId);
        setShowingEditor(true);
      } else {
        throw new Error('Failed to create draft post');
      }
    } catch (err) {
      logger.error(
        { component: 'ForumCreatePost', communityId, error: err },
        'Failed to create draft post',
      );
      toast.error('Failed to create draft. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSuccess = async (postId: string) => {
    const logger = await getLogger();
    logger.info(
      { component: 'ForumCreatePost', communityId, postId },
      'Post creation successful',
    );
    setShowingEditor(false);
    setIsLoading(false);
    setDraftId(null);
    onSuccess?.(postId);
  };

  const handleCancel = async () => {
    const logger = await getLogger();
    logger.info(
      { component: 'ForumCreatePost', communityId },
      'Cancelling post creation',
    );
    setShowingEditor(false);
    setIsLoading(false);
    setDraftId(null);
  };

  const handleLoadingChange = async (loading: boolean) => {
    const logger = await getLogger();
    logger.info(
      { component: 'ForumCreatePost', communityId, loading },
      'Loading state changed',
    );
    setIsLoading(loading);
  };

  return (
    <>
      <div
        onClick={!isLoading ? handleEditorOpen : undefined}
        className={`mx-auto my-2 w-full rounded-xl border bg-white p-4 shadow-md transition-all duration-200 ease-in dark:bg-gray-900 ${
          !isLoading ? 'hover:border-primary cursor-pointer' : 'opacity-50'
        }`}
        data-test="forum-create-post-trigger"
        aria-label="Create a new post"
        role="button"
      >
        <div className="text-muted-foreground">
          {isLoading ? 'Creating post...' : 'Create a new post...'}
        </div>
      </div>

      {showingEditor && draftId && (
        <ForumPostForm
          communityId={communityId}
          categories={categories}
          onSuccess={handleSuccess}
          onCancel={handleCancel}
          onLoadingChange={handleLoadingChange}
          mode="create"
          isOpen={showingEditor}
          onOpenChange={setShowingEditor}
          initialDraftId={draftId}
        />
      )}
    </>
  );
}

ForumCreatePost.displayName = 'ForumCreatePost';
