import Link from 'next/link';

import { Building2, MessagesSquare } from 'lucide-react';

import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { NotFound } from '@kit/ui/dojo/molecules/not-found';
import type { JSONContent } from '@kit/ui/dojo/organisms/tiptap-editor';
import { Heading } from '@kit/ui/heading';
import { PageBody } from '@kit/ui/page';
import { getTimeAgo } from '@kit/ui/utils';

import { CommunityNotFound } from '~/[community]/_components/community-not-found';
import { ForumPostContainer } from '~/community/forums/_components/forum-post-container';
import pathsConfig from '~/config/paths.config';
import { loadCommunityWorkspace } from '~/lib/communities/community-workspace.loader';
import { ForumPost as ForumPostCardType } from '~/lib/communities/forums/types';
import type { PermissionsEnum } from '~/lib/communities/members/types';
import { withI18n } from '~/lib/i18n/with-i18n';

import { ForumPostCommentsContainer } from './_components/forum-post-comments-container';
import { ForumPostDetailsContainer } from './_components/forum-post-details-container';
import { loadForumPostData } from './_lib/server/forum-post.loader';

type ForumPostDetailPageProps = {
  params: Promise<{
    postSlug: string;
    community: string;
  }>;

  searchParams: Promise<{
    page?: string;
  }>;
};

async function ForumPostDetailPage({ params }: ForumPostDetailPageProps) {
  const communitySlug = (await params).community;

  const postSlug = (await params).postSlug;

  const workspace = await loadCommunityWorkspace(communitySlug);

  if (!workspace || !workspace.community?.id) {
    const homePath = pathsConfig.app.home; // Redirect to generic home
    return (
      <PageBody>
        <NotFound
          title="Community not found"
          message="The community you are looking for may not exist or you may not have access."
          icon={Building2}
          actions={
            <Button asChild>
              <Link href={homePath}>Return to Home</Link>
            </Button>
          }
        />
      </PageBody>
    );
  }

  const communityId = workspace.community.id;
  const user = workspace.user;
  const isMember = !!workspace.community.memberId;
  const permissions = (workspace.community.permissions ??
    []) as PermissionsEnum[];

  const { post, categories } = await loadForumPostData(communityId, postSlug);

  const publicView = !isMember;

  if (!post) {
    const forumsPath = pathsConfig.app.forums.replace(
      '[community]',
      communitySlug,
    );

    return (
      <CommunityNotFound
        icon={MessagesSquare}
        title="Post not found"
        message="The post you are looking for does not exist or has been removed."
        actions={
          <Button asChild>
            <Link href={forumsPath}>Return to Forums</Link>
          </Button>
        }
      />
    );
  }

  const userId = user?.id ?? '';
  const timeAgo = getTimeAgo(post.createdAt);

  const postCardContent: ForumPostCardType = {
    ...post,
    content: post.content as JSONContent,
    slug: postSlug,
    isPinned: Boolean(post.isPinned),
    status: 'published',
    autoSavedAt: null,
    totalCount: post.commentCount ?? 0,
  };

  return (
    <div className={'flex flex-1'}>
      <div className={'flex flex-1 flex-col'}>
        <div className={'p-4'}>
          <div className={'flex flex-col space-y-2.5'}>
            <Heading className={'font-semibold'} level={5}>
              {post.title}
            </Heading>

            <div className={'flex space-x-2.5'}>
              <Badge variant={'outline'}>Created {timeAgo}</Badge>
            </div>
          </div>
          <ForumPostContainer
            communitySlug={communitySlug}
            categories={categories ?? []}
            post={postCardContent}
            permissions={permissions}
            userId={userId}
            redirectTo={`${pathsConfig.app.forums.replace('[community]', communitySlug)}`}
          />
        </div>

        <PageBody className="flex-1 overflow-y-auto">
          <div className="h-full">
            <ForumPostCommentsContainer
              postId={post.id}
              permissions={permissions}
              userId={userId}
              communityId={communityId}
              publicView={publicView}
            />
          </div>
        </PageBody>
      </div>

      <div className={'w-[25%]'}>
        <PageBody>
          <ForumPostDetailsContainer
            post={postCardContent}
            communityId={communityId}
            publicView={publicView}
          />
        </PageBody>
      </div>
    </div>
  );
}

export default withI18n(ForumPostDetailPage);

ForumPostDetailPage.displayName = 'ForumPostDetailPage';
