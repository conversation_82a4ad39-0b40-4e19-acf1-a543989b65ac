'use client';

import { useLayoutEffect, useRef } from 'react';

import { Alert, AlertTitle } from '@kit/ui/alert';
import { Button } from '@kit/ui/button';
import { LoadingOverlay } from '@kit/ui/loading-overlay';
import { Trans } from '@kit/ui/trans';

import type { Comment } from '~/lib/communities/forums/types';
import type { PermissionsEnum } from '~/lib/communities/members/types';

import { useAppendNewComment } from '../_lib/hooks/use-append-new-comment';
import { useFetchForumPostComments } from '../_lib/hooks/use-fetch-forum-post-comments';
import { organizeCommentsIntoTree } from '../_lib/utils/comment-tree-utils';
import { CommentThread } from './comment-thread';
import { ReplyCommentInput } from './reply-comment-input';

type ForumPostCommentsContainerProps = {
  postId: string;
  permissions: PermissionsEnum[];
  userId: string;
  communityId: string;
  publicView: boolean;
};

export function ForumPostCommentsContainer(
  props: ForumPostCommentsContainerProps,
) {
  const scrollingDiv = useRef<HTMLDivElement | null>(null);
  const queryKey = ['forum-post-comments', props.postId];
  const appendComment = useAppendNewComment({
    queryKey,
    permissions: props.permissions,
    userId: props.userId,
    publicView: props.publicView,
  });

  const { status, data, hasNextPage, fetchNextPage, isFetchingNextPage } =
    useFetchForumPostComments({
      postId: props.postId,
      queryKey,
      permissions: props.permissions,
      userId: props.userId,
      publicView: props.publicView,
    });

  // Calculate the ID of the last comment across all pages
  const lastCommentId = data?.pages.flat().pop()?.id;

  // Scroll to bottom when the last comment changes
  useLayoutEffect(() => {
    const node = scrollingDiv.current;
    if (!node) return;

    // Schedule scroll after the DOM has been updated and before the next paint
    const rafId = requestAnimationFrame(() => {
      node.scrollTo({ top: node.scrollHeight, behavior: 'smooth' });
    });

    // Clean up the animation frame request if the effect re-runs or component unmounts
    return () => cancelAnimationFrame(rafId);
  }, [lastCommentId]); // Depend only on the last comment ID

  if (status === 'pending') {
    return (
      <LoadingOverlay fullPage={false}>
        <Trans i18nKey={'forums:comments.loadingComments'} />
      </LoadingOverlay>
    );
  }

  if (status === 'error') {
    return (
      <Alert variant={'destructive'}>
        <AlertTitle>
          <Trans i18nKey={'forums:comments.errorLoadingComments'} />
        </AlertTitle>
      </Alert>
    );
  }

  return (
    <div className={'flex h-full w-full flex-col gap-8'}>
      <div
        ref={scrollingDiv}
        className="flex flex-1 flex-col overflow-y-auto [scrollbar-gutter:stable]"
        data-test="comments-scrolling-container"
        aria-live="polite"
      >
        {data?.pages?.map((page: Comment[] | undefined, pageIndex: number) => (
          <div key={pageIndex} className="flex flex-col gap-2">
            {page &&
              organizeCommentsIntoTree(
                page || [],
                props.permissions,
                props.userId,
                props.publicView,
              )?.map((comment) => (
                <CommentThread
                  key={comment.id}
                  comment={comment}
                  postId={props.postId}
                  communityId={props.communityId}
                  onReply={appendComment}
                  permissions={props.permissions}
                  userId={props.userId}
                  publicView={props.publicView}
                />
              ))}
          </div>
        ))}

        {hasNextPage && (
          <Button
            variant="ghost"
            className="mt-2"
            onClick={() => fetchNextPage()}
            disabled={isFetchingNextPage}
            data-test="show-more-comments-button"
            aria-label="Show more comments"
          >
            {isFetchingNextPage ? (
              <Trans i18nKey="forums:comments.loadingMore" />
            ) : (
              <Trans i18nKey="forums:comments.showMore" />
            )}
          </Button>
        )}
      </div>
      {!props.publicView && (
        <ReplyCommentInput
          postId={props.postId}
          communityId={props.communityId}
          onCommentReply={appendComment}
          className="mb-4"
        />
      )}
    </div>
  );
}

ForumPostCommentsContainer.displayName = 'ForumPostCommentsContainer';
