'use client';

import React, { useState, useTransition } from 'react';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';
import { z } from 'zod';

import { Button } from '@kit/ui/button';
import { CharacterCountDisplay } from '@kit/ui/dojo/molecules/character-count-display';
import {
  Content,
  Editor,
  JSONContent,
} from '@kit/ui/dojo/organisms/tiptap-editor';
import {
  TipTapEditor,
  useTipTapCharacterCount,
} from '@kit/ui/dojo/organisms/tiptap-editor';
import { Spinner } from '@kit/ui/spinner';
import { TooltipProvider } from '@kit/ui/tooltip';
import { Trans } from '@kit/ui/trans';
import { cn } from '@kit/ui/utils';

import type { Comment } from '~/lib/communities/forums/types';
import {
  createImageDeleteHandler,
  createImageUpload<PERSON>andler,
} from '~/lib/images/handler/image-handlers';
import { transformTiptapContent } from '~/lib/utils/transform-tiptap-content';

import { CommentFormSchema } from '../_lib/schema/comment-form.schema';
import { createForumPostCommentAction } from '../_lib/server/actions/create-forum-post-comment';

const FORUM_COMMENT_CHARACTER_LIMIT = 2000;

type ReplyCommentInputProps = {
  postId: string;
  communityId: string;
  parentCommentId?: string;
  onCommentReply: (comment: Comment) => void;
  className?: string;
};

export function ReplyCommentInput({
  postId,
  communityId,
  parentCommentId,
  onCommentReply,
  className,
}: ReplyCommentInputProps) {
  const { t } = useTranslation();
  const [isPending, startTransition] = useTransition();

  // State to store the editor instance
  const [editor, setEditor] = useState<Editor | null>(null);

  // Use the character count hook to get counts from the editor
  const { characterCount: rawCharacterCount, wordCount: rawWordCount } =
    useTipTapCharacterCount(editor);

  // defend against the void
  const characterCount = rawCharacterCount ?? 0;
  const wordCount = rawWordCount ?? 0;

  const form = useForm({
    resolver: zodResolver(CommentFormSchema),
    defaultValues: {
      comment: {},
      commentPostId: postId,
      parentCommentId,
    },
  });

  const handleContentChange = (content: Content) => {
    // Update the form state when editor content changes
    // form.setValue('comment', JSON.parse(JSON.stringify(content)));
    form.setValue('comment', content as JSONContent, {
      shouldDirty: true,
      shouldTouch: true,
    });
  };

  // Define storage context parts
  const featureDir = 'forums';
  const storageDirPath = postId;

  const handleImageUpload = createImageUploadHandler({
    featureDir,
    storageDirPath,
    communityId,
  });

  const handleImageDelete = createImageDeleteHandler({
    featureDir,
    storageDirPath,
    communityId,
  });

  const onSubmit = form.handleSubmit(
    (formData: z.infer<typeof CommentFormSchema>) => {
      startTransition(async () => {
        try {
          const comment = await createForumPostCommentAction({
            comment: transformTiptapContent(formData.comment),
            commentPostId: formData.commentPostId,
            parentCommentId: formData.parentCommentId,
            characterCount: characterCount,
            wordCount: wordCount,
          });

          if (!comment) {
            throw new Error(t('forums:comments.errorCreatingComment'));
          }

          onCommentReply(comment as Comment);
          form.reset();

          toast.success(t('forums:comments.successMessage'));
        } catch (err: unknown) {
          const error = err instanceof Error ? err : new Error(String(err));
          console.error('Failed to submit comment:', error);

          toast.error(error.message || t('forums:comments.errorMessage'));
        }
      });
    },
  );

  return (
    <>
      <form
        onSubmit={onSubmit}
        className={cn(
          'relative z-10 rounded-xl bg-white dark:bg-gray-900',
          className,
        )}
        data-test="reply-comment-form"
      >
        <TooltipProvider>
          <TipTapEditor
            onChange={handleContentChange}
            className="border-input focus-within:border-primary shadow-xs grow rounded-xl border"
            editorContentClassName="p-5 grow"
            editorClassName="focus:outline-hidden h-full"
            output="json"
            placeholder={t('forums:comments.replyCommentPlaceholder')}
            autofocus={true}
            editable={!isPending}
            onImageUpload={handleImageUpload}
            onImageDelete={handleImageDelete}
            contextType="forumPostComment"
            characterLimit={FORUM_COMMENT_CHARACTER_LIMIT}
            onReady={setEditor}
            onDestroy={() => setEditor(null)}
          />
        </TooltipProvider>

        <div className="mt-2 flex items-center justify-between">
          <CharacterCountDisplay
            characterCount={characterCount}
            wordCount={wordCount}
            characterLimit={FORUM_COMMENT_CHARACTER_LIMIT}
            className="ml-2"
          />

          <Button
            type="submit"
            disabled={isPending}
            className="mr-2"
            data-test="reply-comment-button"
            aria-label="Reply to comment"
          >
            {isPending ? (
              <span className="flex items-center gap-2">
                <Spinner className="size-4" />
                <Trans i18nKey={'common:submitting'} />
              </span>
            ) : (
              <Trans i18nKey={'forums:comments.replyCommentButton'} />
            )}
          </Button>
        </div>
      </form>
    </>
  );
}

ReplyCommentInput.displayName = 'ReplyCommentInput';
