'use client';

import React, { useCallback } from 'react';

import { useQueryClient } from '@tanstack/react-query';

import { Badge } from '@kit/ui/badge';
import { Heading } from '@kit/ui/heading';
import { If } from '@kit/ui/if';
import { Trans } from '@kit/ui/trans';

import type { ForumPost } from '~/lib/communities/forums/types';

import { ReplyCommentInput } from './reply-comment-input';

type ForumPostDetailsContainerProps = {
  post: ForumPost;
  communityId: string;
  publicView: boolean;
};

export function ForumPostDetailsContainer({
  post,
  communityId,
  publicView,
}: ForumPostDetailsContainerProps) {
  const queryClient = useQueryClient();

  const appendComment = useCallback(() => {
    void queryClient.invalidateQueries({
      queryKey: ['forum-post-comments', post.id],
    });
  }, [queryClient, post.id]);

  return (
    <div
      className={'flex h-screen flex-1 flex-col space-y-8'}
      data-test="forum-post-details-container"
    >
      <div>
        <Heading level={4}>
          <Trans i18nKey={'forums:postDetails.pageTitle'} />
        </Heading>

        <Heading level={6} className={'text-muted-foreground'}>
          <Trans i18nKey={'forums:postDetails.pageDescription'} />
        </Heading>
        <If condition={post.createdByUserId}>
          <Badge variant={'outline'} data-test="post-author-badge">
            by {post.authorFirstName} {post.authorLastName}
          </Badge>
        </If>
      </div>
      {!publicView && (
        <ReplyCommentInput
          postId={post.id}
          communityId={communityId}
          onCommentReply={appendComment}
          className="mt-4"
        />
      )}
    </div>
  );
}

ForumPostDetailsContainer.displayName = 'ForumPostDetailsContainer';
