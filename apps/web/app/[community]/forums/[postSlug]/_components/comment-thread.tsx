'use client';

import { useState } from 'react';

import { cn } from '@kit/ui/utils';

import type { Comment } from '~/lib/communities/forums/types';
import type { PermissionsEnum } from '~/lib/communities/members/types';

import { ForumPostCommentCard } from './forum-post-comment-card';
import { ReplyCommentInput } from './reply-comment-input';

type CommentNode = Comment & {
  children: CommentNode[];
  commentPostId: string;
};

type CommentThreadProps = {
  comment: Comment & {
    children: CommentNode[];
    commentPostId: string;
  };
  postId: string;
  communityId: string;
  permissions: PermissionsEnum[];
  userId: string;
  onReply: (comment: Comment) => void;
  publicView: boolean;
};

export function CommentThread({
  comment,
  postId,
  communityId,
  permissions,
  userId,
  onReply,
  publicView,
}: CommentThreadProps) {
  const [isReplying, setIsReplying] = useState(false);

  return (
    <div
      className={cn('flex flex-col gap-2', comment.depth > 1 && 'ml-8')}
      data-test={`comment-thread-${comment.id}`}
    >
      <div className={'flex w-full flex-col gap-2'}>
        <ForumPostCommentCard
          comment={comment}
          onReplyClick={() => setIsReplying(!isReplying)}
          permissions={permissions}
          userId={userId}
          publicView={publicView}
        />
        {!publicView && isReplying && (
          <div className="ml-8">
            <ReplyCommentInput
              postId={postId}
              communityId={communityId}
              parentCommentId={comment.id}
              onCommentReply={(newComment) => {
                onReply(newComment);
                setIsReplying(false);
              }}
            />
          </div>
        )}
      </div>

      {comment.children.length > 0 && (
        <div className="flex flex-col gap-4">
          {comment.children.map((childComment) => (
            <CommentThread
              key={childComment.id}
              comment={childComment}
              postId={postId}
              communityId={communityId}
              permissions={permissions}
              userId={userId}
              onReply={onReply}
              publicView={publicView}
            />
          ))}
        </div>
      )}
    </div>
  );
}

CommentThread.displayName = 'CommentThread';
