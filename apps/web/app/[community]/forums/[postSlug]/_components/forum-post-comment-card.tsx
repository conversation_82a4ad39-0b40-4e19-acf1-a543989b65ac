'use client';

import { useCallback, useState, useTransition } from 'react';

import { useQueryClient } from '@tanstack/react-query';
import { Trash } from 'lucide-react';
import { toast } from 'sonner';

import { Button } from '@kit/ui/button';
import { ReactionsGroup } from '@kit/ui/dojo/molecules/reactions-group';
import { CommentCard } from '@kit/ui/dojo/organisms/comment-card';
import type { JSONContent } from '@kit/ui/dojo/organisms/tiptap-editor';
import { Spinner } from '@kit/ui/spinner';
import { Trans } from '@kit/ui/trans';

import { AvatarHoverWrapper } from '~/[community]/_components/avatar-hover-wrapper';
import { DeletePostCommentAlert } from '~/community/forums/_components/delete-post-comment-alert';
import { toggleReactionAction } from '~/community/forums/_lib/server/actions/toggle-reaction';
import { forumReactionValues } from '~/lib/communities/forums/reaction.types';
import type {
  Comment,
  ForumReactionType,
} from '~/lib/communities/forums/types';
import type { PermissionsEnum } from '~/lib/communities/members/types';
import { hasCommunityPermission } from '~/lib/communities/permissions.utils';

import { useReactionCounts } from '../../_lib/hooks/use-reaction-counts';
import { deletePostCommentAction } from '../_lib/server/actions/delete-forum-post-comment';

type ForumPostCommentProps = {
  comment: Comment;
  permissions: PermissionsEnum[];
  userId: string;
  onReplyClick?: () => void;
  publicView: boolean;
};

export function ForumPostCommentCard({
  comment,
  permissions,
  userId,
  onReplyClick,
  publicView,
}: ForumPostCommentProps) {
  const [isPending, startTransition] = useTransition();
  const queryClient = useQueryClient();
  const { data: reactionCounts, isLoading: isLoadingReactions } =
    useReactionCounts({
      commentId: comment.id,
    });

  const handleReaction = (reactionType: ForumReactionType) => {
    startTransition(() => {
      void toggleReactionAction({
        postId: null,
        commentId: comment.id,
        reactionType,
      }).then(() => {
        void queryClient.invalidateQueries({
          queryKey: ['reaction-counts', comment.id],
        });
      });
    });
  };

  const handleDelete = useCallback(async () => {
    try {
      if (!comment.id) {
        toast.error('Unable to delete comment. Please try again.');
        return;
      }

      setIsDeleting(true);

      await deletePostCommentAction({
        commentId: comment.id,
        confirmed: true,
      });

      // Invalidate any relevant queries and wait for it to complete
      await queryClient.invalidateQueries({
        queryKey: ['forum-post-comments'],
      });
    } catch {
      toast.error('Failed to delete comment');
    } finally {
      setIsDeleting(false);
    }
  }, [comment.id, queryClient]);

  const [isDeleting, setIsDeleting] = useState(false);
  const hasDeletePermission = hasCommunityPermission(
    permissions,
    'community.forums.post.delete',
  );
  const isAuthor = comment.createdByUserId === userId;

  const headerActions = (hasDeletePermission || isAuthor) && (
    <DeletePostCommentAlert
      type="comment"
      isDeleting={isDeleting}
      onDelete={handleDelete}
    >
      <Button
        variant="ghost"
        size="icon"
        disabled={isDeleting}
        data-test="forum-post-delete-button"
      >
        {isDeleting ? (
          <Spinner className="h-4 w-4" />
        ) : (
          <Trash className="h-4 w-4" />
        )}
        <span className="sr-only">Delete comment</span>
      </Button>
    </DeletePostCommentAlert>
  );

  const footerActions = onReplyClick && !publicView && (
    <Button
      variant="ghost"
      size="sm"
      onClick={onReplyClick}
      data-test="forum-comment-reply-button"
      aria-label="Reply to comment"
    >
      <Trans i18nKey={'forums:comments.replyCommentButton'} />
    </Button>
  );

  const date = new Date(comment.createdAt);

  const avatarChildren = (
    <AvatarHoverWrapper
      userId={comment.createdByUserId}
      pictureUrl={comment.authorPictureUrl}
      firstName={comment.authorFirstName}
      lastName={comment.authorLastName}
    />
  );

  return (
    <CommentCard
      avatarChildren={avatarChildren}
      timestamp={date.toLocaleString('en-US')}
      timestampLabel={'forums:comments.sentOn'}
      content={comment.content as JSONContent}
      headerActions={headerActions}
      footerActions={footerActions}
      reactions={
        <ReactionsGroup
          reactionCounts={reactionCounts ?? {}}
          onReaction={(reaction) =>
            handleReaction(reaction as ForumReactionType)
          }
          reactionTypes={[...forumReactionValues]}
          isLoading={isLoadingReactions}
          disabled={isPending}
        />
      }
      data-test="forum-comment-card"
    />
  );
}

ForumPostCommentCard.displayName = 'ForumPostCommentCard';
