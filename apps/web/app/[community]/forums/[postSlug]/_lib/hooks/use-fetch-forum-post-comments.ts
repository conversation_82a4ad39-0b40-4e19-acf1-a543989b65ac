import { useEffect } from 'react';

import { useInfiniteQuery } from '@tanstack/react-query';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';

import type { Comment } from '~/lib/communities/forums/types';
import type { PermissionsEnum } from '~/lib/communities/members/types';

import { getForumPostCommentsAction } from '../server/actions/get-form-post-comments';
import { useAppendNewComment } from './use-append-new-comment';

const COMMENTS_PER_PAGE = 25;

// Hook for fetching comments for a post
export function useFetchForumPostComments(params: {
  postId: string;
  queryKey: string[];
  permissions: PermissionsEnum[];
  userId: string;
  publicView: boolean;
}) {
  const appendComment = useAppendNewComment({
    queryKey: params.queryKey,
    permissions: params.permissions,
    userId: params.userId,
    publicView: params.publicView,
  });
  const client = useSupabase();

  const queryFn = async ({ pageParam }: { pageParam?: string }) => {
    const { data: comments } = await getForumPostCommentsAction({
      postId: params.postId,
      cursorTimestamp: pageParam,
      limit: COMMENTS_PER_PAGE,
    });

    return comments || [];
  };

  const query = useInfiniteQuery({
    queryKey: params.queryKey,
    queryFn,
    initialPageParam: undefined,
    getNextPageParam: (lastPage: Comment[]) => {
      if (lastPage.length < COMMENTS_PER_PAGE) {
        return undefined; // No more pages
      }
      return lastPage[lastPage.length - 1]?.createdAt;
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 10, // 10 minutes
  });

  // Subscribe to real-time comment insertions
  useEffect(() => {
    const channel = client.channel(`comments-channel-${params.postId}`);

    const subscription = channel
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          filter: `post_id=eq.${params.postId}`,
          table: 'community_forum_comments',
        },
        (payload) => {
          const rawComment = payload.new as Comment;
          const comment: Comment = {
            ...rawComment,
            authorFirstName: rawComment.authorFirstName ?? '',
            authorLastName: rawComment.authorLastName ?? '',
            authorPictureUrl: rawComment.authorPictureUrl ?? '',
            depth: 1,
            commentPostId: params.postId,
            parentCommentId: rawComment.parentCommentId ?? '',
          };
          appendComment(comment);
        },
      )
      .subscribe();

    return () => {
      void subscription.unsubscribe();
    };
  }, [client, params.postId, appendComment]);

  return query;
}
