import { useCallback } from 'react';

import { useQueryClient } from '@tanstack/react-query';

import type { Comment } from '~/lib/communities/forums/types';
import type { PermissionsEnum } from '~/lib/communities/members/types';

import {
  flattenCommentTree,
  organizeCommentsIntoTree,
} from '../utils/comment-tree-utils';

// Hook for appending a new comment to the query
export function useAppendNewComment(params: {
  queryKey: string[];
  permissions: PermissionsEnum[];
  userId: string;
  publicView: boolean;
}) {
  const queryClient = useQueryClient();
  const { queryKey } = params;

  return useCallback(
    (comment: Comment) => {
      queryClient.setQueryData(
        queryKey,
        (data: { pages: Array<Comment[]> } | undefined) => {
          if (!data) return { pages: [[{ ...comment, depth: 1 }]] };

          // Create a new array with all pages flattened
          const allComments = data.pages.flat();

          // Add the new comment
          allComments.push(comment);

          // Reorganize all comments into a tree (this will set correct depths)
          const commentTree = organizeCommentsIntoTree(
            allComments,
            params.permissions,
            params.userId,
            params.publicView,
          );

          // Flatten the tree back into a single array maintaining the hierarchy
          const flattenedComments = flattenCommentTree(commentTree);

          // Update the pages. Since we reorganized everything,
          // we can put all comments back into the first page.
          // Adjust this logic if you need to maintain pagination structure differently.
          return {
            ...data,
            pages: [flattenedComments],
          };
        },
      );
    },
    [
      queryClient,
      queryKey,
      params.permissions,
      params.userId,
      params.publicView,
    ],
  );
}
