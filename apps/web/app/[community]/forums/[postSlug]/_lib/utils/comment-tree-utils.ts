// Type for a comment node
import type { Comment } from '~/lib/communities/forums/types';
import type { PermissionsEnum } from '~/lib/communities/members/types';

export type CommentNode = Comment & {
  children: CommentNode[];
  commentPostId: string;
  permissions: PermissionsEnum[];
  userId: string;
  publicView: boolean;
};

// Utility function to organize comments into a tree
export function organizeCommentsIntoTree(
  comments: Comment[],
  permissions: PermissionsEnum[],
  userId: string,
  publicView: boolean,
): CommentNode[] {
  const commentMap = new Map<string, CommentNode>();
  const rootComments: CommentNode[] = [];

  // First pass: create CommentNode objects and store in map
  comments.forEach((comment) => {
    commentMap.set(comment.id, {
      ...comment,
      children: [],
      depth: 1,
      commentPostId: comment.parentCommentId || '', // Handle null case by providing empty string default
      permissions: permissions,
      userId: userId,
      publicView: publicView,
    });
  });

  // Second pass: organize into tree structure and update depths
  comments.forEach((comment) => {
    const commentNode = commentMap.get(comment.id)!;

    if (comment.parentCommentId) {
      const parentNode = commentMap.get(comment.parentCommentId);
      if (parentNode) {
        parentNode.children.push(commentNode);
        // Set depth based on parent's depth
        commentNode.depth = parentNode.depth + 1;
      } else {
        // If parent doesn't exist in the current batch, treat as root for now
        // This can happen with pagination
        rootComments.push(commentNode);
      }
    } else {
      rootComments.push(commentNode);
    }
  });

  // Sort root comments by creation date
  rootComments.sort(
    (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
  );

  // Sort children comments recursively by creation date
  function sortChildren(node: CommentNode) {
    node.children.sort(
      (a, b) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
    );
    node.children.forEach(sortChildren);
  }

  rootComments.forEach(sortChildren);

  return rootComments;
}

// Helper function to flatten the comment tree while maintaining order
export function flattenCommentTree(comments: CommentNode[]): Comment[] {
  const result: Comment[] = [];
  function traverse(comment: CommentNode) {
    const { children, ...commentWithoutChildren } = comment;
    result.push({
      ...commentWithoutChildren,
      commentPostId: comment.commentPostId ?? null, // Ensure post_id is included
    });

    for (const child of children) {
      traverse(child);
    }
  }

  // Traverse each root comment
  for (const comment of comments) {
    traverse(comment);
  }

  return result;
}
