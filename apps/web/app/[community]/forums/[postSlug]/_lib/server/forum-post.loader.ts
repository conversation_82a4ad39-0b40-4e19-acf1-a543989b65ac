import 'server-only';

import { SupabaseClient } from '@supabase/supabase-js';

import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createCommunityForumsService } from '~/lib/communities/forums/community-forums.service';
import { GetPostDetailsSchema } from '~/lib/communities/forums/schema/get-post-details.schema';
import type { Database } from '~/lib/database.types';

/**
 * Load data specific to the forum post page (post details, comments, categories).
 * Workspace context (auth, permissions) is loaded separately.
 *
 * @param communityId The UUID of the community.
 * @param postSlug The slug of the forum post.
 */
export async function loadForumPostData(communityId: string, postSlug: string) {
  const logger = await getLogger();
  const client = getSupabaseServerClient();

  const ctx = {
    name: 'forums.post.load',
    communityId,
    postSlug,
  };

  logger.info(ctx, 'Loading forum post specific data...');

  try {
    // Load categories and post details/comments in parallel
    const [categoriesResult, postDetailsResult] = await Promise.all([
      loadForumCategories(client, communityId),
      loadForumPostBySlug(client, postSlug),
    ]);

    logger.info(ctx, 'Loaded forum post specific data successfully');

    return {
      post: postDetailsResult.post?.[0] ?? null, // Return null if post not found
      comments: postDetailsResult.comments,
      categories: categoriesResult.data,
    };
  } catch (error) {
    logger.error({ ...ctx, error }, 'Failed to load forum post specific data');
    // Return empty/null structure on error
    return {
      post: null,
      comments: [],
      categories: [],
    };
  }
}

/**
 * Load forum categories
 */
async function loadForumCategories(
  client: SupabaseClient<Database>,
  communityId: string,
) {
  const logger = await getLogger();
  const ctx = {
    name: 'forums.categories.load',
    communityId,
  };
  const service = createCommunityForumsService(client);
  try {
    const { data } = await service.getForumCategories({
      communityId: communityId,
    });
    return {
      data: data ?? [],
    };
  } catch (error) {
    logger.error({ ...ctx, error }, 'Failed to load forum categories');
    return {
      data: [],
    };
  }
}

/**
 * Load forum post details and initial comments by slug
 */
async function loadForumPostBySlug(
  client: SupabaseClient<Database>,
  postSlug: string,
) {
  const logger = await getLogger();
  const ctx = {
    name: 'forums.post.load',
    postSlug,
  };

  const validatedData = GetPostDetailsSchema.parse({ postSlug });

  const service = createCommunityForumsService(client);
  const post = await service.getPostDetails(validatedData);

  // If no post is found, return early with empty data
  if (!post?.length || !post[0]) {
    return {
      post: null,
      comments: [],
    };
  }

  try {
    const comments = await service.getForumPostComments({
      postId: post[0].id,
      cursorTimestamp: new Date().toISOString(), // Fetch initial comments
      limit: 10, // Adjust limit as needed
    });

    return {
      post,
      comments: comments ?? [],
    };
  } catch (error) {
    logger.error({ ...ctx, error }, 'Failed to load forum post comments');
    return {
      post,
      comments: [],
    };
  }
}
