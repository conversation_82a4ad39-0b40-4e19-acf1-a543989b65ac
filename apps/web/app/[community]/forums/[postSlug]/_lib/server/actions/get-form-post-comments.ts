'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createCommunityForumsService } from '~/lib/communities/forums/community-forums.service';
import type { Comment } from '~/lib/communities/forums/types';

const GetForumPostCommentsSchema = z.object({
  postId: z.string().min(1),
  limit: z.number().min(1),
  cursorTimestamp: z.string().optional(),
});

export const getForumPostCommentsAction = enhanceAction(
  async (
    data: z.infer<typeof GetForumPostCommentsSchema>,
    user,
  ): Promise<{
    error?: boolean;
    message?: string;
    success?: boolean;
    data?: Comment[];
  }> => {
    const logger = await getLogger();
    const client = getSupabaseServerClient();

    const ctx = {
      name: 'forums_post.comment.get',
      userId: user.id, // Add user ID
      postId: data.postId,
    };

    logger.info(ctx, `Fetching forum post comments...`);

    try {
      const service = createCommunityForumsService(client);

      const comments = await service.getForumPostComments({
        postId: data.postId,
        cursorTimestamp: data.cursorTimestamp,
        limit: data.limit,
      });

      logger.info(ctx, `Forum post comments fetched via service`);

      // Return specific fields needed by the UI
      return {
        success: true,
        data: comments,
      };
    } catch (error) {
      logger.error({ ...ctx, error }, `Failed to fetch forum post comments`);
      const message = error instanceof Error ? error.message : 'Unknown error';
      return {
        error: true,
        message: message,
      };
    }
  },
  {
    auth: true,
    schema: GetForumPostCommentsSchema,
  },
);
