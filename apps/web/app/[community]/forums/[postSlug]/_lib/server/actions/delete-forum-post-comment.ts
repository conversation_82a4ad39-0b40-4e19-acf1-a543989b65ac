'use server';

import { revalidatePath } from 'next/cache';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import pathsConfig from '~/config/paths.config';
import { createCommunityForumsService } from '~/lib/communities/forums/community-forums.service';

/**
 * Schema for deleting a forum post
 * Requires confirmation to prevent accidental deletions
 */
const DeleteForumPostCommentSchema = z.object({
  commentId: z.string().min(1),
  confirmed: z.boolean().refine((val) => val === true, {
    message: 'You must confirm deletion',
  }),
});

/**
 * Server action for deleting a forum post
 * Handles both post deletion and associated image cleanup
 */
export const deletePostCommentAction = enhanceAction(
  async function (data, user) {
    const logger = await getLogger();
    const ctx = {
      name: 'delete-forum-post-comment',
      userId: user.id,
      ...data,
    };

    logger.info(ctx, 'Deleting forum post comment...');

    try {
      const client = getSupabaseServerClient();
      const forumsService = createCommunityForumsService(client);

      // Delete the draft post
      await forumsService.deleteForumPostComment({
        commentId: data.commentId,
      });

      logger.info(ctx, 'Successfully deleted forum post comment');

      revalidatePath(`${pathsConfig.app.forumPost}`, 'page');

      return {
        success: true,
        message: 'Forum post comment deleted successfully',
      };
    } catch (error) {
      if (error instanceof Error) {
        logger.error(
          { ...ctx, error: error.message },
          'Failed to delete forum post comment',
        );
      } else {
        logger.error({ ...ctx, error }, 'Failed to delete forum post comment');
      }

      return {
        success: false,
        message: 'Failed to delete forum post comment',
      };
    }
  },
  {
    auth: true,
    schema: DeleteForumPostCommentSchema,
  },
);
