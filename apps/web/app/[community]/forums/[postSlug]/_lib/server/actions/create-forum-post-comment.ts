'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createCommunityForumsService } from '~/lib/communities/forums/community-forums.service';

import { CommentFormSchema } from '../../schema/comment-form.schema';

// Define a clearer return type for the action
type CreateCommentResponse = {
  id: string;
  createdAt: string; // Assuming service returns createdAt
  userId: string; // Assuming service returns userId
  parentId: string | null; // Assuming service returns parentId
  commentPostId: string; // Keep original post ID for context
};

export const createForumPostCommentAction = enhanceAction(
  async (
    data: z.infer<typeof CommentFormSchema> & {
      characterCount: number;
      wordCount: number;
    },
    user,
  ): Promise<{
    error?: boolean;
    message?: string;
    success?: boolean;
    data?: CreateCommentResponse;
  }> => {
    const logger = await getLogger();
    const client = getSupabaseServerClient();

    const ctx = {
      name: 'forums_post.comment.create',
      userId: user.id, // Add user ID
      postId: data.commentPostId,
    };

    logger.info(ctx, `Creating forum post comment...`);

    try {
      const service = createCommunityForumsService(client);

      const newComment = await service.createComment({
        postId: data.commentPostId,
        content: data.comment,
        parentId: data.parentCommentId ?? undefined,
        characterCount: data.characterCount,
        wordCount: data.wordCount,
      });

      logger.info(ctx, `Forum post comment created via service`);

      // Return specific fields needed by the UI
      return {
        success: true,
        data: {
          id: newComment.id,
          createdAt: newComment.createdAt,
          userId: newComment.userId,
          parentId: newComment.parentId,
          commentPostId: data.commentPostId,
        },
      };
    } catch (error) {
      logger.error(ctx, `Failed to create comment`, error);
      const message = error instanceof Error ? error.message : 'Unknown error';
      return {
        error: true,
        message: message,
      };
    }
  },
  {
    auth: true,
    schema: z.object({
      ...CommentFormSchema.shape,
      characterCount: z.number(),
      wordCount: z.number(),
    }),
  },
);
