import { BookOpen, Info, MessageCircle, Settings, Users } from 'lucide-react';

import { NavigationConfigSchema } from '~/_components/navigation-config.schema';
import pathsConfig from '~/config/paths.config';
import type { PermissionsEnum } from '~/lib/communities/members/types';
import { hasCommunityPermission } from '~/lib/communities/permissions.utils';

const iconClasses = 'w-4';

type RouteConfig = {
  label: string;
  path: string;
  Icon: React.ReactNode;
  end?: boolean;
  collapsible?: boolean;
};

const getPermissionFilteredRoute = (
  route: RouteConfig,
  permissions: PermissionsEnum[],
  permission: PermissionsEnum,
  additionalCheck = true,
) =>
  hasCommunityPermission(permissions, permission) && additionalCheck
    ? route
    : undefined;

const getRoutes = (
  community: string,
  permissions: PermissionsEnum[],
  isMember: boolean,
  isPrivate: boolean,
) => {
  return [
    {
      label: 'common:routes.application',
      children:
        !isMember && isPrivate
          ? []
          : [
              {
                label: 'common:routes.forums',
                collapsible: false,
                path: createPath(pathsConfig.app.forums, community),
                Icon: <MessageCircle className={iconClasses} />,
              },
              {
                label: 'common:routes.courses',
                collapsible: false,
                path: createPath(pathsConfig.app.courses, community),
                Icon: <BookOpen className={iconClasses} />,
              },
              {
                label: 'common:routes.members',
                path: createPath(pathsConfig.app.communityMembers, community),
                Icon: <Users className={iconClasses} />,
              },
            ].filter(Boolean),
    },
    {
      label: 'common:routes.about',
      collapsible: false,
      path: createPath(pathsConfig.app.communityAbout, community),
      Icon: <Info className={iconClasses} />,
    },
    {
      label: 'common:routes.tickets',
      collapsible: false,
      children: [
        getPermissionFilteredRoute(
          {
            label: 'common:routes.tickets',
            path: createPath(pathsConfig.app.tickets, community),
            Icon: <MessageCircle className={iconClasses} />,
          },
          permissions,
          'community.tickets.update',
        ),
      ].filter(Boolean),
    },
    {
      label: 'common:settingsTabLabel',
      collapsible: false,
      children: [
        getPermissionFilteredRoute(
          {
            label: 'common:routes.settings',
            path: createPath(pathsConfig.app.communitySettings, community),
            Icon: <Settings className={iconClasses} />,
          },
          permissions,
          'settings.manage',
        ),
      ].filter(Boolean),
    },
  ];
};

export function getCommunitiesRoutesConfig(
  community: string,
  permissions: PermissionsEnum[],
  isMember: boolean,
  isPrivate: boolean,
) {
  return NavigationConfigSchema.parse({
    routes: getRoutes(community, permissions, isMember, isPrivate),
  });
}

function createPath(path: string, community: string) {
  return path.replace('[community]', community);
}
