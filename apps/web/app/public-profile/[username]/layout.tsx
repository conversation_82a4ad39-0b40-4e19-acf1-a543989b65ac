import { Page, PageNavigation } from '@kit/ui/page';

import { SiteHeader } from '~/_components/site-header';
import { withI18n } from '~/lib/i18n/with-i18n';
import { loadUserWorkspace } from '~/lib/users/user-workspace.loader';

async function UserProfileLayout(props: React.PropsWithChildren) {
  const workspace = await loadUserWorkspace();

  return (
    <>
      <Page style={'header'}>
        <PageNavigation>
          <SiteHeader workspace={workspace} />
        </PageNavigation>

        {props.children}
      </Page>
    </>
  );
}

export default withI18n(UserProfileLayout);
