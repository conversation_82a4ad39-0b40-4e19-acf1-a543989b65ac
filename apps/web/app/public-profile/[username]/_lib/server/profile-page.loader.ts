import 'server-only';

import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createCommunityMembersService } from '~/lib/communities/members/community-members.service';
import type {
  UserCreatedCommunity,
  UserMemberCommunity,
} from '~/lib/communities/members/types';
import { createUsersService } from '~/lib/users/services/users.service';

/**
 * Load data for the profile page
 * @param username - The username to load the profile for
 */
export async function loadProfilePageData(username: string) {
  const logger = await getLogger();
  const ctx = {
    name: 'load-profile-page-data',
    username,
  };

  try {
    logger.info(ctx, 'Loading profile page data');
    const client = getSupabaseServerClient();
    const usersService = createUsersService(client);
    const communityMemberService = createCommunityMembersService(client);

    const user = await usersService.getUserByUsername(username);

    if (!user) {
      logger.error(
        { ...ctx, error: 'User not found' },
        'Failed to load profile data',
      );
      return {
        publicProfile: null,
        createdCommunities: [],
        memberCommunities: [],
        total: 0,
        memberTotal: 0,
        error: 'User not found',
      };
    }

    // If we have a valid user ID, fetch both types of communities in parallel
    let createdCommunities: UserCreatedCommunity[] = [];
    let memberCommunities: UserMemberCommunity[] = [];
    let total = 0;
    let memberTotal = 0;

    if (user.id) {
      try {
        const [createdResult, memberResult] = await Promise.all([
          communityMemberService.getUserCreatedCommunities(user.id),
          communityMemberService.getUserMemberCommunities(user.id),
        ]);

        createdCommunities = createdResult.communities;
        total = createdResult.total;
        memberCommunities = memberResult.communities;
        memberTotal = memberResult.total;
      } catch (error) {
        logger.error({ ...ctx, error }, 'Failed to fetch user communities');
        // Set default values on failure
        createdCommunities = [];
        memberCommunities = [];
        total = 0;
        memberTotal = 0;
      }
    }

    const profileData = user;

    logger.info(ctx, 'Successfully loaded profile page data');
    return {
      publicProfile: profileData,
      createdCommunities,
      memberCommunities,
      total,
      memberTotal,
      error: null,
    };
  } catch (error) {
    logger.error(
      { ...ctx, error },
      'Unexpected error loading profile page data',
    );
    return {
      publicProfile: null,
      createdCommunities: [],
      memberCommunities: [],
      total: 0,
      memberTotal: 0,
      error: 'An unexpected error occurred',
    };
  }
}
