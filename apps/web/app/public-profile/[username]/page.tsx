import { Metadata } from 'next';

import { NotFound } from '@kit/ui/dojo/molecules/not-found';
import {
  UserCommunities,
  UserCommunity,
} from '@kit/ui/dojo/organisms/user-communities';
import { UserProfileCard } from '@kit/ui/dojo/organisms/user-profile-card';
import type { UserDetails } from '@kit/ui/dojo/organisms/user-profile-card';

import { withI18n } from '~/lib/i18n/with-i18n';

import { loadProfilePageData } from './_lib/server/profile-page.loader';

type UserProfilePageProps = {
  params: { username: string };
};

export async function generateMetadata({
  params,
}: UserProfilePageProps): Promise<Metadata> {
  const username = (await params).username;

  return {
    title: `@${username} | User Profile`,
    description: `View ${username}'s profile`,
  };
}

async function UserProfilePage({ params }: UserProfilePageProps) {
  const username = (await params).username;

  try {
    const {
      publicProfile,
      createdCommunities,
      memberCommunities,
      total,
      memberTotal,
    } = await loadProfilePageData(username);

    if (!publicProfile) {
      return (
        <NotFound
          title="User not found"
          message="The user you are looking for does not exist."
        />
      );
    }

    return (
      <div className="mt-4 flex flex-col space-y-24 py-6">
        <div className="container mx-auto space-y-12">
          <UserProfileCard
            publicProfile={{
              ...publicProfile,
              userDetails: publicProfile.userDetails as unknown as UserDetails,
            }}
          />

          <UserCommunities
            communities={createdCommunities as unknown as UserCommunity[]}
            total={total}
            isLoading={false}
            type="created"
          />

          <UserCommunities
            communities={memberCommunities as unknown as UserCommunity[]}
            total={memberTotal}
            isLoading={false}
            type="member"
          />
        </div>
      </div>
    );
  } catch (error) {
    console.error('Error in UserProfilePage:', error);
    return <NotFound />;
  }
}

export default withI18n(UserProfilePage);

UserProfilePage.displayName = 'UserProfilePage';
