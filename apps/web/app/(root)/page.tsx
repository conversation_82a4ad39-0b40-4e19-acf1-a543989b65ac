import Image from 'next/image';
import Link from 'next/link';

import { ArrowRightIcon, LayoutDashboard } from 'lucide-react';

import { CtaButton } from '@kit/ui/dojo/atoms/cta-button';
import { Pill } from '@kit/ui/dojo/atoms/marketing-pill';
import { FeatureCard } from '@kit/ui/dojo/molecules/feature-card';
import { FeatureGrid } from '@kit/ui/dojo/organisms/feature-grid';
import {
  FeatureShowcase,
  FeatureShowcaseIconContainer,
} from '@kit/ui/dojo/organisms/feature-showcase';
import { Hero } from '@kit/ui/dojo/organisms/hero';
import { SecondaryHero } from '@kit/ui/dojo/organisms/secondary-hero';
import { Trans } from '@kit/ui/trans';

import pathsConfig from '~/config/paths.config';
import { withI18n } from '~/lib/i18n/with-i18n';

async function Home() {
  return (
    <div className={'mt-4 flex flex-col space-y-24 py-14'}>
      <div className={'container mx-auto'}>
        <Hero
          pill={
            <Pill label={'New'}>
              <span>
                You&apos;re a Master of your Dojo, Be the Sensei to your
                students
              </span>
            </Pill>
          }
          title={<span>The ultimate Community & Learning Platform</span>}
          subtitle={
            <>
              <span>Build your own Dojo Community.</span>
              <br />
              <span>
                As a Sensei spread your wisdom and engage with your
                Gakusei&apos;s.
              </span>
            </>
          }
          cta={<MainCallToActionButton />}
          image={
            <Image
              priority
              className={
                'dark:border-primary/10 rounded-2xl border border-gray-200'
              }
              width={3558}
              height={2222}
              src={`/images/dashboard.webp`}
              alt={`App Image`}
            />
          }
        />
      </div>

      <div className={'container mx-auto'}>
        <div
          className={'flex flex-col space-y-16 xl:space-y-32 2xl:space-y-36'}
        >
          <FeatureShowcase
            heading={
              <div className="space-y-2">
                <h2 className="font-semibold dark:text-white">
                  The ultimate Community Learning Platform.
                </h2>
                <p className="text-muted-foreground">
                  Unleash your inner Sensei and build your Ninja Community
                  faster than ever with Dojo.
                </p>
              </div>
            }
            icon={
              <FeatureShowcaseIconContainer>
                <LayoutDashboard className="h-5" />
                <span>All-in-one solution</span>
              </FeatureShowcaseIconContainer>
            }
          >
            <FeatureGrid>
              <FeatureCard
                className={'relative col-span-2 overflow-hidden lg:h-96'}
                label={'Beautiful Dashboard'}
                description={`Dojo provides a beautiful dashboard to manage your learning community.`}
              >
                <Image
                  className="border-border absolute top-0 right-0 hidden h-full w-full rounded-tl-2xl border lg:top-36 lg:flex lg:h-auto lg:w-10/12"
                  src={'/images/dashboard-header.webp'}
                  width={'2061'}
                  height={'800'}
                  alt={'Dashboard Header'}
                />
              </FeatureCard>

              <FeatureCard
                className={
                  'relative col-span-2 w-full overflow-hidden lg:col-span-1'
                }
                label={'Authentication'}
                description={`Dojo provides a variety of providers to allow your users to sign in.`}
              >
                <Image
                  className="absolute top-32 left-16 hidden h-auto w-8/12 rounded-l-2xl lg:flex"
                  src={'/images/sign-in.webp'}
                  width={'1760'}
                  height={'1680'}
                  alt={'Sign In'}
                />
              </FeatureCard>

              <FeatureCard
                className={
                  'relative col-span-2 overflow-hidden lg:col-span-1 lg:h-96'
                }
                label={'Multi Dojo'}
                description={`Create and manage multiple dojos, each with its own members, content, and settings.`}
              >
                <Image
                  className="absolute top-0 right-0 hidden h-full w-full rounded-tl-2xl border lg:top-28 lg:flex lg:h-auto lg:w-8/12"
                  src={'/images/multi-tenancy.webp'}
                  width={'2061'}
                  height={'800'}
                  alt={'Multi Tenancy'}
                />
              </FeatureCard>

              <FeatureCard
                className={'relative col-span-2 overflow-hidden lg:h-96'}
                label={'Billing'}
                description={`Dojo supports multiple payment gateways to charge your community.`}
              >
                <Image
                  className="border-border absolute top-0 right-0 hidden h-full w-full rounded-tl-2xl border lg:top-36 lg:flex lg:h-auto lg:w-11/12"
                  src={'/images/billing.webp'}
                  width={'2061'}
                  height={'800'}
                  alt={'Billing'}
                />
              </FeatureCard>
            </FeatureGrid>
          </FeatureShowcase>
        </div>
      </div>

      <div className={'container mx-auto'}>
        <div
          className={
            'flex flex-col items-center justify-center space-y-16 py-16'
          }
        >
          <SecondaryHero
            pill={<Pill label="Start for free">No credit card required.</Pill>}
            heading="Fair pricing for all types of businesses"
            subheading="Get started on our free plan and upgrade when you are ready."
          />
        </div>
      </div>
    </div>
  );
}

export default withI18n(Home);

function MainCallToActionButton() {
  return (
    <div className={'flex space-x-4'}>
      <CtaButton>
        <Link href={pathsConfig.app.createCommunity}>
          <span className={'flex items-center space-x-0.5'}>
            <span>
              <Trans i18nKey={'common:createCommunity'} />
            </span>

            <ArrowRightIcon
              className={
                'animate-in fade-in slide-in-from-left-8 h-4' +
                ' zoom-in fill-mode-both delay-1000 duration-1000'
              }
            />
          </span>
        </Link>
      </CtaButton>

      <CtaButton variant={'link'}>
        <Link href={'/contact'}>
          <Trans i18nKey={'common:contactUs'} />
        </Link>
      </CtaButton>
    </div>
  );
}
