'use client';

import { useState, useTransition } from 'react';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';

import { Button } from '@kit/ui/button';
import { StatusAlert } from '@kit/ui/dojo/molecules/status-alert';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { Textarea } from '@kit/ui/textarea';
import { Trans } from '@kit/ui/trans';

import { ContactEmailSchema } from '~/(root)/contact/_lib/contact-email.schema';
import { sendContactEmail } from '~/(root)/contact/_lib/server/server-actions';

export function ContactForm() {
  const [pending, startTransition] = useTransition();

  const [state, setState] = useState({
    success: false,
    error: false,
  });

  const form = useForm({
    resolver: zodResolver(ContactEmailSchema),
    defaultValues: {
      name: '',
      email: '',
      message: '',
    },
  });

  if (state.success) {
    return (
      <StatusAlert
        variant="success"
        titleKey="marketing:contactSuccess"
        descriptionKey="marketing:contactSuccessDescription"
      />
    );
  }

  if (state.error) {
    return (
      <StatusAlert
        variant="destructive"
        titleKey="marketing:contactError"
        descriptionKey="marketing:contactErrorDescription"
      />
    );
  }

  return (
    <Form {...form}>
      <form
        className={'flex flex-col space-y-4'}
        onSubmit={form.handleSubmit((data) => {
          startTransition(async () => {
            try {
              await sendContactEmail(data);

              setState({ success: true, error: false });
            } catch {
              setState({ error: true, success: false });
            }
          });
        })}
      >
        <FormField
          name={'name'}
          render={({ field }) => {
            return (
              <FormItem>
                <FormLabel>
                  <Trans i18nKey={'marketing:contactName'} />
                </FormLabel>

                <FormControl>
                  <Input maxLength={200} {...field} />
                </FormControl>

                <FormMessage />
              </FormItem>
            );
          }}
        />

        <FormField
          name={'email'}
          render={({ field }) => {
            return (
              <FormItem>
                <FormLabel>
                  <Trans i18nKey={'marketing:contactEmail'} />
                </FormLabel>

                <FormControl>
                  <Input type={'email'} {...field} />
                </FormControl>

                <FormMessage />
              </FormItem>
            );
          }}
        />

        <FormField
          name={'message'}
          render={({ field }) => {
            return (
              <FormItem>
                <FormLabel>
                  <Trans i18nKey={'marketing:contactMessage'} />
                </FormLabel>

                <FormControl>
                  <Textarea
                    className={'min-h-36'}
                    maxLength={5000}
                    {...field}
                  />
                </FormControl>

                <FormMessage />
              </FormItem>
            );
          }}
        />

        <Button disabled={pending} type={'submit'}>
          <Trans i18nKey={'marketing:sendMessage'} />
        </Button>
      </form>
    </Form>
  );
}
