import { Page, PageNavigation } from '@kit/ui/page';

import { SiteFooter } from '~/_components/site-footer';
import { SiteHeader } from '~/_components/site-header';
import { BackgroundHue } from '~/components/background-hue';
import { withI18n } from '~/lib/i18n/with-i18n';
import { loadUserWorkspace } from '~/lib/users/user-workspace.loader';

async function MarketingLayout(props: React.PropsWithChildren) {
  const workspace = await loadUserWorkspace();

  return (
    <>
      <Page style={'header'}>
        <PageNavigation>
          <SiteHeader workspace={workspace} />
        </PageNavigation>

        {props.children}
      </Page>
      <BackgroundHue />
      <SiteFooter />
    </>
  );
}

export default withI18n(MarketingLayout);
