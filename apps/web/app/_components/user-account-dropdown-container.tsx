'use client';

import type { User } from '@supabase/supabase-js';

import { useSignOut } from '@kit/supabase/hooks/use-sign-out';
import { useUser } from '@kit/supabase/hooks/use-user';

import pathsConfig from '~/config/paths.config';

import { UserAccountDropdown } from '../home/<USER>/user-account-dropdown';

const paths = {
  home: pathsConfig.app.home,
  userAccountSettings: pathsConfig.app.userAccountSettings,
};

export function UserAccountDropdownContainer(props: {
  user?: User;
  showProfileName?: boolean;
}) {
  const signOut = useSignOut();
  const user = useUser(props.user);
  const userData = user.data;

  if (!userData) {
    return null;
  }

  return (
    <UserAccountDropdown
      className={'w-full'}
      paths={paths}
      user={userData}
      signOutRequested={() => signOut.mutateAsync()}
      showProfileName={props.showProfileName}
    />
  );
}

UserAccountDropdownContainer.displayName = 'UserAccountDropdownContainer';
