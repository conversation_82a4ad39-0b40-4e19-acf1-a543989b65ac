import { Header } from '@kit/ui/dojo/organisms/header';

import { AppLogo } from '~/components/app-logo';
import type { UserWorkspace } from '~/lib/users/user-workspace.loader';

import { SiteHeaderAccountSection } from './site-header-account-section';
import { SiteNavigation } from './site-navigation';

type SiteHeaderProps = {
  workspace: UserWorkspace | null;
};

export function SiteHeader(props: SiteHeaderProps) {
  return (
    <Header
      logo={<AppLogo />}
      navigation={<SiteNavigation />}
      actions={<SiteHeaderAccountSection workspace={props.workspace} />}
    />
  );
}
