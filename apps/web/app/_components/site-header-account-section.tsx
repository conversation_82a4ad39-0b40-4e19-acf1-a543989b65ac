'use client';

import dynamic from 'next/dynamic';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

import { ArrowRightIcon } from 'lucide-react';

import { useSignOut } from '@kit/supabase/hooks/use-sign-out';
import { Button } from '@kit/ui/button';
import { Trans } from '@kit/ui/trans';

import pathsConfig from '~/config/paths.config';
import type { UserWorkspace } from '~/lib/users/user-workspace.loader';
import { HomeCommunitySelector } from '~/user/_components/home-community-selector';
import { UserAccountDropdown } from '~/user/_components/user-account-dropdown';
import { UserNotifications } from '~/user/_components/user-notifications';

const ModeToggle = dynamic(() =>
  import('@kit/ui/mode-toggle').then((mod) => ({
    default: mod.ModeToggle,
  })),
);

const paths = {
  home: pathsConfig.app.home,
  userAccountSettings: pathsConfig.app.userAccountSettings,
};

export function SiteHeaderAccountSection({
  workspace,
}: React.PropsWithChildren<{
  workspace: UserWorkspace | null;
}>) {
  if (!workspace?.user) {
    return <AuthButtons />;
  }

  return <SuspendedUserAccountDropdown workspace={workspace} />;
}

function SuspendedUserAccountDropdown(props: {
  workspace: UserWorkspace | null;
}) {
  const signOut = useSignOut();
  const pathname = usePathname();

  const currentRoute = pathname?.slice(1);

  const userData = props.workspace?.user ?? null;

  if (userData) {
    return (
      <>
        <UserNotifications userId={userData.id} />
        <HomeCommunitySelector
          communities={props.workspace?.communities ?? []}
          userId={userData.id}
          currentPath={currentRoute}
        />
        <UserAccountDropdown
          showProfileName={false}
          paths={paths}
          user={userData}
          signOutRequested={() => signOut.mutateAsync()}
        />
      </>
    );
  }

  return <AuthButtons />;
}

function AuthButtons() {
  return (
    <div className={'flex space-x-2'}>
      <div className={'hidden space-x-0.5 md:flex'}>
        <ModeToggle />

        <Button asChild variant={'ghost'}>
          <Link href={pathsConfig.auth.signIn}>
            <Trans i18nKey={'auth:signIn'} />
          </Link>
        </Button>
      </div>

      <Button asChild className="group" variant={'default'}>
        <Link href={pathsConfig.auth.signUp}>
          <Trans i18nKey={'auth:signUp'} />

          <ArrowRightIcon
            className={
              'ml-1 hidden h-4 w-4 transition-transform duration-500 group-hover:translate-x-1 lg:block'
            }
          />
        </Link>
      </Button>
    </div>
  );
}
