import {
  BorderedNavigationMenu,
  BorderedNavigationMenuItem,
} from '@kit/ui/bordered-navigation-menu';

import { userAccountNavigationConfig } from '~/home/<USER>/config/user-account-navigation.config';
import { type UserWorkspace } from '~/lib/users/user-workspace.loader';

export function HomeMenuNavigation(props: { workspace: UserWorkspace }) {
  const { user } = props.workspace;

  if (!user) {
    return null;
  }

  type RouteItem = {
    path: string;
    label: string;
    Icon?: React.ReactNode;
    end?: boolean | ((path: string) => boolean);
    renderAction?: React.ReactNode;
  };

  const routes = userAccountNavigationConfig.routes.reduce<RouteItem[]>(
    (acc, item) => {
      if ('children' in item && Array.isArray(item.children)) {
        return [...acc, ...item.children];
      }

      if ('divider' in item) {
        return acc;
      }

      return [...acc, item as RouteItem];
    },
    [],
  );

  return (
    <div className={'flex w-full flex-1 justify-between'}>
      <div className={'flex items-center space-x-8'}>
        <BorderedNavigationMenu>
          {routes.map((route) => (
            <BorderedNavigationMenuItem {...route} key={route.path} />
          ))}
        </BorderedNavigationMenu>
      </div>
    </div>
  );
}

HomeMenuNavigation.displayName = 'HomeMenuNavigation';
