'use client';

import { useContext } from 'react';

import { useRouter } from 'next/navigation';

import { SidebarContext } from '@kit/ui/shadcn-sidebar';

import pathsConfig from '~/config/paths.config';
import type { UserWorkspace } from '~/lib/users/user-workspace.loader';

import { CommunitySelector } from './community-selector';

type UserWorkspaceCommunity = UserWorkspace['communities'][number];

export function HomeCommunitySelector(props: {
  communities: UserWorkspaceCommunity[];
  userId: string;
  collisionPadding?: number;
  currentPath?: string;
}) {
  const router = useRouter();
  const context = useContext(SidebarContext);

  const handleCommunityChange = (value: string | undefined) => {
    const path = value
      ? pathsConfig.app.communityHome.replace('[community]', value)
      : pathsConfig.app.home;

    router.replace(path);
  };

  return (
    <CommunitySelector
      collapsed={context?.minimized}
      collisionPadding={props.collisionPadding ?? 20}
      communities={props.communities}
      userId={props.userId}
      onCommunityChange={handleCommunityChange}
      currentPath={props.currentPath}
    />
  );
}

HomeCommunitySelector.displayName = 'HomeCommunitySelector';
