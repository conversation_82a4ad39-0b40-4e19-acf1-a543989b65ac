import { use } from 'react';

import { PageBody } from '@kit/ui/page';

import authConfig from '~/config/auth.config';
import pathsConfig from '~/config/paths.config';
import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { withI18n } from '~/lib/i18n/with-i18n';
import { requireUserInServerComponent } from '~/lib/utils/require-user-in-server-component';

import { UserSettingsContainer } from './_components/user-settings-container';

const features = {
  enablePasswordUpdate: authConfig.providers.password,
};

const callbackPath = pathsConfig.auth.callback;
const accountHomePath = pathsConfig.app.communityHome;

const paths = {
  callback: callbackPath + `?next=${accountHomePath}`,
};

export const generateMetadata = async () => {
  const i18n = await createI18nServerInstance();
  const title = i18n.t('user:settingsTab');

  return {
    title,
  };
};

function PersonalAccountSettingsPage() {
  const user = use(requireUserInServerComponent());

  return (
    <PageBody>
      <div className={'flex w-full flex-1 flex-col'}>
        <UserSettingsContainer user={user} features={features} paths={paths} />
      </div>
    </PageBody>
  );
}

export default withI18n(PersonalAccountSettingsPage);

PersonalAccountSettingsPage.displayName = 'PersonalAccountSettingsPage';
