'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

// Import the image service
import { createImageStorageService } from '~/lib/images/services/image-storage.service';

const AVATARS_BUCKET = 'user_storage';

const DeleteProfilePhotoSchema = z.object({
  url: z.string().url(),
  userId: z.string(),
});

export const deleteProfilePhoto = enhanceAction(
  async ({
    url,
    userId,
  }: z.infer<typeof DeleteProfilePhotoSchema>): Promise<boolean> => {
    const client = getSupabaseServerClient();
    const logger = await getLogger();

    const imageService = createImageStorageService(client);
    const ctx = {
      functionName: 'deleteProfilePhoto',
      bucket: AVATARS_BUCKET,
      url,
      userId,
    };

    logger.info(
      ctx,
      'Attempting to delete user profile photo and update DB via service',
    );

    try {
      // Extract the path from the URL - ensure query params are stripped
      const urlObject = new URL(url);
      const filePath = urlObject.pathname
        .replace(`/storage/v1/object/public/${AVATARS_BUCKET}/`, '') // Remove storage prefix
        .split('?')[0]; // Remove query params just in case

      if (!filePath) {
        logger.warn(ctx, 'File path could not be determined from URL', { url });
        return false;
      }

      logger.info(ctx, 'Removing file via ImageStorageService', { filePath });

      // Step 1: Delete from storage using the service
      try {
        await imageService.deleteImage(filePath, AVATARS_BUCKET);
        logger.info(
          ctx,
          'Profile photo deleted successfully from storage via service',
        );
      } catch (storageError) {
        logger.error(
          {
            ctx,
            storageError,
          },
          'Failed to delete profile photo from storage via service',
        );
        // It's possible the file didn't exist (e.g., URL was stale),
        // but we might still want to clear the DB field.
        // Consider proceeding to DB update even if storage delete fails?
        // For now, let's return false as the primary action failed.
        return false;
      }

      // Step 2: Update the users table
      try {
        const { error: updateError } = await client
          .from('users')
          .update({ picture_url: null })
          .eq('id', userId)
          .throwOnError();

        if (updateError) {
          logger.error(
            { ctx, error: updateError },
            'Storage deleted, but failed to update users table',
          );
          return false; // Indicate DB update failure
        }

        logger.info(ctx, 'Users table updated successfully');
        return true; // Indicate full success (storage and DB)
      } catch (dbError) {
        logger.error(
          { ctx, error: dbError },
          'Storage deleted, but encountered error updating users table',
        );
        return false; // Indicate DB update failure
      }
    } catch (error) {
      // Catch unexpected errors
      logger.error(
        { ...ctx, error },
        'Error during profile photo delete/update process',
      );
      throw error; // Let enhanceAction handle it
    }
  },
  {
    auth: true,
    schema: DeleteProfilePhotoSchema,
  },
);
