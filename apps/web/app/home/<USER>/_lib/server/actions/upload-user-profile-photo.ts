'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

// Import the image service and context type
import { createImageStorageService } from '~/lib/images/services/image-storage.service';
import type { UploadImageContext } from '~/lib/images/types';

const AVATARS_BUCKET = 'user_storage';

export const uploadUserProfilePhoto = enhanceAction(
  async ({
    photoFile,
    userId,
  }: {
    photoFile: File;
    userId: string;
  }): Promise<string> => {
    const logger = await getLogger();
    const ctx = {
      functionName: 'uploadUserProfilePhotoViaService',
      bucket: AVATARS_BUCKET,
      userId,
    };

    const client = getSupabaseServerClient();

    const imageService = createImageStorageService(client);
    logger.info(ctx, 'Uploading user profile photo via service');

    // Validate file size (e.g., 5MB limit)
    const MAX_SIZE = 5 * 1024 * 1024;
    if (photoFile.size > MAX_SIZE) {
      logger.error(ctx, 'File size exceeds the 5MB limit');
      throw new Error('File size exceeds the 5MB limit');
    }

    // Validate file type
    const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'image/webp'];
    if (!ALLOWED_TYPES.includes(photoFile.type)) {
      logger.error(ctx, 'Invalid file type');
      throw new Error('Invalid file type. Only JPEG, PNG and WebP are allowed');
    }

    // Sanitize file extension - still needed for filename generation
    const extension = photoFile.name.split('.').pop()?.toLowerCase();
    if (!extension || !['jpg', 'jpeg', 'png', 'webp'].includes(extension)) {
      logger.error(ctx, 'Invalid file extension');
      throw new Error('Invalid file extension');
    }

    try {
      // Determine the target filename (consistent profile photo name)
      const targetFilename = `${userId}-profile.${extension}`;
      // Storage directory path within the bucket is just the userId
      const storageDirPath = userId;

      // Prepare context for the image service
      // Omit communityId and featureDir as they don't apply and would break the path
      const uploadContext: UploadImageContext = {
        storageDirPath: storageDirPath, // userId is the main identifier
        targetFilename: targetFilename,
        // subPath is not needed
      };

      logger.info(ctx, 'Calling ImageStorageService.uploadImage', {
        context: uploadContext,
        bucket: AVATARS_BUCKET,
      });

      // Step 1: Upload image using the service, specifying the bucket
      // Service handles validation, path construction, upload, upsert, and cache-busting URL
      const uploadResult = await imageService.uploadImage(
        photoFile,
        uploadContext,
        AVATARS_BUCKET, // Specify the correct bucket
      );

      const finalUrl = uploadResult.url; // URL from service includes timestamp
      logger.info(ctx, 'Upload via service successful', {
        publicUrl: finalUrl,
      });

      // Step 2: Update the users table with the new URL
      try {
        const { error: updateError } = await client
          .from('users')
          .update({ picture_url: finalUrl })
          .eq('id', userId)
          .throwOnError();

        if (updateError) {
          logger.error(
            ctx,
            'Upload successful, but failed to update users table',
            updateError,
          );
          throw new Error('Failed to update database after image upload.');
        }

        logger.info(ctx, 'Users table updated successfully');
        return finalUrl; // Return the final URL
      } catch (dbError) {
        logger.error(
          ctx,
          'Upload successful, but encountered error updating users table',
          dbError,
        );
        throw new Error('Database error after image upload.');
      }
    } catch (error) {
      // Catch errors from service call (validation, upload) or DB update
      logger.error({ ...ctx, error }, 'Error during upload/DB update process');
      throw error; // Let enhanceAction handle it
    }
  },
  {
    auth: true,
    schema: z.object({
      photoFile: z.instanceof(File),
      userId: z.string(),
    }),
  },
);
