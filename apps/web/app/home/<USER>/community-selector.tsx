'use client';

import { useState } from 'react';

import Link from 'next/link';
import { useRouter } from 'next/navigation';

import { CaretSortIcon, PersonIcon } from '@radix-ui/react-icons';
import { CheckCircle, Plus, Telescope } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { Avatar, AvatarFallback, AvatarImage } from '@kit/ui/avatar';
import { Button } from '@kit/ui/button';
import {
  Command,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from '@kit/ui/command';
import { If } from '@kit/ui/if';
import { Popover, PopoverContent, PopoverTrigger } from '@kit/ui/popover';
import { Separator } from '@kit/ui/separator';
import { Trans } from '@kit/ui/trans';
import { cn } from '@kit/ui/utils';

import pathsConfig from '~/config/paths.config';
import { useUserData } from '~/lib/users/hooks/use-user-data';
import type { UserWorkspace } from '~/lib/users/user-workspace.loader';
import { castToUserData } from '~/lib/users/utils/type-helpers';

type UserWorkspaceCommunity = UserWorkspace['communities'][number];

type CommunitySelectorProps = {
  communities: UserWorkspaceCommunity[];
  userId: string;
  currentPath?: string;
  collapsed?: boolean;
  className?: string;
  collisionPadding?: number;
  onCommunityChange: (value: string | undefined) => void;
};

export function CommunitySelector({
  communities,
  currentPath,
  onCommunityChange,
  userId,
  className,
  collapsed = false,
  collisionPadding = 20,
}: React.PropsWithChildren<CommunitySelectorProps>) {
  const [open, setOpen] = useState<boolean>(false);
  const router = useRouter();
  const { t } = useTranslation('communities');
  const personalData = useUserData(userId);

  const Icon = (props: { item: string }) => {
    return (
      <CheckCircle
        className={cn(
          'ml-auto h-4 w-4',
          currentPath === props.item ? 'opacity-100' : 'opacity-0',
        )}
      />
    );
  };

  const selected = communities.find(
    (community) => community.slug === currentPath,
  );
  const userData = personalData.data ? castToUserData(personalData.data) : null;
  const pictureUrl = userData?.pictureUrl;

  const PersonalAccountAvatar = () =>
    pictureUrl ? (
      <UserAvatar pictureUrl={pictureUrl} />
    ) : (
      <PersonIcon className="h-5 min-h-5 w-5 min-w-5 rounded-lg" />
    );

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild className="cursor-pointer">
          <Button
            data-test={'account-selector-trigger'}
            size={collapsed ? 'icon' : 'default'}
            variant="ghost"
            role="combobox"
            aria-expanded={open}
            className={cn(
              'dark:shadow-primary/10 group w-full min-w-0 px-2 lg:w-auto lg:max-w-fit',
              {
                'justify-start': !collapsed,
                'm-auto justify-center px-2 lg:w-full': collapsed,
              },
              className,
            )}
          >
            <If
              condition={selected}
              fallback={
                <span className={'flex max-w-full items-center space-x-4'}>
                  <PersonalAccountAvatar />

                  <span
                    className={cn('truncate', {
                      hidden: collapsed,
                    })}
                  >
                    <Trans i18nKey={'communities:personalAccount'} />
                  </span>
                </span>
              }
            >
              {(community) => (
                <span className={'flex max-w-full items-center space-x-4'}>
                  <Avatar className={'h-6 w-6 rounded-sm'}>
                    <AvatarImage
                      src={community.logoUrl ?? undefined}
                      className="rounded-lg"
                    />

                    <AvatarFallback
                      className={'group-hover:bg-background rounded-sm'}
                    >
                      {community.name ? community.name[0] : ''}
                    </AvatarFallback>
                  </Avatar>

                  <span
                    className={cn('truncate', {
                      hidden: collapsed,
                    })}
                  >
                    {community.name}
                  </span>
                </span>
              )}
            </If>

            <CaretSortIcon
              className={cn('ml-2 h-4 w-4 shrink-0 opacity-50', {
                hidden: collapsed,
              })}
            />
          </Button>
        </PopoverTrigger>

        <PopoverContent
          data-test={'account-selector-content'}
          className="w-full p-0"
          collisionPadding={collisionPadding}
        >
          <Command>
            <CommandInput
              placeholder={t('searchCommunities')}
              className="h-9"
            />

            <CommandList>
              <CommandGroup>
                <CommandItem
                  onSelect={() => {
                    router.push(pathsConfig.app.explore);
                  }}
                  value={'explore'}
                  className="cursor-pointer"
                >
                  <Telescope className="h-4 w-4" />

                  <span className={'ml-2'}>
                    <Trans i18nKey={'explore:exploreCommunities'} />
                  </span>

                  <Icon item={'explore'} />
                </CommandItem>
              </CommandGroup>

              <CommandSeparator />
              <CommandGroup>
                <CommandItem
                  onSelect={() => onCommunityChange(undefined)}
                  value={'home'}
                  className="cursor-pointer"
                >
                  <PersonalAccountAvatar />

                  <span className={'ml-2'}>
                    <Trans i18nKey={'communities:personalAccount'} />
                  </span>

                  <Icon item={'home'} />
                </CommandItem>
              </CommandGroup>

              <CommandSeparator />

              <If condition={communities.length > 0}>
                <CommandGroup
                  heading={
                    <Trans
                      i18nKey={'communities:yourCommunities'}
                      values={{ communitiesCount: communities.length }}
                    />
                  }
                >
                  {(communities ?? []).map((community) => (
                    <CommandItem
                      data-test={'community-selector-community'}
                      data-name={community.name}
                      data-slug={community.slug}
                      className={cn(
                        'group my-1 flex cursor-pointer justify-between transition-colors',
                        {
                          ['bg-muted']: currentPath === community.slug,
                        },
                      )}
                      key={community.slug}
                      value={community.slug ?? ''}
                      onSelect={(currentValue) => {
                        setOpen(false);

                        if (onCommunityChange) {
                          onCommunityChange(currentValue);
                        }
                      }}
                    >
                      <div className={'flex items-center'}>
                        <Avatar className={'mr-2 h-6 w-6 rounded-sm'}>
                          <AvatarImage
                            src={community.logoUrl ?? undefined}
                            className="rounded-lg"
                          />

                          <AvatarFallback
                            className={cn('rounded-lg', {
                              ['bg-background']: currentPath === community.slug,
                              ['group-hover:bg-background']:
                                currentPath !== community.slug,
                            })}
                          >
                            {community.name ? community.name[0] : ''}
                          </AvatarFallback>
                        </Avatar>

                        <span className={'mr-2 max-w-[165px] truncate'}>
                          {community.name}
                        </span>
                      </div>

                      <Icon item={community.slug ?? ''} />
                    </CommandItem>
                  ))}
                </CommandGroup>
              </If>
            </CommandList>
          </Command>

          <Separator />

          <div className={'p-1'}>
            <Link href={pathsConfig.app.createCommunity}>
              <Button
                data-test={'create-community-trigger'}
                variant="ghost"
                size={'sm'}
                className="w-full justify-start text-sm font-normal"
              >
                <Plus className="mr-3 h-4 w-4" />

                <span>
                  <Trans i18nKey={'communities:createCommunity'} />
                </span>
                <Icon item={'create-community'} />
              </Button>
            </Link>
          </div>
        </PopoverContent>
      </Popover>
    </>
  );
}

// TODO: Move to a shared component
function UserAvatar(props: { pictureUrl?: string }) {
  return (
    <Avatar className={'h-6 w-6 rounded-lg'}>
      <AvatarImage src={props.pictureUrl} />
    </Avatar>
  );
}

CommunitySelector.displayName = 'CommunitySelector';
UserAvatar.displayName = 'UserAvatar';
