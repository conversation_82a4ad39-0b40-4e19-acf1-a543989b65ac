'use server';

import { enhanceAction } from '@kit/next/actions';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createUsersService } from '~/lib/users/services/users.service';

export const toggleUserCommunityVisibilityAction = enhanceAction(
  async function (params: {
    userId: string;
    communityId: string;
    visibleOnProfile: boolean;
  }) {
    const client = getSupabaseServerClient();

    const usersService = createUsersService(client);

    const communityMembership =
      await usersService.toggleUserCommunityVisibility(
        params.userId,
        params.communityId,
        params.visibleOnProfile,
      );

    return {
      communityMembership,
    };
  },
  {
    auth: true,
  },
);
