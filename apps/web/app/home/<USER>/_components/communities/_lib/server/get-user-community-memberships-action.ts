'use server';

import { enhanceAction } from '@kit/next/actions';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import type { UserCommunity } from '~/lib/communities/community/types';
import { createUsersService } from '~/lib/users/services/users.service';

export const getUserCommunityMembershipsAction = enhanceAction(
  async function () {
    const client = getSupabaseServerClient();

    const usersService = createUsersService(client);

    const communities = await usersService.loadUserCommunities();

    return {
      communities: communities as UserCommunity[],
    };
  },
  {
    auth: true,
  },
);
