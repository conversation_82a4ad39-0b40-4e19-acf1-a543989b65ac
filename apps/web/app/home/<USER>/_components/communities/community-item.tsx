'use client';

import Image from 'next/image';
import Link from 'next/link';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import * as changeCase from 'change-case';
import { Building2, Eye, EyeOff } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';

import { Badge } from '@kit/ui/badge';
import { If } from '@kit/ui/if';
import { Label } from '@kit/ui/label';
import { Switch } from '@kit/ui/switch';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@kit/ui/tooltip';

import type { UserCommunity } from '~/lib/communities/community/types';

import { toggleUserCommunityVisibilityAction } from './_lib/server/toggle-user-community-visibility-action';

type CommunityItemProps = {
  community: UserCommunity;
};

export function CommunityItem(props: CommunityItemProps) {
  const { community } = props;
  const { t } = useTranslation('communities');
  const queryClient = useQueryClient();

  const formattedCategory = t(
    `communities:categories.${community.categoryName}`,
  );

  const { mutate: toggleVisibility, isPending } = useMutation({
    mutationFn: async (visibleOnProfile: boolean) => {
      return toggleUserCommunityVisibilityAction({
        userId: community.userId,
        communityId: community.id,
        visibleOnProfile,
      });
    },
    onSuccess: (_, visibleOnProfile) => {
      toast.success(
        visibleOnProfile
          ? t('communities:visibility.shown_on_profile')
          : t('communities:visibility.hidden_from_profile'),
      );

      // Invalidate query to ensure consistent state across components
      queryClient.invalidateQueries({ queryKey: ['user-communities'] });
    },
    onError: () => {
      toast.error(t('communities:visibility.toggle_error'));
    },
  });

  const handleVisibilityToggle = (checked: boolean) => {
    toggleVisibility(checked);
  };

  return (
    <div className="mb-4 flex flex-row items-center justify-between">
      <div className="flex items-center">
        <div className="mr-2">
          <If condition={community.logoUrl}>
            <Image
              src={community.logoUrl}
              alt={community.name}
              width={50}
              height={50}
              sizes="100vw"
              className="h-10 w-10 rounded-lg"
            />
          </If>
          <If condition={!community.logoUrl}>
            <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-gray-200">
              <Building2 className="h-5 w-5 text-gray-400" />
            </div>
          </If>
        </div>
        <div>
          <Link href={`/${community.slug}`}>
            <div className="font-bold">{community.name}</div>
          </Link>
        </div>
      </div>
      <div className="flex items-center justify-end gap-4">
        <div className="flex items-end justify-end gap-2">
          <Badge variant="secondary">
            <div className="flex items-center gap-2">
              {formattedCategory}
              <div className="h-4 w-4">{community.categoryIcon}</div>
            </div>
          </Badge>
          <Badge>{changeCase.sentenceCase(community.role)}</Badge>
        </div>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div
                className="flex items-center space-x-2"
                data-test="community-visibility-toggle"
              >
                <Switch
                  id={`visibility-${community.id}`}
                  checked={community.visibleOnProfile}
                  disabled={isPending}
                  onCheckedChange={handleVisibilityToggle}
                />
                <Label
                  htmlFor={`visibility-${community.id}`}
                  className="text-muted-foreground cursor-pointer text-sm"
                >
                  {community.visibleOnProfile ? (
                    <Eye className="h-4 w-4" />
                  ) : (
                    <EyeOff className="h-4 w-4" />
                  )}
                </Label>
              </div>
            </TooltipTrigger>
            <TooltipContent>
              {community.visibleOnProfile
                ? t('communities:visibility.shown_tooltip')
                : t('communities:visibility.hidden_tooltip')}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </div>
  );
}

CommunityItem.displayName = 'CommunityItem';
