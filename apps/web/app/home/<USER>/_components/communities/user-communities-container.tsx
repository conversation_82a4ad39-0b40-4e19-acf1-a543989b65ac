'use client';

import { useQuery } from '@tanstack/react-query';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { If } from '@kit/ui/if';
import { Spinner } from '@kit/ui/spinner';
import { Trans } from '@kit/ui/trans';

import { getUserCommunityMembershipsAction } from './_lib/server/get-user-community-memberships-action';
import { CommunityItem } from './community-item';

export function UserCommunitiesContainer() {
  const { data: communitiesData, isLoading } = useQuery({
    queryKey: ['user-communities'],
    queryFn: async () => {
      const result = await getUserCommunityMembershipsAction({});
      return result;
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  const communities = communitiesData?.communities || [];

  return (
    <Card>
      <CardHeader className="flex flex-row justify-between">
        <div className="flex flex-col gap-2">
          <CardTitle className="mr-2">
            <Trans i18nKey={'user:settings.communitiesTitle'} />
          </CardTitle>
          <CardDescription>
            <Trans i18nKey={'user:settings.communitiesDescription'} />
          </CardDescription>
        </div>
      </CardHeader>

      <CardContent>
        <If condition={isLoading}>
          <div className="flex justify-center py-4">
            <Spinner />
          </div>
        </If>
        <If condition={!isLoading && communities.length > 0}>
          {communities.map((community) => (
            <CommunityItem key={community.id} community={community} />
          ))}
        </If>
        <If condition={!isLoading && communities.length === 0}>
          <p className="text-muted-foreground text-center">
            <Trans i18nKey="user:settings.noCommunitiesYet" />
          </p>
        </If>
      </CardContent>
    </Card>
  );
}

UserCommunitiesContainer.displayName = 'UserCommunitiesContainer';
