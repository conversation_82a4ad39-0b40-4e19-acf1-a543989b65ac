'use client';

import { toast } from 'sonner';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { Trans } from '@kit/ui/trans';

import { PaymentMethodsList } from '~/lib/stripe/components/billing/payment-methods-list';

export function UserPaymentMethodsContainer({ userId }: { userId: string }) {
  // Event handlers for payment method operations
  const handlePaymentMethodAdded = () => {
    toast.success(<Trans i18nKey="user:paymentMethodAdded" />);
  };

  const handlePaymentMethodAddError = (error: string) => {
    toast.error(
      <Trans i18nKey="user:paymentMethodAddError" values={{ error }} />,
    );
  };

  const handlePaymentMethodRemoved = () => {
    toast.success(
      <Trans i18nKey="billing:paymentMethods.paymentMethodRemoved" />,
    );
  };

  const handlePaymentMethodRemoveError = (error: string) => {
    toast.error(
      <Trans
        i18nKey="billing:paymentMethods.paymentMethodRemoveError"
        values={{ error }}
      />,
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          <Trans i18nKey={'billing:paymentMethods.title'} />
        </CardTitle>

        <CardDescription>
          <Trans i18nKey={'billing:paymentMethods.description'} />
        </CardDescription>
      </CardHeader>

      <CardContent>
        <PaymentMethodsList
          userId={userId}
          onPaymentMethodAdded={handlePaymentMethodAdded}
          onPaymentMethodAddError={handlePaymentMethodAddError}
          onPaymentMethodRemoved={handlePaymentMethodRemoved}
          onPaymentMethodRemoveError={handlePaymentMethodRemoveError}
        />
      </CardContent>
    </Card>
  );
}

UserPaymentMethodsContainer.displayName = 'UserPaymentMethodsContainer';
