'use client';

import { useFormStatus } from 'react-dom';

import { zodResolver } from '@hookform/resolvers/zod';
import { type Resolver, useForm } from 'react-hook-form';

import { ErrorBoundary } from '@kit/monitoring/components';
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@kit/ui/alert-dialog';
import { Button } from '@kit/ui/button';
import { StatusAlert } from '@kit/ui/dojo/molecules/status-alert';
import { Form, FormControl, FormItem, FormLabel } from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { Trans } from '@kit/ui/trans';

import {
  DeleteUserAccountFormValues,
  DeleteUserAccountSchema,
} from '~/lib/users/schema/delete-user-account.schema';
import { deleteUserAction } from '~/lib/users/user-server-actions';

// TODO: Break into smaller components
export function UserDangerZone() {
  return (
    <div className={'flex flex-col space-y-4'}>
      <div className={'flex flex-col space-y-1'}>
        <span className={'text-sm font-medium'}>
          <Trans i18nKey={'user:deleteAccount'} />
        </span>

        <p className={'text-muted-foreground text-sm'}>
          <Trans i18nKey={'user:deleteAccountDescription'} />
        </p>
      </div>

      <div>
        <DeleteUserModal />
      </div>
    </div>
  );
}

function DeleteUserModal() {
  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button data-test={'delete-user-button'} variant={'destructive'}>
          <Trans i18nKey={'user:deleteAccount'} />
        </Button>
      </AlertDialogTrigger>

      <AlertDialogContent onEscapeKeyDown={(e) => e.preventDefault()}>
        <AlertDialogHeader>
          <AlertDialogTitle>
            <Trans i18nKey={'user:deleteAccount'} />
          </AlertDialogTitle>
        </AlertDialogHeader>

        <ErrorBoundary
          fallback={
            <StatusAlert
              variant="destructive"
              titleKey="user:deleteAccountErrorHeading"
              descriptionKey="common:genericError"
            />
          }
        >
          <DeleteAccountForm />
        </ErrorBoundary>
      </AlertDialogContent>
    </AlertDialog>
  );
}

function DeleteAccountForm() {
  const form = useForm<DeleteUserAccountFormValues>({
    resolver: zodResolver(
      DeleteUserAccountSchema,
    ) as unknown as Resolver<DeleteUserAccountFormValues>,
    defaultValues: {
      confirmation: '',
    },
  });

  return (
    <Form {...form}>
      <form
        data-test={'delete-account-form'}
        action={deleteUserAction}
        className={'flex flex-col space-y-4'}
      >
        <div className={'flex flex-col space-y-6'}>
          <div
            className={'border-destructive text-destructive border p-4 text-sm'}
          >
            <div className={'flex flex-col space-y-2'}>
              <div>
                <Trans i18nKey={'user:deleteAccountDescription'} />
              </div>

              <div>
                <Trans i18nKey={'common:modalConfirmationQuestion'} />
              </div>
            </div>
          </div>

          <FormItem>
            <FormLabel>
              <Trans i18nKey={'user:deleteProfileConfirmationInputLabel'} />
            </FormLabel>

            <FormControl>
              <Input
                autoComplete={'off'}
                data-test={'delete-account-input-field'}
                required
                name={'confirmation'}
                type={'text'}
                className={'w-full'}
                placeholder={''}
                pattern={`DELETE`}
              />
            </FormControl>
          </FormItem>
        </div>

        <AlertDialogFooter>
          <AlertDialogCancel>
            <Trans i18nKey={'common:cancel'} />
          </AlertDialogCancel>

          <DeleteAccountSubmitButton />
        </AlertDialogFooter>
      </form>
    </Form>
  );
}

function DeleteAccountSubmitButton() {
  const { pending } = useFormStatus();

  return (
    <Button
      data-test={'confirm-delete-account-button'}
      type={'submit'}
      disabled={pending}
      name={'action'}
      variant={'destructive'}
    >
      {pending ? (
        <Trans i18nKey={'user:deletingAccount'} />
      ) : (
        <Trans i18nKey={'user:deleteAccount'} />
      )}
    </Button>
  );
}

UserDangerZone.displayName = 'UserDangerZone';
DeleteUserModal.displayName = 'DeleteUserModal';
DeleteAccountForm.displayName = 'DeleteAccountForm';
DeleteAccountSubmitButton.displayName = 'DeleteAccountSubmitButton';
