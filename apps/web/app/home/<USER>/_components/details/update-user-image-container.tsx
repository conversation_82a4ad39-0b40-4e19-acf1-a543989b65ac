'use client';

import { useCallback } from 'react';

import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';

import { ImageUploadContainer } from '@kit/ui/dojo/organisms/image-upload-container';

import { useRevalidateUserDataQuery } from '~/lib/users/hooks/use-user-data';

import { deleteProfilePhoto } from '../../_lib/server/actions/delete-user-profile-photo';
import { uploadUserProfilePhoto } from '../../_lib/server/actions/upload-user-profile-photo';

export function UpdateUserImageContainer({
  user,
}: {
  user: {
    pictureUrl: string | null;
    id: string;
  };
}) {
  const revalidateUserDataQuery = useRevalidateUserDataQuery();

  return (
    <UploadProfileAvatarForm
      pictureUrl={user.pictureUrl ?? null}
      userId={user.id}
      onAvatarUpdated={() => revalidateUserDataQuery(user.id)}
    />
  );
}

type Props = {
  userId: string;
  pictureUrl: string | null;
  onAvatarUpdated: () => void;
};

export function UploadProfileAvatarForm(props: Props) {
  const { userId, pictureUrl, onAvatarUpdated } = props;

  const { t } = useTranslation('user');

  const createToaster = useCallback(
    (promise: () => Promise<unknown>) => {
      return toast.promise(promise, {
        success: t(`updateProfileSuccess`),
        error: t(`updateProfileError`),
        loading: t(`updateProfileLoading`),
      });
    },
    [t],
  );

  const handleDelete = useCallback(async () => {
    const promise = async () => {
      // Only call delete action if pictureUrl exists
      if (pictureUrl) {
        await deleteProfilePhoto({
          url: pictureUrl,
          userId,
        });
      }
      // Revalidate user data after deletion attempt
      onAvatarUpdated();
    };

    await createToaster(promise);
  }, [createToaster, pictureUrl, userId, onAvatarUpdated]);

  const handleUpload = useCallback(
    async (file: File) => {
      const promise = async () => {
        // Delete existing file first if it exists
        if (pictureUrl) {
          try {
            await deleteProfilePhoto({
              url: pictureUrl,
              userId,
            });
          } catch (error) {
            // Log error but continue to upload new file
            console.error('Failed to delete previous avatar:', error);
          }
        }

        await uploadUserProfilePhoto({
          photoFile: file,
          userId,
        });

        onAvatarUpdated();
      };

      await createToaster(promise);
    },
    [createToaster, pictureUrl, userId, onAvatarUpdated],
  );

  return (
    <ImageUploadContainer
      imageUrl={pictureUrl}
      headingKey="user:profilePictureHeading"
      uploadHeadingKey="user:profilePictureUploadHeading"
      uploadSubheadingKey="user:profilePictureSubheading"
      // --- Styling --- //
      imageRounded="rounded-full"
      imageSize="w-20 h-20"
      aspectRatio={1}
      cropShape="round"
      // --- Callbacks --- //
      onDelete={handleDelete}
      onUpload={handleUpload}
    />
  );
}

UploadProfileAvatarForm.displayName = 'UploadProfileAvatarForm';
