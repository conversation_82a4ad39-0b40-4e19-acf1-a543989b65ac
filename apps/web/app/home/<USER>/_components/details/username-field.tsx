import { useCallback, useEffect, useState } from 'react';

import { useWatch } from 'react-hook-form';
import { UseFormReturn } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { Trans } from '@kit/ui/trans';

import { useCheckUsernameAvailability } from '~/lib/users/hooks/use-check-username-availability';

// Make the component accept any form type with a username property
type UsernameFieldProps = {
  form: UseFormReturn<{ username: string }>;
  userId: string;
  initialUsername: string;
  onAvailabilityChange: (isAvailable: boolean | null) => void;
  onCheckingChange: (isChecking: boolean) => void;
};

export function UsernameField({
  form,
  userId,
  initialUsername,
  onAvailabilityChange,
  onCheckingChange,
}: UsernameFieldProps) {
  const [usernameAvailable, setUsernameAvailable] = useState<boolean | null>(
    null,
  );
  const [isChecking, setIsChecking] = useState(false);
  const [hasBeenEdited, setHasBeenEdited] = useState(false);
  const { checkAvailability } = useCheckUsernameAvailability();

  const { t } = useTranslation('user');

  const username = useWatch({
    name: 'username',
    defaultValue: initialUsername,
    control: form.control,
  });

  useEffect(() => {
    if (!username || username === '') {
      setUsernameAvailable(null);
      onAvailabilityChange(null);
      setIsChecking(false);
      return;
    }

    // Only check availability if the field has been edited and the value is different from initial
    if (hasBeenEdited && String(username) !== initialUsername) {
      setIsChecking(true);
      void checkAvailability?.(String(username), userId);
    } else if (String(username) === initialUsername) {
      // Reset states when returning to initial value
      setUsernameAvailable(null);
      onAvailabilityChange(true); // Allow form submission with original username
      setIsChecking(false);
    }
  }, [
    username,
    userId,
    checkAvailability,
    onAvailabilityChange,
    hasBeenEdited,
    initialUsername,
  ]);

  // Add a callback to handle the result
  const handleAvailabilityResult = useCallback(
    (isAvailable: boolean | null) => {
      setUsernameAvailable(isAvailable);
      onAvailabilityChange(isAvailable);
      setIsChecking(false);
    },
    [onAvailabilityChange],
  );

  // Register the callback with the hook
  useEffect(() => {
    if (checkAvailability) {
      checkAvailability.subscribe(handleAvailabilityResult);
      return () => checkAvailability.unsubscribe(handleAvailabilityResult);
    }
  }, [checkAvailability, handleAvailabilityResult]);

  // Add effect to notify parent of checking state changes
  useEffect(() => {
    onCheckingChange(isChecking);
  }, [isChecking, onCheckingChange]);

  return (
    <FormField
      name="username"
      render={({ field }) => (
        <FormItem>
          <FormLabel>
            <Trans i18nKey={'user:username'} />
          </FormLabel>

          <FormControl>
            <Input
              data-test={'account-username'}
              minLength={2}
              placeholder={t('usernamePlaceholder')}
              maxLength={50}
              pattern="[a-z0-9\-]*"
              inputMode="text"
              autoCorrect="off"
              autoCapitalize="none"
              spellCheck={false}
              {...field}
              onChange={(e) => {
                // Remove any characters that aren't lowercase alphanumeric or dash
                const sanitized = e.target.value.replace(/[^a-z0-9-]/g, '');
                setHasBeenEdited(true);
                field.onChange(sanitized);
              }}
              onKeyDown={(e) => {
                // Only allow alphanumeric, dash, and control keys
                if (
                  !/^[a-z0-9-]$/.test(e.key) &&
                  ![
                    'Backspace',
                    'Delete',
                    'ArrowLeft',
                    'ArrowRight',
                    'Tab',
                  ].includes(e.key)
                ) {
                  e.preventDefault();
                }
              }}
            />
          </FormControl>

          <FormMessage>
            {field.value &&
              hasBeenEdited &&
              field.value !== initialUsername && (
                <>
                  {isChecking ? (
                    <span className="text-gray-400">
                      <Trans i18nKey="user:usernameCheckingMessage" />
                    </span>
                  ) : usernameAvailable === true ? (
                    <span className="text-green-500">
                      <Trans i18nKey="user:usernameAvailableMessage" />
                    </span>
                  ) : usernameAvailable === false ? (
                    <span className="text-red-500">
                      <Trans i18nKey="user:usernameNotAvailableMessage" />
                    </span>
                  ) : null}
                </>
              )}
          </FormMessage>
        </FormItem>
      )}
    />
  );
}

UsernameField.displayName = 'UsernameField';
