'use client';

import { useRevalidateUserDataQuery } from '~/lib/users/hooks/use-user-data';

import { UpdateUserDetailsForm } from './update-user-details-form';

export function UpdateUserDetailsFormContainer({
  user,
  countries,
}: {
  user: {
    firstName: string;
    lastName: string | null;
    bio: string | null;
    username: string;
    id: string;
    countryId: string;
  };
  countries: {
    id: string;
    name: string;
    icon: string;
  }[];
}) {
  const revalidateUserDataQuery = useRevalidateUserDataQuery();

  // Make sure the firstName is properly defined
  const firstName = typeof user.firstName === 'string' ? user.firstName : '';

  return (
    <UpdateUserDetailsForm
      firstName={firstName}
      lastName={user.lastName ?? ''}
      bio={user.bio ?? ''}
      username={user.username}
      userId={user.id}
      countryId={user.countryId}
      countries={countries}
      onUpdate={async () => {
        await revalidateUserDataQuery(user.id);
      }}
    />
  );
}

UpdateUserDetailsFormContainer.displayName = 'UpdateUserDetailsFormContainer';
