import { useState } from 'react';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';

import { Button } from '@kit/ui/button';
import { IconTextSelector } from '@kit/ui/dojo/molecules/icon-text-selector';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { Textarea } from '@kit/ui/textarea';
import { Trans } from '@kit/ui/trans';

import { useUpdateUserData } from '~/lib/users/hooks/use-update-user';
import { UserDetailsSchema } from '~/lib/users/schema/user-details.schema';
import { UpdateUser } from '~/lib/users/types';

import { UsernameField } from './username-field';

export function UpdateUserDetailsForm({
  firstName,
  lastName,
  bio,
  username,
  onUpdate,
  userId,
  countryId,
  countries,
}: {
  firstName: string;
  lastName: string | null;
  bio: string | null;
  username: string;
  userId: string;
  countryId: string;
  countries: {
    id: string;
    name: string;
    icon: string;
  }[];
  onUpdate: (user: Partial<UpdateUser>) => void;
}) {
  const form = useForm({
    resolver: zodResolver(UserDetailsSchema),
    defaultValues: {
      firstName: firstName,
      lastName: lastName ?? '',
      bio: bio ?? '',
      username: username ?? '',
      socials: [], // This won't be used, just to satisfy the schema
      countryId: countryId ?? '',
    },
  });

  const updateUserMutation = useUpdateUserData(userId);
  const { t } = useTranslation('user');

  const [isUsernameAvailable, setIsUsernameAvailable] = useState<
    boolean | null
  >(true);
  const [isCheckingUsername, setIsCheckingUsername] = useState(false);

  const onSubmit = (formValues: {
    firstName: string;
    lastName?: string;
    bio?: string;
    username: string;
    socials: Array<{ name: string; url: string }>;
    countryId: string;
  }) => {
    // Just update the bio, our hook will handle merging with existing user_details
    const data = {
      first_name: formValues.firstName,
      last_name: formValues.lastName || null,
      user_details: { bio: formValues.bio || '' },
      username: formValues.username,
      country_id: formValues.countryId,
    };

    const promise = updateUserMutation.mutateAsync(data).then(() => {
      onUpdate(data);
    });

    return toast.promise(() => promise, {
      success: t(`updateProfileSuccess`),
      error: t(`updateProfileError`),
      loading: t(`updateProfileLoading`),
    });
  };

  return (
    <div className={'flex flex-col space-y-8'}>
      <Form {...form}>
        <form
          data-test={'update-account-name-form'}
          className={'flex flex-col space-y-4'}
          onSubmit={form.handleSubmit(onSubmit)}
        >
          <FormField
            name={'firstName'}
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  <Trans i18nKey={'user:firstName'} />
                </FormLabel>

                <FormControl>
                  <Input
                    data-test={'account-first-name'}
                    minLength={2}
                    placeholder={t('firstNamePlaceholder')}
                    maxLength={100}
                    {...field}
                  />
                </FormControl>

                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            name={'lastName'}
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  <Trans i18nKey={'user:lastName'} />
                </FormLabel>

                <FormControl>
                  <Input
                    data-test={'account-last-name'}
                    minLength={2}
                    placeholder={t('lastNamePlaceholder')}
                    maxLength={100}
                    {...field}
                  />
                </FormControl>

                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            name={'bio'}
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  <Trans i18nKey={'user:bio'} />
                </FormLabel>

                <FormControl>
                  <Textarea
                    data-test={'account-bio'}
                    minLength={2}
                    placeholder={t('bioPlaceholder')}
                    maxLength={1000}
                    className="h-32"
                    {...field}
                  />
                </FormControl>

                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            name={'countryId'}
            render={() => {
              return (
                <FormItem>
                  <FormLabel>
                    <Trans i18nKey={'user:country'} />
                  </FormLabel>
                  <FormControl>
                    <IconTextSelector
                      placeholder={'Select a country'}
                      description={t('countryDescription')}
                      currentId={countryId}
                      onChange={(selectedCountry: string) => {
                        form.setValue('countryId', selectedCountry);
                      }}
                      items={countries.map((country) => ({
                        id: country.id,
                        name: country.name,
                        icon: country.icon,
                      }))}
                    />
                  </FormControl>
                </FormItem>
              );
            }}
          />
          <UsernameField
            // @ts-expect-error - The form type is compatible despite what TypeScript thinks
            form={form}
            initialUsername={username}
            userId={userId}
            onAvailabilityChange={setIsUsernameAvailable}
            onCheckingChange={setIsCheckingUsername}
          />

          <div>
            <Button
              disabled={isUsernameAvailable === false || isCheckingUsername}
              type="submit"
              data-test="update-profile-submit"
            >
              <Trans i18nKey={'user:updateProfileSubmitLabel'} />
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}

UpdateUserDetailsForm.displayName = 'UpdateUserDetailsForm';
