'use client';

import React, { useCallback, useEffect, useState } from 'react';

import { useRouter, useSearchParams } from 'next/navigation';

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import type Stripe from 'stripe';

import { ConnectRequirements } from '@kit/ui/dojo/molecules/connect-requirements';
import { ConnectAccountStatus } from '@kit/ui/dojo/molecules/connect-status-badge';
import { StripeAccountLoginCard } from '@kit/ui/dojo/organisms/stripe-account-login-card';
import { StripeAccountOnboardingCard } from '@kit/ui/dojo/organisms/stripe-account-onboarding-card';
import {
  ConnectSetupFormValues,
  StripeAccountSetupForm,
} from '@kit/ui/dojo/organisms/stripe-account-setup-form';

import { DbCountry } from '~/lib/communities/billing/types';
import { DbStripeAccount } from '~/lib/stripe/types';

import { addCountryToDbStripeAccountAction } from './_lib/server/actions/add-country-to-db-stripe-account';
import { createRecipientAccountLinksAction } from './_lib/server/actions/create-recipient-account-links';
import { createRecipientStripeAccountAction } from './_lib/server/actions/create-recipient-stripe-account-action';
import { getAccountSupportedCountriesAction } from './_lib/server/actions/get-account-supported-countries';
import { resetStripeAccountAction } from './_lib/server/actions/reset-connected-account';

type StripeConnectContainerProps = {
  userId: string;
  userEmail: string;
  countries: DbCountry[];
  stripeAccount: DbStripeAccount | null;
  linkData: { url: string; linkType: 'login' | 'onboarding' | 'setup' } | null;
};

export function StripeConnectContainer({
  userId,
  userEmail,
  countries,
  stripeAccount,
  linkData,
}: StripeConnectContainerProps) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const queryClient = useQueryClient();
  const [filteredCountries, setFilteredCountries] =
    useState<DbCountry[]>(countries);

  // Use optimistic state if available, otherwise fall back to initial props
  const stripeAccountId = stripeAccount?.id;

  useEffect(() => {
    const refreshStatus = searchParams.get('refresh_status');
    if (refreshStatus === 'success') {
      toast.info('Account status may take a moment to update.');
    }
  }, [searchParams]);

  const { data: supportedCountries } = useQuery({
    queryKey: ['supported-countries'],
    queryFn: async () => {
      try {
        // Call without a stripeAccountId to get platform supported countries
        return await getAccountSupportedCountriesAction({});
      } catch (error) {
        console.error('Error fetching supported countries:', error);
        toast.error('Failed to fetch supported countries');
        // Return null to indicate error, allowing useEffect to use full countries list
        return null;
      }
    },
  });

  useEffect(() => {
    if (supportedCountries === null) {
      // On error, use all countries as fallback
      setFilteredCountries(countries);
    } else if (supportedCountries && supportedCountries.length > 0) {
      // Filter countries based on supported_transfer_countries
      const filtered = countries.filter((country) =>
        supportedCountries.some(
          (supportedCountry) => supportedCountry === country.isoCode1,
        ),
      );
      setFilteredCountries(filtered);
    }
  }, [supportedCountries, countries]);

  const appUrl = process.env.NEXT_PUBLIC_APP_URL ?? window.location.origin;
  const settingsPath = '/home/<USER>';
  const refreshUrl = `${appUrl}${settingsPath}?tab=stripeAccount&refresh_status=success`;
  const returnUrl = `${appUrl}${settingsPath}?tab=stripeAccount`;

  const createLinkMutation = useMutation({
    mutationFn: async () => {
      if (!stripeAccountId) {
        throw new Error('No stripe account ID available');
      }

      const accountLink = await createRecipientAccountLinksAction({
        stripeAccountId: stripeAccountId,
        type: 'account_onboarding',
        refreshUrl,
        returnUrl,
      });
      return accountLink;
    },
    onSuccess: (data) => {
      window.location.href = data.url;
    },
    onError: (error) => {
      console.error('Error creating account link:', error);
      const message =
        error instanceof Error
          ? error.message
          : 'Failed to create onboarding link.';
      toast.error(message);
    },
  });

  const handleSetupFormSubmit = async (values: ConnectSetupFormValues) => {
    // First update the database with the country
    await addCountryToDbStripeAccountAction({
      stripeAccountId: stripeAccountId ?? '',
      countryISOCode1: values.countryISOCode1,
    });

    // Then update the Stripe account to add recipient configuration
    if (stripeAccount && userEmail) {
      await createRecipientStripeAccountAction({
        stripeAccountId: stripeAccountId ?? '',
        countryISOCode1: values.countryISOCode1,
        email: userEmail,
      });
    }

    // Revalidate the stripeAccountLink query to get updated link data
    await queryClient.invalidateQueries({
      queryKey: ['stripeAccountLink', stripeAccountId],
    });
  };

  const continueSetup = async () => {
    if (linkData?.linkType === 'onboarding') {
      window.location.href = linkData.url;
    } else {
      createLinkMutation.mutateAsync();
    }
  };

  const handleReset = useCallback(async () => {
    const response = await resetStripeAccountAction({
      stripeAccountId: stripeAccountId ?? '',
    });
    if (response.success) {
      // Revalidate the stripeAccountLink query to get updated link data
      await queryClient.invalidateQueries({
        queryKey: ['stripeAccountLink', stripeAccountId],
      });
      toast.success('Stripe account reset successfully');
    } else {
      toast.error('Failed to reset stripe account');
    }
  }, [stripeAccountId, router]);

  // Helper function to convert DbStripeAccount to the expected UI component format
  const getAccountStatus = (): ConnectAccountStatus => {
    // Return default values if no account
    const config = stripeAccount?.configuration as unknown as
      | Stripe.V2.Core.Account.Configuration
      | undefined;
    const merchantCapabilities = config?.merchant?.capabilities;
    const recipientCapabilities = config?.recipient?.capabilities;

    return {
      detailsSubmitted: !!stripeAccount,

      chargesEnabled: merchantCapabilities?.card_payments?.status === 'active',
      payoutsEnabled:
        recipientCapabilities?.stripe_balance?.payouts?.status === 'active',
    };
  };

  const getRequirements = (): ConnectRequirements | undefined => {
    if (!stripeAccount?.requirements) return undefined;

    // Convert Json type to expected format
    const requirements = stripeAccount.requirements as {
      currently_due?: string[];
      past_due?: string[];
      eventually_due?: string[];
      pending_verification?: string[];
      disabled_reason?: string;
      current_deadline?: number;
    };

    return {
      currentlyDue: requirements?.currently_due || [],
      eventuallyDue: requirements?.eventually_due || [],
      pendingVerification: requirements?.pending_verification || [],
      disabledReason: requirements?.disabled_reason,
      currentDeadline: requirements?.current_deadline,
    };
  };

  return (
    <>
      {stripeAccount && linkData?.linkType === 'setup' && (
        <StripeAccountSetupForm
          data-test="connect-setup-form"
          user={{
            userId: userId,
            countryISOCode1: '',
            email: userEmail,
          }}
          countries={filteredCountries}
          onSubmit={handleSetupFormSubmit}
        />
      )}
      {stripeAccount && linkData?.linkType === 'onboarding' && (
        <StripeAccountOnboardingCard
          data-test="connect-onboarding"
          status={getAccountStatus()}
          requirements={getRequirements()}
          onContinueSetup={continueSetup}
          onReset={handleReset}
          stripeDashboardLink={linkData?.url ?? ''}
        />
      )}
      {stripeAccount && linkData?.linkType === 'login' && (
        <StripeAccountLoginCard
          data-test="connect-onboarding"
          status={getAccountStatus()}
          requirements={getRequirements()}
          stripeDashboardLink={linkData?.url ?? ''}
        />
      )}
    </>
  );
}

StripeConnectContainer.displayName = 'StripeConnectContainer';
