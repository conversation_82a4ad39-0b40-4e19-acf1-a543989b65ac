'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { getStripeAccountService } from '~/lib/stripe/services';

/**
 * Create/Update a Stripe recipient account for the given parameters
 * (User-centric context)
 */
export const createRecipientStripeAccountAction = enhanceAction(
  async (
    params: {
      stripeAccountId: string;
      countryISOCode1: string;
      email: string;
    },
    user,
  ) => {
    const client = getSupabaseServerClient();
    const logger = await getLogger();

    const ctx = {
      name: 'createRecipientStripeAccountAction',
      userId: user?.id,
      stripeAccountId: params.stripeAccountId,
      countryISOCode1: params.countryISOCode1,
    };

    logger.info(ctx, 'Creating/updating recipient stripe account for user...');

    try {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      // Ensure the stripeAccountId provided actually belongs to the authenticated user
      const stripeAccountService = getStripeAccountService(client);
      const dbStripeAccount = await stripeAccountService.getDbStripeAccountById(
        params.stripeAccountId,
      );

      if (!dbStripeAccount || dbStripeAccount.userId !== user.id) {
        logger.error(ctx, 'Stripe account does not belong to the user');
        throw new Error('Invalid Stripe account ID');
      }

      // Update the account with recipient configuration
      const response = await stripeAccountService.createRecipientStripeAccount({
        userId: user.id,
        stripeAccountId: params.stripeAccountId,
        countryISOCode1: params.countryISOCode1,
        email: params.email,
        accountType: 'express', // Default to express for recipient accounts
      });

      logger.info(ctx, 'Recipient stripe account created/updated successfully');

      return response;
    } catch (error) {
      logger.error(
        { ...ctx, error },
        'Error creating/updating recipient stripe account',
      );

      throw error;
    }
  },
  {
    auth: true,
    schema: z.object({
      stripeAccountId: z.string(),
      countryISOCode1: z.string().length(2),
      email: z.string().email(),
    }),
  },
);
