'use server';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { getStripeAccountService } from '~/lib/stripe/services';

export const getUserStripeAccountAction = enhanceAction(
  async (_data, user) => {
    const client = getSupabaseServerClient();
    const logger = await getLogger();

    if (!user) {
      throw new Error('User not found');
    }
    const ctx = {
      name: 'getUserStripeAccountAction',
      userId: user.id,
    };

    logger.info(ctx, 'Fetching user stripe account...');

    try {
      const stripeAccountService = getStripeAccountService(client);

      const dbStripeAccount =
        await stripeAccountService.getDbStripeAccountByUserId(user.id);

      if (!dbStripeAccount) {
        logger.info(ctx, 'No stripe account found for user');
        return null;
      }

      logger.info(ctx, 'User stripe account found', {
        stripeAccountId: dbStripeAccount.id,
      });
      return dbStripeAccount;
    } catch (error) {
      logger.error({ ...ctx, error }, 'Failed to fetch user stripe account');
      throw new Error('Failed to check your Stripe account status.');
    }
  },
  {
    auth: true,
  },
);
