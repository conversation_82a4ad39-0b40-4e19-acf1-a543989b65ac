'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { getStripeAccountService } from '~/lib/stripe/services';
import { DbStripeAccount } from '~/lib/stripe/types';

/**
 * Create a login link for the user's stripe account
 * (Verifies ownership before creating link)
 */
export const addCountryToDbStripeAccountAction = enhanceAction(
  async (
    params: {
      stripeAccountId: string;
      countryISOCode1: string;
    },
    user,
  ): Promise<DbStripeAccount> => {
    const client = getSupabaseServerClient();
    const logger = await getLogger();

    if (!user) {
      throw new Error('User not found');
    }

    const ctx = {
      name: 'addCountryToDbStripeAccountAction',
      userId: user.id,
      stripeAccountId: params.stripeAccountId,
    };

    logger.info(ctx, 'Adding country to db stripe account...');

    try {
      const service = getStripeAccountService(client);

      const dbStripeAccount = await service.addCountryToDbStripeAccount(
        params.stripeAccountId,
        params.countryISOCode1,
      );

      if (!dbStripeAccount || dbStripeAccount.userId !== user.id) {
        logger.error(ctx, 'Stripe account does not belong to the user');
        throw new Error('Invalid Stripe account ID');
      }

      logger.info(ctx, 'Country added to db stripe account successfully');
      return dbStripeAccount;
    } catch (error) {
      logger.error(
        { ...ctx, error },
        'Failed to add country to db stripe account',
      );
      throw new Error('Failed to add country to db stripe account.');
    }
  },
  {
    auth: true,
    schema: z.object({
      stripeAccountId: z.string(),
      countryISOCode1: z.string(),
    }),
  },
);
