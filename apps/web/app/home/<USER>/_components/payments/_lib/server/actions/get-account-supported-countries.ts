'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { RetrieveStripeAccountCountriesParamsSchema } from '~/lib/stripe/schema';
import { getStripeAccountService } from '~/lib/stripe/services';

/**
 * Get the allowed account countries for the given parameters
 */
export const getAccountSupportedCountriesAction = enhanceAction(
  async (
    params: z.infer<typeof RetrieveStripeAccountCountriesParamsSchema>,
    user,
  ) => {
    const client = getSupabaseServerClient();
    const logger = await getLogger();

    const ctx = {
      name: 'getAccountSupportedCountriesAction',
      userId: user.id,
      stripeAccountId: params.stripeAccountId,
    };

    logger.info(ctx, 'Retrieving account supported countries...');

    try {
      const stripeAccountService = getStripeAccountService(client);
      const allowedTransferCountries =
        await stripeAccountService.retrieveSupportedCountriesForAccount({
          stripeAccountId: params.stripeAccountId,
        });

      if (allowedTransferCountries.length === 0) {
        logger.error(ctx, 'Stripe account does not support transfer countries');
        throw new Error('Stripe account does not support transfer countries');
      }

      return objectToCamel(allowedTransferCountries);
    } catch (error) {
      logger.error(
        { ...ctx, error },
        'Error retrieving allowed account countries',
      );

      throw error;
    }
  },
  {
    auth: true,
    schema: RetrieveStripeAccountCountriesParamsSchema,
  },
);
