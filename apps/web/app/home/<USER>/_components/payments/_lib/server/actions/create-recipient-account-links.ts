'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { RecipientAccountLinkParamsSchema } from '~/lib/stripe/schema';
import { getStripeAccountService } from '~/lib/stripe/services';

/**
 * Create an account link for the given parameters
 * (User-centric context)
 */
export const createRecipientAccountLinksAction = enhanceAction(
  async (params: z.infer<typeof RecipientAccountLinkParamsSchema>, user) => {
    const client = getSupabaseServerClient();
    const logger = await getLogger();

    const ctx = {
      name: 'createRecipientAccountLinksAction',
      userId: user?.id,
      stripeAccountId: params.stripeAccountId,
      type: params.type,
    };

    logger.info(ctx, 'Creating recipient account link for user...');

    try {
      // Ensure the stripeAccountId provided actually belongs to the authenticated user
      const stripeAccountService = getStripeAccountService(client);
      const stripeAccount = await stripeAccountService.getDbStripeAccountById(
        params.stripeAccountId,
      );

      if (!stripeAccount || stripeAccount.userId !== user?.id) {
        logger.error(ctx, 'Stripe account does not belong to the user');
        throw new Error('Invalid Stripe account ID');
      }

      const response =
        await stripeAccountService.createRecipientAccountLink(params);

      logger.info(ctx, 'Recipient account link created successfully');

      return response;
    } catch (error) {
      logger.error({ ...ctx, error }, 'Error creating recipient account link');

      throw error;
    }
  },
  {
    auth: true,
    schema: RecipientAccountLinkParamsSchema,
  },
);
