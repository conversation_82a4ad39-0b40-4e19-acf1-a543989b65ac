'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { CreateLoginLinkParamsSchema } from '~/lib/stripe/schema';
import { getStripeAccountService } from '~/lib/stripe/services';

/**
 * Create a login link for the user's stripe account
 * (Verifies ownership before creating link)
 */
export const createStripeLoginLinkAction = enhanceAction(
  async (
    params: z.infer<typeof CreateLoginLinkParamsSchema>,
    user,
  ): Promise<{
    url: string;
    linkType: 'setup' | 'login' | 'onboarding';
  }> => {
    const client = getSupabaseServerClient();
    const logger = await getLogger();

    if (!user) {
      throw new Error('User not found');
    }

    const ctx = {
      name: 'createLoginLinkAction',
      userId: user.id,
      stripeAccountId: params.stripeAccountId,
    };

    logger.info(ctx, 'Creating login link for user...');

    try {
      const service = getStripeAccountService(client);

      // First, verify the account belongs to the user
      const dbStripeAccount = await service.getDbStripeAccountById(
        params.stripeAccountId,
      );

      if (!dbStripeAccount || dbStripeAccount.userId !== user.id) {
        logger.error(ctx, 'Stripe account does not belong to the user');
        throw new Error('Invalid Stripe account ID');
      }

      if (dbStripeAccount.countryId === null) {
        logger.warn(ctx, 'Stripe account does not have a country');
        return {
          url: '',
          linkType: 'setup',
        };
      }

      // Create the login link
      const linkResponse = await service.createOnboardingOrLoginLink(
        params.stripeAccountId,
        params.refreshUrl,
        params.returnUrl,
      );

      logger.info(ctx, 'Login link created successfully');
      return linkResponse;
    } catch (error) {
      logger.error({ ...ctx, error }, 'Failed to create login link');
      throw new Error('Failed to create Stripe login link.');
    }
  },
  {
    auth: true,
    schema: CreateLoginLinkParamsSchema,
  },
);
