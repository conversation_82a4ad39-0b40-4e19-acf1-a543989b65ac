'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { ResetStripeAccountParamsSchema } from '~/lib/stripe/schema';
import { getStripeAccountService } from '~/lib/stripe/services';

/**
 * Reset the stripe account
 * This deletes the account from the database & stripe
 * (User-centric context)
 */
export const resetStripeAccountAction = enhanceAction(
  async (params: z.infer<typeof ResetStripeAccountParamsSchema>, user) => {
    const client = getSupabaseServerClient();
    const logger = await getLogger();

    const ctx = {
      name: 'resetStripeAccountAction',
      userId: user?.id,
      stripeAccountId: params.stripeAccountId,
    };

    logger.info(ctx, 'Resetting stripe account...');

    try {
      // Ensure the stripeAccountId provided actually belongs to the authenticated user
      const stripeAccountService = getStripeAccountService(client);
      const stripeAccount = await stripeAccountService.getDbStripeAccountById(
        params.stripeAccountId,
      );

      if (!stripeAccount || stripeAccount.userId !== user?.id) {
        logger.error(ctx, 'Stripe account does not belong to the user');
        throw new Error('Invalid Stripe account ID');
      }

      const response = await stripeAccountService.resetStripeAccount(params);

      logger.info(ctx, 'Stripe account reset successfully');

      return response;
    } catch (error) {
      logger.error({ ...ctx, error }, 'Error resetting stripe account');

      throw error;
    }
  },
  {
    auth: true,
    schema: ResetStripeAccountParamsSchema,
  },
);
