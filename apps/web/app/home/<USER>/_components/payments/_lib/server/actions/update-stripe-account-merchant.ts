'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { getStripeAccountService } from '~/lib/stripe/services';
import { StripeAccountV2 } from '~/lib/stripe/types';

/**
 * Update the stripe account with merchant configuration
 */
export const updateStripeAccountMerchantAction = enhanceAction(
  async ({ stripeAccount, email, countryISOCode1 }, user) => {
    const client = getSupabaseServerClient();
    const logger = await getLogger();

    if (!user) {
      throw new Error('User not found');
    }

    const ctx = {
      name: 'updateStripeAccountMerchantAction',
      userId: user.id,
      country: countryISOCode1,
    };

    logger.info(ctx, 'Updating stripe account for user...');

    try {
      // Update the stripe account using the service instance
      const service = getStripeAccountService(client);
      const response = await service.updateStripeAccount(
        stripeAccount,
        email,
        countryISOCode1,
      );

      logger.info(ctx, 'Stripe account updated successfully', {
        stripeAccountId: response,
      });

      return objectToCamel(response as StripeAccountV2);
    } catch (error) {
      logger.error({ ...ctx, error }, 'Error updating stripe account');
      throw error;
    }
  },
  {
    auth: true,
    schema: z.object({
      stripeAccount: z.any(),
      email: z.string(),
      countryISOCode1: z.string(),
    }),
  },
);
