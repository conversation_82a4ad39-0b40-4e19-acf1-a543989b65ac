'use server';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { objectToCamel } from '@kit/shared/utils';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { RetrieveStripeAccountParamsSchema } from '~/lib/stripe/schema';
import { getStripeAccountService } from '~/lib/stripe/services';

/**
 * Retrieve stripe account details from Stripe API
 * (Verifies user ownership before retrieving)
 * Returns a plain object with simplified, serializable data.
 */
export const getStripeAccountDetailsAction = enhanceAction(
  async (params: z.infer<typeof RetrieveStripeAccountParamsSchema>, user) => {
    const client = getSupabaseServerClient();
    const logger = await getLogger();

    if (!user) {
      throw new Error('User not found');
    }

    const ctx = {
      name: 'getStripeAccountDetailsAction',
      userId: user.id,
      stripeAccountId: params.stripeAccountId,
    };

    logger.info(ctx, 'Retrieving stripe account details for user...');

    try {
      const stripeAccountService = getStripeAccountService(client);

      // First, verify the account belongs to the user
      const dbStripeAccount = await stripeAccountService.getDbStripeAccountById(
        params.stripeAccountId,
      );

      if (!dbStripeAccount || dbStripeAccount.userId !== user.id) {
        logger.error(ctx, 'Stripe account does not belong to the user');
        throw new Error('Invalid Stripe account ID');
      }

      const accountDetails =
        await stripeAccountService.retrieveStripeAccount(params);

      logger.info(ctx, 'Stripe account details retrieved successfully');

      if (!accountDetails) {
        return null;
      }

      return objectToCamel(accountDetails);
    } catch (error) {
      logger.error(
        { ...ctx, error },
        'Error retrieving stripe account details',
      );

      // Throw a standard Error instead of the original potentially non-serializable error
      throw new Error('Failed to retrieve stripe account details.');
    }
  },
  {
    auth: true,
    schema: RetrieveStripeAccountParamsSchema,
  },
);
