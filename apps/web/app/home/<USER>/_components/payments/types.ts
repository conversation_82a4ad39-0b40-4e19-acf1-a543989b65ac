// Import dependent types from UI package
import type { ConnectRequirements } from '@kit/ui/dojo/molecules/connect-requirements';
import type { ConnectAccountStatus } from '@kit/ui/dojo/molecules/connect-status-badge';

// Define the type for the simplified plain object passed from server to client
export type SimplifiedAccountDetails =
  | (ConnectAccountStatus & {
      requirements?: ConnectRequirements;
    })
  | null;
