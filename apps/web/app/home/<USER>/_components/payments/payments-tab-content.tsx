'use client';

import { User } from '@supabase/supabase-js';

import { useQuery } from '@tanstack/react-query';

import { Alert, AlertDescription, AlertTitle } from '@kit/ui/alert';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { If } from '@kit/ui/if';
import { Spinner } from '@kit/ui/spinner';
import { Trans } from '@kit/ui/trans';

import { getCountriesAction } from '~/lib/actions/get-all-countries-action';

import { createStripeLoginLinkAction } from './_lib/server/actions/create-stripe-login-link-action';
import { getUserStripeAccountAction } from './_lib/server/actions/get-user-stripe-account';
import { StripeConnectContainer } from './stripe-connect-container';

interface PaymentsTabContentProps {
  user: User;
}

/**
 * @name PaymentsTabContent
 * @description Client component to fetch and display user's payment settings using TanStack Query.
 */
export function PaymentsTabContent({ user }: PaymentsTabContentProps) {
  const userId = user.id;
  const userEmail = user.email ?? '';

  const { data: countries, error: countriesError } = useQuery({
    queryKey: ['countries'],
    queryFn: () => getCountriesAction({}),
  });

  const { data: dbStripeAccount, error: accountError } = useQuery({
    queryKey: ['userStripeAccount', userId],
    queryFn: () => getUserStripeAccountAction({ userId }),
    enabled: !!userId,
  });

  const stripeAccountId = dbStripeAccount?.id ?? null;

  const shouldFetchLoginLink =
    !!stripeAccountId && !!dbStripeAccount?.capabilities;

  const appUrl = process.env.NEXT_PUBLIC_APP_URL ?? window.location.origin;
  const settingsPath = '/home/<USER>';
  const refreshUrl = `${appUrl}${settingsPath}?tab=stripeAccount&refresh_status=success`;
  const returnUrl = `${appUrl}${settingsPath}?tab=stripeAccount`;

  const { data: linkData, error: loginLinkError } = useQuery({
    queryKey: ['stripeAccountLink', stripeAccountId],
    queryFn: () =>
      createStripeLoginLinkAction({
        stripeAccountId: stripeAccountId!,
        refreshUrl,
        returnUrl,
      }),
    enabled: shouldFetchLoginLink,
    staleTime: 5 * 60 * 1000,
  });

  const error = countriesError || accountError || loginLinkError;

  if (error) {
    console.error('[PaymentsTabContent Client] Data fetching error:', error);
    return (
      <Alert variant="destructive">
        <AlertTitle>Error Loading Payment Settings</AlertTitle>
        <AlertDescription>
          {error instanceof Error ? error.message : 'Please try again later.'}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Card data-test="stripe-connect-container">
      <CardHeader className="flex flex-row justify-between">
        <div className="flex flex-col justify-start gap-2">
          <CardTitle className="mr-2">
            <Trans i18nKey="user:settings.stripeConnect.title" />
          </CardTitle>
          <CardDescription>
            <Trans i18nKey="user:settings.stripeConnect.description" />
          </CardDescription>
        </div>
      </CardHeader>
      <CardContent>
        <If condition={!linkData}>
          <div className="flex justify-center py-4">
            <Spinner />
          </div>
        </If>
        <StripeConnectContainer
          userId={userId}
          userEmail={userEmail}
          countries={countries ?? []}
          stripeAccount={dbStripeAccount ?? null}
          linkData={linkData ?? null}
        />
      </CardContent>
    </Card>
  );
}
