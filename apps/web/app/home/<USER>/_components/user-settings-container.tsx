'use client';

import { Suspense, useEffect, useState } from 'react';

import { usePathname, useRouter, useSearchParams } from 'next/navigation';

import { User } from '@supabase/supabase-js';

import { useTranslation } from 'react-i18next';

import { <PERSON><PERSON> } from '@kit/supabase/database';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { SocialsForm } from '@kit/ui/dojo/organisms/socials-form';
import { type Social } from '@kit/ui/dojo/organisms/socials-form';
import { If } from '@kit/ui/if';
import { LanguageSelector } from '@kit/ui/language-selector';
import { LoadingOverlay } from '@kit/ui/loading-overlay';
import { Spinner } from '@kit/ui/spinner';
import { Tabs, TabsContent, TabsList } from '@kit/ui/tabs';
import { TabsTriggersList } from '@kit/ui/tabs-triggers-list';
import { Trans } from '@kit/ui/trans';

import { useAllCountries } from '~/lib/users/hooks/use-all-countries';
import { useUpdateUserData } from '~/lib/users/hooks/use-update-user';
import {
  useRevalidateUserDataQuery,
  useUserData,
} from '~/lib/users/hooks/use-user-data';
import { castToUserData } from '~/lib/users/utils/type-helpers';

import { UserCommunitiesContainer } from './communities/user-communities-container';
import { UserDangerZone } from './delete/user-danger-zone';
import { UpdateEmailFormContainer } from './details/update-email-form-container';
import { UpdateUserDetailsFormContainer } from './details/update-user-details-form-container';
import { UpdateUserImageContainer } from './details/update-user-image-container';
import { MultiFactorAuthFactorsList } from './mfa/multi-factor-auth-list';
import { UpdatePasswordFormContainer } from './password/update-password-container';
import { UserPaymentMethodsContainer } from './payment-methods/user-payment-methods-container';
import { PaymentsTabContent } from './payments/payments-tab-content';

export function UserSettingsContainer(
  props: React.PropsWithChildren<{
    user: User;

    features: {
      enablePasswordUpdate: boolean;
    };

    paths: {
      callback: string;
    };
  }>,
) {
  const supportsLanguageSelection = useSupportMultiLanguage();
  const userDataQuery = useUserData(props.user.id);
  const countriesQuery = useAllCountries();
  const updateAccount = useUpdateUserData(props.user.id);
  const revalidateUserData = useRevalidateUserDataQuery();

  const { t } = useTranslation();

  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [activeTab, setActiveTab] = useState(
    searchParams.get('tab') ?? 'communities',
  );

  useEffect(() => {
    const tab = searchParams.get('tab') ?? 'communities';
    setActiveTab(tab);
  }, [searchParams]);

  const handleSocialsSubmit = async (socialsArray: Social[]) => {
    if (!userDataQuery.data) return;

    const userData = castToUserData(userDataQuery.data);

    await updateAccount.mutateAsync({
      userDetails: {
        bio: userData.bio || '',
        socials: socialsArray as unknown as Json[],
      },
    });

    await revalidateUserData(props.user.id);
  };

  if (
    !userDataQuery.data ||
    userDataQuery.isPending ||
    countriesQuery.isPending
  ) {
    return <LoadingOverlay fullPage />;
  }

  const countries = countriesQuery.data || [];
  const userData = castToUserData(userDataQuery.data);

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    const params = new URLSearchParams(searchParams.toString());
    if (value === 'communities') {
      params.delete('tab');
    } else {
      params.set('tab', value);
    }
    router.push(`${pathname}?${params.toString()}`, { scroll: false });
  };

  return (
    <Tabs
      value={activeTab}
      className="flex h-full"
      orientation="vertical"
      onValueChange={handleTabChange}
    >
      <TabsList className="mr-4 flex h-min w-[200px] shrink-0 flex-col justify-start space-y-2 rounded-xl border py-4">
        <TabsTriggersList
          values={[
            'communities',
            'details',
            'socials',
            'paymentMethods',
            'stripeAccount',
            'password',
            'mfa',
            'delete',
          ]}
          labels={[
            t('user:settings.communitiesTabLabel'),
            t('user:settings.detailsTabLabel'),
            t('user:settings.socialsTabLabel'),
            t('user:settings.paymentMethodsTabLabel'),
            t('user:settings.stripeAccountTabLabel'),
            t('user:settings.passwordTabLabel'),
            t('user:settings.mfaTabLabel'),
            t('user:settings.deleteTabLabel'),
          ]}
          className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground h-10 w-full justify-start rounded pl-2 text-left hover:bg-slate-200"
        />
      </TabsList>
      <div className="flex-1 overflow-auto">
        <TabsContent value="communities">
          <UserCommunitiesContainer />
        </TabsContent>
        <TabsContent value="details">
          <Card className="mb-4">
            <CardHeader>
              <CardTitle>
                <Trans i18nKey={'user:accountImage'} />
              </CardTitle>

              <CardDescription>
                <Trans i18nKey={'user:accountImageDescription'} />
              </CardDescription>
            </CardHeader>

            <CardContent>
              {userData.pictureUrl !== undefined && (
                <UpdateUserImageContainer
                  user={{
                    pictureUrl: userData.pictureUrl,
                    id: userData.id,
                  }}
                />
              )}
            </CardContent>
          </Card>

          <Card className="mb-4">
            <CardHeader>
              <CardTitle>
                <Trans i18nKey={'user:accountDetails'} />
              </CardTitle>

              <CardDescription>
                <Trans i18nKey={'user:accountDetailsDescription'} />
              </CardDescription>
            </CardHeader>

            <CardContent>
              <UpdateUserDetailsFormContainer
                user={{
                  firstName: userData.firstName,
                  lastName: userData.lastName,
                  bio: userData.bio,
                  username: userData.username ?? '',
                  id: userData.id,
                  countryId: userData.countryId ?? '',
                }}
                countries={countries}
              />
            </CardContent>
          </Card>

          <If condition={supportsLanguageSelection}>
            <Card>
              <CardHeader>
                <CardTitle>
                  <Trans i18nKey={'user:language'} />
                </CardTitle>

                <CardDescription>
                  <Trans i18nKey={'user:languageDescription'} />
                </CardDescription>
              </CardHeader>

              <CardContent>
                <LanguageSelector />
              </CardContent>
            </Card>
          </If>

          <Card>
            <CardHeader>
              <CardTitle>
                <Trans i18nKey={'user:updateEmailCardTitle'} />
              </CardTitle>

              <CardDescription>
                <Trans i18nKey={'user:updateEmailCardDescription'} />
              </CardDescription>
            </CardHeader>

            <CardContent>
              <UpdateEmailFormContainer callbackPath={props.paths.callback} />
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="socials">
          <Card className="mb-4">
            <CardHeader>
              <CardTitle>
                <Trans i18nKey={'user:socialsTitle'} />
              </CardTitle>

              <CardDescription>
                <Trans i18nKey={'user:socialsDescription'} />
              </CardDescription>
            </CardHeader>

            <CardContent>
              <SocialsForm
                socials={userData.socials ?? []}
                onSubmit={handleSocialsSubmit}
                testId="user-socials-form"
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="paymentMethods">
          <UserPaymentMethodsContainer userId={props.user.id} />
        </TabsContent>

        <TabsContent value="stripeAccount">
          <Suspense
            fallback={
              <div className="p-4">
                <Spinner />
              </div>
            }
          >
            <PaymentsTabContent user={props.user} />
          </Suspense>
        </TabsContent>

        <TabsContent value="password">
          <If condition={props.features.enablePasswordUpdate}>
            <Card>
              <CardHeader>
                <CardTitle>
                  <Trans i18nKey={'user:updatePasswordCardTitle'} />
                </CardTitle>

                <CardDescription>
                  <Trans i18nKey={'user:updatePasswordCardDescription'} />
                </CardDescription>
              </CardHeader>

              <CardContent>
                <UpdatePasswordFormContainer
                  callbackPath={props.paths.callback}
                />
              </CardContent>
            </Card>
          </If>
        </TabsContent>

        <TabsContent value="mfa">
          <Card>
            <CardHeader>
              <CardTitle>
                <Trans i18nKey={'user:multiFactorAuth'} />
              </CardTitle>

              <CardDescription>
                <Trans i18nKey={'user:multiFactorAuthDescription'} />
              </CardDescription>
            </CardHeader>

            <CardContent>
              <MultiFactorAuthFactorsList userId={props.user.id} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="delete">
          <Card className={'border-destructive'}>
            <CardHeader>
              <CardTitle>
                <Trans i18nKey={'user:dangerZone'} />
              </CardTitle>

              <CardDescription>
                <Trans i18nKey={'user:dangerZoneDescription'} />
              </CardDescription>
            </CardHeader>

            <CardContent>
              <UserDangerZone />
            </CardContent>
          </Card>
        </TabsContent>
      </div>
    </Tabs>
  );
}

function useSupportMultiLanguage() {
  const { i18n } = useTranslation();
  const langs = (i18n?.options?.supportedLngs as string[]) ?? [];

  const supportedLangs = langs.filter((lang) => lang !== 'cimode');

  return supportedLangs.length > 1;
}

UserSettingsContainer.displayName = 'UserSettingsContainer';
useSupportMultiLanguage.displayName = 'useSupportMultiLanguage';
