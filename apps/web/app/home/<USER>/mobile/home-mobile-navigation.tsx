'use client';

import React from 'react';

import { Menu } from 'lucide-react';

import { useSignOut } from '@kit/supabase/hooks/use-sign-out';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@kit/ui/dropdown-menu';
import { Trans } from '@kit/ui/trans';

import { userAccountNavigationConfig } from '~/home/<USER>/config/user-account-navigation.config';
import { type UserWorkspace } from '~/lib/users/user-workspace.loader';

import { HomeCommunitySelector } from '../home-community-selector';
import { DropdownLink } from './dropdown-link';
import { SignOutDropdownItem } from './sign-out-dropdown-item';

export function HomeMobileNavigation(props: { workspace: UserWorkspace }) {
  const signOut = useSignOut();

  const Links = userAccountNavigationConfig.routes.map((item, index) => {
    if ('children' in item) {
      return item.children.map((child) => {
        return (
          <DropdownLink
            key={child.path}
            icon={child.Icon}
            path={child.path}
            label={child.label}
          />
        );
      });
    }

    if ('divider' in item) {
      return <DropdownMenuSeparator key={`divider-${index}`} />;
    }
  });

  return (
    <DropdownMenu>
      <DropdownMenuTrigger>
        <Menu className={'h-9'} />
      </DropdownMenuTrigger>

      <DropdownMenuContent sideOffset={10} className={'w-screen rounded-none'}>
        <DropdownMenuGroup>
          <DropdownMenuLabel>
            <Trans i18nKey={'common:yourCommunities'} />
          </DropdownMenuLabel>

          <HomeCommunitySelector
            userId={props.workspace.user.id}
            communities={props.workspace.communities ?? []}
            collisionPadding={0}
          />
        </DropdownMenuGroup>

        <DropdownMenuSeparator />

        <DropdownMenuGroup>{Links}</DropdownMenuGroup>

        <DropdownMenuSeparator />

        <SignOutDropdownItem onSignOut={() => signOut.mutateAsync()} />
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

HomeMobileNavigation.displayName = 'HomeMobileNavigation';
