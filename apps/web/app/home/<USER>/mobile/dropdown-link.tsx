'use client';

import Link from 'next/link';

import { DropdownMenuItem } from '@kit/ui/dropdown-menu';
import { Trans } from '@kit/ui/trans';

export function DropdownLink(
  props: React.PropsWithChildren<{
    path: string;
    label: string;
    icon: React.ReactNode;
  }>,
) {
  return (
    <DropdownMenuItem asChild key={props.path}>
      <Link
        href={props.path}
        className={'flex h-12 w-full items-center space-x-4'}
      >
        {props.icon}

        <span>
          <Trans i18nKey={props.label} defaults={props.label} />
        </span>
      </Link>
    </DropdownMenuItem>
  );
}

DropdownLink.displayName = 'DropdownLink';
