'use client';

import React from 'react';

import { LogOut } from 'lucide-react';

import { DropdownMenuItem } from '@kit/ui/dropdown-menu';
import { Trans } from '@kit/ui/trans';

export function SignOutDropdownItem(
  props: React.PropsWithChildren<{
    onSignOut: () => unknown;
  }>,
) {
  return (
    <DropdownMenuItem
      className={'flex h-12 w-full items-center space-x-4'}
      onClick={props.onSignOut}
    >
      <LogOut className={'h-6'} />

      <span>
        <Trans i18nKey={'common:signOut'} defaults={'Sign out'} />
      </span>
    </DropdownMenuItem>
  );
}

SignOutDropdownItem.displayName = 'SignOutDropdownItem';
