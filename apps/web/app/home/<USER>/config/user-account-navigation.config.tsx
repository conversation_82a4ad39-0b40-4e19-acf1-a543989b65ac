import { Home } from 'lucide-react';
import { z } from 'zod';

import { NavigationConfigSchema } from '~/_components/navigation-config.schema';
import pathsConfig from '~/config/paths.config';

const iconClasses = 'w-4';

const routes = [
  {
    label: 'common:routes.application',
    children: [
      {
        label: 'common:routes.home',
        path: pathsConfig.app.home,
        Icon: <Home className={iconClasses} />,
        end: true,
      },
    ],
  },
  {
    label: 'common:routes.settings',
    children: [
      // {
      //   label: 'common:routes.profile',
      //   path: pathsConfig.app.userAccountSettings,
      //   Icon: <User className={iconClasses} />,
      // },
    ].filter((route) => !!route),
  },
] satisfies z.infer<typeof NavigationConfigSchema>['routes'];

export const userAccountNavigationConfig = NavigationConfigSchema.parse({
  routes,
});
