import { use } from 'react';

import Link from 'next/link';

import { CommunityCard } from '@kit/ui/dojo/organisms/community-card';
import {
  EmptyState,
  EmptyStateButton,
  EmptyStateHeading,
  EmptyStateText,
} from '@kit/ui/empty-state';
import { Trans } from '@kit/ui/trans';

import pathsConfig from '~/config/paths.config';
import { CreateCommunityButton } from '~/create-community/_components/create-community-button';
import { loadUserWorkspace } from '~/lib/users/user-workspace.loader';

export function HomeCommunitiesList() {
  const { communities } = use(loadUserWorkspace());

  if (!communities.length) {
    return <HomeCommunitiesListEmptyState />;
  }

  return (
    <div className="flex flex-col">
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3">
        {communities.map((community) => (
          <CommunityCard
            key={community.slug}
            name={community.name ?? ''}
            description={community.description ?? ''}
            pictureUrl={community.logoUrl ?? ''}
            coverImage={community.coverUrl ?? ''}
            slug={community.slug ?? ''}
            memberCount={community.memberCount ?? 0}
            showFooter={false}
            showPrice={false}
            isPrivate={community.isPrivate}
            categoryName={community.categoryName ?? ''}
            categoryIcon={community.categoryIcon ?? ''}
            languageName={community.languageName ?? ''}
            languageIcon={community.languageIcon ?? ''}
            data-test={`home-communities-list-item-${community.slug}`}
          />
        ))}
      </div>
    </div>
  );
}

function HomeCommunitiesListEmptyState() {
  return (
    <div className={'flex items-start justify-center'}>
      <EmptyState className={'border-0 shadow-none'}>
        <CreateCommunityButton className={'mt-4'} />
        <EmptyStateButton asChild>
          <Link href={pathsConfig.app.explore}>
            <Trans i18nKey={'user:exploreCommunities'} />
          </Link>
        </EmptyStateButton>
        <EmptyStateHeading>
          <Trans i18nKey={'user:noCommunitiesYet'} />
        </EmptyStateHeading>
        <EmptyStateText>
          <Trans i18nKey={'user:createCommunity'} />
        </EmptyStateText>
      </EmptyState>
    </div>
  );
}

HomeCommunitiesList.displayName = 'HomeCommunitiesList';
HomeCommunitiesListEmptyState.displayName = 'HomeCommunitiesListEmptyState';
