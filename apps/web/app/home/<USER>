import { use } from 'react';

import { Header } from '@kit/ui/dojo/organisms/header';
import {
  Page,
  PageMobileNavigation,
  PageNavigation,
  PageSecondaryNavigation,
} from '@kit/ui/page';

import { SiteHeaderAccountSection } from '~/_components/site-header-account-section';
import { AppLogo } from '~/components/app-logo';
import { withI18n } from '~/lib/i18n/with-i18n';
import { UserWorkspaceContextProvider } from '~/lib/users/context/user-workspace-context';
import { loadUserWorkspace } from '~/lib/users/user-workspace.loader';

import { HomeMenuNavigation } from './_components/home-menu-navigation';
import { HomeMobileNavigation } from './_components/mobile/home-mobile-navigation';

function UserHomeLayout({ children }: React.PropsWithChildren) {
  const workspace = use(loadUserWorkspace());

  return (
    <UserWorkspaceContextProvider value={workspace}>
      <Page style={'header'}>
        <PageNavigation>
          <Header
            logo={<AppLogo />}
            actions={<SiteHeaderAccountSection workspace={workspace} />}
          />
        </PageNavigation>
        <PageSecondaryNavigation>
          <HomeMenuNavigation workspace={workspace} />
        </PageSecondaryNavigation>

        <PageMobileNavigation className={'flex items-center justify-between'}>
          <MobileNavigation workspace={workspace} />
        </PageMobileNavigation>

        {children}
      </Page>
    </UserWorkspaceContextProvider>
  );
}

export default withI18n(UserHomeLayout);

function MobileNavigation({
  workspace,
}: {
  workspace: Awaited<ReturnType<typeof loadUserWorkspace>>;
}) {
  return (
    <>
      <AppLogo />

      <HomeMobileNavigation workspace={workspace} />
    </>
  );
}

UserHomeLayout.displayName = 'UserHomeLayout';
MobileNavigation.displayName = 'MobileNavigation';
