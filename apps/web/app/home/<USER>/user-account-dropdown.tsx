'use client';

import { useMemo } from 'react';

import Link from 'next/link';

import type { User } from '@supabase/supabase-js';

import {
  ChevronsUpDown,
  Home,
  LogOut,
  MessageCircleQuestion,
  Settings,
  Shield,
  User as UserIcon,
} from 'lucide-react';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@kit/ui/dropdown-menu';
import { If } from '@kit/ui/if';
import { SubMenuModeToggle } from '@kit/ui/mode-toggle';
import { ProfileAvatar } from '@kit/ui/profile-avatar';
import { Trans } from '@kit/ui/trans';
import { cn } from '@kit/ui/utils';

import { useUserData } from '~/lib/users/hooks/use-user-data';
import { castToUserData } from '~/lib/users/utils/type-helpers';

export function UserAccountDropdown({
  className,
  user,
  signOutRequested,
  showProfileName = false,
  paths,
}: {
  user: User;

  signOutRequested: () => unknown;

  paths: {
    home: string;
    userAccountSettings: string;
  };

  showProfileName?: boolean;

  className?: string;
}) {
  const { data: userAccountData } = useUserData(user.id);

  const userData = userAccountData ? castToUserData(userAccountData) : null;

  const displayName = userData?.firstName
    ? `${userData.firstName} ${userData.lastName}`
    : (user?.email ?? 'User');

  const isSuperAdmin = useMemo(() => {
    return user?.app_metadata.role === 'super-admin';
  }, [user]);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger
        aria-label="Open your profile menu"
        data-test={'user-account-dropdown-trigger'}
        className={cn(
          'animate-in fade-in focus:outline-primary flex cursor-pointer items-center duration-500 group-data-[minimized=true]:px-0',
          className ?? '',
          {
            ['active:bg-secondary/50 items-center space-x-4 rounded-md' +
            ' hover:bg-secondary p-2 transition-colors']: showProfileName,
          },
        )}
      >
        <ProfileAvatar
          displayName={displayName}
          pictureUrl={userData?.pictureUrl}
        />

        <If condition={showProfileName}>
          <div
            className={
              'animate-in fade-in flex w-full flex-col truncate text-left group-data-[minimized=true]:hidden'
            }
          >
            <span
              data-test={'user-account-dropdown-display-name'}
              className={'truncate text-sm'}
            >
              {displayName}
            </span>
          </div>

          <ChevronsUpDown
            className={
              'text-muted-foreground mr-1 h-8 group-data-[minimized=true]:hidden'
            }
          />
        </If>
      </DropdownMenuTrigger>

      <DropdownMenuContent className={'xl:min-w-[15rem]!'}>
        <DropdownMenuItem className={'h-10! rounded-none'}>
          <div
            className={'flex flex-col justify-start truncate text-left text-xs'}
          >
            <div
              data-test={'user-account-dropdown-signed-in-as'}
              className={'text-muted-foreground'}
            >
              <Trans i18nKey={'common:signedInAs'} />
            </div>

            <div>
              <span
                data-test={'user-account-dropdown-signed-in-as-label'}
                className={'block truncate'}
              >
                {displayName}
              </span>
            </div>
          </div>
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        <DropdownMenuItem asChild>
          <Link
            className={'s-full flex cursor-pointer items-center space-x-2'}
            href={paths.home}
          >
            <Home className={'h-5'} />

            <span>
              <Trans i18nKey={'common:routes.home'} />
            </span>
          </Link>
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        <DropdownMenuItem asChild>
          <Link
            className={'s-full flex cursor-pointer items-center space-x-2'}
            href={`/@${userData?.username}`}
          >
            <UserIcon className={'h-5'} />

            <span>
              <Trans i18nKey={'common:viewProfile'} />
            </span>
          </Link>
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        <DropdownMenuItem asChild>
          <Link
            className={'s-full flex cursor-pointer items-center space-x-2'}
            href={paths.userAccountSettings}
          >
            <Settings className={'h-5'} />

            <span>
              <Trans i18nKey={'common:routes.settings'} />
            </span>
          </Link>
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        <DropdownMenuItem asChild>
          <Link
            className={'s-full flex cursor-pointer items-center space-x-2'}
            href={'/docs'}
          >
            <MessageCircleQuestion className={'h-5'} />

            <span>
              <Trans i18nKey={'common:documentation'} />
            </span>
          </Link>
        </DropdownMenuItem>

        <If condition={isSuperAdmin}>
          <DropdownMenuSeparator />

          <DropdownMenuItem asChild>
            <Link
              className={'s-full flex cursor-pointer items-center space-x-2'}
              href={'/admin'}
            >
              <Shield className={'h-5'} />

              <span>Admin</span>
            </Link>
          </DropdownMenuItem>
        </If>

        <DropdownMenuSeparator />

        <SubMenuModeToggle />

        <DropdownMenuSeparator />

        <DropdownMenuItem
          data-test={'user-account-dropdown-sign-out'}
          role={'button'}
          className={'cursor-pointer'}
          onClick={signOutRequested}
        >
          <span className={'flex w-full items-center space-x-2'}>
            <LogOut className={'h-5'} />

            <span>
              <Trans i18nKey={'auth:signOut'} />
            </span>
          </span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

UserAccountDropdown.displayName = 'UserAccountDropdown';
