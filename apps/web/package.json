{"name": "web", "version": "0.1.0", "private": true, "sideEffects": false, "type": "module", "scripts": {"analyze": "ANALYZE=true pnpm run build", "build": "pnpm with-env next build", "build:test": "NODE_ENV=test pnpm with-env:test next build", "clean": "git clean -xdf .next .turbo node_modules", "dev": "pnpm with-env next dev --turbo | pino-pretty -c", "format": "prettier --check --ignore-path ./.prettierignore \"**/*.{js,cjs,mjs,ts,tsx,md,json,sql}\"", "lint": "next lint && eslint .", "start": "pnpm with-env next start", "start:test": "NODE_ENV=test pnpm with-env:test next start", "typecheck": "tsc --noEmit", "with-env": "dotenv -e ./.env.local --", "with-env:test": "dotenv -e ./.env.test --", "supabase": "supabase", "supabase:start": "supabase status || supabase start", "supabase:stop": "supabase stop", "supabase:reset": "supabase db reset", "supabase:status": "supabase status", "supabase:test": "supabase db test", "supabase:db:lint": "supabase db lint", "supabase:deploy": "supabase link --project-ref $SUPABASE_PROJECT_REF && supabase db push", "supabase:typegen": "pnpm run supabase:typegen:packages && pnpm run supabase:typegen:app", "supabase:typegen:packages": "supabase gen types typescript --local > ../../packages/supabase/src/database.types.ts", "supabase:typegen:app": "supabase gen types typescript --local > ./lib/database.types.ts", "supabase:db:dump:local": "supabase db dump --local --data-only", "supabase:seed:full": "pnpm run supabase:reset && pnpm run seed:images", "snaplet:sync": "npx @snaplet/seed sync", "snaplet:seed": "npx tsx snaplet/seed.ts", "seed:images:legacy": "npx tsx supabase/seed_images/seed-images.ts", "seed:images": "npx tsx scripts/seed/index.ts", "stripe:start": "docker run --rm -it --name=stripe -v ~/.config/stripe:/root/.config/stripe stripe/stripe-cli:latest listen --latest --forward-to http://host.docker.internal:3000/api/stripe/webhook", "stripe:delete-accounts": "NODE_ENV=development tsx scripts/stripe/delete-stripe-accounts.ts", "stripe:sync-customers": "NODE_ENV=development tsx scripts/stripe/sync-stripe-customers.ts", "stripe:sync-products": "NODE_ENV=development tsx scripts/stripe/sync-stripe-products.ts", "stripe:sync-accounts": "NODE_ENV=development tsx scripts/stripe/sync-stripe-accounts.ts", "stripe:sync-customer-subscriptions": "NODE_ENV=development tsx scripts/stripe/sync-customer-subscriptions.ts", "stripe:sync-community-products": "NODE_ENV=development tsx scripts/stripe/sync-community-products.ts", "stripe:add-community-plans": "NODE_ENV=development tsx scripts/stripe/add-community-plans.ts", "stripe:sync-all": "pnpm run stripe:sync-products -y && pnpm run stripe:sync-customers -y && pnpm run stripe:sync-accounts -y && pnpm run stripe:sync-customer-subscriptions -y && pnpm run stripe:sync-community-products -y && pnpm run stripe:add-community-plans -y", "stripe:cli": "NODE_ENV=development tsx scripts/index.ts"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@edge-csrf/nextjs": "2.5.3-cloudflare-rc1", "@hookform/resolvers": "^5.1.0", "@keystatic/next": "^5.0.4", "@kit/analytics": "workspace:*", "@kit/auth": "workspace:*", "@kit/cms": "workspace:*", "@kit/email-templates": "workspace:*", "@kit/i18n": "workspace:*", "@kit/keystatic": "workspace:*", "@kit/mailers": "workspace:*", "@kit/monitoring": "workspace:*", "@kit/next": "workspace:*", "@kit/notifications": "workspace:*", "@kit/shared": "workspace:*", "@kit/supabase": "workspace:*", "@kit/ui": "workspace:*", "@makerkit/data-loader-supabase-core": "^0.0.10", "@makerkit/data-loader-supabase-nextjs": "^1.2.5", "@marsidev/react-turnstile": "^1.1.0", "@radix-ui/react-icons": "^1.3.2", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "@supabase/supabase-js": "2.50.0", "@tanstack/react-query": "5.80.6", "@tanstack/react-table": "^8.21.3", "change-case": "^5.4.4", "date-fns": "^4.1.0", "lodash": "^4.17.21", "lowlight": "^3.3.0", "lucide-react": "^0.513.0", "nanoid": "^5.1.5", "next": "15.3.3", "next-sitemap": "^4.2.3", "next-themes": "0.4.6", "openai": "^5.1.1", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.57.0", "react-i18next": "^15.5.2", "recharts": "2.15.3", "server-only": "^0.0.1", "sonner": "^2.0.5", "stripe": "18.3.0-beta.1", "tailwind-merge": "^3.3.0", "use-debounce": "^10.0.5", "zod": "^3.25.56"}, "devDependencies": {"@kit/eslint-config": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@next/bundle-analyzer": "15.3.3", "@snaplet/copycat": "^6.0.0", "@snaplet/seed": "0.98.0", "@tailwindcss/postcss": "^4.1.8", "@tailwindcss/typography": "^0.5.16", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.17", "@types/mdx": "^2.0.13", "@types/node": "^22.15.30", "@types/pg": "^8.15.4", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "babel-plugin-react-compiler": "19.1.0-rc.2", "chalk": "^5.4.1", "class-variance-authority": "^0.7.1", "dotenv": "^16.5.0", "dotenv-cli": "^8.0.0", "eslint": "^9.28.0", "execa": "^9.6.0", "import-in-the-middle": "1.14.0", "inquirer": "^12.6.3", "jest": "^29.7.0", "jest-mock": "^29.7.0", "pg": "^8.16.0", "pino-pretty": "^13.0.0", "prettier": "^3.5.3", "react-markdown": "^10.1.0", "react-player": "^2.16.0", "require-in-the-middle": "7.5.2", "supabase": "^2.24.3", "tailwindcss": "^4.1.8", "tsup": "^8.5.0", "tsx": "^4.19.4", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3", "uuid": "^11.1.0"}, "prettier": "@kit/prettier-config", "browserslist": ["last 1 versions", "> 0.7%", "not dead"], "@snaplet/seed": {"config": "snaplet/seed.config.ts"}}