{"home": {"pageTitle": "Home"}, "settings": {"pageTitle": "Settings", "pageDescription": "Manage your Community details", "communityImages": "Community Images", "communityImagesDescription": "Update your community's images to make it easier to identify", "logoPictureHeading": "Logo", "logoPictureUploadHeading": "Upload a Logo", "logoPictureUploadSubheading": "Choose a photo to upload as your logo.", "coverImageHeading": "Cover", "coverImageUploadHeading": "Upload a Cover Image", "coverImageUploadSubheading": "Choose a photo to upload as your cover image.", "communityDetails": "Details", "communityDetailsDescription": "Update your community's details", "communityNameInputLabel": "Name", "communityNameInputDescription": "Your community name should be unique and descriptive", "communitySlugInputLabel": "Slug", "communitySlugInputDescription": "The slug is used in your community's URL. Use lowercase letters, numbers, and hyphens only.", "communityDescriptionInputLabel": "Description", "communityDescriptionInputDescription": "The description is used to describe your community. It helps users find your community.", "communityCategoryInputLabel": "Category", "communityCategoryInputDescription": "The category is used to categorize your community. It helps users find your community.", "communityLanguageInputLabel": "Language", "communityLanguageInputDescription": "The language is used to set the language of your community. It helps users find your community.", "communityPrimaryColorInputLabel": "Primary Color", "communityPrimaryColorInputDescription": "The primary color is used to set the primary color of your community. It helps users find your community.", "dangerZone": "Danger Zone", "dangerZoneDescription": "This section contains actions that are irreversible", "communityIsPrivateInputLabel": "Visibility", "privateTabLabel": "Private", "privateTabDescription": "Community content and membership are visible only to members, and search engines cannot index this content.", "publicTabLabel": "Public", "publicTabDescription": "Community content and membership are visible to everyone, and search engines can index this content.", "detailsTabLabel": "Details", "imagesTabLabel": "Images", "forumCategoriesTabLabel": "Categories", "payoutsTabLabel": "Payouts", "plansTabLabel": "Plans", "plansTitle": "Membership Plans", "plansDescription": "Manage your community's membership plans and pricing", "trialDaysBadge": "{{trialDays}}-day free trial", "billingTabLabel": "Billing", "billingTabDescription": "Manage your community's billing", "deleteTabLabel": "Delete", "noAccount": "You need to connect your Stripe account to manage membership plans", "connectAccountButtonLabel": "Connect Stripe Account", "explorePageRequestTabLabel": "Explore Page", "explorePageRequest": {"errorLoadingStatus": "Failed to load explore page status", "title": "Explore Page Request", "description": "Request to add your community to the explore page for greater visibility", "notListed": "Not listed", "communityIsListed": "Listed", "successTitle": "Congratulations!", "successMessage": "Your community is listed on the explore page.", "successDescription": "Your community is now discoverable by users browsing the explore page.", "readyToList": "Ready to list", "requestInfo": "To be listed on the explore page, you need to request to be added.", "requirementsTitle": "Requirements:", "requirements": {"communityName": "Community name", "aboutPageMedia": "About page with media ({{current}}/{{required}})", "aboutPage": "About page filled out", "aboutPageContent": "About page with meaningful content ({{current}}/{{required}} chars)", "qualityContent": "High quality content", "activeCommunity": "Active community", "coverAndLogo": "Cover & logo images", "cover": "Cover image", "logo": "Logo image", "communityDescription": "Quality community description ({{current}}/{{required}} chars)", "communityCategory": "Community category"}, "requestButton": "Request to be added", "pendingApproval": "Pending Approval", "pendingTitle": "Your request is under review", "pendingDescription": "We're reviewing your request to be added to the explore page. This process may take a few days.", "requestRejected": "Request Rejected", "rejectedTitle": "Your request was rejected", "rejectedDescription": "Unfortunately, your request to be added to the explore page was rejected. You can address the feedback below and submit a new request.", "rejectionReason": "Rejection Reason:", "waitingPeriod": "Waiting Period:", "canReapplyOn": "You can reapply on {{date}}", "reapplyButton": "Reapply for listing"}}, "members": {"pageTitle": "Members", "communityTeamMembersTabLabel": "Team", "communityMembersTabLabel": "Members", "communityPendingInvitesTabLabel": "Pending Invites", "joinCommunityCTA": {"loginTitle": "<PERSON>gin to join community", "loginDescription": "Login to join community to engage and connect with others", "loginButtonLabel": "<PERSON><PERSON>", "joinTitle": "Join community", "joinDescription": "Join community to engage and connect with others", "joinButtonLabel": "Join Community"}}, "billing": {"pageTitle": "Billing"}, "yourCommunities": "Your Communities ({{communitiesCount}})", "createCommunity": "Create a Community", "creatingCommunity": "Creating Community...", "personalAccount": "Personal Account", "searchCommunities": "Search Communities...", "membersTabLabel": "Members", "memberName": "Name", "youLabel": "You", "emailLabel": "Email", "roleLabel": "Role", "primaryOwnerLabel": "Primary Owner", "joinedAtLabel": "Joined at", "invitedAtLabel": "Invited at", "inviteMembersPageSubheading": "Invite members to your Community", "createCommunityModalHeading": "Create Community", "createCommunityModalDescription": "Create a new Community to manage your projects and members.", "communityNameLabel": "Community Name", "communityNameDescription": "Only letters, numbers, dashes, spaces, and ampersands allowed. You can change this later {{current}} / {{max}}", "communityNameValidationWarning": "Special characters are not allowed. Only letters, numbers, dashes, spaces, and ampersands.", "createCommunitySubmitLabel": "Create Community", "createTeamSuccess": "Community created successfully", "createTeamError": "Community not created. Please try again.", "createTeamLoading": "Creating community...", "settingsPageLabel": "General", "createTeamDropdownLabel": "New community", "changeRole": "Change Role", "removeMember": "Remove from Community", "inviteMembersSuccess": "Members invited successfully!", "inviteMembersError": "Sorry, we encountered an error! Please try again", "inviteMembersLoading": "Inviting members...", "removeInviteButtonLabel": "Remove invite", "addAnotherMemberButtonLabel": "Add another one", "inviteMembersButtonLabel": "Send Invites", "removeMemberModalHeading": "You are removing this user", "removeMemberModalDescription": "Remove this member from the community. They will no longer have access to the community.", "removeMemberSuccessMessage": "Member removed successfully", "removeMemberErrorMessage": "Sorry, we encountered an error. Please try again", "removeMemberErrorHeading": "Sorry, we couldn't remove the selected member.", "removeMemberLoadingMessage": "Removing member...", "removeMemberSubmitLabel": "Remove User from Community", "chooseDifferentRoleError": "Role is the same as the current one", "updateRole": "Update Role", "updateRoleLoadingMessage": "Updating role...", "updateRoleSuccessMessage": "Role updated successfully", "updatingRoleErrorMessage": "Sorry, we encountered an error. Please try again.", "updateMemberRoleModalHeading": "Update Member's Role", "updateMemberRoleModalDescription": "Change the role of the selected member. The role determines the permissions of the member.", "roleMustBeDifferent": "Role must be different from the current one", "memberRoleInputLabel": "Member role", "updateRoleDescription": "Pick a role for this member.", "updateRoleSubmitLabel": "Update Role", "transferOwnership": "Transfer Ownership", "transferOwnershipDescription": "Transfer ownership of the community to another member.", "transferOwnershipInputLabel": "Please type TRANSFER to confirm the transfer of ownership.", "transferOwnershipInputDescription": "By transferring ownership, you will no longer be the primary owner of the community.", "sendMemberEmail": "Send Email", "deleteInvitation": "Delete Invitation", "deleteInvitationDialogDescription": "You are about to delete the invitation. The user will no longer be able to join the community.", "deleteInviteSuccessMessage": "Invite deleted successfully", "deleteInviteErrorMessage": "Invite not deleted. Please try again.", "deleteInviteLoadingMessage": "Deleting invite. Please wait...", "confirmDeletingMemberInvite": "You are deleting the invite to <strong>{{ email }}</strong>", "transferOwnershipDisclaimer": "You are transferring ownership of the selected community to <strong>{{ member }}</strong>.", "transferringOwnership": "Transferring ownership...", "transferOwnershipSuccess": "Ownership successfully transferred", "transferOwnershipError": "Sorry, we could not transfer ownership to the selected member. Please try again.", "deleteInviteSubmitLabel": "Delete Invite", "youBadgeLabel": "You", "updateCommunityLoadingMessage": "Updating Community...", "updateCommunitySuccessMessage": "Community successfully updated", "updateCommunityErrorMessage": "Could not update <PERSON>. Please try again.", "updateLogoErrorMessage": "Could not update <PERSON><PERSON>. Please try again.", "communityLogoInputSubheading": "Please choose a photo to upload as your community logo.", "updateCommunitySubmitLabel": "Update Community", "inviteMembersHeading": "Invite Members to your Community", "inviteMembersDescription": "Invite members to your community by entering their email and role.", "emailPlaceholder": "<EMAIL>", "membersPageHeading": "Members", "inviteMembersButton": "Add Team Members", "invitingMembers": "Inviting members...", "inviteMembersSuccessMessage": "Members invited successfully", "inviteMembersErrorMessage": "Sorry, members could not be invited. Please try again.", "pendingInvitesHeading": "Pending Invites", "pendingInvitesDescription": " Here you can manage the pending invitations to your community.", "noPendingInvites": "No pending invites found", "loadingMembers": "Loading members...", "loadMembersError": "Sorry, we couldn't fetch your communities members.", "loadInvitedMembersError": "Sorry, we couldn't fetch your communities invited members.", "loadingInvitedMembers": "Loading invited members...", "invitedBadge": "Invited", "duplicateInviteEmailError": "You have already entered this email address", "invitingOwnAccountError": "Hey, that's your email!", "dangerZone": "Danger Zone", "dangerZoneSubheading": "Delete or leave your community", "deleteCommunity": "Delete Community", "deleteCommunityDescription": "This action cannot be undone. All data associated with this community will be deleted.", "deletingCommunity": "Deleting community", "deleteCommunityModalHeading": "Deleting Community", "deletingCommunityDescription": "You are about to delete the community {{ communityName }}. This action cannot be undone.", "deleteCommunityInputField": "Type the name of the community to confirm", "leaveCommunity": "Leave Community", "leaving": "Leaving community...", "leavingCommunityModalHeading": "Leaving Community", "leavingCommunityModalDescription": "You are about to leave this community. You will no longer have access to it.", "leaveCommunityDescription": "Click the button below to leave the community. Remember, you will no longer have access to it and will need to join again.", "deleteCommunityDisclaimer": "You are deleting the community <strong>{{ communityName }}</strong>. This action cannot be undone.", "deleteCommunityDisclaimerInputField": "Type the name of the community to confirm <strong>{{ communityName }}</strong>", "leaveCommunityDisclaimer": "You are leaving the community <strong>{{ communityName }}</strong>. You will no longer have access to it.", "deleteCommunityErrorHeading": "Sorry, we couldn't delete your community.", "leaveCommunityErrorHeading": "Sorry, we couldn't leave your community.", "searchMembersPlaceholder": "Search members", "createCommunityErrorHeading": "Sorry, we couldn't create your community.", "createCommunityErrorMessage": "We encountered an error creating your community. Please try again.", "transferCommunityErrorHeading": "Sorry, we couldn't transfer ownership of your community.", "transferCommunityErrorMessage": "We encountered an error transferring ownership of your community. Please try again.", "updateRoleErrorHeading": "Sorry, we couldn't update the role of the selected member.", "updateRoleErrorMessage": "We encountered an error updating the role of the selected member. Please try again.", "searchInvitations": "Search Invitations", "updateInvitation": "Update Invitation", "removeInvitation": "Remove Invitation", "acceptInvitation": "Accept Invitation", "renewInvitation": "Renew Invitation", "resendInvitation": "Resend Invitation", "expiresAtLabel": "Expires at", "expired": "Expired", "active": "Active", "inviteStatus": "Status", "inviteNotFoundOrExpired": "Invite not found or expired", "inviteNotFoundOrExpiredDescription": "The invite you are looking for is either expired or does not exist. Please contact the community owner to renew the invite.", "backToHome": "Back to Home", "renewInvitationDialogDescription": "You are about to renew the invitation to {{ email }}. The user will be able to join the community.", "renewInvitationErrorTitle": "Sorry, we couldn't renew the invitation.", "renewInvitationErrorDescription": "We encountered an error renewing the invitation. Please try again.", "signInWithDifferentAccount": "Sign in with a different account", "signInWithDifferentAccountDescription": "If you wish to accept the invitation with a different account, please sign out and back in with the account you wish to use.", "acceptInvitationHeading": "Accept Invitation to join {{accountName}}", "acceptInvitationDescription": "You have been invited to join the community {{accountName}}. If you wish to accept the invitation, please click the button below.", "continueAs": "Continue as {{email}}", "createCommunityButtonLabel": "Create a Community", "joinCommunity": "Join Community", "joiningCommunity": "Joining community...", "leaveCommunityInputLabel": "Please type LEAVE to confirm leaving the community.", "leaveCommunityInputDescription": "By leaving the community, you will no longer have access to it.", "reservedNameError": "This name is reserved. Please choose a different one.", "about": {"pageTitle": "About"}, "categories": {"arts_and_crafts": "Art and Crafts", "beauty_and_fashion": "Beauty and Fashion", "business": "Business", "career": "Career", "crypto": "Crypto", "dating": "Dating", "e_commerce": "E-commerce", "finance": "Finance", "gaming": "Gaming", "health_and_fitness": "Health and Fitness", "hobbies": "Hobbies", "languages": "Languages", "misc": "Misc", "music": "Music", "personal_development": "Personal Development", "pets": "Pets", "photo_and_video": "Photo and Video", "podcasts": "Podcasts", "productivity": "Productivity", "programming": "Software Development", "real_estate": "Real Estate", "relationships": "Relationships", "sales_and_marketing": "Sales and Marketing", "social_media": "Social Media", "spirituality": "Spirituality", "sports": "Sports", "tech": "Technology & Gadgets", "trading": "Trading", "travel": "Travel"}, "languages": {"english": "English", "french": "French", "spanish": "Spanish", "german": "German", "italian": "Italian", "russian": "Russian", "japanese": "Japanese", "chinese": "Chinese", "korean": "Korean"}, "createPriceTitle": "Create Price", "createPriceDescription": "Set up a new price for your community plan", "updatePriceTitle": "Update Price", "updatePriceDescription": "Update the details of your existing price", "priceType": "Price Type", "oneTimePrice": "One-time Payment", "oneTimePriceDescription": "Members pay a single payment to unlock access", "recurringPrice": "Recurring Payment", "recurringPriceDescription": "Members pay a subscription to maintain access", "priceInterval": "Billing Interval", "monthlyInterval": "Monthly billing", "monthlyIntervalDescription": "Members are billed every month", "yearlyInterval": "Yearly billing", "yearlyIntervalDescription": "Members are billed once per year", "monthYearInterval": "Monthly & Yearly", "monthYearIntervalDescription": "Offer both monthly and yearly billing options", "monthlyPriceAmount": "Monthly Price", "yearlyPriceAmount": "Yearly Price", "priceAmount": "Price Amount", "priceNickname": "Price Nickname (Optional)", "createPriceError": "Failed to create price. Please try again.", "updatePriceError": "Failed to update price. Please try again.", "creatingPrice": "Creating price...", "updatingPrice": "Updating price...", "createPrice": "Create Price", "updatePrice": "Update Price", "priceActive": "Active", "priceTypeRecurring": "Subscription", "priceTypeOneTime": "One-time payment", "deletePriceTitle": "Delete Price", "deletePriceDescription": "Are you sure you want to delete this price? This action cannot be undone.", "deletePriceError": "Failed to delete price. Please try again.", "deletingPrice": "Deleting price...", "confirmDeletePrice": "Delete Price", "deletePrice": "Delete Price", "loadCommunityPlanError": "Failed to load community plan", "noCommunityPlan": "No community plan found", "communityPlanPrices": "Plan Prices", "noPrices": "No prices created yet", "defaultProductName": "Community Free Plan", "defaultProductDescription": "Community Free Plan is the default product for the community. It is used as the default product in the community. This product can not be removed.", "freeTrialBlurb": "{{trialDays}}-day free trial. Your first charge will be on {{nextPaymentDate}} for {{price}}. Cancel anytime with 1-click. You accept our <Link>terms</Link>.", "oneTimePaymentBlurb": "One-time payment of {{price}} for lifetime access to this community.", "recurringPaymentBlurb": "Recurring payment of {{price}} to maintain access to this community.", "discountPercentage": "Yearly Discount Percentage", "discountDescription": "Select a discount to automatically calculate yearly price", "discountNone": "None", "discount10": "10%", "discount20": "20%", "discount25": "25%", "discount50": "50%", "discountBest": "Best", "updateProductSettings": "Update Plan Settings", "updateProductSettingsDescription": "Update the settings for your plan", "updateTrialPeriod": "Trial Period", "trialPeriodExplanation": "Offer a free trial to allow users to try your plan before they're charged", "yearlyPriceWithDiscount": "Yearly Price (Discount recommended)", "communityPlans": {"noTrial": "No Trial", "threeDaysTrial": "3-Day Trial", "sevenDaysTrial": "7-Day Trial", "fourteenDaysTrial": "14-Day Trial", "thirtyDaysTrial": "30-Day Trial", "oneTime": "One-time", "monthly": "Monthly", "yearly": "Yearly", "discount": "Discount"}, "pricing": {"intervals": {"day": "Day", "week": "Week", "month": "Month", "year": "Year", "once": "Once"}, "labels": {"daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "annual": "Annual"}, "billing": {"billedDaily": "billed daily", "billedWeekly": "billed weekly", "billedMonthly": "billed monthly", "billedAnnually": "billed annually"}}, "priceValidation": {"wholeNumber": "Price must be a whole number between $1 and $999", "yearlyRequired": "Yearly amount is required when selecting month & year interval"}, "visibility": {"shown_on_profile": "Community is now visible on your profile", "hidden_from_profile": "Community is now hidden from your profile", "toggle_error": "Failed to update visibility setting", "shown_tooltip": "This community is visible on your profile", "hidden_tooltip": "This community is hidden from your public profile"}, "joinCommunityTitle": "Join {{name}}", "selectPricingPlan": "Select your pricing plan for {{name}}", "completePaymentFor": "Complete payment for {{price}} to join {{name}}", "continueToPayment": "Continue to Payment - {{price}}", "selectedPlan": "Selected Plan", "joinPaidCommunityDescription": "Join {{name}} for {{price}} and get access to exclusive content and community features.", "paymentSetupFailed": "Payment setup failed. Please try again.", "preparingPaymentForm": "Setting up your payment form...", "joinCommunityErrorHeading": "Payment Error", "joinCommunityErrorMessage": "We encountered an issue processing your payment. Please try again.", "startFreeTrial": "Start Free Trial", "joinNow": "Join Now", "freeTrialBadge": "{{trialDays}}-day free trial", "membershipTrialInfo": "Start with a {{trialDays}}-day free trial, then continue with your selected plan.", "startFreeTrialButton": "Start Free Trial", "joinCommunityButton": "Join Community", "joinFreeCommunityButton": "Join Free Community", "joinPaidCommunityButton": "Join {{price}}", "addMediaTitle": "Add Media", "addMediaDescription": "Upload an image or add a video link from YouTube or Vimeo.", "addMediaButton": "Add Media", "processingMedia": "Processing...", "addMedia": "Add", "imageTab": "Image", "videoTab": "Video", "ariaLabels": {"productNickname": "Product nickname", "editProduct": "Edit product {{productName}}", "updateProduct": "Update product", "searchInvitationsInput": "Search invitations", "invitationExpired": "Invitation expired", "invitationActive": "Invitation active", "actionsForInvitation": "Actions for invitation {{email}}", "updateInvitationAction": "Update invitation", "renewInvitationAction": "Renew invitation", "removeInvitationAction": "Remove invitation", "deleteMedia": "Delete media {{caption}}", "activateProduct": "Activate product", "deactivateProduct": "Deactivate product", "invitationEmailAriaLabel": "Email for invitation {{number}}", "requestExplorePageAriaLabel": "Request explore page"}, "noSessionIdOrCommunityNameProvided": "No session ID or community name provided", "settingUpYourCommunity": "Setting up your community...", "yourNewCommunity": "Your New Community", "SetupCommunityTitle": "Community Setup Complete!", "communitySlugProcessing": "Community slug is being processed...", "goToCommunity": "Go to Community", "processingMembership": "Processing your membership...", "membershipTakingLonger": "Membership processing is taking longer than expected...", "membershipComplete": "Your membership has been activated!", "welcomeToCommunity": "Welcome to {{name}}!", "noSessionIdProvided": "No session ID provided", "createCommunityBenefits1": "Build and grow your own community", "createCommunityBenefits2": "Engage with members and create content", "createCommunityBenefits3": "Monetize your expertise and knowledge", "confirmFreeMembership": "Ready to join this free community?", "settingUpFreeMembership": "Setting up your free membership...", "joinFreeCommunityTitle": "Join Free Community", "joinFreeCommunityDescription": "You're about to join {{name}} for free. <PERSON><PERSON> continue to complete your membership.", "setupTakingLonger": "Setup is taking longer than expected...", "communityCreationInProgress": "We're creating your community in the background. This usually takes just a few seconds.", "communityCreationTimeout": "You'll receive an email confirmation when your community is ready."}