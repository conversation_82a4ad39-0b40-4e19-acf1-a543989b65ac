export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          operationName?: string
          query?: string
          variables?: Json
          extensions?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      checkout_sessions: {
        Row: {
          community_id: string | null
          community_name: string | null
          created_at: string | null
          id: string
          metadata: Json
          price_id: string
          purchase_type: Database["public"]["Enums"]["purchase_type"]
          status: Database["public"]["Enums"]["checkout_session_status"]
          trial_days: number
          updated_at: string | null
          user_id: string
        }
        Insert: {
          community_id?: string | null
          community_name?: string | null
          created_at?: string | null
          id: string
          metadata: Json
          price_id: string
          purchase_type: Database["public"]["Enums"]["purchase_type"]
          status?: Database["public"]["Enums"]["checkout_session_status"]
          trial_days?: number
          updated_at?: string | null
          user_id: string
        }
        Update: {
          community_id?: string | null
          community_name?: string | null
          created_at?: string | null
          id?: string
          metadata?: Json
          price_id?: string
          purchase_type?: Database["public"]["Enums"]["purchase_type"]
          status?: Database["public"]["Enums"]["checkout_session_status"]
          trial_days?: number
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "checkout_sessions_price_id_fkey"
            columns: ["price_id"]
            isOneToOne: false
            referencedRelation: "product_prices"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "checkout_sessions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["primary_owner_user_id"]
          },
          {
            foreignKeyName: "checkout_sessions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "public_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "checkout_sessions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_subscriptions_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "checkout_sessions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      communities: {
        Row: {
          category_id: string
          country_id: string
          cover_url: string | null
          created_at: string | null
          created_by_user_id: string | null
          default_product_id: string | null
          description: string | null
          id: string
          is_enabled: boolean | null
          is_listed: boolean | null
          is_private: boolean | null
          language_id: string
          logo_url: string | null
          name: string
          primary_color: string
          primary_owner_user_id: string
          slug: string
          socials: Json
          subscription_id: string | null
          updated_at: string | null
          updated_by_user_id: string | null
        }
        Insert: {
          category_id: string
          country_id: string
          cover_url?: string | null
          created_at?: string | null
          created_by_user_id?: string | null
          default_product_id?: string | null
          description?: string | null
          id?: string
          is_enabled?: boolean | null
          is_listed?: boolean | null
          is_private?: boolean | null
          language_id: string
          logo_url?: string | null
          name: string
          primary_color?: string
          primary_owner_user_id?: string
          slug: string
          socials?: Json
          subscription_id?: string | null
          updated_at?: string | null
          updated_by_user_id?: string | null
        }
        Update: {
          category_id?: string
          country_id?: string
          cover_url?: string | null
          created_at?: string | null
          created_by_user_id?: string | null
          default_product_id?: string | null
          description?: string | null
          id?: string
          is_enabled?: boolean | null
          is_listed?: boolean | null
          is_private?: boolean | null
          language_id?: string
          logo_url?: string | null
          name?: string
          primary_color?: string
          primary_owner_user_id?: string
          slug?: string
          socials?: Json
          subscription_id?: string | null
          updated_at?: string | null
          updated_by_user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "communities_default_product_id_fkey"
            columns: ["default_product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "communities_primary_owner_user_id_fkey"
            columns: ["primary_owner_user_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["primary_owner_user_id"]
          },
          {
            foreignKeyName: "communities_primary_owner_user_id_fkey"
            columns: ["primary_owner_user_id"]
            isOneToOne: false
            referencedRelation: "public_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "communities_primary_owner_user_id_fkey"
            columns: ["primary_owner_user_id"]
            isOneToOne: false
            referencedRelation: "user_subscriptions_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "communities_primary_owner_user_id_fkey"
            columns: ["primary_owner_user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "communities_subscription_id_fkey"
            columns: ["subscription_id"]
            isOneToOne: false
            referencedRelation: "subscriptions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_communities_category"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "community_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_communities_category"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["category_id"]
          },
          {
            foreignKeyName: "fk_communities_country"
            columns: ["country_id"]
            isOneToOne: false
            referencedRelation: "countries"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_communities_language"
            columns: ["language_id"]
            isOneToOne: false
            referencedRelation: "languages"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_communities_language"
            columns: ["language_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["language_id"]
          },
        ]
      }
      community_categories: {
        Row: {
          description: string | null
          icon: string
          id: string
          name: string
          slug: string
        }
        Insert: {
          description?: string | null
          icon: string
          id?: string
          name: string
          slug: string
        }
        Update: {
          description?: string | null
          icon?: string
          id?: string
          name?: string
          slug?: string
        }
        Relationships: []
      }
      community_course_chapters: {
        Row: {
          course_id: string
          created_at: string
          description: string | null
          icon: string | null
          id: string
          sequence_order: number
          title: string
          updated_at: string
        }
        Insert: {
          course_id: string
          created_at?: string
          description?: string | null
          icon?: string | null
          id?: string
          sequence_order: number
          title: string
          updated_at?: string
        }
        Update: {
          course_id?: string
          created_at?: string
          description?: string | null
          icon?: string | null
          id?: string
          sequence_order?: number
          title?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "community_course_chapters_course_id_fkey"
            columns: ["course_id"]
            isOneToOne: false
            referencedRelation: "community_courses"
            referencedColumns: ["id"]
          },
        ]
      }
      community_course_lessons: {
        Row: {
          chapter_id: string | null
          character_count: number | null
          content_data: Json | null
          content_type:
            | Database["public"]["Enums"]["community_course_content_type_enum"]
            | null
          course_id: string
          created_at: string
          duration_seconds: number | null
          icon: string | null
          id: string
          lesson_text: Json | null
          sequence_order: number
          status:
            | Database["public"]["Enums"]["community_content_status_enum"]
            | null
          title: string
          updated_at: string
          word_count: number | null
        }
        Insert: {
          chapter_id?: string | null
          character_count?: number | null
          content_data?: Json | null
          content_type?:
            | Database["public"]["Enums"]["community_course_content_type_enum"]
            | null
          course_id: string
          created_at?: string
          duration_seconds?: number | null
          icon?: string | null
          id?: string
          lesson_text?: Json | null
          sequence_order: number
          status?:
            | Database["public"]["Enums"]["community_content_status_enum"]
            | null
          title: string
          updated_at?: string
          word_count?: number | null
        }
        Update: {
          chapter_id?: string | null
          character_count?: number | null
          content_data?: Json | null
          content_type?:
            | Database["public"]["Enums"]["community_course_content_type_enum"]
            | null
          course_id?: string
          created_at?: string
          duration_seconds?: number | null
          icon?: string | null
          id?: string
          lesson_text?: Json | null
          sequence_order?: number
          status?:
            | Database["public"]["Enums"]["community_content_status_enum"]
            | null
          title?: string
          updated_at?: string
          word_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "community_course_lessons_chapter_id_fkey"
            columns: ["chapter_id"]
            isOneToOne: false
            referencedRelation: "community_course_chapters"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_course_lessons_course_id_fkey"
            columns: ["course_id"]
            isOneToOne: false
            referencedRelation: "community_courses"
            referencedColumns: ["id"]
          },
        ]
      }
      community_course_prerequisites: {
        Row: {
          course_id: string
          created_at: string
          id: string
          prerequisite_id: string
        }
        Insert: {
          course_id: string
          created_at?: string
          id?: string
          prerequisite_id: string
        }
        Update: {
          course_id?: string
          created_at?: string
          id?: string
          prerequisite_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "community_course_prerequisites_course_id_fkey"
            columns: ["course_id"]
            isOneToOne: false
            referencedRelation: "community_courses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_course_prerequisites_prerequisite_id_fkey"
            columns: ["prerequisite_id"]
            isOneToOne: false
            referencedRelation: "community_courses"
            referencedColumns: ["id"]
          },
        ]
      }
      community_courses: {
        Row: {
          access:
            | Database["public"]["Enums"]["community_course_access_enum"]
            | null
          access_details: Json | null
          community_id: string
          cover_url: string | null
          created_at: string
          description: string | null
          id: string
          sequence_order: number
          slug: string | null
          status:
            | Database["public"]["Enums"]["community_content_status_enum"]
            | null
          title: string
          updated_at: string
        }
        Insert: {
          access?:
            | Database["public"]["Enums"]["community_course_access_enum"]
            | null
          access_details?: Json | null
          community_id: string
          cover_url?: string | null
          created_at?: string
          description?: string | null
          id?: string
          sequence_order: number
          slug?: string | null
          status?:
            | Database["public"]["Enums"]["community_content_status_enum"]
            | null
          title: string
          updated_at?: string
        }
        Update: {
          access?:
            | Database["public"]["Enums"]["community_course_access_enum"]
            | null
          access_details?: Json | null
          community_id?: string
          cover_url?: string | null
          created_at?: string
          description?: string | null
          id?: string
          sequence_order?: number
          slug?: string | null
          status?:
            | Database["public"]["Enums"]["community_content_status_enum"]
            | null
          title?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "community_courses_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_courses_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_courses_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "user_communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_courses_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "user_created_communities"
            referencedColumns: ["id"]
          },
        ]
      }
      community_explore_requests: {
        Row: {
          community_id: string | null
          created_at: string | null
          created_by_member_id: string | null
          created_by_user_id: string | null
          id: string
          reason: string | null
          status: Database["public"]["Enums"]["community_explore_request_status"]
          updated_at: string | null
          updated_by_user_id: string | null
        }
        Insert: {
          community_id?: string | null
          created_at?: string | null
          created_by_member_id?: string | null
          created_by_user_id?: string | null
          id?: string
          reason?: string | null
          status?: Database["public"]["Enums"]["community_explore_request_status"]
          updated_at?: string | null
          updated_by_user_id?: string | null
        }
        Update: {
          community_id?: string | null
          created_at?: string | null
          created_by_member_id?: string | null
          created_by_user_id?: string | null
          id?: string
          reason?: string | null
          status?: Database["public"]["Enums"]["community_explore_request_status"]
          updated_at?: string | null
          updated_by_user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "community_explore_requests_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_explore_requests_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_explore_requests_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "user_communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_explore_requests_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "user_created_communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_explore_requests_created_by_member_id_fkey"
            columns: ["created_by_member_id"]
            isOneToOne: false
            referencedRelation: "community_memberships"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_explore_requests_created_by_member_id_fkey"
            columns: ["created_by_member_id"]
            isOneToOne: false
            referencedRelation: "public_community_memberships"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_explore_requests_created_by_member_id_fkey"
            columns: ["created_by_member_id"]
            isOneToOne: false
            referencedRelation: "user_communities"
            referencedColumns: ["member_id"]
          },
          {
            foreignKeyName: "community_explore_requests_created_by_member_id_fkey"
            columns: ["created_by_member_id"]
            isOneToOne: false
            referencedRelation: "user_created_communities"
            referencedColumns: ["member_id"]
          },
          {
            foreignKeyName: "community_explore_requests_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["primary_owner_user_id"]
          },
          {
            foreignKeyName: "community_explore_requests_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "public_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_explore_requests_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "user_subscriptions_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_explore_requests_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_explore_requests_updated_by_user_id_fkey"
            columns: ["updated_by_user_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["primary_owner_user_id"]
          },
          {
            foreignKeyName: "community_explore_requests_updated_by_user_id_fkey"
            columns: ["updated_by_user_id"]
            isOneToOne: false
            referencedRelation: "public_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_explore_requests_updated_by_user_id_fkey"
            columns: ["updated_by_user_id"]
            isOneToOne: false
            referencedRelation: "user_subscriptions_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_explore_requests_updated_by_user_id_fkey"
            columns: ["updated_by_user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      community_forum_categories: {
        Row: {
          community_id: string
          description: string | null
          icon: string | null
          id: string
          name: string
          slug: string
        }
        Insert: {
          community_id: string
          description?: string | null
          icon?: string | null
          id?: string
          name: string
          slug: string
        }
        Update: {
          community_id?: string
          description?: string | null
          icon?: string | null
          id?: string
          name?: string
          slug?: string
        }
        Relationships: [
          {
            foreignKeyName: "community_forum_categories_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_forum_categories_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_forum_categories_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "user_communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_forum_categories_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "user_created_communities"
            referencedColumns: ["id"]
          },
        ]
      }
      community_forum_comments: {
        Row: {
          character_count: number | null
          content: Json
          created_at: string
          created_by_member_id: string | null
          created_by_user_id: string | null
          id: string
          parent_comment_id: string | null
          post_id: string
          updated_at: string
          word_count: number | null
        }
        Insert: {
          character_count?: number | null
          content: Json
          created_at?: string
          created_by_member_id?: string | null
          created_by_user_id?: string | null
          id?: string
          parent_comment_id?: string | null
          post_id: string
          updated_at?: string
          word_count?: number | null
        }
        Update: {
          character_count?: number | null
          content?: Json
          created_at?: string
          created_by_member_id?: string | null
          created_by_user_id?: string | null
          id?: string
          parent_comment_id?: string | null
          post_id?: string
          updated_at?: string
          word_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "community_forum_comments_created_by_member_id_fkey"
            columns: ["created_by_member_id"]
            isOneToOne: false
            referencedRelation: "community_memberships"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_forum_comments_created_by_member_id_fkey"
            columns: ["created_by_member_id"]
            isOneToOne: false
            referencedRelation: "public_community_memberships"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_forum_comments_created_by_member_id_fkey"
            columns: ["created_by_member_id"]
            isOneToOne: false
            referencedRelation: "user_communities"
            referencedColumns: ["member_id"]
          },
          {
            foreignKeyName: "community_forum_comments_created_by_member_id_fkey"
            columns: ["created_by_member_id"]
            isOneToOne: false
            referencedRelation: "user_created_communities"
            referencedColumns: ["member_id"]
          },
          {
            foreignKeyName: "community_forum_comments_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["primary_owner_user_id"]
          },
          {
            foreignKeyName: "community_forum_comments_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "public_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_forum_comments_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "user_subscriptions_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_forum_comments_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_forum_comments_parent_comment_id_fkey"
            columns: ["parent_comment_id"]
            isOneToOne: false
            referencedRelation: "community_forum_comments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_forum_comments_post_id_fkey"
            columns: ["post_id"]
            isOneToOne: false
            referencedRelation: "community_forum_posts"
            referencedColumns: ["id"]
          },
        ]
      }
      community_forum_posts: {
        Row: {
          auto_saved_at: string | null
          category_id: string | null
          character_count: number | null
          community_id: string
          content: Json | null
          created_at: string
          created_by_member_id: string | null
          created_by_user_id: string | null
          draft_category_id: string | null
          draft_character_count: number | null
          draft_content: Json | null
          draft_title: string | null
          draft_word_count: number | null
          id: string
          is_pinned: boolean | null
          last_edited_at: string | null
          slug: string
          status: Database["public"]["Enums"]["community_forum_post_status"]
          title: string
          updated_at: string
          word_count: number | null
        }
        Insert: {
          auto_saved_at?: string | null
          category_id?: string | null
          character_count?: number | null
          community_id: string
          content?: Json | null
          created_at?: string
          created_by_member_id?: string | null
          created_by_user_id?: string | null
          draft_category_id?: string | null
          draft_character_count?: number | null
          draft_content?: Json | null
          draft_title?: string | null
          draft_word_count?: number | null
          id?: string
          is_pinned?: boolean | null
          last_edited_at?: string | null
          slug: string
          status?: Database["public"]["Enums"]["community_forum_post_status"]
          title: string
          updated_at?: string
          word_count?: number | null
        }
        Update: {
          auto_saved_at?: string | null
          category_id?: string | null
          character_count?: number | null
          community_id?: string
          content?: Json | null
          created_at?: string
          created_by_member_id?: string | null
          created_by_user_id?: string | null
          draft_category_id?: string | null
          draft_character_count?: number | null
          draft_content?: Json | null
          draft_title?: string | null
          draft_word_count?: number | null
          id?: string
          is_pinned?: boolean | null
          last_edited_at?: string | null
          slug?: string
          status?: Database["public"]["Enums"]["community_forum_post_status"]
          title?: string
          updated_at?: string
          word_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "community_forum_posts_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "community_forum_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_forum_posts_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_forum_posts_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_forum_posts_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "user_communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_forum_posts_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "user_created_communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_forum_posts_created_by_member_id_fkey"
            columns: ["created_by_member_id"]
            isOneToOne: false
            referencedRelation: "community_memberships"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_forum_posts_created_by_member_id_fkey"
            columns: ["created_by_member_id"]
            isOneToOne: false
            referencedRelation: "public_community_memberships"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_forum_posts_created_by_member_id_fkey"
            columns: ["created_by_member_id"]
            isOneToOne: false
            referencedRelation: "user_communities"
            referencedColumns: ["member_id"]
          },
          {
            foreignKeyName: "community_forum_posts_created_by_member_id_fkey"
            columns: ["created_by_member_id"]
            isOneToOne: false
            referencedRelation: "user_created_communities"
            referencedColumns: ["member_id"]
          },
          {
            foreignKeyName: "community_forum_posts_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["primary_owner_user_id"]
          },
          {
            foreignKeyName: "community_forum_posts_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "public_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_forum_posts_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "user_subscriptions_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_forum_posts_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_forum_posts_draft_category_id_fkey"
            columns: ["draft_category_id"]
            isOneToOne: false
            referencedRelation: "community_forum_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      community_forum_reactions: {
        Row: {
          comment_id: string | null
          created_at: string
          created_by_member_id: string | null
          created_by_user_id: string | null
          id: string
          post_id: string | null
          reaction_type: Database["public"]["Enums"]["community_forum_reaction_type"]
        }
        Insert: {
          comment_id?: string | null
          created_at?: string
          created_by_member_id?: string | null
          created_by_user_id?: string | null
          id?: string
          post_id?: string | null
          reaction_type: Database["public"]["Enums"]["community_forum_reaction_type"]
        }
        Update: {
          comment_id?: string | null
          created_at?: string
          created_by_member_id?: string | null
          created_by_user_id?: string | null
          id?: string
          post_id?: string | null
          reaction_type?: Database["public"]["Enums"]["community_forum_reaction_type"]
        }
        Relationships: [
          {
            foreignKeyName: "community_forum_reactions_comment_id_fkey"
            columns: ["comment_id"]
            isOneToOne: false
            referencedRelation: "community_forum_comments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_forum_reactions_created_by_member_id_fkey"
            columns: ["created_by_member_id"]
            isOneToOne: false
            referencedRelation: "community_memberships"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_forum_reactions_created_by_member_id_fkey"
            columns: ["created_by_member_id"]
            isOneToOne: false
            referencedRelation: "public_community_memberships"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_forum_reactions_created_by_member_id_fkey"
            columns: ["created_by_member_id"]
            isOneToOne: false
            referencedRelation: "user_communities"
            referencedColumns: ["member_id"]
          },
          {
            foreignKeyName: "community_forum_reactions_created_by_member_id_fkey"
            columns: ["created_by_member_id"]
            isOneToOne: false
            referencedRelation: "user_created_communities"
            referencedColumns: ["member_id"]
          },
          {
            foreignKeyName: "community_forum_reactions_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["primary_owner_user_id"]
          },
          {
            foreignKeyName: "community_forum_reactions_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "public_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_forum_reactions_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "user_subscriptions_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_forum_reactions_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_forum_reactions_post_id_fkey"
            columns: ["post_id"]
            isOneToOne: false
            referencedRelation: "community_forum_posts"
            referencedColumns: ["id"]
          },
        ]
      }
      community_invitations: {
        Row: {
          community_id: string
          created_at: string
          created_by_user_id: string
          email: string
          expires_at: string
          id: string
          invite_token: string
          invited_by_member_id: string
          role: string
          updated_at: string
        }
        Insert: {
          community_id: string
          created_at?: string
          created_by_user_id: string
          email: string
          expires_at?: string
          id?: string
          invite_token: string
          invited_by_member_id: string
          role: string
          updated_at?: string
        }
        Update: {
          community_id?: string
          created_at?: string
          created_by_user_id?: string
          email?: string
          expires_at?: string
          id?: string
          invite_token?: string
          invited_by_member_id?: string
          role?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "community_invitations_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_invitations_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_invitations_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "user_communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_invitations_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "user_created_communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_invitations_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["primary_owner_user_id"]
          },
          {
            foreignKeyName: "community_invitations_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "public_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_invitations_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "user_subscriptions_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_invitations_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_invitations_invited_by_member_id_fkey"
            columns: ["invited_by_member_id"]
            isOneToOne: false
            referencedRelation: "community_memberships"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_invitations_invited_by_member_id_fkey"
            columns: ["invited_by_member_id"]
            isOneToOne: false
            referencedRelation: "public_community_memberships"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_invitations_invited_by_member_id_fkey"
            columns: ["invited_by_member_id"]
            isOneToOne: false
            referencedRelation: "user_communities"
            referencedColumns: ["member_id"]
          },
          {
            foreignKeyName: "community_invitations_invited_by_member_id_fkey"
            columns: ["invited_by_member_id"]
            isOneToOne: false
            referencedRelation: "user_created_communities"
            referencedColumns: ["member_id"]
          },
          {
            foreignKeyName: "community_invitations_role_fkey"
            columns: ["role"]
            isOneToOne: false
            referencedRelation: "community_roles"
            referencedColumns: ["name"]
          },
        ]
      }
      community_learning_path_courses: {
        Row: {
          course_id: string
          created_at: string
          learning_path_id: string
          sequence_order: number
          updated_at: string
        }
        Insert: {
          course_id: string
          created_at?: string
          learning_path_id: string
          sequence_order: number
          updated_at?: string
        }
        Update: {
          course_id?: string
          created_at?: string
          learning_path_id?: string
          sequence_order?: number
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "community_learning_path_courses_course_id_fkey"
            columns: ["course_id"]
            isOneToOne: false
            referencedRelation: "community_courses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_learning_path_courses_learning_path_id_fkey"
            columns: ["learning_path_id"]
            isOneToOne: false
            referencedRelation: "community_learning_paths"
            referencedColumns: ["id"]
          },
        ]
      }
      community_learning_paths: {
        Row: {
          community_id: string
          cover_url: string | null
          created_at: string
          description: string | null
          id: string
          slug: string | null
          status:
            | Database["public"]["Enums"]["community_content_status_enum"]
            | null
          title: string
          updated_at: string
        }
        Insert: {
          community_id: string
          cover_url?: string | null
          created_at?: string
          description?: string | null
          id?: string
          slug?: string | null
          status?:
            | Database["public"]["Enums"]["community_content_status_enum"]
            | null
          title: string
          updated_at?: string
        }
        Update: {
          community_id?: string
          cover_url?: string | null
          created_at?: string
          description?: string | null
          id?: string
          slug?: string | null
          status?:
            | Database["public"]["Enums"]["community_content_status_enum"]
            | null
          title?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "community_learning_paths_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_learning_paths_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_learning_paths_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "user_communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_learning_paths_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "user_created_communities"
            referencedColumns: ["id"]
          },
        ]
      }
      community_memberships: {
        Row: {
          community_id: string
          community_role: string
          created_at: string
          created_by_user_id: string | null
          id: string
          latest_invoice_id: string | null
          price_id: string | null
          status: Database["public"]["Enums"]["community_membership_status"]
          subscription_id: string | null
          updated_at: string
          updated_by_user_id: string | null
          user_id: string
          visible_on_profile: boolean
        }
        Insert: {
          community_id: string
          community_role: string
          created_at?: string
          created_by_user_id?: string | null
          id?: string
          latest_invoice_id?: string | null
          price_id?: string | null
          status?: Database["public"]["Enums"]["community_membership_status"]
          subscription_id?: string | null
          updated_at?: string
          updated_by_user_id?: string | null
          user_id: string
          visible_on_profile?: boolean
        }
        Update: {
          community_id?: string
          community_role?: string
          created_at?: string
          created_by_user_id?: string | null
          id?: string
          latest_invoice_id?: string | null
          price_id?: string | null
          status?: Database["public"]["Enums"]["community_membership_status"]
          subscription_id?: string | null
          updated_at?: string
          updated_by_user_id?: string | null
          user_id?: string
          visible_on_profile?: boolean
        }
        Relationships: [
          {
            foreignKeyName: "community_memberships_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_memberships_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_memberships_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "user_communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_memberships_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "user_created_communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_memberships_community_role_fkey"
            columns: ["community_role"]
            isOneToOne: false
            referencedRelation: "community_roles"
            referencedColumns: ["name"]
          },
          {
            foreignKeyName: "community_memberships_latest_invoice_id_fkey"
            columns: ["latest_invoice_id"]
            isOneToOne: false
            referencedRelation: "invoices"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_memberships_price_id_fkey"
            columns: ["price_id"]
            isOneToOne: false
            referencedRelation: "product_prices"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_memberships_subscription_id_fkey"
            columns: ["subscription_id"]
            isOneToOne: false
            referencedRelation: "subscriptions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_memberships_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["primary_owner_user_id"]
          },
          {
            foreignKeyName: "community_memberships_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "public_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_memberships_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_subscriptions_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_memberships_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      community_page_media: {
        Row: {
          alt_text: string | null
          caption: string | null
          created_at: string | null
          created_by: string | null
          dimensions: Json | null
          display_order: number
          id: string
          media_type: Database["public"]["Enums"]["community_page_media_type"]
          page_id: string | null
          thumbnail_url: string | null
          updated_at: string | null
          url: string
        }
        Insert: {
          alt_text?: string | null
          caption?: string | null
          created_at?: string | null
          created_by?: string | null
          dimensions?: Json | null
          display_order: number
          id?: string
          media_type?: Database["public"]["Enums"]["community_page_media_type"]
          page_id?: string | null
          thumbnail_url?: string | null
          updated_at?: string | null
          url: string
        }
        Update: {
          alt_text?: string | null
          caption?: string | null
          created_at?: string | null
          created_by?: string | null
          dimensions?: Json | null
          display_order?: number
          id?: string
          media_type?: Database["public"]["Enums"]["community_page_media_type"]
          page_id?: string | null
          thumbnail_url?: string | null
          updated_at?: string | null
          url?: string
        }
        Relationships: [
          {
            foreignKeyName: "community_page_media_page_id_fkey"
            columns: ["page_id"]
            isOneToOne: false
            referencedRelation: "community_pages"
            referencedColumns: ["id"]
          },
        ]
      }
      community_pages: {
        Row: {
          character_count: number | null
          community_id: string | null
          content: Json | null
          created_at: string | null
          created_by_member_id: string | null
          created_by_user_id: string | null
          description: string | null
          id: string
          is_published: boolean | null
          meta_data: Json | null
          page_type: Database["public"]["Enums"]["community_page_type"]
          slug: string
          title: string
          updated_at: string | null
          updated_by_user_id: string | null
          word_count: number | null
        }
        Insert: {
          character_count?: number | null
          community_id?: string | null
          content?: Json | null
          created_at?: string | null
          created_by_member_id?: string | null
          created_by_user_id?: string | null
          description?: string | null
          id?: string
          is_published?: boolean | null
          meta_data?: Json | null
          page_type: Database["public"]["Enums"]["community_page_type"]
          slug: string
          title: string
          updated_at?: string | null
          updated_by_user_id?: string | null
          word_count?: number | null
        }
        Update: {
          character_count?: number | null
          community_id?: string | null
          content?: Json | null
          created_at?: string | null
          created_by_member_id?: string | null
          created_by_user_id?: string | null
          description?: string | null
          id?: string
          is_published?: boolean | null
          meta_data?: Json | null
          page_type?: Database["public"]["Enums"]["community_page_type"]
          slug?: string
          title?: string
          updated_at?: string | null
          updated_by_user_id?: string | null
          word_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "community_pages_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_pages_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_pages_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "user_communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_pages_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "user_created_communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_pages_created_by_member_id_fkey"
            columns: ["created_by_member_id"]
            isOneToOne: false
            referencedRelation: "community_memberships"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_pages_created_by_member_id_fkey"
            columns: ["created_by_member_id"]
            isOneToOne: false
            referencedRelation: "public_community_memberships"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_pages_created_by_member_id_fkey"
            columns: ["created_by_member_id"]
            isOneToOne: false
            referencedRelation: "user_communities"
            referencedColumns: ["member_id"]
          },
          {
            foreignKeyName: "community_pages_created_by_member_id_fkey"
            columns: ["created_by_member_id"]
            isOneToOne: false
            referencedRelation: "user_created_communities"
            referencedColumns: ["member_id"]
          },
        ]
      }
      community_role_permissions: {
        Row: {
          id: string
          permission: Database["public"]["Enums"]["community_permissions"]
          role: string
        }
        Insert: {
          id?: string
          permission: Database["public"]["Enums"]["community_permissions"]
          role: string
        }
        Update: {
          id?: string
          permission?: Database["public"]["Enums"]["community_permissions"]
          role?: string
        }
        Relationships: [
          {
            foreignKeyName: "community_role_permissions_role_fkey"
            columns: ["role"]
            isOneToOne: false
            referencedRelation: "community_roles"
            referencedColumns: ["name"]
          },
        ]
      }
      community_roles: {
        Row: {
          hierarchy_level: number
          name: string
        }
        Insert: {
          hierarchy_level: number
          name: string
        }
        Update: {
          hierarchy_level?: number
          name?: string
        }
        Relationships: []
      }
      community_team_payouts: {
        Row: {
          created_at: string
          id: string
          member_id: string
          payout_percentage: number
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          member_id: string
          payout_percentage: number
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          member_id?: string
          payout_percentage?: number
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "community_team_payouts_member_id_fkey"
            columns: ["member_id"]
            isOneToOne: true
            referencedRelation: "community_memberships"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_team_payouts_member_id_fkey"
            columns: ["member_id"]
            isOneToOne: true
            referencedRelation: "public_community_memberships"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_team_payouts_member_id_fkey"
            columns: ["member_id"]
            isOneToOne: true
            referencedRelation: "user_communities"
            referencedColumns: ["member_id"]
          },
          {
            foreignKeyName: "community_team_payouts_member_id_fkey"
            columns: ["member_id"]
            isOneToOne: true
            referencedRelation: "user_created_communities"
            referencedColumns: ["member_id"]
          },
        ]
      }
      community_ticket_messages: {
        Row: {
          attachment_url: string | null
          author: Database["public"]["Enums"]["community_message_author"]
          content: string
          created_at: string
          created_by_member_id: string | null
          created_by_user_id: string | null
          id: string
          ticket_id: string
          updated_at: string
        }
        Insert: {
          attachment_url?: string | null
          author: Database["public"]["Enums"]["community_message_author"]
          content: string
          created_at?: string
          created_by_member_id?: string | null
          created_by_user_id?: string | null
          id?: string
          ticket_id: string
          updated_at?: string
        }
        Update: {
          attachment_url?: string | null
          author?: Database["public"]["Enums"]["community_message_author"]
          content?: string
          created_at?: string
          created_by_member_id?: string | null
          created_by_user_id?: string | null
          id?: string
          ticket_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "community_ticket_messages_created_by_member_id_fkey"
            columns: ["created_by_member_id"]
            isOneToOne: false
            referencedRelation: "community_memberships"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_ticket_messages_created_by_member_id_fkey"
            columns: ["created_by_member_id"]
            isOneToOne: false
            referencedRelation: "public_community_memberships"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_ticket_messages_created_by_member_id_fkey"
            columns: ["created_by_member_id"]
            isOneToOne: false
            referencedRelation: "user_communities"
            referencedColumns: ["member_id"]
          },
          {
            foreignKeyName: "community_ticket_messages_created_by_member_id_fkey"
            columns: ["created_by_member_id"]
            isOneToOne: false
            referencedRelation: "user_created_communities"
            referencedColumns: ["member_id"]
          },
          {
            foreignKeyName: "community_ticket_messages_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["primary_owner_user_id"]
          },
          {
            foreignKeyName: "community_ticket_messages_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "public_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_ticket_messages_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "user_subscriptions_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_ticket_messages_created_by_user_id_fkey"
            columns: ["created_by_user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_ticket_messages_ticket_id_fkey"
            columns: ["ticket_id"]
            isOneToOne: false
            referencedRelation: "community_tickets"
            referencedColumns: ["id"]
          },
        ]
      }
      community_tickets: {
        Row: {
          assigned_to_member_id: string | null
          assigned_to_user_id: string | null
          category: string
          closed_at: string | null
          closed_by_member_id: string | null
          closed_by_user_id: string | null
          community_id: string
          created_at: string
          customer_email: string | null
          id: string
          priority: Database["public"]["Enums"]["community_ticket_priority"]
          resolution: string | null
          resolved_at: string | null
          resolved_by_member_id: string | null
          resolved_by_user_id: string | null
          status: Database["public"]["Enums"]["community_ticket_status"]
          title: string
          updated_at: string
        }
        Insert: {
          assigned_to_member_id?: string | null
          assigned_to_user_id?: string | null
          category?: string
          closed_at?: string | null
          closed_by_member_id?: string | null
          closed_by_user_id?: string | null
          community_id: string
          created_at?: string
          customer_email?: string | null
          id?: string
          priority?: Database["public"]["Enums"]["community_ticket_priority"]
          resolution?: string | null
          resolved_at?: string | null
          resolved_by_member_id?: string | null
          resolved_by_user_id?: string | null
          status?: Database["public"]["Enums"]["community_ticket_status"]
          title: string
          updated_at?: string
        }
        Update: {
          assigned_to_member_id?: string | null
          assigned_to_user_id?: string | null
          category?: string
          closed_at?: string | null
          closed_by_member_id?: string | null
          closed_by_user_id?: string | null
          community_id?: string
          created_at?: string
          customer_email?: string | null
          id?: string
          priority?: Database["public"]["Enums"]["community_ticket_priority"]
          resolution?: string | null
          resolved_at?: string | null
          resolved_by_member_id?: string | null
          resolved_by_user_id?: string | null
          status?: Database["public"]["Enums"]["community_ticket_status"]
          title?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "community_tickets_assigned_to_member_id_fkey"
            columns: ["assigned_to_member_id"]
            isOneToOne: false
            referencedRelation: "community_memberships"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_tickets_assigned_to_member_id_fkey"
            columns: ["assigned_to_member_id"]
            isOneToOne: false
            referencedRelation: "public_community_memberships"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_tickets_assigned_to_member_id_fkey"
            columns: ["assigned_to_member_id"]
            isOneToOne: false
            referencedRelation: "user_communities"
            referencedColumns: ["member_id"]
          },
          {
            foreignKeyName: "community_tickets_assigned_to_member_id_fkey"
            columns: ["assigned_to_member_id"]
            isOneToOne: false
            referencedRelation: "user_created_communities"
            referencedColumns: ["member_id"]
          },
          {
            foreignKeyName: "community_tickets_assigned_to_user_id_fkey"
            columns: ["assigned_to_user_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["primary_owner_user_id"]
          },
          {
            foreignKeyName: "community_tickets_assigned_to_user_id_fkey"
            columns: ["assigned_to_user_id"]
            isOneToOne: false
            referencedRelation: "public_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_tickets_assigned_to_user_id_fkey"
            columns: ["assigned_to_user_id"]
            isOneToOne: false
            referencedRelation: "user_subscriptions_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_tickets_assigned_to_user_id_fkey"
            columns: ["assigned_to_user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_tickets_closed_by_member_id_fkey"
            columns: ["closed_by_member_id"]
            isOneToOne: false
            referencedRelation: "community_memberships"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_tickets_closed_by_member_id_fkey"
            columns: ["closed_by_member_id"]
            isOneToOne: false
            referencedRelation: "public_community_memberships"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_tickets_closed_by_member_id_fkey"
            columns: ["closed_by_member_id"]
            isOneToOne: false
            referencedRelation: "user_communities"
            referencedColumns: ["member_id"]
          },
          {
            foreignKeyName: "community_tickets_closed_by_member_id_fkey"
            columns: ["closed_by_member_id"]
            isOneToOne: false
            referencedRelation: "user_created_communities"
            referencedColumns: ["member_id"]
          },
          {
            foreignKeyName: "community_tickets_closed_by_user_id_fkey"
            columns: ["closed_by_user_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["primary_owner_user_id"]
          },
          {
            foreignKeyName: "community_tickets_closed_by_user_id_fkey"
            columns: ["closed_by_user_id"]
            isOneToOne: false
            referencedRelation: "public_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_tickets_closed_by_user_id_fkey"
            columns: ["closed_by_user_id"]
            isOneToOne: false
            referencedRelation: "user_subscriptions_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_tickets_closed_by_user_id_fkey"
            columns: ["closed_by_user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_tickets_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_tickets_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_tickets_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "user_communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_tickets_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "user_created_communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_tickets_resolved_by_member_id_fkey"
            columns: ["resolved_by_member_id"]
            isOneToOne: false
            referencedRelation: "community_memberships"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_tickets_resolved_by_member_id_fkey"
            columns: ["resolved_by_member_id"]
            isOneToOne: false
            referencedRelation: "public_community_memberships"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_tickets_resolved_by_member_id_fkey"
            columns: ["resolved_by_member_id"]
            isOneToOne: false
            referencedRelation: "user_communities"
            referencedColumns: ["member_id"]
          },
          {
            foreignKeyName: "community_tickets_resolved_by_member_id_fkey"
            columns: ["resolved_by_member_id"]
            isOneToOne: false
            referencedRelation: "user_created_communities"
            referencedColumns: ["member_id"]
          },
          {
            foreignKeyName: "community_tickets_resolved_by_user_id_fkey"
            columns: ["resolved_by_user_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["primary_owner_user_id"]
          },
          {
            foreignKeyName: "community_tickets_resolved_by_user_id_fkey"
            columns: ["resolved_by_user_id"]
            isOneToOne: false
            referencedRelation: "public_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_tickets_resolved_by_user_id_fkey"
            columns: ["resolved_by_user_id"]
            isOneToOne: false
            referencedRelation: "user_subscriptions_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_tickets_resolved_by_user_id_fkey"
            columns: ["resolved_by_user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      community_user_content_progress: {
        Row: {
          completed_at: string | null
          id: string
          lesson_id: string
          member_id: string
          progress_data: Json | null
          started_at: string
          updated_at: string
          user_id: string
        }
        Insert: {
          completed_at?: string | null
          id?: string
          lesson_id: string
          member_id: string
          progress_data?: Json | null
          started_at?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          completed_at?: string | null
          id?: string
          lesson_id?: string
          member_id?: string
          progress_data?: Json | null
          started_at?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "community_user_content_progress_lesson_id_fkey"
            columns: ["lesson_id"]
            isOneToOne: false
            referencedRelation: "community_course_lessons"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_user_content_progress_member_id_fkey"
            columns: ["member_id"]
            isOneToOne: false
            referencedRelation: "community_memberships"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_user_content_progress_member_id_fkey"
            columns: ["member_id"]
            isOneToOne: false
            referencedRelation: "public_community_memberships"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_user_content_progress_member_id_fkey"
            columns: ["member_id"]
            isOneToOne: false
            referencedRelation: "user_communities"
            referencedColumns: ["member_id"]
          },
          {
            foreignKeyName: "community_user_content_progress_member_id_fkey"
            columns: ["member_id"]
            isOneToOne: false
            referencedRelation: "user_created_communities"
            referencedColumns: ["member_id"]
          },
          {
            foreignKeyName: "community_user_content_progress_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["primary_owner_user_id"]
          },
          {
            foreignKeyName: "community_user_content_progress_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "public_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_user_content_progress_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_subscriptions_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_user_content_progress_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      countries: {
        Row: {
          icon: string
          id: string
          iso_code_1: string
          iso_code_2: string
          iso_code_3: string
          name: string
        }
        Insert: {
          icon: string
          id?: string
          iso_code_1: string
          iso_code_2: string
          iso_code_3: string
          name: string
        }
        Update: {
          icon?: string
          id?: string
          iso_code_1?: string
          iso_code_2?: string
          iso_code_3?: string
          name?: string
        }
        Relationships: []
      }
      invoice_items: {
        Row: {
          created_at: string
          id: string
          invoice_id: string
          price_amount: number | null
          product_id: string
          quantity: number
          updated_at: string
          variant_id: string
        }
        Insert: {
          created_at?: string
          id: string
          invoice_id: string
          price_amount?: number | null
          product_id: string
          quantity?: number
          updated_at?: string
          variant_id: string
        }
        Update: {
          created_at?: string
          id?: string
          invoice_id?: string
          price_amount?: number | null
          product_id?: string
          quantity?: number
          updated_at?: string
          variant_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "invoice_items_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "invoices"
            referencedColumns: ["id"]
          },
        ]
      }
      invoices: {
        Row: {
          community_id: string | null
          created_at: string
          currency: string
          id: string
          purchase_type: Database["public"]["Enums"]["purchase_type"]
          status: Database["public"]["Enums"]["payment_status"]
          total_amount: number
          updated_at: string
          user_id: string | null
        }
        Insert: {
          community_id?: string | null
          created_at?: string
          currency: string
          id: string
          purchase_type: Database["public"]["Enums"]["purchase_type"]
          status: Database["public"]["Enums"]["payment_status"]
          total_amount: number
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          community_id?: string | null
          created_at?: string
          currency?: string
          id?: string
          purchase_type?: Database["public"]["Enums"]["purchase_type"]
          status?: Database["public"]["Enums"]["payment_status"]
          total_amount?: number
          updated_at?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "invoices_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invoices_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invoices_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "user_communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invoices_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "user_created_communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invoices_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["primary_owner_user_id"]
          },
          {
            foreignKeyName: "invoices_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "public_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invoices_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_subscriptions_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invoices_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      languages: {
        Row: {
          icon: string
          id: string
          iso_code_1: string
          iso_code_2: string
          iso_code_3: string
          name: string
        }
        Insert: {
          icon: string
          id?: string
          iso_code_1: string
          iso_code_2: string
          iso_code_3: string
          name: string
        }
        Update: {
          icon?: string
          id?: string
          iso_code_1?: string
          iso_code_2?: string
          iso_code_3?: string
          name?: string
        }
        Relationships: []
      }
      notifications: {
        Row: {
          body: string
          channel: Database["public"]["Enums"]["notification_channel"]
          community_id: string | null
          created_at: string
          dismissed: boolean
          expires_at: string | null
          id: string
          link: string | null
          recipient_user_id: string
          type: Database["public"]["Enums"]["notification_type"]
        }
        Insert: {
          body: string
          channel?: Database["public"]["Enums"]["notification_channel"]
          community_id?: string | null
          created_at?: string
          dismissed?: boolean
          expires_at?: string | null
          id?: string
          link?: string | null
          recipient_user_id: string
          type?: Database["public"]["Enums"]["notification_type"]
        }
        Update: {
          body?: string
          channel?: Database["public"]["Enums"]["notification_channel"]
          community_id?: string | null
          created_at?: string
          dismissed?: boolean
          expires_at?: string | null
          id?: string
          link?: string | null
          recipient_user_id?: string
          type?: Database["public"]["Enums"]["notification_type"]
        }
        Relationships: [
          {
            foreignKeyName: "notifications_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "user_communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "user_created_communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_recipient_user_id_fkey"
            columns: ["recipient_user_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["primary_owner_user_id"]
          },
          {
            foreignKeyName: "notifications_recipient_user_id_fkey"
            columns: ["recipient_user_id"]
            isOneToOne: false
            referencedRelation: "public_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_recipient_user_id_fkey"
            columns: ["recipient_user_id"]
            isOneToOne: false
            referencedRelation: "user_subscriptions_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_recipient_user_id_fkey"
            columns: ["recipient_user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      product_prices: {
        Row: {
          active: boolean
          created_at: string
          currency: string
          id: string
          interval: Database["public"]["Enums"]["price_interval"]
          nickname: string | null
          product_id: string
          type: Database["public"]["Enums"]["price_type"]
          unit_amount: number | null
          updated_at: string
        }
        Insert: {
          active?: boolean
          created_at?: string
          currency: string
          id: string
          interval?: Database["public"]["Enums"]["price_interval"]
          nickname?: string | null
          product_id: string
          type?: Database["public"]["Enums"]["price_type"]
          unit_amount?: number | null
          updated_at?: string
        }
        Update: {
          active?: boolean
          created_at?: string
          currency?: string
          id?: string
          interval?: Database["public"]["Enums"]["price_interval"]
          nickname?: string | null
          product_id?: string
          type?: Database["public"]["Enums"]["price_type"]
          unit_amount?: number | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_product_prices_product_id"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      products: {
        Row: {
          active: boolean
          community_id: string | null
          created_at: string
          description: string | null
          id: string
          name: string
          seller: Database["public"]["Enums"]["seller_type"]
          trial_days: number
          updated_at: string
        }
        Insert: {
          active?: boolean
          community_id?: string | null
          created_at?: string
          description?: string | null
          id: string
          name: string
          seller: Database["public"]["Enums"]["seller_type"]
          trial_days?: number
          updated_at?: string
        }
        Update: {
          active?: boolean
          community_id?: string | null
          created_at?: string
          description?: string | null
          id?: string
          name?: string
          seller?: Database["public"]["Enums"]["seller_type"]
          trial_days?: number
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "products_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "products_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "products_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "user_communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "products_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "user_created_communities"
            referencedColumns: ["id"]
          },
        ]
      }
      reserved_slugs: {
        Row: {
          created_at: string
          reason: string
          slug: string
        }
        Insert: {
          created_at?: string
          reason: string
          slug: string
        }
        Update: {
          created_at?: string
          reason?: string
          slug?: string
        }
        Relationships: []
      }
      stripe_account_payouts: {
        Row: {
          amount: number
          arrival_date: string | null
          created_at: string
          currency: string
          destination: string | null
          failure_code: string | null
          failure_message: string | null
          id: string
          method: string | null
          payout_type: string | null
          status: Database["public"]["Enums"]["stripe_account_payout_status_type"]
          stripe_account_id: string
          stripe_payout_id: string
          updated_at: string
        }
        Insert: {
          amount: number
          arrival_date?: string | null
          created_at?: string
          currency: string
          destination?: string | null
          failure_code?: string | null
          failure_message?: string | null
          id: string
          method?: string | null
          payout_type?: string | null
          status: Database["public"]["Enums"]["stripe_account_payout_status_type"]
          stripe_account_id: string
          stripe_payout_id: string
          updated_at?: string
        }
        Update: {
          amount?: number
          arrival_date?: string | null
          created_at?: string
          currency?: string
          destination?: string | null
          failure_code?: string | null
          failure_message?: string | null
          id?: string
          method?: string | null
          payout_type?: string | null
          status?: Database["public"]["Enums"]["stripe_account_payout_status_type"]
          stripe_account_id?: string
          stripe_payout_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "stripe_account_payouts_stripe_account_id_fkey"
            columns: ["stripe_account_id"]
            isOneToOne: false
            referencedRelation: "stripe_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      stripe_accounts: {
        Row: {
          applied_configurations: string[] | null
          capabilities: Json | null
          configuration: Json | null
          contact_email: string | null
          country_id: string | null
          created_at: string
          dashboard: string | null
          defaults: Json | null
          display_name: string | null
          id: string
          identity: Json | null
          metadata: Json | null
          requirements: Json | null
          updated_at: string
          user_id: string
        }
        Insert: {
          applied_configurations?: string[] | null
          capabilities?: Json | null
          configuration?: Json | null
          contact_email?: string | null
          country_id?: string | null
          created_at?: string
          dashboard?: string | null
          defaults?: Json | null
          display_name?: string | null
          id: string
          identity?: Json | null
          metadata?: Json | null
          requirements?: Json | null
          updated_at?: string
          user_id: string
        }
        Update: {
          applied_configurations?: string[] | null
          capabilities?: Json | null
          configuration?: Json | null
          contact_email?: string | null
          country_id?: string | null
          created_at?: string
          dashboard?: string | null
          defaults?: Json | null
          display_name?: string | null
          id?: string
          identity?: Json | null
          metadata?: Json | null
          requirements?: Json | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "stripe_accounts_country_id_fkey"
            columns: ["country_id"]
            isOneToOne: false
            referencedRelation: "countries"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "stripe_accounts_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "public_communities_view"
            referencedColumns: ["primary_owner_user_id"]
          },
          {
            foreignKeyName: "stripe_accounts_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "public_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "stripe_accounts_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "user_subscriptions_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "stripe_accounts_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      subscription_items: {
        Row: {
          created_at: string
          id: string
          interval: string
          interval_count: number
          price_amount: number | null
          product_id: string
          quantity: number
          subscription_id: string
          type: Database["public"]["Enums"]["subscription_item_type"]
          updated_at: string
          variant_id: string
        }
        Insert: {
          created_at?: string
          id: string
          interval: string
          interval_count: number
          price_amount?: number | null
          product_id: string
          quantity?: number
          subscription_id: string
          type: Database["public"]["Enums"]["subscription_item_type"]
          updated_at?: string
          variant_id: string
        }
        Update: {
          created_at?: string
          id?: string
          interval?: string
          interval_count?: number
          price_amount?: number | null
          product_id?: string
          quantity?: number
          subscription_id?: string
          type?: Database["public"]["Enums"]["subscription_item_type"]
          updated_at?: string
          variant_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "subscription_items_subscription_id_fkey"
            columns: ["subscription_id"]
            isOneToOne: false
            referencedRelation: "subscriptions"
            referencedColumns: ["id"]
          },
        ]
      }
      subscriptions: {
        Row: {
          active: boolean
          cancel_at_period_end: boolean | null
          canceled_at: string | null
          community_id: string | null
          created_at: string
          currency: string
          current_period_end: string
          current_period_start: string
          ended_at: string | null
          id: string
          metadata: Json | null
          payment_method_id: string | null
          purchase_type: Database["public"]["Enums"]["purchase_type"]
          status: Database["public"]["Enums"]["subscription_status"]
          trial_ends_at: string | null
          trial_starts_at: string | null
          updated_at: string
          user_id: string | null
        }
        Insert: {
          active: boolean
          cancel_at_period_end?: boolean | null
          canceled_at?: string | null
          community_id?: string | null
          created_at?: string
          currency: string
          current_period_end: string
          current_period_start: string
          ended_at?: string | null
          id: string
          metadata?: Json | null
          payment_method_id?: string | null
          purchase_type: Database["public"]["Enums"]["purchase_type"]
          status: Database["public"]["Enums"]["subscription_status"]
          trial_ends_at?: string | null
          trial_starts_at?: string | null
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          active?: boolean
          cancel_at_period_end?: boolean | null
          canceled_at?: string | null
          community_id?: string | null
          created_at?: string
          currency?: string
          current_period_end?: string
          current_period_start?: string
          ended_at?: string | null
          id?: string
          metadata?: Json | null
          payment_method_id?: string | null
          purchase_type?: Database["public"]["Enums"]["purchase_type"]
          status?: Database["public"]["Enums"]["subscription_status"]
          trial_ends_at?: string | null
          trial_starts_at?: string | null
          updated_at?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "subscriptions_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "user_communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "user_created_communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["primary_owner_user_id"]
          },
          {
            foreignKeyName: "subscriptions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "public_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_subscriptions_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      users: {
        Row: {
          country_id: string | null
          created_at: string | null
          default_payment_method_id: string | null
          email: string | null
          first_name: string
          id: string
          last_name: string | null
          picture_url: string | null
          public_data: Json
          stripe_account_id: string | null
          updated_at: string | null
          user_details: Json
          username: string | null
        }
        Insert: {
          country_id?: string | null
          created_at?: string | null
          default_payment_method_id?: string | null
          email?: string | null
          first_name: string
          id: string
          last_name?: string | null
          picture_url?: string | null
          public_data?: Json
          stripe_account_id?: string | null
          updated_at?: string | null
          user_details?: Json
          username?: string | null
        }
        Update: {
          country_id?: string | null
          created_at?: string | null
          default_payment_method_id?: string | null
          email?: string | null
          first_name?: string
          id?: string
          last_name?: string | null
          picture_url?: string | null
          public_data?: Json
          stripe_account_id?: string | null
          updated_at?: string | null
          user_details?: Json
          username?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_users_country"
            columns: ["country_id"]
            isOneToOne: false
            referencedRelation: "countries"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "users_stripe_account_id_fkey"
            columns: ["stripe_account_id"]
            isOneToOne: true
            referencedRelation: "stripe_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      public_communities_view: {
        Row: {
          admin_count: number | null
          category_icon: string | null
          category_id: string | null
          category_name: string | null
          cover_url: string | null
          created_at: string | null
          default_product_id: string | null
          description: string | null
          id: string | null
          is_enabled: boolean | null
          is_listed: boolean | null
          is_private: boolean | null
          language_icon: string | null
          language_id: string | null
          language_name: string | null
          logo_url: string | null
          member_count: number | null
          name: string | null
          owner_first_name: string | null
          owner_last_name: string | null
          owner_picture_url: string | null
          owner_username: string | null
          primary_color: string | null
          primary_owner_user_id: string | null
          slug: string | null
          socials: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "communities_default_product_id_fkey"
            columns: ["default_product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      public_community_memberships: {
        Row: {
          community_id: string | null
          community_role: string | null
          created_at: string | null
          id: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          community_id?: string | null
          community_role?: string | null
          created_at?: string | null
          id?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          community_id?: string | null
          community_role?: string | null
          created_at?: string | null
          id?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "community_memberships_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_memberships_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_memberships_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "user_communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_memberships_community_id_fkey"
            columns: ["community_id"]
            isOneToOne: false
            referencedRelation: "user_created_communities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_memberships_community_role_fkey"
            columns: ["community_role"]
            isOneToOne: false
            referencedRelation: "community_roles"
            referencedColumns: ["name"]
          },
          {
            foreignKeyName: "community_memberships_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["primary_owner_user_id"]
          },
          {
            foreignKeyName: "community_memberships_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "public_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_memberships_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_subscriptions_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_memberships_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      public_profiles: {
        Row: {
          country: string | null
          country_icon: string | null
          created_at: string | null
          first_name: string | null
          id: string | null
          last_name: string | null
          last_sign_in_at: string | null
          picture_url: string | null
          updated_at: string | null
          user_details: Json | null
          username: string | null
        }
        Relationships: []
      }
      user_communities: {
        Row: {
          category_icon: string | null
          category_name: string | null
          cover_url: string | null
          description: string | null
          id: string | null
          is_private: boolean | null
          language_icon: string | null
          language_name: string | null
          logo_url: string | null
          member_count: number | null
          member_id: string | null
          name: string | null
          primary_color: string | null
          role: string | null
          slug: string | null
          user_id: string | null
          visible_on_profile: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "community_memberships_community_role_fkey"
            columns: ["role"]
            isOneToOne: false
            referencedRelation: "community_roles"
            referencedColumns: ["name"]
          },
          {
            foreignKeyName: "community_memberships_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["primary_owner_user_id"]
          },
          {
            foreignKeyName: "community_memberships_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "public_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_memberships_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_subscriptions_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_memberships_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_created_communities: {
        Row: {
          category_icon: string | null
          category_name: string | null
          cover_url: string | null
          created_at: string | null
          description: string | null
          id: string | null
          is_private: boolean | null
          language_icon: string | null
          language_name: string | null
          logo_url: string | null
          member_count: number | null
          member_id: string | null
          name: string | null
          primary_owner_user_id: string | null
          role: string | null
          slug: string | null
        }
        Relationships: [
          {
            foreignKeyName: "communities_primary_owner_user_id_fkey"
            columns: ["primary_owner_user_id"]
            isOneToOne: false
            referencedRelation: "public_communities_view"
            referencedColumns: ["primary_owner_user_id"]
          },
          {
            foreignKeyName: "communities_primary_owner_user_id_fkey"
            columns: ["primary_owner_user_id"]
            isOneToOne: false
            referencedRelation: "public_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "communities_primary_owner_user_id_fkey"
            columns: ["primary_owner_user_id"]
            isOneToOne: false
            referencedRelation: "user_subscriptions_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "communities_primary_owner_user_id_fkey"
            columns: ["primary_owner_user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_memberships_community_role_fkey"
            columns: ["role"]
            isOneToOne: false
            referencedRelation: "community_roles"
            referencedColumns: ["name"]
          },
        ]
      }
      user_subscriptions_workspace: {
        Row: {
          first_name: string | null
          id: string | null
          last_name: string | null
          picture_url: string | null
          subscription_status:
            | Database["public"]["Enums"]["subscription_status"]
            | null
        }
        Insert: {
          first_name?: string | null
          id?: string | null
          last_name?: string | null
          picture_url?: string | null
          subscription_status?: never
        }
        Update: {
          first_name?: string | null
          id?: string | null
          last_name?: string | null
          picture_url?: string | null
          subscription_status?: never
        }
        Relationships: []
      }
    }
    Functions: {
      accept_community_invitation: {
        Args: { p_token: string; p_user_id: string }
        Returns: string
      }
      add_community_course_prerequisite: {
        Args: { p_course_id: string; p_prerequisite_id: string }
        Returns: boolean
      }
      add_invitations_to_community: {
        Args: {
          p_community_id: string
          p_invitations: Database["public"]["CompositeTypes"]["community_invitation"][]
        }
        Returns: Database["public"]["Tables"]["community_invitations"]["Row"][]
      }
      add_user_to_community: {
        Args: {
          p_community_id: string
          p_user_id: string
          p_order_type: Database["public"]["Enums"]["order_type"]
          p_price_id: string
          p_latest_invoice_id: string
          p_subscription_id?: string
        }
        Returns: string
      }
      auto_save_community_forum_post: {
        Args: {
          p_post_id: string
          p_new_content: Json
          p_draft_character_count?: number
          p_draft_word_count?: number
        }
        Returns: boolean
      }
      can_action_community_member: {
        Args: { p_target_community_id: string; p_target_user_id: string }
        Returns: boolean
      }
      can_reapply_for_explore_listing: {
        Args: { p_community_id: string }
        Returns: boolean
      }
      check_community_listing_requirements: {
        Args: { p_community_id: string }
        Returns: Database["public"]["CompositeTypes"]["community_listing_requirements_result"]
      }
      check_community_slug_availability: {
        Args: { p_slug_to_check: string; p_current_community_id?: string }
        Returns: boolean
      }
      check_image_storage_policy: {
        Args: { bucket_name: string; permission_name: string }
        Returns: boolean
      }
      check_user_username_availability: {
        Args: { p_username_to_check: string; p_current_user_id?: string }
        Returns: boolean
      }
      community_create_forum_category: {
        Args: {
          p_community_id: string
          p_name: string
          p_description?: string
          p_icon?: string
        }
        Returns: string
      }
      community_delete_forum_category: {
        Args: { p_category_id: string }
        Returns: undefined
      }
      community_forum_get_reaction_counts: {
        Args: { p_post_id?: string; p_comment_id?: string }
        Returns: {
          reaction: Database["public"]["Enums"]["community_forum_reaction_type"]
          count: number
          user_reacted: boolean
        }[]
      }
      community_forum_toggle_reaction: {
        Args: {
          p_post_id?: string
          p_comment_id?: string
          p_reaction_type?: Database["public"]["Enums"]["community_forum_reaction_type"]
        }
        Returns: boolean
      }
      community_update_forum_category: {
        Args: {
          p_category_id: string
          p_new_name: string
          p_new_description: string
          p_new_icon: string
        }
        Returns: boolean
      }
      community_workspace: {
        Args: { p_community_slug: string }
        Returns: {
          id: string
          name: string
          logo_url: string
          slug: string
          is_private: boolean
          primary_owner_user_id: string
          member_id: string
          role: string
          role_hierarchy_level: number
          subscription_status: Database["public"]["Enums"]["subscription_status"]
          permissions: Database["public"]["Enums"]["community_permissions"][]
        }[]
      }
      compare_user_role_to_content_role: {
        Args: {
          p_user_id: string
          p_community_id: string
          p_content_role: string
        }
        Returns: boolean
      }
      create_community: {
        Args: {
          p_community_name: string
          p_primary_owner_user_id: string
          p_community_id?: string
        }
        Returns: {
          category_id: string
          country_id: string
          cover_url: string | null
          created_at: string | null
          created_by_user_id: string | null
          default_product_id: string | null
          description: string | null
          id: string
          is_enabled: boolean | null
          is_listed: boolean | null
          is_private: boolean | null
          language_id: string
          logo_url: string | null
          name: string
          primary_color: string
          primary_owner_user_id: string
          slug: string
          socials: Json
          subscription_id: string | null
          updated_at: string | null
          updated_by_user_id: string | null
        }
      }
      create_community_course: {
        Args: {
          p_community_id: string
          p_title: string
          p_description?: string
          p_cover_url?: string
          p_access?: Database["public"]["Enums"]["community_course_access_enum"]
        }
        Returns: {
          access:
            | Database["public"]["Enums"]["community_course_access_enum"]
            | null
          access_details: Json | null
          community_id: string
          cover_url: string | null
          created_at: string
          description: string | null
          id: string
          sequence_order: number
          slug: string | null
          status:
            | Database["public"]["Enums"]["community_content_status_enum"]
            | null
          title: string
          updated_at: string
        }
      }
      create_community_course_chapter: {
        Args: {
          p_course_slug: string
          p_title: string
          p_description?: string
          p_icon?: string
        }
        Returns: string
      }
      create_community_course_lesson: {
        Args: { p_course_slug: string; p_title: string; p_chapter_id?: string }
        Returns: string
      }
      create_community_course_with_prerequisites: {
        Args: {
          p_community_id: string
          p_title: string
          p_description?: string
          p_cover_url?: string
          p_prerequisite_ids?: string[]
          p_access?: Database["public"]["Enums"]["community_course_access_enum"]
        }
        Returns: {
          access:
            | Database["public"]["Enums"]["community_course_access_enum"]
            | null
          access_details: Json | null
          community_id: string
          cover_url: string | null
          created_at: string
          description: string | null
          id: string
          sequence_order: number
          slug: string | null
          status:
            | Database["public"]["Enums"]["community_content_status_enum"]
            | null
          title: string
          updated_at: string
        }
      }
      create_community_forum_comment: {
        Args: {
          p_post_id: string
          p_comment_content: Json
          p_character_count?: number
          p_word_count?: number
          p_parent_id?: string
        }
        Returns: {
          id: string
          content: Json
          parent_comment_id: string
          created_at: string
          updated_at: string
          created_by_user_id: string
          created_by_member_id: string
          author_first_name: string
          author_last_name: string
          author_picture_url: string
          depth: number
          comment_post_id: string
        }[]
      }
      create_community_forum_post: {
        Args: { p_title: string; p_content: Json; p_category_id: string }
        Returns: string
      }
      create_community_invitation: {
        Args: { p_community_id: string; p_email: string; p_role: string }
        Returns: {
          community_id: string
          created_at: string
          created_by_user_id: string
          email: string
          expires_at: string
          id: string
          invite_token: string
          invited_by_member_id: string
          role: string
          updated_at: string
        }
      }
      create_community_server_actions: {
        Args: {
          p_community_name: string
          p_primary_owner_user_id: string
          p_subscription_id: string
          p_is_enabled?: boolean
          p_community_id?: string
        }
        Returns: {
          category_id: string
          country_id: string
          cover_url: string | null
          created_at: string | null
          created_by_user_id: string | null
          default_product_id: string | null
          description: string | null
          id: string
          is_enabled: boolean | null
          is_listed: boolean | null
          is_private: boolean | null
          language_id: string
          logo_url: string | null
          name: string
          primary_color: string
          primary_owner_user_id: string
          slug: string
          socials: Json
          subscription_id: string | null
          updated_at: string | null
          updated_by_user_id: string | null
        }
      }
      create_community_with_subscription: {
        Args: {
          p_community_id: string
          p_community_name: string
          p_primary_owner_user_id: string
          p_subscription_id: string
          p_purchase_type: Database["public"]["Enums"]["purchase_type"]
          p_status: Database["public"]["Enums"]["subscription_status"]
          p_active: boolean
          p_currency: string
          p_line_items: Json
          p_cancel_at_period_end: boolean
          p_current_period_start: string
          p_current_period_end: string
          p_trial_starts_at?: string
          p_trial_ends_at?: string
          p_payment_method_id?: string
        }
        Returns: Json
      }
      create_draft_community_forum_post: {
        Args: {
          p_draft_title: string
          p_draft_content: Json
          p_draft_category_id: string
          p_draft_character_count?: number
          p_draft_word_count?: number
        }
        Returns: string
      }
      create_stripe_account: {
        Args: {
          p_stripe_account_id: string
          p_user_id: string
          p_country_iso_code_1?: string
          p_applied_configurations?: string[]
          p_contact_email?: string
          p_display_name?: string
          p_dashboard?: string
          p_configuration?: Json
          p_capabilities?: Json
          p_requirements?: Json
          p_defaults?: Json
          p_identity?: Json
          p_metadata?: Json
        }
        Returns: string
      }
      create_subscription: {
        Args: {
          p_target_user_id: string
          p_target_subscription_id: string
          p_target_community_id: string
          p_purchase_type: Database["public"]["Enums"]["purchase_type"]
          p_status: Database["public"]["Enums"]["subscription_status"]
          p_active: boolean
          p_cancel_at_period_end: boolean
          p_currency: string
          p_current_period_start: string
          p_current_period_end: string
          p_trial_starts_at?: string
          p_trial_ends_at?: string
          p_payment_method_id?: string
        }
        Returns: {
          active: boolean
          cancel_at_period_end: boolean | null
          canceled_at: string | null
          community_id: string | null
          created_at: string
          currency: string
          current_period_end: string
          current_period_start: string
          ended_at: string | null
          id: string
          metadata: Json | null
          payment_method_id: string | null
          purchase_type: Database["public"]["Enums"]["purchase_type"]
          status: Database["public"]["Enums"]["subscription_status"]
          trial_ends_at: string | null
          trial_starts_at: string | null
          updated_at: string
          user_id: string | null
        }
      }
      delete_community_course: {
        Args: { course_slug: string }
        Returns: undefined
      }
      delete_community_course_chapter: {
        Args: { p_chapter_id: string }
        Returns: undefined
      }
      delete_community_course_lesson: {
        Args: { p_lesson_id: string }
        Returns: undefined
      }
      delete_community_forum_comment: {
        Args: { p_comment_id: string }
        Returns: boolean
      }
      delete_community_forum_draft_post: {
        Args: { p_post_id: string }
        Returns: boolean
      }
      delete_community_forum_post: {
        Args: { p_post_id: string }
        Returns: boolean
      }
      discard_community_forum_post_draft: {
        Args: { p_post_id: string }
        Returns: boolean
      }
      find_post_by_title: {
        Args: { search_title: string }
        Returns: string
      }
      generate_base_slug: {
        Args: { input_text: string }
        Returns: string
      }
      generate_random_digits: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_active_membership_details: {
        Args: { p_user_id: string; p_community_id: string }
        Returns: {
          member_id: string
          user_id: string
          community_id: string
          role: string
          role_hierarchy_level: number
          created_at: string
        }[]
      }
      get_community_categories: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          name: string
          icon: string
          slug: string
          description: string
          community_count: number
        }[]
      }
      get_community_course_chapters: {
        Args: { p_course_id?: string }
        Returns: {
          id: string
          course_id: string
          title: string
          description: string
          icon: string
          sequence_order: number
          lesson_count: number
          created_at: string
          updated_at: string
        }[]
      }
      get_community_course_details: {
        Args: { course_id?: string; course_slug?: string }
        Returns: {
          id: string
          title: string
          description: string
          cover_url: string
          slug: string
          status: Database["public"]["Enums"]["community_content_status_enum"]
          access: Database["public"]["Enums"]["community_course_access_enum"]
          created_at: string
          updated_at: string
        }[]
      }
      get_community_course_lessons: {
        Args: {
          p_course_id?: string
          p_status_filter?: Database["public"]["Enums"]["community_content_status_enum"]
        }
        Returns: {
          id: string
          course_id: string
          chapter_id: string
          title: string
          icon: string
          lesson_text: Json
          content_type: Database["public"]["Enums"]["community_course_content_type_enum"]
          content_data: Json
          character_count: number
          word_count: number
          sequence_order: number
          duration_seconds: number
          status: Database["public"]["Enums"]["community_content_status_enum"]
          created_at: string
          updated_at: string
        }[]
      }
      get_community_course_prerequisites: {
        Args: { p_course_id: string }
        Returns: {
          prerequisite_id: string
          title: string
          slug: string
        }[]
      }
      get_community_explore_page_request_status: {
        Args: { p_community_id: string }
        Returns: Json
      }
      get_community_forum_categories: {
        Args: { p_community_id: string }
        Returns: {
          id: string
          name: string
          description: string
          icon: string
          slug: string
          posts_count: number
        }[]
      }
      get_community_forum_post_comments: {
        Args: {
          p_post_id: string
          p_cursor_timestamp?: string
          limit_rows?: number
        }
        Returns: {
          id: string
          content: Json
          parent_comment_id: string
          created_at: string
          updated_at: string
          created_by_user_id: string
          created_by_member_id: string
          author_first_name: string
          author_last_name: string
          author_picture_url: string
          depth: number
          comment_post_id: string
        }[]
      }
      get_community_forum_post_details: {
        Args: { p_post_id?: string; p_post_slug?: string }
        Returns: {
          id: string
          title: string
          content: Json
          category_id: string
          category_name: string
          created_by_user_id: string
          created_by_member_id: string
          author_first_name: string
          author_last_name: string
          author_picture_url: string
          is_pinned: boolean
          created_at: string
          updated_at: string
          comment_count: number
        }[]
      }
      get_community_invitations: {
        Args: { p_community_id: string }
        Returns: {
          id: string
          email: string
          community_id: string
          created_by_user_id: string
          role: string
          created_at: string
          updated_at: string
          expires_at: string
          inviter_first_name: string
          inviter_email: string
        }[]
      }
      get_community_members: {
        Args: { p_community_id: string }
        Returns: {
          id: string
          user_id: string
          member_id: string
          community_id: string
          role: string
          role_hierarchy_level: number
          primary_owner_user_id: string
          first_name: string
          last_name: string
          email: string
          picture_url: string
          created_at: string
          updated_at: string
        }[]
      }
      get_community_members_only: {
        Args: { p_community_id: string }
        Returns: {
          user_id: string
          community_id: string
          role: string
          role_hierarchy_level: number
          primary_owner_user_id: string
          first_name: string
          last_name: string
          username: string
          picture_url: string
          user_details: Json
          country: string
          country_icon: string
          last_sign_in_at: string
          created_at: string
          updated_at: string
        }[]
      }
      get_community_stripe_account_status: {
        Args: { p_community_id: string }
        Returns: {
          id: string
          country_id: string
          applied_configurations: string[]
          contact_email: string
          display_name: string
          dashboard: string
          configuration: Json
          capabilities: Json
          requirements: Json
          defaults: Json
          identity: Json
          metadata: Json
        }[]
      }
      get_community_team_members: {
        Args: { p_community_id: string }
        Returns: {
          user_id: string
          community_id: string
          role: string
          role_hierarchy_level: number
          primary_owner_user_id: string
          first_name: string
          last_name: string
          username: string
          picture_url: string
          user_details: Json
          country: string
          country_icon: string
          last_sign_in_at: string
          created_at: string
          updated_at: string
        }[]
      }
      get_countries: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          name: string
          icon: string
          iso_code_1: string
          iso_code_2: string
          iso_code_3: string
        }[]
      }
      get_country_id_by_iso: {
        Args: { iso_code: string }
        Returns: string
      }
      get_iso_code_by_country_id: {
        Args: { country_uuid: string }
        Returns: string
      }
      get_languages: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          name: string
          icon: string
          iso_code_1: string
          iso_code_2: string
          iso_code_3: string
        }[]
      }
      get_membership_from_user_and_community: {
        Args: { p_user_id: string; p_community_id: string }
        Returns: string
      }
      get_paginated_community_courses: {
        Args: {
          p_community_id: string
          p_status_filter?: Database["public"]["Enums"]["community_content_status_enum"]
          limit_rows?: number
          offset_rows?: number
        }
        Returns: {
          id: string
          slug: string
          title: string
          description: string
          cover_url: string
          status: Database["public"]["Enums"]["community_content_status_enum"]
          sequence_order: number
          prerequisites: Json
          prerequisite_parents: Json
          access: Database["public"]["Enums"]["community_course_access_enum"]
          lessons: number
          duration_seconds: number
          created_at: string
          updated_at: string
          total_count: number
        }[]
      }
      get_paginated_community_forum_drafts: {
        Args: {
          p_community_id: string
          p_category_id?: string
          limit_rows?: number
          offset_rows?: number
        }
        Returns: {
          id: string
          title: string
          content: Json
          category_id: string
          slug: string
          created_at: string
          updated_at: string
          created_by_user_id: string
          created_by_member_id: string
          author_first_name: string
          author_last_name: string
          author_picture_url: string
          category_name: string
          comment_count: number
          total_count: number
          status: Database["public"]["Enums"]["community_forum_post_status"]
          auto_saved_at: string
        }[]
      }
      get_paginated_community_forum_posts: {
        Args: {
          p_community_id: string
          p_category_id?: string
          p_pinned_limit?: number
          p_post_status?: Database["public"]["Enums"]["community_forum_post_status"]
          limit_rows?: number
          offset_rows?: number
        }
        Returns: {
          id: string
          title: string
          content: Json
          category_id: string
          slug: string
          created_at: string
          updated_at: string
          created_by_user_id: string
          created_by_member_id: string
          author_first_name: string
          author_last_name: string
          author_picture_url: string
          category_name: string
          comment_count: number
          is_pinned: boolean
          total_count: number
          status: Database["public"]["Enums"]["community_forum_post_status"]
        }[]
      }
      get_paginated_community_forum_posts_base: {
        Args: {
          p_community_id: string
          p_filter_category_id?: string
          p_pinned_limit?: number
          p_is_draft?: boolean
          limit_rows?: number
          offset_rows?: number
        }
        Returns: {
          id: string
          title: string
          content: Json
          draft_title: string
          draft_content: Json
          draft_category_id: string
          slug: string
          created_at: string
          updated_at: string
          created_by_user_id: string
          created_by_member_id: string
          author_first_name: string
          author_last_name: string
          author_picture_url: string
          category_id: string
          category_name: string
          comment_count: number
          is_pinned: boolean
          total_count: number
          status: Database["public"]["Enums"]["community_forum_post_status"]
          auto_saved_at: string
        }[]
      }
      get_upper_system_role: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_user_by_id: {
        Args: { p_id: string }
        Returns: {
          id: string
          first_name: string
          last_name: string
          username: string
          picture_url: string
          user_details: Json
          country: string
          country_icon: string
          last_sign_in_at: string
          created_at: string
          updated_at: string
        }[]
      }
      get_user_by_username: {
        Args: { p_username: string }
        Returns: {
          id: string
          first_name: string
          last_name: string
          username: string
          picture_url: string
          user_details: Json
          country: string
          country_icon: string
          last_sign_in_at: string
          created_at: string
          updated_at: string
        }[]
      }
      get_user_from_membership: {
        Args: { p_member_id: string }
        Returns: {
          country_id: string | null
          created_at: string | null
          default_payment_method_id: string | null
          email: string | null
          first_name: string
          id: string
          last_name: string | null
          picture_url: string | null
          public_data: Json
          stripe_account_id: string | null
          updated_at: string | null
          user_details: Json
          username: string | null
        }
      }
      get_user_member_communities: {
        Args: { p_user_id: string }
        Returns: {
          id: string
          name: string
          slug: string
          logo_url: string
          cover_url: string
          description: string
          is_private: boolean
          member_id: string
          role: string
          joined_at: string
          category_name: string
          category_icon: string
          language_name: string
          language_icon: string
          member_count: number
        }[]
      }
      get_user_stripe_account: {
        Args: { p_user_id: string }
        Returns: {
          id: string
          country_id: string
          applied_configurations: string[]
          contact_email: string
          display_name: string
          dashboard: string
          configuration: Json
          capabilities: Json
          requirements: Json
          defaults: Json
          identity: Json
          metadata: Json
          created_at: string
          updated_at: string
        }[]
      }
      has_active_community_subscription: {
        Args: { p_target_community_id: string }
        Returns: boolean
      }
      has_active_user_subscription: {
        Args: { p_target_user_id: string }
        Returns: boolean
      }
      has_community_permission: {
        Args: {
          p_user_id: string
          p_community_id: string
          p_permission_name: Database["public"]["Enums"]["community_permissions"]
        }
        Returns: boolean
      }
      has_community_price_permission: {
        Args: {
          p_product_id: string
          p_permission: Database["public"]["Enums"]["community_permissions"]
        }
        Returns: boolean
      }
      has_more_elevated_community_role: {
        Args: {
          p_target_user_id: string
          p_target_community_id: string
          p_role_name: string
        }
        Returns: boolean
      }
      has_role_on_comment_community: {
        Args: { p_comment_id: string }
        Returns: boolean
      }
      has_role_on_community: {
        Args: { p_community_id: string; p_community_role?: string }
        Returns: boolean
      }
      has_role_on_community_course_lessons_community: {
        Args: { p_community_course_lessons_id: string }
        Returns: boolean
      }
      has_role_on_course_community: {
        Args: { p_course_id: string }
        Returns: boolean
      }
      has_role_on_learning_path_community: {
        Args: { p_learning_path_id: string }
        Returns: boolean
      }
      has_role_on_post_community: {
        Args: { p_post_id: string }
        Returns: boolean
      }
      has_role_on_ticket_community: {
        Args: { p_ticket_id: string }
        Returns: boolean
      }
      has_same_community_role_hierarchy_level: {
        Args: {
          p_target_user_id: string
          p_target_community_id: string
          p_role_name: string
        }
        Returns: boolean
      }
      is_community_owner: {
        Args: { community_id: string }
        Returns: boolean
      }
      is_community_team_member: {
        Args: { p_target_community_id: string }
        Returns: boolean
      }
      is_super_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      is_valid_image_extension: {
        Args: { file_name: string }
        Returns: boolean
      }
      link_subscription_to_community: {
        Args: { p_subscription_id: string; p_community_id: string }
        Returns: boolean
      }
      move_community_course_chapter_sequence_order: {
        Args: { p_chapter_id: string; p_direction: string }
        Returns: boolean
      }
      move_community_course_lesson_sequence_order: {
        Args: {
          p_lesson_id: string
          p_target_sequence: number
          p_direction: string
        }
        Returns: boolean
      }
      move_community_course_lesson_to_chapter: {
        Args: { p_lesson_id: string; p_new_chapter_id: string }
        Returns: boolean
      }
      publish_community_forum_post_draft: {
        Args: { p_post_id: string }
        Returns: boolean
      }
      remove_community_course_prerequisite: {
        Args: { p_course_id: string; p_prerequisite_id: string }
        Returns: boolean
      }
      renumber_community_course_lessons: {
        Args: { p_course_id: string }
        Returns: undefined
      }
      reorder_community_course: {
        Args: { p_course_id: string; p_direction: string }
        Returns: {
          access:
            | Database["public"]["Enums"]["community_course_access_enum"]
            | null
          access_details: Json | null
          community_id: string
          cover_url: string | null
          created_at: string
          description: string | null
          id: string
          sequence_order: number
          slug: string | null
          status:
            | Database["public"]["Enums"]["community_content_status_enum"]
            | null
          title: string
          updated_at: string
        }[]
      }
      set_community_course_prerequisites: {
        Args: { p_course_id: string; p_prerequisite_ids: string[] }
        Returns: boolean
      }
      toggle_community_course_published: {
        Args: { course_slug: string }
        Returns: Database["public"]["Enums"]["community_content_status_enum"]
      }
      toggle_community_forum_post_pinned: {
        Args: { p_post_id: string }
        Returns: boolean
      }
      toggle_community_listing: {
        Args: { p_community_id: string }
        Returns: undefined
      }
      toggle_role_permissions: {
        Args: { p_role_name: string; p_permission_name: string }
        Returns: boolean
      }
      transfer_community_ownership: {
        Args: { target_community_id: string; new_owner_id: string }
        Returns: undefined
      }
      update_community_course: {
        Args: {
          p_course_id: string
          p_title: string
          p_description?: string
          p_cover_url?: string
          p_access?: Database["public"]["Enums"]["community_course_access_enum"]
        }
        Returns: {
          access:
            | Database["public"]["Enums"]["community_course_access_enum"]
            | null
          access_details: Json | null
          community_id: string
          cover_url: string | null
          created_at: string
          description: string | null
          id: string
          sequence_order: number
          slug: string | null
          status:
            | Database["public"]["Enums"]["community_content_status_enum"]
            | null
          title: string
          updated_at: string
        }
      }
      update_community_course_chapter: {
        Args: {
          p_chapter_id: string
          p_title: string
          p_description: string
          p_icon: string
        }
        Returns: boolean
      }
      update_community_course_lesson: {
        Args: {
          p_lesson_id: string
          p_new_title: string
          p_new_icon: string
          p_new_lesson_text: Json
          p_new_content_data: Json
          p_new_content_type?: Database["public"]["Enums"]["community_course_content_type_enum"]
          p_new_character_count?: number
          p_new_word_count?: number
        }
        Returns: boolean
      }
      update_community_course_lesson_status: {
        Args: {
          p_lesson_id: string
          p_target_status: Database["public"]["Enums"]["community_content_status_enum"]
        }
        Returns: Database["public"]["Enums"]["community_content_status_enum"]
      }
      update_community_course_status: {
        Args: {
          p_course_slug: string
          p_target_status: Database["public"]["Enums"]["community_content_status_enum"]
        }
        Returns: Database["public"]["Enums"]["community_content_status_enum"]
      }
      update_community_course_with_prerequisites: {
        Args: {
          p_course_id: string
          p_title: string
          p_description?: string
          p_cover_url?: string
          p_prerequisite_ids?: string[]
          p_access?: Database["public"]["Enums"]["community_course_access_enum"]
        }
        Returns: {
          access:
            | Database["public"]["Enums"]["community_course_access_enum"]
            | null
          access_details: Json | null
          community_id: string
          cover_url: string | null
          created_at: string
          description: string | null
          id: string
          sequence_order: number
          slug: string | null
          status:
            | Database["public"]["Enums"]["community_content_status_enum"]
            | null
          title: string
          updated_at: string
        }
      }
      update_community_explore_request_status: {
        Args: {
          p_community_id: string
          p_status: Database["public"]["Enums"]["community_explore_request_status"]
          p_reason?: string
        }
        Returns: undefined
      }
      update_draft_community_forum_post: {
        Args: {
          p_post_id: string
          p_draft_content: Json
          p_draft_title?: string
          p_draft_category_id?: string
          p_draft_character_count?: number
          p_draft_word_count?: number
        }
        Returns: boolean
      }
      update_stripe_account: {
        Args: {
          p_stripe_account_id: string
          p_applied_configurations?: string[]
          p_contact_email?: string
          p_display_name?: string
          p_dashboard?: string
          p_configuration?: Json
          p_capabilities?: Json
          p_requirements?: Json
          p_defaults?: Json
          p_identity?: Json
          p_metadata?: Json
        }
        Returns: string
      }
      upsert_invoice: {
        Args: {
          p_target_user_id: string
          p_target_community_id: string
          p_target_invoice_id: string
          p_status: Database["public"]["Enums"]["payment_status"]
          p_purchase_type: Database["public"]["Enums"]["purchase_type"]
          p_total_amount: number
          p_currency: string
          p_line_items: Json
        }
        Returns: {
          community_id: string | null
          created_at: string
          currency: string
          id: string
          purchase_type: Database["public"]["Enums"]["purchase_type"]
          status: Database["public"]["Enums"]["payment_status"]
          total_amount: number
          updated_at: string
          user_id: string | null
        }
      }
      upsert_subscription: {
        Args: {
          p_target_user_id: string
          p_target_subscription_id: string
          p_purchase_type: Database["public"]["Enums"]["purchase_type"]
          p_status: Database["public"]["Enums"]["subscription_status"]
          p_active: boolean
          p_currency: string
          p_line_items: Json
          p_cancel_at_period_end: boolean
          p_current_period_start: string
          p_current_period_end: string
          p_trial_starts_at?: string
          p_trial_ends_at?: string
          p_target_community_id?: string
          p_payment_method_id?: string
        }
        Returns: {
          active: boolean
          cancel_at_period_end: boolean | null
          canceled_at: string | null
          community_id: string | null
          created_at: string
          currency: string
          current_period_end: string
          current_period_start: string
          ended_at: string | null
          id: string
          metadata: Json | null
          payment_method_id: string | null
          purchase_type: Database["public"]["Enums"]["purchase_type"]
          status: Database["public"]["Enums"]["subscription_status"]
          trial_ends_at: string | null
          trial_starts_at: string | null
          updated_at: string
          user_id: string | null
        }
      }
    }
    Enums: {
      app_permissions:
        | "roles.manage"
        | "billing.manage"
        | "settings.manage"
        | "members.manage"
        | "invites.manage"
      checkout_session_status: "open" | "complete" | "expired"
      community_content_status_enum: "draft" | "published" | "archived"
      community_course_access_enum:
        | "standard"
        | "private"
        | "level"
        | "paid"
        | "time"
      community_course_content_type_enum: "video" | "quiz" | "exercise" | "none"
      community_explore_request_status:
        | "not_submitted"
        | "pending"
        | "approved"
        | "rejected"
      community_forum_post_status: "draft" | "published"
      community_forum_reaction_type:
        | "+1"
        | "heart"
        | "fire"
        | "100"
        | "pray"
        | "joy"
        | "tada"
        | "cry"
        | "clap"
        | "-1"
      community_membership_status: "active" | "inactive" | "banned"
      community_message_author: "support" | "customer"
      community_page_media_type: "image" | "video" | "embed"
      community_page_type: "system" | "blog" | "page"
      community_permissions:
        | "roles.manage"
        | "billing.manage"
        | "settings.manage"
        | "members.manage"
        | "invites.manage"
        | "community.tickets.update"
        | "community.tickets.delete"
        | "community.forums.settings"
        | "community.forums.category.settings"
        | "community.forums.category.update"
        | "community.forums.category.delete"
        | "community.forums.post.update"
        | "community.forums.post.delete"
        | "community.forums.post.pin_post"
        | "community.forums.comment.update"
        | "community.forums.comment.delete"
        | "community.forums.reaction.delete"
        | "community.courses.settings"
        | "community.courses.create"
        | "community.courses.update"
        | "community.courses.archive"
        | "community.courses.publish"
        | "community.courses.delete"
        | "community.courses.chapters.settings"
        | "community.courses.chapters.create"
        | "community.courses.chapters.update"
        | "community.courses.chapters.delete"
        | "community.courses.lessons.settings"
        | "community.courses.lessons.create"
        | "community.courses.lessons.update"
        | "community.courses.lessons.archive"
        | "community.courses.lessons.publish"
        | "community.courses.lessons.delete"
        | "community.user.content_progress.reset"
        | "community.members.remove"
        | "community.members.send_email"
        | "community.members.ban_member"
        | "community.team.payouts"
        | "community.stripe_account.settings"
        | "community.stripe_account.payouts.update"
        | "community.stripe_account.payouts.delete"
        | "community.stripe_account.payouts.request"
        | "community.products.settings"
        | "community.products.create"
        | "community.products.update"
        | "community.products.delete"
        | "community.prices.create"
        | "community.prices.update"
        | "community.prices.delete"
        | "community.prices.toggle"
        | "community.pages.settings"
        | "community.pages.create"
        | "community.pages.update"
        | "community.pages.delete"
        | "community.pages.publish"
        | "community.pages.unpublish"
        | "community.learning_paths.settings"
        | "community.learning_paths.create"
        | "community.learning_paths.update"
        | "community.learning_paths.delete"
      community_ticket_priority: "low" | "medium" | "high"
      community_ticket_status: "open" | "closed" | "resolved" | "in_progress"
      notification_channel: "in_app" | "email"
      notification_type: "info" | "warning" | "error"
      order_type: "subscription" | "one-time"
      payment_status: "pending" | "succeeded" | "failed"
      price_interval: "month" | "year" | "week" | "day" | "one_time"
      price_type: "one_time" | "recurring"
      product_type: "platform_plan" | "community_plan" | "digital_product"
      purchase_type:
        | "community_membership"
        | "community_ownership"
        | "community_product"
        | "platform_product"
      seller_type: "platform" | "community" | "user"
      stripe_account_payout_status_type:
        | "pending"
        | "paid"
        | "failed"
        | "canceled"
      subscription_item_type: "flat" | "per_seat" | "metered"
      subscription_status:
        | "active"
        | "trialing"
        | "past_due"
        | "canceled"
        | "unpaid"
        | "incomplete"
        | "incomplete_expired"
        | "paused"
    }
    CompositeTypes: {
      community_invitation: {
        email: string | null
        role: string | null
      }
      community_listing_requirements_result: {
        meets_requirements: boolean | null
        name_valid: boolean | null
        description_length_sufficient: boolean | null
        category_changed: boolean | null
        logo_exists: boolean | null
        cover_exists: boolean | null
        about_page_exists: boolean | null
        about_page_content_length: number | null
        about_page_media_count: number | null
      }
    }
  }
  storage: {
    Tables: {
      buckets: {
        Row: {
          allowed_mime_types: string[] | null
          avif_autodetection: boolean | null
          created_at: string | null
          file_size_limit: number | null
          id: string
          name: string
          owner: string | null
          owner_id: string | null
          public: boolean | null
          updated_at: string | null
        }
        Insert: {
          allowed_mime_types?: string[] | null
          avif_autodetection?: boolean | null
          created_at?: string | null
          file_size_limit?: number | null
          id: string
          name: string
          owner?: string | null
          owner_id?: string | null
          public?: boolean | null
          updated_at?: string | null
        }
        Update: {
          allowed_mime_types?: string[] | null
          avif_autodetection?: boolean | null
          created_at?: string | null
          file_size_limit?: number | null
          id?: string
          name?: string
          owner?: string | null
          owner_id?: string | null
          public?: boolean | null
          updated_at?: string | null
        }
        Relationships: []
      }
      migrations: {
        Row: {
          executed_at: string | null
          hash: string
          id: number
          name: string
        }
        Insert: {
          executed_at?: string | null
          hash: string
          id: number
          name: string
        }
        Update: {
          executed_at?: string | null
          hash?: string
          id?: number
          name?: string
        }
        Relationships: []
      }
      objects: {
        Row: {
          bucket_id: string | null
          created_at: string | null
          id: string
          last_accessed_at: string | null
          level: number | null
          metadata: Json | null
          name: string | null
          owner: string | null
          owner_id: string | null
          path_tokens: string[] | null
          updated_at: string | null
          user_metadata: Json | null
          version: string | null
        }
        Insert: {
          bucket_id?: string | null
          created_at?: string | null
          id?: string
          last_accessed_at?: string | null
          level?: number | null
          metadata?: Json | null
          name?: string | null
          owner?: string | null
          owner_id?: string | null
          path_tokens?: string[] | null
          updated_at?: string | null
          user_metadata?: Json | null
          version?: string | null
        }
        Update: {
          bucket_id?: string | null
          created_at?: string | null
          id?: string
          last_accessed_at?: string | null
          level?: number | null
          metadata?: Json | null
          name?: string | null
          owner?: string | null
          owner_id?: string | null
          path_tokens?: string[] | null
          updated_at?: string | null
          user_metadata?: Json | null
          version?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "objects_bucketId_fkey"
            columns: ["bucket_id"]
            isOneToOne: false
            referencedRelation: "buckets"
            referencedColumns: ["id"]
          },
        ]
      }
      prefixes: {
        Row: {
          bucket_id: string
          created_at: string | null
          level: number
          name: string
          updated_at: string | null
        }
        Insert: {
          bucket_id: string
          created_at?: string | null
          level?: number
          name: string
          updated_at?: string | null
        }
        Update: {
          bucket_id?: string
          created_at?: string | null
          level?: number
          name?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "prefixes_bucketId_fkey"
            columns: ["bucket_id"]
            isOneToOne: false
            referencedRelation: "buckets"
            referencedColumns: ["id"]
          },
        ]
      }
      s3_multipart_uploads: {
        Row: {
          bucket_id: string
          created_at: string
          id: string
          in_progress_size: number
          key: string
          owner_id: string | null
          upload_signature: string
          user_metadata: Json | null
          version: string
        }
        Insert: {
          bucket_id: string
          created_at?: string
          id: string
          in_progress_size?: number
          key: string
          owner_id?: string | null
          upload_signature: string
          user_metadata?: Json | null
          version: string
        }
        Update: {
          bucket_id?: string
          created_at?: string
          id?: string
          in_progress_size?: number
          key?: string
          owner_id?: string | null
          upload_signature?: string
          user_metadata?: Json | null
          version?: string
        }
        Relationships: [
          {
            foreignKeyName: "s3_multipart_uploads_bucket_id_fkey"
            columns: ["bucket_id"]
            isOneToOne: false
            referencedRelation: "buckets"
            referencedColumns: ["id"]
          },
        ]
      }
      s3_multipart_uploads_parts: {
        Row: {
          bucket_id: string
          created_at: string
          etag: string
          id: string
          key: string
          owner_id: string | null
          part_number: number
          size: number
          upload_id: string
          version: string
        }
        Insert: {
          bucket_id: string
          created_at?: string
          etag: string
          id?: string
          key: string
          owner_id?: string | null
          part_number: number
          size?: number
          upload_id: string
          version: string
        }
        Update: {
          bucket_id?: string
          created_at?: string
          etag?: string
          id?: string
          key?: string
          owner_id?: string | null
          part_number?: number
          size?: number
          upload_id?: string
          version?: string
        }
        Relationships: [
          {
            foreignKeyName: "s3_multipart_uploads_parts_bucket_id_fkey"
            columns: ["bucket_id"]
            isOneToOne: false
            referencedRelation: "buckets"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "s3_multipart_uploads_parts_upload_id_fkey"
            columns: ["upload_id"]
            isOneToOne: false
            referencedRelation: "s3_multipart_uploads"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      add_prefixes: {
        Args: { _bucket_id: string; _name: string }
        Returns: undefined
      }
      can_insert_object: {
        Args: { bucketid: string; name: string; owner: string; metadata: Json }
        Returns: undefined
      }
      delete_prefix: {
        Args: { _bucket_id: string; _name: string }
        Returns: boolean
      }
      extension: {
        Args: { name: string }
        Returns: string
      }
      filename: {
        Args: { name: string }
        Returns: string
      }
      foldername: {
        Args: { name: string }
        Returns: string[]
      }
      get_level: {
        Args: { name: string }
        Returns: number
      }
      get_prefix: {
        Args: { name: string }
        Returns: string
      }
      get_prefixes: {
        Args: { name: string }
        Returns: string[]
      }
      get_size_by_bucket: {
        Args: Record<PropertyKey, never>
        Returns: {
          size: number
          bucket_id: string
        }[]
      }
      list_multipart_uploads_with_delimiter: {
        Args: {
          bucket_id: string
          prefix_param: string
          delimiter_param: string
          max_keys?: number
          next_key_token?: string
          next_upload_token?: string
        }
        Returns: {
          key: string
          id: string
          created_at: string
        }[]
      }
      list_objects_with_delimiter: {
        Args: {
          bucket_id: string
          prefix_param: string
          delimiter_param: string
          max_keys?: number
          start_after?: string
          next_token?: string
        }
        Returns: {
          name: string
          id: string
          metadata: Json
          updated_at: string
        }[]
      }
      operation: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      search: {
        Args: {
          prefix: string
          bucketname: string
          limits?: number
          levels?: number
          offsets?: number
          search?: string
          sortcolumn?: string
          sortorder?: string
        }
        Returns: {
          name: string
          id: string
          updated_at: string
          created_at: string
          last_accessed_at: string
          metadata: Json
        }[]
      }
      search_legacy_v1: {
        Args: {
          prefix: string
          bucketname: string
          limits?: number
          levels?: number
          offsets?: number
          search?: string
          sortcolumn?: string
          sortorder?: string
        }
        Returns: {
          name: string
          id: string
          updated_at: string
          created_at: string
          last_accessed_at: string
          metadata: Json
        }[]
      }
      search_v1_optimised: {
        Args: {
          prefix: string
          bucketname: string
          limits?: number
          levels?: number
          offsets?: number
          search?: string
          sortcolumn?: string
          sortorder?: string
        }
        Returns: {
          name: string
          id: string
          updated_at: string
          created_at: string
          last_accessed_at: string
          metadata: Json
        }[]
      }
      search_v2: {
        Args: {
          prefix: string
          bucket_name: string
          limits?: number
          levels?: number
          start_after?: string
        }
        Returns: {
          key: string
          name: string
          id: string
          updated_at: string
          created_at: string
          metadata: Json
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {
      app_permissions: [
        "roles.manage",
        "billing.manage",
        "settings.manage",
        "members.manage",
        "invites.manage",
      ],
      checkout_session_status: ["open", "complete", "expired"],
      community_content_status_enum: ["draft", "published", "archived"],
      community_course_access_enum: [
        "standard",
        "private",
        "level",
        "paid",
        "time",
      ],
      community_course_content_type_enum: ["video", "quiz", "exercise", "none"],
      community_explore_request_status: [
        "not_submitted",
        "pending",
        "approved",
        "rejected",
      ],
      community_forum_post_status: ["draft", "published"],
      community_forum_reaction_type: [
        "+1",
        "heart",
        "fire",
        "100",
        "pray",
        "joy",
        "tada",
        "cry",
        "clap",
        "-1",
      ],
      community_membership_status: ["active", "inactive", "banned"],
      community_message_author: ["support", "customer"],
      community_page_media_type: ["image", "video", "embed"],
      community_page_type: ["system", "blog", "page"],
      community_permissions: [
        "roles.manage",
        "billing.manage",
        "settings.manage",
        "members.manage",
        "invites.manage",
        "community.tickets.update",
        "community.tickets.delete",
        "community.forums.settings",
        "community.forums.category.settings",
        "community.forums.category.update",
        "community.forums.category.delete",
        "community.forums.post.update",
        "community.forums.post.delete",
        "community.forums.post.pin_post",
        "community.forums.comment.update",
        "community.forums.comment.delete",
        "community.forums.reaction.delete",
        "community.courses.settings",
        "community.courses.create",
        "community.courses.update",
        "community.courses.archive",
        "community.courses.publish",
        "community.courses.delete",
        "community.courses.chapters.settings",
        "community.courses.chapters.create",
        "community.courses.chapters.update",
        "community.courses.chapters.delete",
        "community.courses.lessons.settings",
        "community.courses.lessons.create",
        "community.courses.lessons.update",
        "community.courses.lessons.archive",
        "community.courses.lessons.publish",
        "community.courses.lessons.delete",
        "community.user.content_progress.reset",
        "community.members.remove",
        "community.members.send_email",
        "community.members.ban_member",
        "community.team.payouts",
        "community.stripe_account.settings",
        "community.stripe_account.payouts.update",
        "community.stripe_account.payouts.delete",
        "community.stripe_account.payouts.request",
        "community.products.settings",
        "community.products.create",
        "community.products.update",
        "community.products.delete",
        "community.prices.create",
        "community.prices.update",
        "community.prices.delete",
        "community.prices.toggle",
        "community.pages.settings",
        "community.pages.create",
        "community.pages.update",
        "community.pages.delete",
        "community.pages.publish",
        "community.pages.unpublish",
        "community.learning_paths.settings",
        "community.learning_paths.create",
        "community.learning_paths.update",
        "community.learning_paths.delete",
      ],
      community_ticket_priority: ["low", "medium", "high"],
      community_ticket_status: ["open", "closed", "resolved", "in_progress"],
      notification_channel: ["in_app", "email"],
      notification_type: ["info", "warning", "error"],
      order_type: ["subscription", "one-time"],
      payment_status: ["pending", "succeeded", "failed"],
      price_interval: ["month", "year", "week", "day", "one_time"],
      price_type: ["one_time", "recurring"],
      product_type: ["platform_plan", "community_plan", "digital_product"],
      purchase_type: [
        "community_membership",
        "community_ownership",
        "community_product",
        "platform_product",
      ],
      seller_type: ["platform", "community", "user"],
      stripe_account_payout_status_type: [
        "pending",
        "paid",
        "failed",
        "canceled",
      ],
      subscription_item_type: ["flat", "per_seat", "metered"],
      subscription_status: [
        "active",
        "trialing",
        "past_due",
        "canceled",
        "unpaid",
        "incomplete",
        "incomplete_expired",
        "paused",
      ],
    },
  },
  storage: {
    Enums: {},
  },
} as const

