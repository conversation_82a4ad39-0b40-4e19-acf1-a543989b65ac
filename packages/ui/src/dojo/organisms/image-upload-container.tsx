'use client';

import { useCallback, useEffect, useState } from 'react';

import Image from 'next/image';

import { ImageIcon, RefreshCw, X } from 'lucide-react';
import { useDropzone } from 'react-dropzone';

import { cn } from '../../lib/utils/cn';
import { Button } from '../../shadcn/button';
import { Trans } from '../atoms/trans';
import { ImageCropDialog } from './image-crop-dialog';

type ImageUploadContainerProps = {
  imageUrl: string | null;
  headingKey: string;
  uploadHeadingKey: string;
  uploadSubheadingKey: string;
  imageRounded?: string;
  imageSize?: string;
  aspectRatio?: number; // Keep for passing down
  cropShape?: 'rect' | 'round'; // Keep for passing down
  onDelete: () => Promise<void> | void;
  onUpload: (file: File) => Promise<void> | void;
};

export function ImageUploadContainer({
  imageUrl,
  headingKey,
  uploadHeadingKey,
  uploadSubheadingKey,
  imageRounded = 'rounded-xl',
  imageSize = 'w-20 h-20',
  aspectRatio = 1,
  cropShape = 'rect',
  onDelete,
  onUpload,
}: ImageUploadContainerProps) {
  const [displayPreviewUrl, setDisplayPreviewUrl] = useState<string | null>(
    imageUrl,
  );
  const [originalImageSrc, setOriginalImageSrc] = useState<string | null>(null);
  const [originalFile, setOriginalFile] = useState<File | null>(null);
  const [isHovering, setIsHovering] = useState(false);

  const [isCropDialogOpen, setIsCropDialogOpen] = useState(false);

  // Update preview ONLY if imageUrl prop changes EXTERNALLY to a non-null value.
  // Internal state changes (after crop/upload or clear) are handled by their respective callbacks.
  useEffect(() => {
    // If the external prop provides a new, non-null URL that's different
    // from the current preview, update the internal state.
    if (imageUrl && imageUrl !== displayPreviewUrl) {
      setDisplayPreviewUrl(imageUrl);
      // Clear any pending crop state if the external source changes
      setOriginalImageSrc(null);
      setOriginalFile(null);
    }
    // DO NOT reset the preview to null here based on the prop becoming null.
    // This prevents clearing the preview immediately after an internal upload
    // when the parent RHF state changes to a File object.
    // Clearing is handled explicitly by handleClear.
  }, [imageUrl, displayPreviewUrl]); // Include displayPreviewUrl in dependencies

  const handleClear = useCallback(async () => {
    setDisplayPreviewUrl(null);
    setOriginalImageSrc(null);
    setOriginalFile(null);
    setIsCropDialogOpen(false);
    await onDelete();
  }, [onDelete]);

  // This function is called when a file is dropped/selected
  const handleDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles && acceptedFiles.length > 0) {
      const file = acceptedFiles[0]!;
      setOriginalFile(file);

      const reader = new FileReader();
      reader.onload = () => {
        setOriginalImageSrc(reader.result as string);
        setIsCropDialogOpen(true);
      };
      reader.readAsDataURL(file);
    }
  }, []);

  // Callback for when the Dialog's Save button is clicked
  const handleDialogSave = useCallback(
    async (finalFile: File) => {
      const newPreviewUrl = URL.createObjectURL(finalFile);
      setDisplayPreviewUrl(newPreviewUrl);
      await onUpload(finalFile);
      setOriginalImageSrc(null);
      setOriginalFile(null);
    },
    [onUpload],
  );

  // Callback for when the Dialog is cancelled/closed
  const handleDialogCancel = useCallback(() => {
    setOriginalImageSrc(null);
    setOriginalFile(null);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop: handleDrop,
    accept: {
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png'],
      'image/webp': ['.webp'],
      'image/svg+xml': ['.svg'],
    },
    maxSize: 5 * 1024 * 1024,
    maxFiles: 1,
  });

  // Conditional sizing logic remains the same
  const hasExplicitHeight = imageSize.includes('h-');
  const sizeClasses = imageSize.includes('size-')
    ? imageSize.replace('size-', 'w-').replace('size-', 'h-')
    : imageSize;
  const paddingTopPercent =
    !hasExplicitHeight && aspectRatio
      ? `${(1 / aspectRatio) * 100}%`
      : undefined;

  return (
    <>
      <div className="flex flex-col space-y-4">
        <span className="text-sm leading-none font-semibold tracking-tight">
          <Trans i18nKey={headingKey} />
        </span>

        <div className="flex items-start space-x-4">
          {displayPreviewUrl ? (
            <div
              {...getRootProps()}
              className={cn('relative cursor-pointer', sizeClasses)}
              style={paddingTopPercent ? { paddingTop: paddingTopPercent } : {}}
              onMouseEnter={() => setIsHovering(true)}
              onMouseLeave={() => setIsHovering(false)}
            >
              <Image
                src={displayPreviewUrl}
                alt="Preview"
                fill
                sizes="(max-width: 768px) 100vw, 33vw"
                className={cn(
                  'absolute inset-0 h-full w-full',
                  imageRounded,
                  'object-cover',
                )}
                key={displayPreviewUrl}
              />
              <input {...getInputProps()} />

              <Button
                variant="ghost"
                size="icon"
                className={cn(
                  'bg-background hover:bg-primary/80 absolute -top-2 -right-2 z-10 h-6 w-6 rounded-full p-1 transition-opacity',
                  isHovering ? 'opacity-100' : 'opacity-0',
                )}
                onClick={(e) => {
                  e.stopPropagation();
                  handleClear();
                }}
              >
                <X className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className={cn(
                  'bg-background hover:bg-secondary/80 absolute -right-2 -bottom-2 z-10 h-6 w-6 rounded-full p-1 transition-opacity',
                  isHovering ? 'opacity-100' : 'opacity-0',
                )}
                aria-label="Upload new image"
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>
          ) : (
            <div
              {...getRootProps()}
              className={cn(
                'border-border relative flex cursor-pointer flex-col items-center justify-center border-2 border-dashed transition-colors',
                sizeClasses,
                imageRounded,
                isDragActive
                  ? 'border-primary bg-primary/5'
                  : 'hover:border-muted-foreground/50',
              )}
              style={paddingTopPercent ? { paddingTop: paddingTopPercent } : {}}
            >
              <input {...getInputProps()} />
              <div className="absolute inset-0 flex flex-col items-center justify-center">
                <ImageIcon className="text-muted-foreground mb-1 h-6 w-6" />
                <span className="text-muted-foreground text-xs">Upload</span>
              </div>
            </div>
          )}
        </div>

        {!displayPreviewUrl ? (
          <div className="flex flex-col space-y-1 pt-2 text-sm">
            <span>
              <Trans i18nKey={uploadHeadingKey} />
            </span>
            <span className="text-muted-foreground text-xs">
              <Trans i18nKey={uploadSubheadingKey} />
            </span>
          </div>
        ) : null}
      </div>

      <ImageCropDialog
        open={isCropDialogOpen}
        onOpenChange={setIsCropDialogOpen}
        imageSrc={originalImageSrc}
        originalFile={originalFile}
        aspectRatio={aspectRatio}
        cropShape={cropShape}
        onSave={handleDialogSave}
        onCancel={handleDialogCancel}
      />
    </>
  );
}

ImageUploadContainer.displayName = 'ImageUploadContainer';
