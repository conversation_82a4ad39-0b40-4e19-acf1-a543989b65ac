import { cn } from '@kit/ui/utils';

type HeaderProps = React.HTMLAttributes<HTMLDivElement> & {
  logo?: React.ReactNode;
  navigation?: React.ReactNode;
  actions?: React.ReactNode;
};

export const Header: React.FC<HeaderProps> = function ({
  className,
  logo,
  navigation,
  actions,
  ...props
}) {
  return (
    <div
      className={cn(
        'bg-background/80 dark:bg-background/50 sticky top-0 z-100 w-full py-2 backdrop-blur-md',
        className,
      )}
      {...props}
    >
      <div className="container mx-auto max-w-7xl">
        <div className="grid h-14 grid-cols-3 items-center">
          <div className={'flex items-center justify-start'}>{logo}</div>
          <div className="flex justify-center">{navigation}</div>
          <div className="flex items-center justify-end space-x-2">
            {actions}
          </div>
        </div>
      </div>
    </div>
  );
};
