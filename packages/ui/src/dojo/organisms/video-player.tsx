'use client';

import React, { useCallback, useEffect, useRef, useState } from 'react';

import dynamic from 'next/dynamic';

import {
  Pause,
  Play,
  RotateCcw,
  RotateCw,
  Volume2,
  VolumeX,
} from 'lucide-react';

import { Button } from '@kit/ui/button';
import { Slider } from '@kit/ui/slider';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@kit/ui/tooltip';

// Dynamically import ReactPlayer with no SSR
const ReactPlayer = dynamic(() => import('react-player'), { ssr: false });

export type VideoPlayerProps = {
  videoUrl: string;
  shortCutKeysEnabled: boolean;
  additionalControls?: React.ReactNode;
};

export function VideoPlayer({
  videoUrl,
  shortCutKeysEnabled = true,
  additionalControls,
}: VideoPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(0.5);
  const [previousVolume, setPreviousVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [isHovering, setIsHovering] = useState(false);
  const playerRef = useRef<{ seekTo: (time: number) => void } | null>(null);
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const announceVideoState = useCallback((message: string) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('role', 'status');
    announcement.className = 'sr-only'; // Hide visually but keep for screen readers
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  }, []);

  const togglePlay = useCallback(() => {
    setIsPlaying((prev) => {
      const newState = !prev;
      announceVideoState(newState ? 'Video playing' : 'Video paused');
      return newState;
    });
  }, [announceVideoState]);

  const handleProgress = useCallback(
    (state: {
      played: number;
      playedSeconds: number;
      loaded: number;
      loadedSeconds: number;
    }) => {
      setCurrentTime(state.playedSeconds);
    },
    [],
  );

  const handleDuration = useCallback((duration: number) => {
    setDuration(duration);
  }, []);

  const handleSeek = useCallback((newValue: number[]) => {
    if (playerRef.current && typeof newValue[0] === 'number') {
      playerRef.current.seekTo(newValue[0]);
    }
  }, []);

  const handleVolumeChange = useCallback((newValue: number[]) => {
    const newVolume = newValue[0] ?? 0;
    setVolume(newVolume);
    setIsMuted(newVolume === 0);
  }, []);

  const toggleMute = useCallback(() => {
    setIsMuted((prev) => {
      const newMuted = !prev;
      if (newMuted) {
        setPreviousVolume(volume);
        setVolume(0);
      } else {
        setVolume(previousVolume);
      }
      return newMuted;
    });
  }, [volume, previousVolume]);

  const reverse10Seconds = useCallback(() => {
    if (playerRef.current) {
      const newTime = Math.max(currentTime - 10, 0);
      playerRef.current.seekTo(newTime);
      announceVideoState('Rewound 10 seconds');
    }
  }, [currentTime, announceVideoState]);

  const forward10Seconds = useCallback(() => {
    if (playerRef.current) {
      const newTime = Math.min(currentTime + 10, duration);
      playerRef.current.seekTo(newTime);
      announceVideoState('Forward 10 seconds');
    }
  }, [currentTime, duration, announceVideoState]);

  const formatTime = useCallback((time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }, []);

  const handleMouseEnter = useCallback(() => {
    setIsHovering(true);
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
    }
  }, []);

  const handleMouseLeave = useCallback(() => {
    hoverTimeoutRef.current = setTimeout(() => {
      setIsHovering(false);
    }, 700);
  }, []);

  useEffect(() => {
    return () => {
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (!shortCutKeysEnabled) return;
    const handleKeyPress = (e: KeyboardEvent) => {
      // Only handle keypresses if not typing in an input
      if (
        ['input', 'textarea'].includes(
          (e.target as HTMLElement).tagName.toLowerCase(),
        )
      ) {
        return;
      }

      switch (e.code) {
        case 'Space':
          e.preventDefault();
          togglePlay();
          break;
        case 'ArrowLeft':
          e.preventDefault();
          reverse10Seconds();
          break;
        case 'ArrowRight':
          e.preventDefault();
          forward10Seconds();
          break;
        case 'KeyM':
          e.preventDefault();
          toggleMute();
          announceVideoState(!isMuted ? 'Sound unmuted' : 'Sound muted');
          break;
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [
    togglePlay,
    reverse10Seconds,
    forward10Seconds,
    toggleMute,
    isMuted,
    announceVideoState,

    shortCutKeysEnabled,
  ]);

  return (
    <div className="space-y-4">
      <div
        className="relative aspect-video overflow-hidden rounded-lg bg-black"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        role="application"
        aria-label="Video player"
      >
        <ReactPlayer
          ref={playerRef}
          url={videoUrl}
          width="100%"
          height="100%"
          playing={isPlaying}
          volume={volume}
          muted={isMuted}
          onProgress={handleProgress}
          onDuration={handleDuration}
          config={{
            youtube: {
              playerVars: { modestbranding: 1 },
            },
            vimeo: {
              playerOptions: { title: false, byline: false, portrait: false },
            },
          }}
        />
        {(isHovering || !isPlaying) && (
          <div className="absolute inset-0 flex flex-col items-center justify-center bg-black/50 transition-opacity duration-100">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={reverse10Seconds}
                className="rounded-full text-white hover:bg-white/20"
                title="Rewind 10s (←)"
              >
                <RotateCcw className="h-8 w-8" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={togglePlay}
                className="rounded-full text-white hover:bg-white/20"
                title="Play/Pause (Space)"
              >
                {isPlaying ? (
                  <Pause className="h-12 w-12" />
                ) : (
                  <Play className="h-12 w-12" />
                )}
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={forward10Seconds}
                className="rounded-full text-white hover:bg-white/20"
                title="Forward 10s (→)"
              >
                <RotateCw className="h-8 w-8" />
              </Button>
            </div>
            <div className="mt-2 text-white">
              {formatTime(currentTime)} / {formatTime(duration)}
            </div>
          </div>
        )}
      </div>
      <div className="space-y-2">
        <div className="flex items-center space-x-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={togglePlay}
                  className="rounded-full"
                >
                  {isPlaying ? (
                    <Pause className="h-4 w-4" />
                  ) : (
                    <Play className="h-4 w-4" />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Play/Pause (Space)</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={reverse10Seconds}
                  className="rounded-full"
                >
                  <RotateCcw className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Rewind 10s (←)</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={forward10Seconds}
                  className="rounded-full"
                >
                  <RotateCw className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Forward 10s (→)</p>
              </TooltipContent>
            </Tooltip>

            <div className="flex grow items-center gap-4">
              <Slider
                value={[currentTime]}
                max={duration}
                step={1}
                onValueChange={handleSeek}
                className="grow"
              />
              <span className="text-sm whitespace-nowrap">
                {formatTime(currentTime)} / {formatTime(duration)}
              </span>
            </div>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={toggleMute}
                  className="rounded-full"
                >
                  {isMuted ? (
                    <VolumeX className="h-4 w-4" />
                  ) : (
                    <Volume2 className="h-4 w-4" />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Toggle Mute (M)</p>
              </TooltipContent>
            </Tooltip>

            <Slider
              value={[volume]}
              max={1}
              step={0.1}
              onValueChange={handleVolumeChange}
              className="w-24"
            />
          </TooltipProvider>

          {additionalControls}
        </div>
      </div>
    </div>
  );
}

VideoPlayer.displayName = 'VideoPlayer';
