'use client';

import { useEffect, useState } from 'react';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { IconTextSelector } from '@kit/ui/dojo/molecules/icon-text-selector';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { Trans } from '@kit/ui/trans';
import { cn } from '@kit/ui/utils';

export type CountryData = {
  id: string;
  name: string;
  isoCode1: string;
  isoCode2: string;
  isoCode3: string;
  icon: string;
};

export type ConnectSetupFormProps = {
  user: { userId: string; countryISOCode1: string; email: string };
  countries: CountryData[];
  onSubmit: (values: ConnectSetupFormValues) => Promise<void>;
  className?: string;
};

export type ConnectSetupFormValues = {
  userId: string;
  countryISOCode1: string;
  email: string;
};

const formSchema = z.object({
  userId: z.string().uuid(),
  countryISOCode1: z.string().length(2),
  email: z.string().email({ message: 'Please enter a valid email address' }),
});

/**
 * Form component for setting up a Stripe Connect account
 */
export function StripeAccountSetupForm(props: ConnectSetupFormProps) {
  const { user, onSubmit, countries, className } = props;
  const { userId, countryISOCode1, email } = user;
  const { t } = useTranslation();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formReady, setFormReady] = useState(false);

  const form = useForm<ConnectSetupFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      userId,
      countryISOCode1: countryISOCode1 || '',
      email,
    },
    mode: 'onChange',
  });

  useEffect(() => {
    setFormReady(true);
  }, []);

  const handleSubmit = async (values: ConnectSetupFormValues) => {
    try {
      setIsSubmitting(true);
      await onSubmit(values);
    } catch (error) {
      console.error('Error submitting Connect setup form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const isEmailValid = !form.formState.errors.email;
  const isCountryValid =
    !form.formState.errors.countryISOCode1 &&
    !!form.getValues().countryISOCode1;
  const isFormValid = isEmailValid && isCountryValid;

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader>
        <CardTitle>
          <Trans i18nKey="billing:connect.setup.title" />
        </CardTitle>
        <CardDescription>
          <Trans i18nKey="billing:connect.setup.description" />
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6"
          >
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <Trans i18nKey="billing:connect.setup.emailLabel" />
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="<EMAIL>" {...field} />
                  </FormControl>
                  <FormDescription>
                    <Trans i18nKey="billing:connect.setup.emailDescription" />
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="countryISOCode1"
              render={({ field }) => {
                return (
                  <FormItem>
                    <FormLabel>
                      <Trans i18nKey="billing:connect.setup.countryLabel" />
                    </FormLabel>
                    <FormControl>
                      <IconTextSelector
                        placeholder={t(
                          'billing:connect.setup.countryPlaceholder',
                        )}
                        description={t(
                          'billing:connect.setup.countrySearchDescription',
                        )}
                        currentId={field.value}
                        onChange={(selectedCountry: string) => {
                          field.onChange(selectedCountry);
                        }}
                        items={countries.map((country) => ({
                          id: country.isoCode1,
                          name: country.name,
                          icon: country.icon,
                        }))}
                      />
                    </FormControl>

                    <FormDescription>
                      <Trans i18nKey="billing:connect.setup.countryDescription" />
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                );
              }}
            />
            <Button
              type="submit"
              disabled={isSubmitting || !formReady || !isFormValid}
              className="w-full"
            >
              {isSubmitting ? (
                <Trans i18nKey="billing:connect.setup.submitting" />
              ) : (
                <Trans i18nKey="billing:connect.setup.submit" />
              )}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
