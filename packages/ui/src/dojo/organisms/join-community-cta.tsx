import Link from 'next/link';

import { Button } from '@kit/ui/button';
import { Card, CardContent } from '@kit/ui/card';

import { Trans } from '../atoms/trans';

interface JoinCommunityCTAProps {
  title: string;
  titleIcon?: React.ReactNode;
  descriptionText: string;
  buttonLabel: string;
  buttonLink: string;
  buttonIcon: React.ReactNode;
  dataTestButton?: string;
  ariaLabelButton?: string;
}

export function JoinCommunityCTA({
  title,
  titleIcon,
  descriptionText,
  buttonLabel,
  buttonLink,
  buttonIcon,
  dataTestButton,
  ariaLabelButton,
}: JoinCommunityCTAProps) {
  return (
    <Card className="w-full border border-gray-200 shadow-sm transition-shadow duration-300 hover:shadow-md dark:border-gray-800">
      <CardContent className="p-6">
        <div className="flex flex-col justify-between gap-4 md:flex-row md:items-center">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <div className="rounded-full bg-red-600 p-2">{titleIcon}</div>
              <h3 className="text-lg font-semibold">
                <Trans i18nKey={title} />
              </h3>
            </div>
            <p className="max-w-lg text-gray-600 dark:text-gray-400">
              <Trans i18nKey={descriptionText} />
            </p>
          </div>
          <Button
            className="self-start bg-red-600 text-white hover:bg-red-700 md:self-center"
            asChild
          >
            <Link
              href={buttonLink}
              data-test={dataTestButton}
              aria-label={ariaLabelButton}
            >
              <>
                <Trans i18nKey={buttonLabel} />
                {buttonIcon}
              </>
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
