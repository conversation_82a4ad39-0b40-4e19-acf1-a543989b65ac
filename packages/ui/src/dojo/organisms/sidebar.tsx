import { ReactNode } from 'react';

export interface SidebarRoute {
  path: string;
  label: string;
  icon?: ReactNode;
  children?: SidebarRoute[];
  renderAction?: () => ReactNode;
}

export interface SidebarDivider {
  divider: true;
}

export interface SidebarConfig {
  routes: (SidebarRoute | SidebarDivider)[];
}

// This is a placeholder component - the actual sidebar implementation
// is in the shadcn sidebar component
export function Sidebar() {
  return null;
}
