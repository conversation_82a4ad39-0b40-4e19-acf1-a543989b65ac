'use client';

import { useEffect, useState } from 'react';

import type { Editor } from '@tiptap/react';

/**
 * Hook to get character and word counts from a TipTap editor
 * @param editor - The TipTap editor instance
 * @returns An object with character and word counts
 */
export function useTipTapCharacterCount(editor: Editor | null) {
  const [characterCount, setCharacterCount] = useState(0);
  const [wordCount, setWordCount] = useState(0);

  useEffect(() => {
    if (!editor) return;

    // Function to update counts
    const updateCounts = () => {
      if (editor.storage.characterCount) {
        const chars = editor.storage.characterCount.characters();
        const words = editor.storage.characterCount.words();

        // Only update and log if values have changed
        if (chars !== characterCount || words !== wordCount) {
          setCharacterCount(chars);
          setWordCount(words);
        }
      }
    };

    // Initial update
    updateCounts();

    // Listen for changes
    editor.on('update', updateCounts);

    // Listen for transaction completion (happens after content is loaded)
    editor.on('transaction', updateCounts);

    // Listen for focus (another opportunity to update counts)
    editor.on('focus', updateCounts);

    // Force an update after a short delay to ensure content is loaded
    const timer = setTimeout(() => {
      updateCounts();
    }, 100);

    return () => {
      editor.off('update', updateCounts);
      editor.off('transaction', updateCounts);
      editor.off('focus', updateCounts);
      clearTimeout(timer);
    };
  }, [editor]);

  return { characterCount, wordCount };
}
