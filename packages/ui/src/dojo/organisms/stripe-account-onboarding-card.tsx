'use client';

import { useState } from 'react';

import Link from 'next/link';

import { ArrowR<PERSON>, Banknote } from 'lucide-react';

import { ErrorBoundary } from '@kit/monitoring/components';
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@kit/ui/alert-dialog';
import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import {
  ConnectRequirements,
  ConnectRequirements as ConnectRequirementsType,
} from '@kit/ui/dojo/molecules/connect-requirements';
import {
  ConnectAccountStatus,
  ConnectStatusBadge,
} from '@kit/ui/dojo/molecules/connect-status-badge';
import { StatusAlert } from '@kit/ui/dojo/molecules/status-alert';
import { If } from '@kit/ui/if';
import { Progress } from '@kit/ui/progress';
import { Trans } from '@kit/ui/trans';
import { cn } from '@kit/ui/utils';

export type StripeAccountOnboardingProps = {
  status: ConnectAccountStatus;
  requirements?: ConnectRequirementsType;
  stripeDashboardLink: string;
  onContinueSetup: () => Promise<void>;
  onReset: () => Promise<void>;
  className?: string;
};

/**
 * Component for guiding users through the Connect onboarding process
 */
export function StripeAccountOnboardingCard({
  status,
  requirements,
  stripeDashboardLink,
  onContinueSetup,
  onReset,
  className,
}: StripeAccountOnboardingProps) {
  const [isLoading, setIsLoading] = useState(false);
  const { detailsSubmitted, chargesEnabled, payoutsEnabled } = status;

  // Calculate onboarding progress percentage
  const calculateProgress = () => {
    let progress = 0;
    if (detailsSubmitted) progress += 30;
    if (chargesEnabled) progress += 35;
    if (payoutsEnabled) progress += 35;
    return progress;
  };

  const progress = calculateProgress();
  const isComplete = progress === 100;

  const handleContinueSetup = async () => {
    try {
      setIsLoading(true);
      await onContinueSetup();
    } catch (error) {
      console.error('Error continuing setup:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleReset = async () => {
    try {
      setIsLoading(true);
      await onReset();
    } catch (error) {
      console.error('Error resetting setup:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader>
        <div className="mb-2 flex items-center justify-between">
          <CardTitle>
            <Trans i18nKey="billing:connect.onboarding.title" />
          </CardTitle>
          <ConnectStatusBadge status={status} />
        </div>
        <CardDescription>
          <Trans i18nKey="billing:connect.onboarding.description" />
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">
              <Trans i18nKey="billing:connect.onboarding.progress" />
            </span>
            <span className="font-medium">{progress}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        <div className="grid gap-4">
          <div className="flex items-start gap-2">
            <div
              className={cn(
                'flex h-6 w-6 shrink-0 items-center justify-center rounded-full border',
                detailsSubmitted
                  ? 'bg-primary text-primary-foreground border-transparent'
                  : 'border-primary',
              )}
            >
              <span className="text-xs font-bold">1</span>
            </div>
            <div>
              <p className="font-medium">
                <Trans i18nKey="billing:connect.onboarding.step1Title" />
              </p>
              <p className="text-muted-foreground text-sm">
                <Trans i18nKey="billing:connect.onboarding.step1Description" />
              </p>
            </div>
          </div>

          <div className="flex items-start gap-2">
            <div
              className={cn(
                'flex h-6 w-6 shrink-0 items-center justify-center rounded-full border',
                chargesEnabled
                  ? 'bg-primary text-primary-foreground border-transparent'
                  : 'border-primary',
              )}
            >
              <span className="text-xs font-bold">2</span>
            </div>
            <div>
              <p className="font-medium">
                <Trans i18nKey="billing:connect.onboarding.step2Title" />
              </p>
              <p className="text-muted-foreground text-sm">
                <Trans i18nKey="billing:connect.onboarding.step2Description" />
              </p>
            </div>
          </div>

          <div className="flex items-start gap-2">
            <div
              className={cn(
                'flex h-6 w-6 shrink-0 items-center justify-center rounded-full border',
                payoutsEnabled
                  ? 'bg-primary text-primary-foreground border-transparent'
                  : 'border-primary',
              )}
            >
              <span className="text-xs font-bold">3</span>
            </div>
            <div>
              <p className="font-medium">
                <Trans i18nKey="billing:connect.onboarding.step3Title" />
              </p>
              <p className="text-muted-foreground text-sm">
                <Trans i18nKey="billing:connect.onboarding.step3Description" />
              </p>
            </div>
          </div>
        </div>

        {requirements && <ConnectRequirements requirements={requirements} />}
      </CardContent>
      <CardFooter>
        {isComplete ? (
          <Link
            href={stripeDashboardLink}
            target="_blank"
            rel="noopener noreferrer"
            className="w-full"
          >
            <Button className="w-full" disabled={isLoading}>
              <Banknote className="mr-2 h-4 w-4" />
              <Trans i18nKey="billing:connect.onboarding.goToDashboard" />
            </Button>
          </Link>
        ) : (
          <div className="flex w-full flex-row gap-2">
            <If
              condition={
                !detailsSubmitted || !chargesEnabled || !payoutsEnabled
              }
            >
              <ResetStripeAccountModal onConfirmReset={handleReset} />
            </If>
            <Button
              className="w-full"
              onClick={handleContinueSetup}
              disabled={isLoading}
            >
              <Trans i18nKey="billing:connect.onboarding.continueSetup" />
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        )}
      </CardFooter>
    </Card>
  );
}

function ResetStripeAccountModal({
  onConfirmReset,
}: {
  onConfirmReset: () => Promise<void>;
}) {
  const [isOpen, setIsOpen] = useState(false);
  const [isResetting, setIsResetting] = useState(false);

  const handleConfirmClick = async () => {
    setIsResetting(true);
    try {
      await onConfirmReset();
      setIsOpen(false);
    } catch (error) {
      console.error('Failed to reset stripe account:', error);
    } finally {
      setIsResetting(false);
    }
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialogTrigger asChild>
        <Button data-test={'reset-account-button'} variant="outline">
          <Trans i18nKey="billing:connect.onboarding.reset" />
        </Button>
      </AlertDialogTrigger>

      <AlertDialogContent onEscapeKeyDown={(e) => e.preventDefault()}>
        <AlertDialogHeader>
          <AlertDialogTitle>
            <Trans i18nKey="billing:connect.onboarding.reset" />
          </AlertDialogTitle>
        </AlertDialogHeader>
        <AlertDialogDescription>
          <Trans i18nKey="billing:connect.onboarding.resetDescription" />
        </AlertDialogDescription>

        <ErrorBoundary
          fallback={
            <StatusAlert
              variant="destructive"
              titleKey="billing:connect.onboarding.resetErrorHeading"
              descriptionKey="common:genericError"
            />
          }
        >
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isResetting}>
              <Trans i18nKey="common:cancel" />
            </AlertDialogCancel>

            <Button
              onClick={handleConfirmClick}
              data-test={'confirm-reset-connected-account-button'}
              disabled={isResetting}
              variant={'destructive'}
            >
              {isResetting ? (
                <Trans i18nKey="common:loading" />
              ) : (
                <Trans i18nKey="billing:connect.onboarding.reset" />
              )}
            </Button>
          </AlertDialogFooter>
        </ErrorBoundary>
      </AlertDialogContent>
    </AlertDialog>
  );
}
