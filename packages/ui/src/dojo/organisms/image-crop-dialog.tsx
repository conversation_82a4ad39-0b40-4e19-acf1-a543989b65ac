'use client';

import { use<PERSON><PERSON>back, useEffect, useRef, useState, useTransition } from 'react';

// Re-import imageCompression
import imageCompression from 'browser-image-compression';
// Import react-image-crop and its types/helpers
import ReactCrop, {
  type Crop,
  type PixelCrop,
  centerCrop,
  makeAspectCrop,
} from 'react-image-crop';
// Import CSS
import 'react-image-crop/dist/ReactCrop.css';
import { toast } from 'sonner';

import { But<PERSON> } from '../../shadcn/button';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../../shadcn/dialog';
import { Trans } from '../atoms/trans';

/**
 * Props for the ImageCropDialog component.
 */
type ImageCropDialogProps = {
  /** Controls whether the dialog is open. */
  open: boolean;
  /** Callback fired when the dialog open state changes. */
  onOpenChange: (open: boolean) => void;
  /** The source URL (data URL or web URL) of the image to crop. */
  imageSrc: string | null;
  /** The original file object (used for determining output type and filename). */
  originalFile: File | null;
  /**
   * The desired aspect ratio for the crop (e.g., 16 / 9, 1).
   * If omitted or set to `undefined`, free-form cropping is enabled.
   * @default undefined (free-form crop)
   */
  aspectRatio?: number;
  /**
   * The shape of the crop area ('rect' or 'circ'). react-image-crop uses 'circularCrop' boolean.
   * @default 'rect'
   */
  cropShape?: 'rect' | 'round'; // Keep prop name, map to circularCrop
  /** Callback fired with the cropped and compressed File object when the user saves. */
  onSave: (file: File) => Promise<void> | void;
  /** Optional callback fired when the user cancels the dialog. */
  onCancel?: () => void;
};

// --- Component --- //

/**
 * A dialog component for cropping and compressing images using react-image-crop.
 */
export function ImageCropDialog({
  open,
  onOpenChange,
  imageSrc,
  originalFile,
  aspectRatio,
  cropShape = 'rect',
  onSave,
  onCancel,
}: ImageCropDialogProps) {
  const [isProcessing, startTransition] = useTransition();
  // State for react-image-crop
  const [crop, setCrop] = useState<Crop>();
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>();
  const imgRef = useRef<HTMLImageElement>(null);

  // Calculate initial crop when image loads
  function onImageLoad(e: React.SyntheticEvent<HTMLImageElement>) {
    const { width, height } = e.currentTarget;
    let newCrop: Crop;

    if (aspectRatio) {
      // If aspect ratio is defined, create a centered crop with that ratio
      newCrop = centerCrop(
        makeAspectCrop(
          {
            unit: '%',
            width: 90, // Start with 90% width for aspect crops
          },
          aspectRatio, // Use the provided aspect ratio
          width,
          height,
        ),
        width,
        height,
      );
    } else {
      // If no aspect ratio (free-form), default to covering the whole image
      newCrop = {
        unit: '%', // Use percentage for responsiveness
        x: 0,
        y: 0,
        width: 100,
        height: 100,
      };
    }

    setCrop(newCrop);
    setCompletedCrop(undefined); // Reset completed crop on new image load
  }

  const resetLocalState = () => {
    setCrop(undefined);
    setCompletedCrop(undefined);
    // imgRef doesn't need resetting
  };

  const handleSaveClick = useCallback(async () => {
    if (!completedCrop || !imgRef.current) {
      console.error('Crop details or image ref not available');
      return;
    }

    startTransition(async () => {
      try {
        const targetType = originalFile?.type ?? 'image/jpeg';

        // --- Calculate scaled dimensions and redraw on correctly sized canvas --- //
        const image = imgRef.current!;
        const scaleX = image.naturalWidth / image.width;
        const scaleY = image.naturalHeight / image.height;

        // Target canvas dimensions based on natural image size
        const targetWidth = Math.round(completedCrop.width * scaleX);
        const targetHeight = Math.round(completedCrop.height * scaleY);

        const canvas = document.createElement('canvas');
        canvas.width = targetWidth;
        canvas.height = targetHeight;
        const ctx = canvas.getContext('2d');

        if (!ctx) {
          throw new Error('Failed to get canvas context');
        }

        // Draw the scaled source region onto the target-sized canvas
        ctx.drawImage(
          image,
          completedCrop.x * scaleX, // Source X
          completedCrop.y * scaleY, // Source Y
          completedCrop.width * scaleX, // Source Width
          completedCrop.height * scaleY, // Source Height
          0, // Destination X
          0, // Destination Y
          targetWidth, // Destination Width (full canvas)
          targetHeight, // Destination Height (full canvas)
        );
        // --- End of scaled redraw --- //

        // Get blob from the newly drawn canvas (uncompressed)
        const uncompressedBlob = await new Promise<Blob | null>((resolve) => {
          canvas.toBlob((blob) => resolve(blob), targetType, 0.95);
        });

        if (!uncompressedBlob) {
          throw new Error('Failed to get blob from canvas');
        }

        // --- Reintroduce compression --- //
        let finalBlob: Blob = uncompressedBlob; // Start with uncompressed blob
        const options = {
          useWebWorker: true,
          // Keep maxSizeMB and fileType commented out for now
          // maxSizeMB: 5,
          // fileType: targetType,
        };
        try {
          // Create a temporary File object for the compression library
          const tempFileName = originalFile?.name ?? 'temp_image.tmp';
          const tempFile = new File([uncompressedBlob], tempFileName, {
            type: uncompressedBlob.type,
          });

          // Attempt compression using the temp File, assign resulting Blob to finalBlob
          finalBlob = await imageCompression(tempFile, options);
        } catch (compressionError) {
          console.error('Image compression failed:', compressionError);
          console.warn('Falling back to uncompressed cropped image.');
          // finalBlob already holds uncompressedBlob as fallback
        }
        // --- End of compression step --- //

        // Use the finalBlob (either compressed or original) for the rest of the logic
        const finalFileType = finalBlob.type;
        const fileExtension = finalFileType.split('/')[1];
        if (!fileExtension) {
          console.error(
            'Could not determine file extension from blob type:',
            finalFileType,
          );
          const fallbackExt = originalFile?.name.split('.').pop() ?? 'jpg';
          const fallbackMime = `image/${fallbackExt}`;
          console.warn(
            `Falling back to file extension: ${fallbackExt}, type: ${fallbackMime}`,
          );
          const originalNameStem =
            originalFile?.name.split('.').slice(0, -1).join('.') || 'image';
          const newFileName = `cropped_${originalNameStem}.${fallbackExt}`;
          // Create File from finalBlob
          const finalFileToUpload = new File([finalBlob], newFileName, {
            type: fallbackMime,
            lastModified: Date.now(),
          });
          await onSave(finalFileToUpload);
        } else {
          const originalNameStem =
            originalFile?.name.split('.').slice(0, -1).join('.') || 'image';
          const newFileName = `cropped_${originalNameStem}.${fileExtension}`;
          // Create File from finalBlob
          const finalFileToUpload = new File([finalBlob], newFileName, {
            type: finalFileType,
            lastModified: Date.now(),
          });
          await onSave(finalFileToUpload);
        }

        onOpenChange(false);
      } catch (error) {
        console.error('Error during crop/compression/save:', error);
        toast.error('Failed to process image. Please try again.');
      } finally {
        // No need to call resetLocalState here, useEffect handles it
      }
    });
  }, [
    completedCrop,
    originalFile,
    onSave,
    onOpenChange,
    // imgRef is stable, no need to include
  ]);

  const handleCancelClick = () => {
    if (onCancel) {
      onCancel();
    }
    resetLocalState();
    onOpenChange(false);
  };

  // Reset local state when dialog is closed externally or image changes
  useEffect(() => {
    if (!open || !imageSrc) {
      resetLocalState();
    }
  }, [open, imageSrc]);

  // Map cropShape prop to circularCrop boolean
  const circularCrop = cropShape === 'round';

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>
            <Trans i18nKey="common:cropImage" defaults="Crop Image" />
          </DialogTitle>
        </DialogHeader>

        {/* Container for ReactCrop - Remove fixed height, add max-height */}
        <div className="bg-muted relative max-h-[60vh] w-full">
          {imageSrc ? (
            <ReactCrop
              crop={crop}
              onChange={(_, percentCrop) => setCrop(percentCrop)} // Use percent crop for resilience
              onComplete={(c) => setCompletedCrop(c)}
              aspect={aspectRatio}
              circularCrop={circularCrop}
              // Add other props like minWidth, minHeight if needed
            >
              {/* Render the image as a child */}
              {/* eslint-disable-next-line @next/next/no-img-element -- Required by react-image-crop */}
              <img
                ref={imgRef}
                alt="Crop me"
                src={imageSrc}
                style={{ transform: `scale(1) rotate(0deg)` }} // react-image-crop handles scaling/positioning
                onLoad={onImageLoad}
              />
            </ReactCrop>
          ) : (
            <div className="text-muted-foreground flex h-full items-center justify-center">
              No image selected
            </div>
          )}
        </div>

        {/* Remove zoom/rotation sliders - react-image-crop handles zoom via interaction */}

        <DialogFooter>
          <Button
            variant="ghost"
            onClick={handleCancelClick}
            disabled={isProcessing}
          >
            <Trans i18nKey="common:cancel" defaults="Cancel" />
          </Button>
          <Button
            onClick={handleSaveClick}
            disabled={!completedCrop || isProcessing}
          >
            {isProcessing ? (
              <Trans i18nKey="common:processing" defaults="Processing..." />
            ) : (
              <Trans i18nKey="common:save" defaults="Save Crop" />
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

ImageCropDialog.displayName = 'ImageCropDialog';
