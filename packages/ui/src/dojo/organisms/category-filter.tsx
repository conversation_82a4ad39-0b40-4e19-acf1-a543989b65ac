'use client';

import { useState } from 'react';

import { usePathname, useRouter, useSearchParams } from 'next/navigation';

import { z } from 'zod';
import { object, string } from 'zod';

import { Button } from '@kit/ui/button';
import { Trans } from '@kit/ui/trans';

export const CategorySchema = object({
  id: string().uuid(),
  name: string().min(1),
  description: string().min(1).nullable(),
  icon: string().min(1).nullable(),
  slug: string().min(1),
});

export function CategoryFilter({
  categories,
  initialCategoriesShown = 4,
  minCategoriesForShowMore = 5,
  defaultCategory = 'all',
}: {
  categories: z.infer<typeof CategorySchema>[];
  initialCategoriesShown?: number;
  minCategoriesForShowMore?: number;
  defaultCategory?: string;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // State to track whether to show all categories or just the first 5
  const [showAllCategories, setShowAllCategories] = useState(false);

  // Get current category from URL or use default
  const currentCategory = searchParams.get('category') ?? defaultCategory;

  // Handle no categories scenario
  if (!categories || categories.length === 0) {
    return (
      <p>
        <Trans i18nKey="forums:settings.noCategoriesFound" />
      </p>
    );
  }

  const handleCategoryClick = (categoryId: string) => {
    const params = new URLSearchParams(searchParams.toString());

    if (categoryId === defaultCategory) {
      params.delete('category');
    } else {
      params.set('category', categoryId);
    }

    router.push(`${pathname}?${params.toString()}`);
  };

  const handleClearCategory = () => {
    const params = new URLSearchParams(searchParams.toString());
    params.delete('category');
    router.push(`${pathname}?${params.toString()}`);
  };

  // Determine which categories to display based on state
  const visibleCategories = showAllCategories
    ? categories
    : categories.slice(0, initialCategoriesShown);
  const hasMoreCategories = categories.length > minCategoriesForShowMore;

  return (
    <div data-component-name="CategoryFilter">
      {/* All category button */}
      <Button
        key="all"
        variant={currentCategory === defaultCategory ? 'default' : 'secondary'}
        onClick={handleClearCategory}
        className="mr-1 hover:bg-slate-200"
      >
        <span className="mr-2">🔍</span>
        All
      </Button>

      {/* Category buttons */}
      {visibleCategories.map((category) => (
        <Button
          key={category.id}
          variant={currentCategory === category.id ? 'default' : 'secondary'}
          onClick={() => handleCategoryClick(category.id)}
          className="mx-1 my-1 hover:bg-slate-200"
        >
          <span className="mr-2">{category.icon}</span>
          {category.name}
        </Button>
      ))}

      {hasMoreCategories && (
        <Button
          variant="outline"
          onClick={() => setShowAllCategories(!showAllCategories)}
          className="mx-1 my-1"
        >
          {showAllCategories
            ? 'Less'
            : `More (${categories.length - initialCategoriesShown})`}
        </Button>
      )}
    </div>
  );
}
