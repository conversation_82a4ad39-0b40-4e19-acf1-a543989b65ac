'use client';

import Link from 'next/link';

import { Banknote } from 'lucide-react';

import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import {
  ConnectRequirements,
  ConnectRequirements as ConnectRequirementsType,
} from '@kit/ui/dojo/molecules/connect-requirements';
import {
  ConnectAccountStatus,
  ConnectStatusBadge,
} from '@kit/ui/dojo/molecules/connect-status-badge';
import { Trans } from '@kit/ui/trans';
import { cn } from '@kit/ui/utils';

export type StripeAccountLoginProps = {
  status: ConnectAccountStatus;
  requirements?: ConnectRequirementsType;
  stripeDashboardLink: string;
  className?: string;
};

/**
 * Component to give the users a link to manage their Stripe Account on Stripe
 */
export function StripeAccountLoginCard({
  status,
  requirements,
  stripeDashboardLink,
  className,
}: StripeAccountLoginProps) {
  return (
    <Card className={cn('w-full', className)}>
      <CardHeader>
        <div className="mb-2 flex items-center justify-between">
          <CardTitle>
            <Trans i18nKey="billing:connect.onboarding.title" />
          </CardTitle>
          <ConnectStatusBadge status={status} />
        </div>
        <CardDescription>
          <Trans i18nKey="billing:connect.onboarding.description" />
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {requirements && <ConnectRequirements requirements={requirements} />}
      </CardContent>
      <CardFooter>
        <Link
          href={stripeDashboardLink}
          target="_blank"
          rel="noopener noreferrer"
          className="w-full"
        >
          <Button className="w-full">
            <Banknote className="mr-2 h-4 w-4" />
            <Trans i18nKey="billing:connect.onboarding.goToDashboard" />
          </Button>
        </Link>
      </CardFooter>
    </Card>
  );
}
