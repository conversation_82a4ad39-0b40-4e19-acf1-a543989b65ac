import { Trans } from '@kit/ui/trans';
import { cn } from '@kit/ui/utils';

export interface CharacterCountDisplayProps {
  characterCount: number;
  wordCount: number;
  characterLimit?: number;
  warningThreshold?: number;
  errorThreshold?: number;
  className?: string;
  showWords?: boolean;
  showCharacters?: boolean;
}

export function CharacterCountDisplay({
  characterCount,
  wordCount,
  characterLimit,
  warningThreshold = 0.8, // 80% default
  errorThreshold = 0.97, // 97% default
  className,
  showWords = true,
  showCharacters = true,
}: CharacterCountDisplayProps) {
  // Calculate thresholds only when limit is provided
  const isApproachingLimit = characterLimit
    ? characterCount > characterLimit * warningThreshold
    : false;
  const isNearLimit = characterLimit
    ? characterCount > characterLimit * errorThreshold
    : false;

  // Determine style based on thresholds
  const characterCountClass = isNearLimit
    ? 'text-destructive'
    : isApproachingLimit
      ? 'text-amber-500'
      : '';

  return (
    <div
      className={cn(
        'text-muted-foreground flex items-center justify-end gap-4 text-sm',
        className,
      )}
    >
      {showWords && (
        <span>
          <Trans i18nKey="common:words" values={{ count: wordCount }} />
        </span>
      )}
      {showCharacters && (
        <span className={characterCountClass}>
          {characterLimit ? (
            <Trans
              i18nKey="common:charactersWithLimit"
              values={{
                count: characterCount,
                limit: characterLimit,
              }}
            />
          ) : (
            <Trans
              i18nKey="common:characters"
              values={{ count: characterCount }}
            />
          )}
        </span>
      )}
    </div>
  );
}

CharacterCountDisplay.displayName = 'CharacterCountDisplay';
