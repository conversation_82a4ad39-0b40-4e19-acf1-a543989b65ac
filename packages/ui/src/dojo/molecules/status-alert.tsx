'use client';

import { Alert, AlertDescription, AlertTitle } from '@kit/ui/alert';
import { AlertDialogCancel, AlertDialogFooter } from '@kit/ui/alert-dialog';
import { Trans } from '@kit/ui/trans';
import { cn } from '@kit/ui/utils';

type StatusAlertProps = {
  variant: 'success' | 'destructive' | 'default' | 'info' | 'warning';
  titleKey: string;
  descriptionKey: string;
  icon?: React.ReactNode;
  showCancelButton?: boolean;
  className?: string;
  'data-test'?: string;
};

/**
 * @name StatusAlert
 * @description A reusable alert component to display status messages (success, error, info, default).
 * Uses Trans component for internationalized title and description.
 * Optionally includes a cancel button in the footer.
 */
export function StatusAlert({
  variant,
  titleKey,
  descriptionKey,
  icon,
  showCancelButton,
  className,
  'data-test': dataTest,
}: StatusAlertProps) {
  return (
    <>
      <Alert variant={variant} className={cn(className)} data-test={dataTest}>
        {icon && <div className="mr-2 shrink-0">{icon}</div>}
        <div className="flex-1">
          <AlertTitle>
            <Trans i18nKey={titleKey} />
          </AlertTitle>
          <AlertDescription>
            <Trans i18nKey={descriptionKey} />
          </AlertDescription>
        </div>
      </Alert>

      {showCancelButton && (
        <AlertDialogFooter className="mt-4">
          <AlertDialogCancel>
            <Trans i18nKey={'common:cancel'} />
          </AlertDialogCancel>
        </AlertDialogFooter>
      )}
    </>
  );
}
