'use client';

import { useCallback, useEffect, useState } from 'react';

import Image from 'next/image';

import { Image as ImageIcon } from 'lucide-react';
import { useForm } from 'react-hook-form';

import { Button } from '../../shadcn/button';
import { Trans } from '../atoms/trans';
import { ImageUploadInput } from './image-upload-input';

export function ImageUploader(
  props: React.PropsWithChildren<{
    value: string | null | undefined;
    imageRounded?: string;
    imageSize?: string;
    onValueChange: (value: File | null) => unknown;
  }>,
) {
  const {
    value,
    imageRounded = 'rounded-full',
    imageSize = 'size-20',
    children,
  } = props;

  const [image, setImage] = useState(value);

  const { setValue, register } = useForm<{
    value: string | null | FileList;
  }>({
    defaultValues: {
      value: props.value,
    },
    mode: 'onChange',
    reValidateMode: 'onChange',
  });

  const control = register('value');

  const onClear = useCallback(() => {
    props.onValueChange(null);
    setValue('value', null);
    setImage('');
  }, [props, setValue]);

  const onValueChange = useCallback(
    ({ image, file }: { image: string; file: File }) => {
      props.onValueChange(file);

      setImage(image);
    },
    [props],
  );

  const Input = () => (
    <ImageUploadInput
      {...control}
      accept={'image/*'}
      className={'absolute h-full w-full'}
      visible={false}
      multiple={false}
      onValueChange={onValueChange}
    />
  );

  useEffect(() => {
    setImage(value);
  }, [value]);

  if (!image) {
    return (
      <FallbackImage
        descriptionSection={children}
        imageRounded={imageRounded}
        imageSize={imageSize}
      >
        <Input />
      </FallbackImage>
    );
  }

  return (
    <div className={'flex items-center space-x-4'}>
      <label className={`relative ${imageSize} animate-in fade-in zoom-in-50`}>
        <Image
          fill
          className={`${imageSize} ${imageRounded}`}
          src={image}
          alt={''}
        />

        <Input />
      </label>
      <div>
        <Button onClick={onClear} size={'sm'} variant={'ghost'}>
          <Trans i18nKey={'common:clear'} />
        </Button>
      </div>
    </div>
  );
}

function FallbackImage(
  props: React.PropsWithChildren<{
    descriptionSection?: React.ReactNode;
    imageRounded?: string;
    imageSize?: string;
  }>,
) {
  return (
    <div className={'flex items-center space-x-4'}>
      <label
        className={`${props.imageSize} ${props.imageRounded} border-border animate-in fade-in zoom-in-50 hover:border-primary relative flex cursor-pointer flex-col items-center justify-center border`}
      >
        <ImageIcon className={'text-primary h-8'} />

        {props.children}
      </label>

      {props.descriptionSection}
    </div>
  );
}
