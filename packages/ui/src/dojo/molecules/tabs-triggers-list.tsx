import type { ReactNode } from 'react';

import { TabsTrigger } from '@radix-ui/react-tabs';

import { cn } from '@kit/ui/utils';

export function TabsTriggersList(props: {
  values: string[];
  labels: (string | ReactNode)[];
  className?: string;
}) {
  return (
    <>
      {props.values.map((value, index) => (
        <TabsTrigger
          value={value}
          className={cn(props.className)}
          key={`${index}->${value}`}
        >
          {props.labels[index]}
        </TabsTrigger>
      ))}
    </>
  );
}
