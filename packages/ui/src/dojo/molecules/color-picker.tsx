'use client';

import { Paintbrush } from 'lucide-react';

import { cn } from '@kit/ui/utils';

import { Button } from '../../shadcn/button';
import { Input } from '../../shadcn/input';
import { Popover, PopoverContent, PopoverTrigger } from '../../shadcn/popover';
import { Tabs } from '../../shadcn/tabs';

export function ColorPicker({
  pickedColor,
  onColorChange,
  className,
}: {
  pickedColor: string;
  onColorChange: (pickedColor: string) => void;
  className?: string;
}) {
  const solids = [
    '#09203f', // Dark Blue
    '#3357FF', // Blue
    '#338CFF', // Light Blue
    '#70e2ff', // Sky Blue
    '#00CED1', // Dark Turquoise
    '#cd93ff', // Lavender
    '#A133FF', // Purple
    '#9400D3', // Dark Violet
    '#FF33A1', // Pink
    '#ff75c3', // Light Pink
    '#FF338C', // Hot Pink
    '#FF8C33', // Orange
    '#ffa647', // Light Orange
    '#FF5733', // Red-Orange
    '#8B0000', // Dark Red
    '#FF0000', // Red
    '#ffe83f', // Yellow
    '#FFD700', // Gold
    '#33FFA1', // Mint
    '#33FF57', // Green
    '#9fff5b', // Light Green
    '#FFB6C1', // Light Pink
    '#E2E2E2', // Light Gray
    '#FFFFFF', // White
  ];

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant={'outline'}
          className={cn(
            'w-32 justify-start text-left font-normal',
            !pickedColor && 'text-muted-foreground',
            className,
          )}
        >
          <div className="flex w-full items-center gap-2">
            {pickedColor ? (
              <div
                className="h-4 w-4 rounded border bg-cover! bg-center! transition-all"
                style={{ background: pickedColor }}
              />
            ) : (
              <Paintbrush className="h-4 w-4 border" />
            )}
            <div className="flex-1 truncate">
              {pickedColor ? pickedColor : 'Pick a color'}
            </div>
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-64">
        <Tabs className="w-full">
          <div className="mb-4 flex w-full flex-wrap gap-1">
            {solids.map((s) => (
              <div
                key={s}
                style={{ background: s }}
                className="h-6 w-6 cursor-pointer rounded-md border border-slate-300 active:scale-105"
                onClick={() => onColorChange(s)}
              />
            ))}
          </div>
        </Tabs>

        <Input
          id="custom"
          value={pickedColor}
          className="col-span-2 mt-4 h-8"
          onChange={(e) => onColorChange(e.currentTarget.value)}
        />
      </PopoverContent>
    </Popover>
  );
}
