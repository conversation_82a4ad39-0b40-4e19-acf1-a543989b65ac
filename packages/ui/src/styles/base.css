/*
 * base.css
 *
 * Contains base styles, resets, and global component overrides.
 */

/* <PERSON><PERSON><PERSON> commented out v3 border fix - rely on rule in globals.css */
/*
  Tailwind v3 Border Compatibility Fix (Commented out)

  The default border color changed to `currentcolor` in Tailwind CSS v4.
  This rule was added for v3 compatibility.
  It might not be needed in v4 if border utilities (e.g., `border-border`)
  are used explicitly where needed.
  Uncomment and test if you notice border issues after upgrading.
*/
/*
*, ::after, ::before, ::backdrop, ::file-selector-button {
  border-color: hsl(var(--border) / <alpha-value>);
}
*/

/* Global container adjustments */
.container {
  @apply max-sm:px-4;
}

/* Optimize Radix dropdowns for mobile */
[data-radix-popper-content-wrapper] {
  @apply w-full md:w-auto;
}

[data-radix-menu-content] {
  @apply w-full rounded-none md:w-auto md:rounded-lg;
}

[data-radix-menu-content] [role='menuitem'] {
  @apply min-h-12 md:min-h-0;
}

/* Site Header/Footer dividers */
.site-header > .container:before,
.site-footer > .container:before {
  background: radial-gradient(
    62.87% 100% at 50% 100%,
    var(--color-gray-200) 0%,
    rgba(255, 255, 255, 0) 100%
  );

  bottom: 0;
  content: '';
  height: 1px;
  left: 0;
  position: absolute;
  width: 100%;
}

.dark .site-header > .container:before,
.dark .site-footer > .container:before {
  background: radial-gradient(
    62.87% 100% at 50% 100%,
    rgba(255, 255, 255, 0.16) 0%,
    rgba(255, 255, 255, 0) 100%
  );
}

.site-footer > .container:before {
  top: 0;
} 