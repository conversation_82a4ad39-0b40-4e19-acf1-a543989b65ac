/*
* global.css
*
* Global styles for the entire application
 */

@import 'tailwindcss';
@import "tw-animate-css";

/* 1. Theme Definition: Define variables and theme mapping first */
@import './theme.css';
@import './theme.utilities.css';
/* @import './shadcn-ui.css'; */

/* 2. Variants: Define how variants like dark mode work */
@variant dark (&:where(.dark, .dark *));

/* 3. Plugins: Register any Tailwind plugins */


/* 4. Content Sources: Tell Tailwind where to scan for classes */
@source "../../../../packages/*/src/**/*.{ts,tsx}";
@source "../../../../packages/ui/src/**/*.{ts,tsx}";
@source "../../../../packages/features/*/src/**/*.{ts,tsx}";
@source "../../../../packages/plugins/*/src/**/*.{ts,tsx}";
@source "../../../../packages/cms/*/src/**/*.{ts,tsx}";
@source "../../../../apps/web/{app,components,config,lib}/**/*.{ts,tsx}";
@source "../../../../apps/storybook/{app,components,config,lib}/**/*.{ts,tsx}";

/* 5. Tailwind Core: Import Tailwind itself to generate utilities based on the above config */


/* 6. Base Styles: Apply global styles using the generated utilities */
@import './base.css';

/* 7. Specific Component Styles */
@import './markdoc.css';
@import './dojo.css';



/* 8. Base Layer Overrides/Defaults */
@layer base {
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--border, currentColor);
  }
  button:not([disabled]),
  [role="button"]:not([disabled]) {
    cursor: pointer;
  }
  [role="menuitem"]:not([disabled]) {
    cursor: pointer;
  }
/* 
  input::placeholder,
  textarea::placeholder {
    color: theme(--color-muted-foreground);
  } */
}