import { ObjectToCamel } from '@kit/shared/utils';
import type { Database } from '@kit/supabase/database';

type NotificationRow = Database['public']['Tables']['notifications']['Row'];

export type Notification = ObjectToCamel<NotificationRow>;

export type NotificationType = Notification['type'];

export type PartialNotification = Pick<
  Notification,
  'id' | 'body' | 'dismissed' | 'type' | 'createdAt' | 'link'
>;

export type NotificationInsert =
  Database['public']['Tables']['notifications']['Insert'];
